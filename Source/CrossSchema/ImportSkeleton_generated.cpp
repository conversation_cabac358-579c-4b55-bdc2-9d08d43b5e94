#include "ImportSkeleton_generated.h"
namespace CrossSchema {
 const flatbuffers::TypeTable * ImportSlotGroup::MiniReflectTypeTable(){
    return ImportSlotGroupTypeTable();
  }
const flatbuffers::String * ImportSlotGroup::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportSlotGroup::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * ImportSlotGroup::slot_names() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_SLOT_NAMES);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * ImportSlotGroup::mutable_slot_names() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_SLOT_NAMES);
  }
 bool ImportSlotGroup::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_SLOT_NAMES) &&
           verifier.VerifyVector(slot_names()) &&
           verifier.VerifyVectorOfStrings(slot_names()) &&
           verifier.EndTable();
  }
  void ImportSlotGroupBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportSlotGroup::VT_NAME, name);
  }
  void ImportSlotGroupBuilder::add_slot_names(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> slot_names) {
    fbb_.AddOffset(ImportSlotGroup::VT_SLOT_NAMES, slot_names);
  }
flatbuffers::Offset<ImportSlotGroup> CreateImportSlotGroup(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> slot_names){
    ImportSlotGroupBuilder builder_(_fbb);
  builder_.add_slot_names(slot_names);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ImportSlotGroup> CreateImportSlotGroupDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *slot_names) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto slot_names__ = slot_names ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*slot_names) : 0;
  return CrossSchema::CreateImportSlotGroup(
      _fbb,
      name__,
      slot_names__);
}

 const flatbuffers::TypeTable * ImportTwinBone::MiniReflectTypeTable(){
    return ImportTwinBoneTypeTable();
  }
const flatbuffers::String * ImportTwinBone::bonename() const{
    return GetPointer<const flatbuffers::String *>(VT_BONENAME);
  }
  flatbuffers::String * ImportTwinBone::mutable_bonename() {
    return GetPointer<flatbuffers::String *>(VT_BONENAME);
  }
const flatbuffers::String * ImportTwinBone::twinname() const{
    return GetPointer<const flatbuffers::String *>(VT_TWINNAME);
  }
  flatbuffers::String * ImportTwinBone::mutable_twinname() {
    return GetPointer<flatbuffers::String *>(VT_TWINNAME);
  }
 bool ImportTwinBone::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_BONENAME) &&
           verifier.VerifyString(bonename()) &&
           VerifyOffset(verifier, VT_TWINNAME) &&
           verifier.VerifyString(twinname()) &&
           verifier.EndTable();
  }
  void ImportTwinBoneBuilder::add_bonename(flatbuffers::Offset<flatbuffers::String> bonename) {
    fbb_.AddOffset(ImportTwinBone::VT_BONENAME, bonename);
  }
  void ImportTwinBoneBuilder::add_twinname(flatbuffers::Offset<flatbuffers::String> twinname) {
    fbb_.AddOffset(ImportTwinBone::VT_TWINNAME, twinname);
  }
flatbuffers::Offset<ImportTwinBone> CreateImportTwinBone(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> bonename,
    flatbuffers::Offset<flatbuffers::String> twinname){
    ImportTwinBoneBuilder builder_(_fbb);
  builder_.add_twinname(twinname);
  builder_.add_bonename(bonename);
  return builder_.Finish();
}

flatbuffers::Offset<ImportTwinBone> CreateImportTwinBoneDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *bonename,
    const char *twinname) {
  auto bonename__ = bonename ? _fbb.CreateString(bonename) : 0;
  auto twinname__ = twinname ? _fbb.CreateString(twinname) : 0;
  return CrossSchema::CreateImportTwinBone(
      _fbb,
      bonename__,
      twinname__);
}

 const flatbuffers::TypeTable * ImportSocket::MiniReflectTypeTable(){
    return ImportSocketTypeTable();
  }
const flatbuffers::String * ImportSocket::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportSocket::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::String * ImportSocket::attached_bone() const{
    return GetPointer<const flatbuffers::String *>(VT_ATTACHED_BONE);
  }
  flatbuffers::String * ImportSocket::mutable_attached_bone() {
    return GetPointer<flatbuffers::String *>(VT_ATTACHED_BONE);
  }
 bool ImportSocket::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_ATTACHED_BONE) &&
           verifier.VerifyString(attached_bone()) &&
           verifier.EndTable();
  }
  void ImportSocketBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportSocket::VT_NAME, name);
  }
  void ImportSocketBuilder::add_attached_bone(flatbuffers::Offset<flatbuffers::String> attached_bone) {
    fbb_.AddOffset(ImportSocket::VT_ATTACHED_BONE, attached_bone);
  }
flatbuffers::Offset<ImportSocket> CreateImportSocket(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::String> attached_bone){
    ImportSocketBuilder builder_(_fbb);
  builder_.add_attached_bone(attached_bone);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ImportSocket> CreateImportSocketDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const char *attached_bone) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto attached_bone__ = attached_bone ? _fbb.CreateString(attached_bone) : 0;
  return CrossSchema::CreateImportSocket(
      _fbb,
      name__,
      attached_bone__);
}

 const flatbuffers::TypeTable * ImportRefSkeleton::MiniReflectTypeTable(){
    return ImportRefSkeletonTypeTable();
  }
const flatbuffers::String * ImportRefSkeleton::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportRefSkeleton::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> * ImportRefSkeleton::skelteon() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> *>(VT_SKELTEON);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> * ImportRefSkeleton::mutable_skelteon() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> *>(VT_SKELTEON);
  }
 bool ImportRefSkeleton::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_SKELTEON) &&
           verifier.VerifyVector(skelteon()) &&
           verifier.VerifyVectorOfTables(skelteon()) &&
           verifier.EndTable();
  }
  void ImportRefSkeletonBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportRefSkeleton::VT_NAME, name);
  }
  void ImportRefSkeletonBuilder::add_skelteon(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>>> skelteon) {
    fbb_.AddOffset(ImportRefSkeleton::VT_SKELTEON, skelteon);
  }
flatbuffers::Offset<ImportRefSkeleton> CreateImportRefSkeleton(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>>> skelteon){
    ImportRefSkeletonBuilder builder_(_fbb);
  builder_.add_skelteon(skelteon);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ImportRefSkeleton> CreateImportRefSkeletonDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> *skelteon) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto skelteon__ = skelteon ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportBoneNode>>(*skelteon) : 0;
  return CrossSchema::CreateImportRefSkeleton(
      _fbb,
      name__,
      skelteon__);
}

 const flatbuffers::TypeTable * ImportRunSkeleton::MiniReflectTypeTable(){
    return ImportRunSkeletonTypeTable();
  }
uint32_t  ImportRunSkeleton::fversion() const{
    return GetField<uint32_t>(VT_FVERSION, 0);
  }
  bool ImportRunSkeleton::mutate_fversion (uint32_t _fversion) {
    return SetField<uint32_t>(VT_FVERSION, _fversion, 0);
  }
const CrossSchema::ImportRefSkeleton * ImportRunSkeleton::fref_skelt() const{
    return GetPointer<const CrossSchema::ImportRefSkeleton *>(VT_FREF_SKELT);
  }
  CrossSchema::ImportRefSkeleton * ImportRunSkeleton::mutable_fref_skelt() {
    return GetPointer<CrossSchema::ImportRefSkeleton *>(VT_FREF_SKELT);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>> * ImportRunSkeleton::fslots() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>> *>(VT_FSLOTS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>> * ImportRunSkeleton::mutable_fslots() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>> *>(VT_FSLOTS);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSocket>> * ImportRunSkeleton::fsockets() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSocket>> *>(VT_FSOCKETS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSocket>> * ImportRunSkeleton::mutable_fsockets() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSocket>> *>(VT_FSOCKETS);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>> * ImportRunSkeleton::ftwinbones() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>> *>(VT_FTWINBONES);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>> * ImportRunSkeleton::mutable_ftwinbones() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>> *>(VT_FTWINBONES);
  }
 bool ImportRunSkeleton::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_FVERSION) &&
           VerifyOffset(verifier, VT_FREF_SKELT) &&
           verifier.VerifyTable(fref_skelt()) &&
           VerifyOffset(verifier, VT_FSLOTS) &&
           verifier.VerifyVector(fslots()) &&
           verifier.VerifyVectorOfTables(fslots()) &&
           VerifyOffset(verifier, VT_FSOCKETS) &&
           verifier.VerifyVector(fsockets()) &&
           verifier.VerifyVectorOfTables(fsockets()) &&
           VerifyOffset(verifier, VT_FTWINBONES) &&
           verifier.VerifyVector(ftwinbones()) &&
           verifier.VerifyVectorOfTables(ftwinbones()) &&
           verifier.EndTable();
  }
  void ImportRunSkeletonBuilder::add_fversion(uint32_t fversion) {
    fbb_.AddElement<uint32_t>(ImportRunSkeleton::VT_FVERSION, fversion, 0);
  }
  void ImportRunSkeletonBuilder::add_fref_skelt(flatbuffers::Offset<CrossSchema::ImportRefSkeleton> fref_skelt) {
    fbb_.AddOffset(ImportRunSkeleton::VT_FREF_SKELT, fref_skelt);
  }
  void ImportRunSkeletonBuilder::add_fslots(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>>> fslots) {
    fbb_.AddOffset(ImportRunSkeleton::VT_FSLOTS, fslots);
  }
  void ImportRunSkeletonBuilder::add_fsockets(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSocket>>> fsockets) {
    fbb_.AddOffset(ImportRunSkeleton::VT_FSOCKETS, fsockets);
  }
  void ImportRunSkeletonBuilder::add_ftwinbones(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>>> ftwinbones) {
    fbb_.AddOffset(ImportRunSkeleton::VT_FTWINBONES, ftwinbones);
  }
flatbuffers::Offset<ImportRunSkeleton> CreateImportRunSkeleton(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fversion,
    flatbuffers::Offset<CrossSchema::ImportRefSkeleton> fref_skelt,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>>> fslots,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportSocket>>> fsockets,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>>> ftwinbones){
    ImportRunSkeletonBuilder builder_(_fbb);
  builder_.add_ftwinbones(ftwinbones);
  builder_.add_fsockets(fsockets);
  builder_.add_fslots(fslots);
  builder_.add_fref_skelt(fref_skelt);
  builder_.add_fversion(fversion);
  return builder_.Finish();
}

flatbuffers::Offset<ImportRunSkeleton> CreateImportRunSkeletonDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fversion,
    flatbuffers::Offset<CrossSchema::ImportRefSkeleton> fref_skelt,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>> *fslots,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportSocket>> *fsockets,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportTwinBone>> *ftwinbones) {
  auto fslots__ = fslots ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>>(*fslots) : 0;
  auto fsockets__ = fsockets ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportSocket>>(*fsockets) : 0;
  auto ftwinbones__ = ftwinbones ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportTwinBone>>(*ftwinbones) : 0;
  return CrossSchema::CreateImportRunSkeleton(
      _fbb,
      fversion,
      fref_skelt,
      fslots__,
      fsockets__,
      ftwinbones__);
}

ImportSlotGroupT *ImportSlotGroup::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportSlotGroupT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportSlotGroup::UnPackTo(ImportSlotGroupT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = slot_names(); if (_e) { _o->slot_names.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->slot_names[_i] = _e->Get(_i)->str(); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportSlotGroup> ImportSlotGroup::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportSlotGroupT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportSlotGroup(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportSlotGroup> CreateImportSlotGroup(flatbuffers::FlatBufferBuilder &_fbb, const ImportSlotGroupT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportSlotGroupT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _slot_names = _o->slot_names.size() ? _fbb.CreateVectorOfStrings(_o->slot_names) : 0;
  return CrossSchema::CreateImportSlotGroup(
      _fbb,
      _name,
      _slot_names);
}

ImportTwinBoneT *ImportTwinBone::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportTwinBoneT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportTwinBone::UnPackTo(ImportTwinBoneT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = bonename(); if (_e) _o->bonename = _e->str(); }
  { auto _e = twinname(); if (_e) _o->twinname = _e->str(); }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportTwinBone> ImportTwinBone::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportTwinBoneT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportTwinBone(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportTwinBone> CreateImportTwinBone(flatbuffers::FlatBufferBuilder &_fbb, const ImportTwinBoneT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportTwinBoneT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _bonename = _o->bonename.empty() ? 0 : _fbb.CreateString(_o->bonename);
  auto _twinname = _o->twinname.empty() ? 0 : _fbb.CreateString(_o->twinname);
  return CrossSchema::CreateImportTwinBone(
      _fbb,
      _bonename,
      _twinname);
}

ImportSocketT *ImportSocket::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportSocketT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportSocket::UnPackTo(ImportSocketT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = attached_bone(); if (_e) _o->attached_bone = _e->str(); }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportSocket> ImportSocket::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportSocketT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportSocket(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportSocket> CreateImportSocket(flatbuffers::FlatBufferBuilder &_fbb, const ImportSocketT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportSocketT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _attached_bone = _o->attached_bone.empty() ? 0 : _fbb.CreateString(_o->attached_bone);
  return CrossSchema::CreateImportSocket(
      _fbb,
      _name,
      _attached_bone);
}

ImportRefSkeletonT *ImportRefSkeleton::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportRefSkeletonT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportRefSkeleton::UnPackTo(ImportRefSkeletonT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = skelteon(); if (_e) { _o->skelteon.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->skelteon[_i] = std::unique_ptr<CrossSchema::ImportBoneNodeT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportRefSkeleton> ImportRefSkeleton::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportRefSkeletonT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportRefSkeleton(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportRefSkeleton> CreateImportRefSkeleton(flatbuffers::FlatBufferBuilder &_fbb, const ImportRefSkeletonT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportRefSkeletonT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _skelteon = _o->skelteon.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> (_o->skelteon.size(), [](size_t i, _VectorArgs *__va) { return CreateImportBoneNode(*__va->__fbb, __va->__o->skelteon[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateImportRefSkeleton(
      _fbb,
      _name,
      _skelteon);
}

ImportRunSkeletonT *ImportRunSkeleton::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportRunSkeletonT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportRunSkeleton::UnPackTo(ImportRunSkeletonT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = fversion(); _o->fversion = _e; }
  { auto _e = fref_skelt(); if (_e) _o->fref_skelt = std::unique_ptr<CrossSchema::ImportRefSkeletonT>(_e->UnPack(_resolver)); }
  { auto _e = fslots(); if (_e) { _o->fslots.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->fslots[_i] = std::unique_ptr<CrossSchema::ImportSlotGroupT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = fsockets(); if (_e) { _o->fsockets.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->fsockets[_i] = std::unique_ptr<CrossSchema::ImportSocketT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = ftwinbones(); if (_e) { _o->ftwinbones.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->ftwinbones[_i] = std::unique_ptr<CrossSchema::ImportTwinBoneT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportRunSkeleton> ImportRunSkeleton::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportRunSkeletonT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportRunSkeleton(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportRunSkeleton> CreateImportRunSkeleton(flatbuffers::FlatBufferBuilder &_fbb, const ImportRunSkeletonT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportRunSkeletonT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _fversion = _o->fversion;
  auto _fref_skelt = _o->fref_skelt ? CreateImportRefSkeleton(_fbb, _o->fref_skelt.get(), _rehasher) : 0;
  auto _fslots = _o->fslots.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportSlotGroup>> (_o->fslots.size(), [](size_t i, _VectorArgs *__va) { return CreateImportSlotGroup(*__va->__fbb, __va->__o->fslots[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _fsockets = _o->fsockets.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportSocket>> (_o->fsockets.size(), [](size_t i, _VectorArgs *__va) { return CreateImportSocket(*__va->__fbb, __va->__o->fsockets[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _ftwinbones = _o->ftwinbones.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportTwinBone>> (_o->ftwinbones.size(), [](size_t i, _VectorArgs *__va) { return CreateImportTwinBone(*__va->__fbb, __va->__o->ftwinbones[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateImportRunSkeleton(
      _fbb,
      _fversion,
      _fref_skelt,
      _fslots,
      _fsockets,
      _ftwinbones);
}

const CrossSchema::ImportRunSkeleton *GetImportRunSkeleton(const void *buf){
 return flatbuffers::GetRoot<CrossSchema::ImportRunSkeleton>(buf);
}

const CrossSchema::ImportRunSkeleton *GetSizePrefixedImportRunSkeleton(const void *buf) {
return flatbuffers::GetSizePrefixedRoot<CrossSchema::ImportRunSkeleton>(buf);
}

ImportRunSkeleton *GetMutableImportRunSkeleton(void *buf) {
return flatbuffers::GetMutableRoot<ImportRunSkeleton>(buf);
}

bool VerifyImportRunSkeletonBuffer(flatbuffers::Verifier &verifier) {
return verifier.VerifyBuffer<CrossSchema::ImportRunSkeleton>(nullptr);
}

bool VerifySizePrefixedImportRunSkeletonBuffer(flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<CrossSchema::ImportRunSkeleton>(nullptr);
}

void FinishImportRunSkeletonBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportRunSkeleton> root) {
  fbb.Finish(root);
}

void FinishSizePrefixedImportRunSkeletonBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportRunSkeleton> root) {
fbb.FinishSizePrefixed(root);
}

std::unique_ptr<CrossSchema::ImportRunSkeletonT> UnPackImportRunSkeleton(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::ImportRunSkeletonT>(GetImportRunSkeleton(buf)->UnPack(res));
}

std::unique_ptr<CrossSchema::ImportRunSkeletonT> UnPackSizePrefixedImportRunSkeleton(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::ImportRunSkeletonT>(GetSizePrefixedImportRunSkeleton(buf)->UnPack(res));
}

}  // namespace CrossSchema
