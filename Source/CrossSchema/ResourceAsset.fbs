include "TextureAsset.fbs";
include "MaterialAsset.fbs";
include "ImportMesh.fbs";
include "ImportSkeleton.fbs";
include "ImportAnimation.fbs";
include "ShaderAsset.fbs";
include "ImportMeshAssetData.fbs";

namespace CrossSchema;

union ResourceType{TextureAsset,MaterialAsset,ImportMeshes,ImportRunSkeleton,ImportAnimation,GraphicsShaderAsset,ComputeShaderAsset,ImportMeshAssetData}

struct ResourceHeader
{
    magicnumber:int;
    version:int;
    classid:int;
    datasize:int;
    reserved:int;
}

table ResourceAsset
{
    header:ResourceHeader;
    name:string;
    resource:ResourceType;
}

root_type ResourceAsset;