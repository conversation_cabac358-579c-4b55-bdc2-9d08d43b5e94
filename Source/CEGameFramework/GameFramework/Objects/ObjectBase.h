#pragma once
#include "GameFramework/GameFrameworkTypes.h"
#include "reflection/objects/rtti_base.hpp"

#define StaticMetaClassName(ClassName)                                                                                                                                                                                                         \
    CEMeta(ScriptCallable)                                                                                                                                                                                                                     \
    static const std::string& MetaClassName()                                                                                                                                                                                                  \
    {                                                                                                                                                                                                                                          \
        return gbf::reflection::query_meta_class<ClassName>()->name();                                                                                                                                                                         \
    }

namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli) ObjectBase : public gbf::reflection::RttiBase
{
    CEGameplayInternal() 
public:
    ObjectBase() {}

    virtual ~ObjectBase() {}
    
    virtual void BeginDestroy() { mIsPendingDestroy = true; }

    virtual void Destroyed() {}

    const gbf::reflection::MetaClass* GetMetaClass() const
    {
        return __rtti_meta();
    }

    virtual const std::string& GetName() const { return mName; }

    virtual void SetName(const std::string& objName) { mName = objName; };

    bool IsPendingDestroy() const { return mIsPendingDestroy; }

    std::string MakeUniqueObjectName(const gbf::reflection::MetaClass* metaClass);
    
    std::string MakeUniqueObjectName(const std::string& className);

    template<typename T>
    bool CheckCast() const;

protected:
    bool mIsPendingDestroy = false;

    std::string mName;

private:
    static std::atomic<UInt32> ObjectNameIndex;
};

using ObjectBasePtr = std::shared_ptr<ObjectBase>;

template<typename T>
bool ObjectBase::CheckCast() const
{
    if constexpr (!std::is_base_of_v<ObjectBase, T>)
    {
        return false;
    }

    auto otherMetaClass = QueryMetaClass<T>();
    if (otherMetaClass == nullptr)
    {
        return false;
    }

    auto metaClass = GetMetaClass();
    if (metaClass == nullptr)
    {
        return false;
    }
    return metaClass->IsTypeMatch(otherMetaClass->id());
}

template<typename TargetType, typename SrcType>
TargetType* CastTo(SrcType* objPtr)
{
    if constexpr (!std::is_base_of_v<ObjectBase, SrcType> || !std::is_base_of_v<ObjectBase, TargetType>)
    {
        return nullptr;
    }

    if (objPtr)
    {
        return TYPE_CAST(TargetType*, objPtr);
    }
    return nullptr;
}

template<typename TargetType, typename SrcType>
TargetType* CheckCastTo(SrcType* objPtr)
{
    if constexpr (!std::is_base_of_v<ObjectBase, SrcType> || !std::is_base_of_v<ObjectBase, TargetType>)
    {
        return nullptr;
    }

    if (objPtr && objPtr->CheckCast<TargetType>())
    {
        return TYPE_CAST(TargetType*, objPtr);
    }
    return nullptr;
}

}
