#pragma once

#include "core/modules/imodule.h"
#include "GameFrameworkTypes.h"

namespace cegf
{
static const char kModuleGameFramework[] = "GameFramework";

class GameFrameworkModule : public gbf::IModule
{
public:
    GameFrameworkModule();

    virtual ~GameFrameworkModule();

    //method from IModule
    virtual gbf::ModuleCallReturnStatus Init() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    
    virtual gbf::ModuleCallReturnStatus Start() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    
    virtual gbf::ModuleCallReturnStatus Update() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    
    virtual gbf::ModuleCallReturnStatus Stop() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    
    virtual gbf::ModuleCallReturnStatus Release() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    
    void Free() override
    {
        delete this;
    }

private:
    void RegisterGameFrameworkClasses();
};

}// namespace cegf
MAKE_MODULE(cegf::GameFrameworkModule, GameFramework);