#pragma once
#include "details/dynamic_blueprint_binding.h"
#include "GameFrameworkTypes.h"
#include "InputChord.h"
#include "GameObjects/GameObject.h"
#include "Runtime/Input/Core/InputEvents.h"
#include "Resource/InputActionMappingResource.h"

namespace cegf {
class InputComponent;

struct GAMEFRAMEWORK_API CEMeta(Reflect) BlueprintInputDelegateBinding
{
    bool bConsumeInput : 1;
    bool bExecuteWhenPaused : 1;

    bool bOverrideParentBinding;

    BlueprintInputDelegateBinding()
        : bConsumeInput(true)
        , bExecuteWhenPaused(false)
        , bOverrideParentBinding(true)
    {}
};

// Used By InputKeyPressedEventNode and InputKeyReleasedEventNode RegisterDynamicBinding
struct GAMEFRAMEWORK_API CEMeta(Reflect) BlueprintInputKeyDelegateBinding : public BlueprintInputDelegateBinding
{
    cegf::InputChord InputChord;

    cross::input::CEInputEvent::Type InputKeyEventType;

    struct NodeID
    {
        gbf::logic::UBlueprintNode::NodeIDType _node_id = gbf::logic::UBlueprintNode::INVALID_NODE_ID;
        gbf::logic::UBlueprintNode::NodeIDType _owner_graph_id = gbf::logic::UBlueprintNode::INVALID_NODE_ID;
    };
    NodeID NodeID;   // should be a node desc to find a unique node in the graph

    BlueprintInputKeyDelegateBinding()
        : BlueprintInputDelegateBinding()
        , InputKeyEventType(cross::input::CEInputEvent::Pressed)
    {}
};

struct GAMEFRAMEWORK_API CEMeta(Reflect) BlueprintInputActionDelegateBinding : public BlueprintInputDelegateBinding
{
    std::string InputActionName;

    cross::TriggerEvent TriggerEventState;

    struct NodeID
    {
        gbf::logic::UBlueprintNode::NodeIDType _node_id = gbf::logic::UBlueprintNode::INVALID_NODE_ID;
        gbf::logic::UBlueprintNode::NodeIDType _owner_graph_id = gbf::logic::UBlueprintNode::INVALID_NODE_ID;
    };
    NodeID NodeID;   // should be a node desc to find a unique node in the graph

    BlueprintInputActionDelegateBinding()
        : BlueprintInputDelegateBinding()
        , TriggerEventState(cross::TriggerEvent::Triggered)
        , InputActionName("")
    {}
};

//   Binding processor, provided by node::GetDynamicBindingClass, used by blueprint graph's initialization and
// binding node delegate to input components
// blueprint graph's initialization: traverse all nodes and call GetDynamicBindingClass and create and hold bind obj in the graph, then call RegisterDynamicBinding
// binding node delegate to input components: explicit call InputDelegateBinding::BindToInputComponent, because it is a virtual function, so InputKeyDelegateBinding::BindToInputComponent will be called
// and then IC->KeyBindings will be set by InputKeyDelegateBinding::BindToInputComponent and real node ptr in the graph instance will be found and will be bind to the IC->KeyBindings


class GAMEFRAMEWORK_API CEMeta(Reflect) InputDelegateBinding : public DynamicBlueprintBinding{
public:

    /**
     * Override this function to bind a delegate to the given input component.
     *
     * @param InputComponent		The InputComponent to Bind a delegate to
     * @param ObjectToBindTo		The UObject that the binding should use.
     */
    virtual void BindToInputComponent(cegf::InputComponent * InputComponent, const gbf::logic::UBlueprintGraphGroup* InBlueprintPtr) const {};

    /** Returns true if the given class supports input binding delegates (i.e. it is a BP generated class) */
    static bool SupportsInputDelegate(const gbf::reflection::MetaClass* InClass);

    /**
     * Calls BindToInputComponent for each dynamic binding object on the given Class if it supports input delegates.
     *
     * @param InBlueprintPtr		The BP that will should be used to determine if Input Delegates are supported
     * @param InputComponent		The InputComponent to Bind a delegate to
     * @param ObjectToBindTo		The UObject that the binding should use. If this is null, the Owner of the input componet will be used.
     */
    static void BindInputDelegates(const gbf::logic::UBlueprintGraphGroup* InBlueprintPtr, cegf::InputComponent* InputComponent);

    /**
     * Will bind input delegates for the given Actor and traverse it's subobjects attempting to bind
     * each of them
     */
    static void BindInputDelegatesWithSubojects(cegf::GameObject * InActor, cegf::InputComponent * InputComponent);

protected:
    static std::unordered_set<const gbf::reflection::MetaClass*> InputBindingClasses;
};

class GAMEFRAMEWORK_API CEMeta(Reflect) InputKeyDelegateBinding : public InputDelegateBinding{
public:
    CEMeta(Reflect)
    InputKeyDelegateBinding();

    std::vector<BlueprintInputKeyDelegateBinding> InputKeyDelegateBindings;

    virtual void BindToInputComponent(cegf::InputComponent * InputComponent, const gbf::logic::UBlueprintGraphGroup* InBlueprintPtr) const override;
};

class GAMEFRAMEWORK_API CEMeta(Reflect) InputActionDelegateBinding : public InputDelegateBinding
{
public:
    CEMeta(Reflect)
    InputActionDelegateBinding();

    std::vector<BlueprintInputActionDelegateBinding> InputActionDelegateBindings;

    virtual void BindToInputComponent(cegf::InputComponent * InputComponent, const gbf::logic::UBlueprintGraphGroup* InBlueprintPtr) const override;
};

}
