#include "AudioPortal.h"
#include "Utility.h"
#include "../GameObjects/GameObject.h"
#include <CECommon/Common/EngineGlobal.h>
#include <RenderEngine/PrimitiveGenerator.h>
#include "../GameWorld.h"
#include "EditorPrimitiveSystemG.h"

constexpr UInt32 sColorWhite = 0xffffffff;
cross::ColorRGBAf const cColorWhite{1.0f, 1.0f, 1.0f, 1.0f};
constexpr UInt32 sColorGreen = 0xff00ff00;
cross::ColorRGBAf const cColorGreen{0.0f, 1.0f, 0.0f, 1.0f};

namespace cegf {
AudioPortal::AudioPortal()
{
}

AudioPortal::~AudioPortal()
{
}

void AudioPortal::Serialize(SerializeNode& node, SerializeContext& context) const
{
    node["ExtentX"] = mExtent.x;
    node["ExtentY"] = mExtent.y;
    node["ExtentZ"] = mExtent.z;
    PrimitiveComponent::Serialize(node, context);
}
bool AudioPortal::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    if(in.HasMember("ExtentX"))
        mExtent.x = in["ExtentX"].AsFloat();
    else
        mExtent.x = 1;
    if(in.HasMember("ExtentY"))
        mExtent.y = in["ExtentY"].AsFloat();
    else
        mExtent.y = 1;
    if(in.HasMember("ExtentZ"))
        mExtent.z = in["ExtentZ"].AsFloat();
    else
        mExtent.z = 1;
    return PrimitiveComponent::Deserialize(in, context);
}

cross::AudioEngine::ObjectID AudioPortal::GetObjectID() const
{
    return reinterpret_cast<cross::AudioEngine::ObjectID>(this);
}

cross::Float3 AudioPortal::GetExtent() const
{
    return mExtent;
}
void AudioPortal::SetExtent(cross::Float3 value)
{
    mExtent = value;
}

void AudioPortal::Init()
{
    PrimitiveComponent::Init();

    mDrawOnlyOnEditor = true;

    cross::EditorPrimitiveSystemG* sys = GetSystem<cross::EditorPrimitiveSystemG>();
    if(sys)
    {
        cross::ecs::EntityID entity = GetOwnerEntityID();
        sys->RegisterRemoveEditorSelectedDrawingEventHandler(entity, this, [this](cross::ecs::EntityID entity)
            {
                GameWorld* const world = GetWorld();
                GameObject * const gameObject = world->GetGameObject(entity);
                if(gameObject->GetComponent<AudioPortal>())
                {
                    if(mDrawn)
                    {
                        mDrawn = false;
                        MarkForNoNeedEndOfFrameDraw();
                    }
                }
            });
        sys->RegisterDrawEditorSelectedEntityEventHandler(entity, this, [this](cross::ecs::EntityID entity)
            {
                GameWorld* const world = GetWorld();
                GameObject * const gameObject = world->GetGameObject(entity);
                if(gameObject->GetComponent<AudioPortal>())
                {
                    if(false == mDrawn)
                    {
                        mDrawn = true;
                        MarkForNeedEndOfFrameDraw();
                    }
                }
            });
    }

    mPrimitiveRenderSystem = GetSystem<cross::PrimitiveRenderSystemG>();
}

void AudioPortal::Uninit(bool bShouldNotifyECS)
{
    PrimitiveComponent::Uninit();
    cross::EditorPrimitiveSystemG* sys = GetSystem<cross::EditorPrimitiveSystemG>();
    if(sys)
    {
        cross::ecs::EntityID entity = GetOwnerEntityID();
        sys->UnregisterRemoveEditorSelectedDrawingEventHandler(entity, this);
        sys->UnregisterDrawEditorSelectedEntityEventHandler(entity, this);
    }
}

void AudioPortal::Tick(float deltaTime)
{
    PrimitiveComponent::Tick(deltaTime);
}

void AppendPoint(cross::PrimitiveRenderSystemG * const renderer
    , cross::Float3 const & point, cross::Float4x4 const & transform
    , float const size, cross::ColorRGBAf const & color, cross::MaterialPtr const material)
{
    cross::Float3 tilePosition, offset;
    cross::GetTileAndOffsetForAbsolutePosition(cross::Double3(point), tilePosition, offset);
    renderer->DrawPoint(cross::Float3::Transform(offset, transform), size, cColorWhite, material, tilePosition, true);
}

void AppendVertex(UInt8 * const data, cross::Float3 const & position, UInt32 const & color)
{
    memcpy(data, &position, sizeof(cross::Float3));
    memcpy(data + sizeof(cross::Float3), &color, sizeof(UInt32));
}

void AudioPortal::Draw() const
{
    PrimitiveComponent::Draw();

    if(mDrawn)
    {
        // prepare points
        cross::Float3 const halfExtent = mExtent * 0.5f;
        cross::Float3 const rects[] = 
            { cross::Float3(-halfExtent.x, -halfExtent.y, -halfExtent.z)
            , cross::Float3(halfExtent.x, -halfExtent.y, -halfExtent.z)
            , cross::Float3(halfExtent.x, halfExtent.y, -halfExtent.z)
            , cross::Float3(-halfExtent.x, halfExtent.y, -halfExtent.z)
            , cross::Float3(-halfExtent.x, -halfExtent.y, halfExtent.z)
            , cross::Float3(halfExtent.x, -halfExtent.y, halfExtent.z)
            , cross::Float3(halfExtent.x, halfExtent.y, halfExtent.z)
            , cross::Float3(-halfExtent.x, halfExtent.y, halfExtent.z)
            };
        UInt16 const greenLines[] = {0, 1, 1, 2, 2, 3, 3, 0, 4, 5, 5, 6, 6, 7, 7, 4};
        UInt16 const whiteLines[] = {0, 4, 1, 5, 2, 6, 3, 7};

        // prepare line data container
        std::unique_ptr<cross::PrimitiveData> lineData = std::make_unique<cross::PrimitiveData>(cross::PrimitiveData());
        cross::VertexStreamLayout lineLayout = cross::PrimitiveGenerator::GetStandardLineLayout();
        lineData->Resize(2 * (sizeof(rects) / sizeof(rects[0])) // green and white vertexes
            , sizeof(greenLines) / sizeof(greenLines[0]) + sizeof(whiteLines) / sizeof(whiteLines[0])
            , 1, lineLayout, false);
        lineData->SetTopology(cross::PrimitiveTopology::LineList);
        
        // prepare materials
        cross::MaterialPtr pointMaterial = mPrimitiveRenderSystem->GetMaterial(true, false, true);
        cross::MaterialPtr lineMaterial = mPrimitiveRenderSystem->GetMaterial(true, false, false);
        auto fx = lineMaterial->GetFx();
        for (auto& pass : fx->GetAllPass())
        {
            auto rasterize = lineMaterial->GetState(pass.first)->RasterizationStateDesc;
            rasterize.LineWidth = 2.0f;
            lineMaterial->SetRasterizerState(pass.first, rasterize);
        }

        // draw points
        for(cross::Float3 const & p : rects)
        {
            cross::Float3 tilePosition, offset;
            cross::GetTileAndOffsetForAbsolutePosition(cross::Double3(p), tilePosition, offset);
            mPrimitiveRenderSystem->DrawPoint(cross::Float3::Transform(offset, cross::Float4x4(GetOwner()->GetWorldMatrix())), 10.0f, cColorWhite, pointMaterial, tilePosition, true);
        }

        // draw green lines
        UInt32 vertexOffset = UInt32(0);
        UInt32 const vertexStride = lineLayout.GetVertexStride();
        assert(sizeof(cross::Float3) + sizeof(UInt32) == vertexStride);
        for(cross::Float3 const & p : rects)
        {
            AppendVertex(lineData->GetVertexData() + vertexOffset, p, sColorGreen);
            vertexOffset += vertexStride;
        }

        UInt32 indexOffset = UInt32(0);
        UInt32 const indexStride = 1;
        for(UInt16 const & i : greenLines)
        {
            lineData->GetIndexArray()[indexOffset] = i;
            indexOffset += indexStride;
        }

        // draw white lines
        for(cross::Float3 const & p : rects)
        {
            AppendVertex(lineData->GetVertexData() + vertexOffset, p, sColorWhite);
            vertexOffset += vertexStride;
        }
        for(UInt16 const & i : whiteLines)
        {
            lineData->GetIndexArray()[indexOffset] = i + (sizeof(rects) / sizeof(rects[0]));
            indexOffset += indexStride;
        }

        mPrimitiveRenderSystem->DrawPrimitive(lineData.get(), GetOwner()->GetWorldMatrix(), lineMaterial);
    }
}
void AudioPortal::StartGame()
{
    PrimitiveComponent::StartGame();
    cross::AudioEngine* const audioEngine = cross::EngineGlobal::Inst().GetAudioEngine();
    GameObject const * const gameObject = GetOwner();
    if(audioEngine->SpatialCreatePortal(Hash(gameObject), mExtent, gameObject->GetWorldMatrix()))
        LOG_ERROR("AudioObstacle failed to create portal");
}
void AudioPortal::EndGame()
{
    cross::AudioEngine* const audioEngine = cross::EngineGlobal::Inst().GetAudioEngine();
    GameObject const * const gameObject = GetOwner();
    if(audioEngine->SpatialDestroyPortal(Hash(gameObject)))
        LOG_ERROR("AudioObstacle failed to remove portal");
    PrimitiveComponent::EndGame();
}
void AudioPortal::BeginDestroy()
{
    PrimitiveComponent::BeginDestroy();
}
void AudioPortal::Activate()
{
    PrimitiveComponent::Activate();
}
void AudioPortal::Deactivate()
{
    PrimitiveComponent::Deactivate();
}
void AudioPortal::SetOwner(GameObject* owner)
{
    PrimitiveComponent::SetOwner(owner);
}
void AudioPortal::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask & bitMask) const
{
    PrimitiveComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::EditorPrimitiveComponentG::GetDesc()->GetMaskBitIndex(), true);
}
}   // namespace cegf
