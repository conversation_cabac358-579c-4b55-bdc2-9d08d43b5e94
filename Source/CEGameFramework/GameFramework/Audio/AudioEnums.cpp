#include "AudioEnums.h"

namespace cegf
{
	cross::AudioEngine::CurveInterpolation Convert(AudioCurveInterpolation const c)
	{
		switch (c)
		{
		case AudioCurveInterpolation::Linear:
			return cross::AudioEngine::CurveInterpolation::Linear;
		case AudioCurveInterpolation::Log1:
			return cross::AudioEngine::CurveInterpolation::Log1;
		case AudioCurveInterpolation::Log3:
			return cross::AudioEngine::CurveInterpolation::Log3;
		case AudioCurveInterpolation::Sine:
			return cross::AudioEngine::CurveInterpolation::Sine;
		case AudioCurveInterpolation::SCurve:
			return cross::AudioEngine::CurveInterpolation::SCurve;
		case AudioCurveInterpolation::InvSCurve:
			return cross::AudioEngine::CurveInterpolation::InvSCurve;
		case AudioCurveInterpolation::Exp1:
			return cross::AudioEngine::CurveInterpolation::Exp1;
		case AudioCurveInterpolation::Exp3:
			return cross::AudioEngine::CurveInterpolation::Exp3;
		case AudioCurveInterpolation::SineRecip:
			return cross::AudioEngine::CurveInterpolation::SineRecip;
		}
		return cross::AudioEngine::CurveInterpolation::Linear;
	}
}