#pragma once
#include <unordered_set>
#include "Runtime/GameWorld/GameWorld.h"

#include "Event/Common.h"
#include "CECommon/Common/SettingsManager.h"

namespace cegf {
namespace evt {

    template<class System, class Event, std::enable_if_t<std::is_base_of_v<cross::GameSystemBase, System>, int> = 0, std::enable_if_t<std::is_base_of_v<cross::SystemEventBase, Event>, int> = 0>
    class SystemEventDispatcherImpl : public GOEventDispatcherBase
    {
    public:
        SystemEventDispatcherImpl(cross::GameWorld* inWorld)
            : GOEventDispatcherBase(inWorld)
        {}

        void NotifyEvent(const cross::SystemEventBase& event, UInt32& flag) override
        {
            if (event.mEventType == Event::sEventType)
            {
                auto& e = static_cast<const Event&>(event);
                for (auto listener : mListeners)
                {
                    listener->NotifyEvent(GetEventCallerID(), e.mData);
                }
            }
        }

    protected:
        virtual void SubscribeInternal() override { mWorld->GetGameSystem<System>()->SubscribeEvent<Event>(this); }

        virtual void UnSubscribeInternal() override { mWorld->GetGameSystem<System>()->Unsubscribe<Event>(this); }

        std::string_view RemoveNamespaceAndSuffix(std::string_view className) const
        {
            size_t start = className.rfind("::") + 1;
            size_t end = className.find('>');

            if (start == std::string_view::npos || end == std::string_view::npos || start >= end)
            {
                return "";
            }
            std::string_view innerContent = className.substr(start + 1, end - start - 1);

            if (innerContent.size() >= 9 && innerContent.substr(innerContent.size() - 9) == "EventData")
            {
                innerContent = innerContent.substr(0, innerContent.size() - 9);
            }

            return innerContent;
        }   // constexpr func to generate event name

        std::string GetEventName() const
        {
            // get type
            std::string_view typeName = typeid(Event).name();

            std::string prefix = "";
            if (GetGameWorld()->GetWorldType() != cross::WorldTypeTag::PIEWorld && cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpTypeStandAlone)
            {
                prefix = EDITOR_SCRIPT_FUNCTION_PREFIX;
            }

            // process and return event name
            return prefix + "On" + std::string(RemoveNamespaceAndSuffix(typeName)) + "Event";
        }

        // cross::AirportLoadStateChangedEvent
        virtual CallbackSignature GetEventCallerID() const override { return {std::string{GetEventName()}}; }
    public:
        auto test() const
        { return GetEventCallerID().CallBackName;
        }
    };
        class TransformJointEventDispatcherBase : public GOEventDispatcherBase
        {
        public:
            TransformJointEventDispatcherBase(cross::GameWorld* inWorld)
                : GOEventDispatcherBase(inWorld)
            {}

        protected:
            virtual void SubscribeInternal() override;

            virtual void UnSubscribeInternal() override;
        };

        class TransformParentJointEventDispatcher : public TransformJointEventDispatcherBase
        {
        public:
            TransformParentJointEventDispatcher(cross::GameWorld* inWorld)
                : TransformJointEventDispatcherBase(inWorld)
            {}

            void NotifyEvent(const cross::SystemEventBase& event, UInt32& flag) override;

            virtual CallbackSignature GetEventCallerID() const override { return {"OnJointParentChanged"}; }
        };

        class TransformChildrenJointEventDispatcher : public TransformJointEventDispatcherBase
        {
        public:
            TransformChildrenJointEventDispatcher(cross::GameWorld* inWorld)
                : TransformJointEventDispatcherBase(inWorld)
            {}

            void NotifyEvent(const cross::SystemEventBase& event, UInt32& flag) override;

        protected:
            virtual CallbackSignature GetEventCallerID() const override { return {"OnJointChildrenChanged"}; }
        };
    }
}   // namespace cegf::event
