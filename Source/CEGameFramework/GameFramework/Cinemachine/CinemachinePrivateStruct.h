#pragma once
#include "CmCameraComponent.h"
#include "CinemachineComponent.h"
#include "Math/CrossMath.h"
#include <string>

namespace cegf {
struct FCmBrain : public CmBrain
{
    CE_Serialize_Deserialize;
    // Binding Camera
    cross::ecs::EntityID camera_entity;

    // Applied CmCameras
    std::vector<cross::ecs::EntityID> cmcamera_entities;

    // Blend info
    CEProperty(Serialize)
    std::vector<CmBrainBlendType> blend_types;
    CEProperty(Serialize)
    std::vector<float> hold_times;
    CEProperty(Serialize)
    std::vector<float> blend_times;
    CEProperty(Serialize)
    bool blend_loop = false;
    CEProperty(Serialize)
    bool blend_paused = false;

    // Mix info
    CEProperty(Serialize)
    float mix_weight = 0.5f;

    // Runtime
    float cur_time = 0.0f;
    CmBrainCameraApplyType prev_type;
    bool inited = false;
    std::vector<float> total_times;
    int cur_blend_index = 0;
    float cur_blend_weight = 0.0f;
    cross::Transform_D prev_transform;
    float prev_focal_length = 50.0f;
};

struct FCmCamera : public CmCamera
{
    CE_Serialize_Deserialize;
    // Position Binding
    cross::ecs::EntityID pos_entity;
    CEProperty(Serialize)
    std::string pos_bone;

    // Aiming Binding
    cross::ecs::EntityID aim_entity;
    CEProperty(Serialize)
    std::string aim_bone;

    // Dolly Track
    CEProperty(Serialize)
    std::vector<cross::Double4> dolly_track;   // xyz: position, w: roll

    // Plane Colliders
    CEProperty(Serialize)
    std::vector<Plane> planes;

    // Runtime
    cross::ecs::EntityID camera_entity;
    cross::Double4x4 pos_transform;
    cross::Double4x4 aim_transform;
    bool pos_inited = false;
    bool aim_inited = false;

    struct DollyInfo
    {
        cross::Double4x4 transform;
        cross::Double2 roll;
        float distance;
    };
    std::vector<DollyInfo> dolly_infos;
    float dolly_total_distance;
    bool prev_loop = false;
    uint32_t prev_resolution = 10;
    uint32_t dolly_cur_index = 0;
    float dolly_cur_fraction = 0.0f;
    float dolly_cur_distance = 0.0f;
    float auto_dolly_min_pos = 0.0f;

    bool is_active = true;
    float cur_time = 0.0f;

    auto SetProgress(float progress) -> void;
    auto GetProgress() const -> float;
};
}