#pragma once
#include "GameFramework/Components/Component.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Math/CrossMath.h"
namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(Reflect, WorkflowType,Puerts) AnimationComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect)
    AnimationComponent();
    void Init() override;
    void Uninit(bool bShouldNotifyECS) override;
    virtual ~AnimationComponent() = default;

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask & bitMask) const override;

    void SetSkeletonAnimation(std::string _SkeletonPath, std::string _AnimatorResourcePath);

//Animation Related
    cross::Transform GetRootSpaceTransform(const std::string& boneName);
    cross::Transform GetLocalSpaceTransform(const std::string& boneName);
public:
    virtual void Tick(float deltaTime) override;

    std::string GetAnimatorAssetPath();

    std::string GetAnimatorName();

    template<class ParamT, class ValueT>
    void SetParameter(std::string paramName, ValueT value)
    {
        auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
        auto animatorSys = GetSystem<cross::AnimatorSystemG>();
        auto entity = GetOwnerEntityID();

        // animatorSys->SetParam<IntParameter, int>(entity, animatorComp.Read(), paramName, value);
        animatorSys->SetParam<ParamT, ValueT>(entity, animatorComp.Read(), paramName, value);
    }
    CEFunction(ScriptCallable)
    void SetParamInt(std::string paramName, int value);

    CEFunction(ScriptCallable)
    void SetParamFloat(std::string paramName, float value);

    CEFunction(ScriptCallable)
    void SetParamBool(std::string paramName, bool value);

    CEFunction(ScriptCallable)
    void SetParamString(std::string paramName, std::string value);

    CEFunction(ScriptCallable)
    void SetParamVec2(std::string paramName, cross::Float2A value);

    CEFunction(ScriptCallable)
    void PlayAnimation(const char* aniPath, bool loop, float playRate, const char* slotName, const char* groupName);
    //script::Local<script::Value> GetParameter(script::Arguments const& args);

    CEFunction(ScriptCallable)
    bool IsAnySlotPlaying();
 //Skeleton Related
 public:

protected:
    std::string SkeletonPath;
    std::string AnimatorResourcePath;

private:
    void OnAnimNotify(const Animator* animator, const std::string& ScriptCallback);
};
}