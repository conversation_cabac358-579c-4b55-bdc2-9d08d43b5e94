#include "GameFramework/Components/AnimationComponent.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "Runtime/Animation/Notify/AnimNotify_Script.h"

namespace cegf
{

AnimationComponent::AnimationComponent()
    : SkeletonPath("")
    , AnimatorResourcePath("")
{
    mTickFunction->bCanEverTick = true;
}

void AnimationComponent::Init()
{
    GameObjectComponent::Init();
    AnimNotifyScript::RegistryEntityCallBack(GetOwnerEntityID(), std::bind(&AnimationComponent::OnAnimNotify, this, std::placeholders::_1, std::placeholders::_2));
}

void AnimationComponent::Uninit(bool bShouldNotifyECS)
{
    GameObjectComponent::Uninit(bShouldNotifyECS);
    AnimNotifyScript::UnregistryEntityCallBack(GetOwnerEntityID());
}

void AnimationComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::AnimatorComponentG::GetDesc()->GetMaskBitIndex(), true);
    bitMask.Set(cross::SkeletonComponentG::GetDesc()->GetMaskBitIndex(), true);
}

void AnimationComponent::SetSkeletonAnimation(std::string _SkeletonPath, std::string _AnimatorResourcePath)
{
    SkeletonPath = _SkeletonPath;
    AnimatorResourcePath = _AnimatorResourcePath;

    auto skeletonComp = GetECSComponent<cross::SkeletonComponentG>();
    auto skeletonSys = GetSystem<cross::SkeletonSystemG>();
    skeletonSys->SetSkeleton(skeletonComp.Write(), SkeletonPath);

    auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
    auto animatorSys = GetSystem<cross::AnimatorSystemG>();
    if(!(animatorSys->SetAnimator(animatorComp.Write(), AnimatorResourcePath)))
    {
        LOG_ERROR("Animatior load fail");
    }
}

void AnimationComponent::SetParamInt(std::string paramName, int value)
{
    SetParameter<IntParameter, int>(paramName, value);
}

void AnimationComponent::SetParamFloat(std::string paramName, float value)
{
    SetParameter<FloatParameter, float>(paramName, value);
}

void AnimationComponent::SetParamBool(std::string paramName, bool value)
{
    SetParameter<BoolParameter, bool>(paramName, value);
}

void AnimationComponent::SetParamString(std::string paramName, std::string value)
{
    SetParameter<StringParameter, std::string>(paramName, value);
}

void AnimationComponent::SetParamVec2(std::string paramName, cross::Float2A value)
{
    SetParameter<Vector2Parameter, cross::Float2A>(paramName, value);
}

void AnimationComponent::PlayAnimation(const char* aniPath, bool loop, float playRate, const char* slotName = "DefaultSlot", const char* groupName = "DefaultGroup")
{
    auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
    auto animatorSys = GetSystem<cross::AnimatorSystemG>();
    animatorSys->PlayAnimation(animatorComp.Write(), aniPath, loop, playRate, slotName, groupName);
}

bool AnimationComponent::IsAnySlotPlaying()
{
    auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
    auto animatorSys = GetSystem<cross::AnimatorSystemG>();
    auto& animator = animatorSys->GetAnimator(animatorComp.Read());
    return animator.IsAnySlotPlaying();
}

//script::Local<script::Value> AnimationComponent::GetParameter(script::Arguments const& args)
//{
//    auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
//    auto animatorSys = GetSystem<cross::AnimatorSystemG>();
//    return (animatorSys->GetParameter(animatorComp.Read(), args));
//}

std::string AnimationComponent::GetAnimatorAssetPath()
{
    auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
    auto animatorSys = GetSystem<cross::AnimatorSystemG>();
    return (animatorSys->GetAnimatorAssetPath(animatorComp.Read()));
}

std::string AnimationComponent::GetAnimatorName()
{
    auto animatorComp = GetECSComponent<cross::AnimatorComponentG>();
    auto animatorSys = GetSystem<cross::AnimatorSystemG>();
    return (animatorSys->GetAnimatorName(animatorComp.Read()));
}

void AnimationComponent::OnAnimNotify(const Animator* animator, const std::string& ScriptCallback)
{
    auto& tsObject = GetOwner()->GetTsObject();
    tsObject.Action(ScriptCallback.c_str());
}

cross::Transform AnimationComponent::GetRootSpaceTransform(const std::string& boneName)
{
    auto skeletonComp = GetECSComponent<cross::SkeletonComponentG>();
    if (skeletonComp && skeletonComp.Read()->RunSkelt && skeletonComp.Read()->PosePtr)
    {
        if (auto skBoneHandle = skeletonComp.Read()->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(CEName{boneName.c_str()}))
        {
            if (auto poseBoneHandle = skeletonComp.Read()->PosePtr->GetPoseIndexFromFilteredBoneIndex(skBoneHandle))
            {
                auto transform = skeletonComp.Read()->PosePtr->GetRootSpaceTransform(poseBoneHandle);
                cross::Transform{transform.GetTranslation(), transform.GetScale(), transform.GetRotation()};
            }
        }
    }
    return cross::Transform{};
}

void AnimationComponent::Tick(float deltaTime)
{
    auto animatorHandle = GetECSComponent<cross::AnimatorComponentG>();
    auto animatorSystemG = GetSystem<cross::AnimatorSystemG>();

    auto& animator = animatorSystemG->GetAnimator(animatorHandle.Read());
    auto& animatorNotifyQueue = animator.GetNotifyQueue();
    
    std::vector<AnimNotifyEventReference> notifiesForScript;
    animatorNotifyQueue.FilteringNotifiesFromType<AnimNotifyScript>(notifiesForScript, [this, &animator](AnimNotifyScript const* castedNotify, AnimNotifyEventReference& ref) { ref.SetOwner(&animator); });
    
    for (auto& notifyRef : notifiesForScript)
        notifyRef.Broadcast();
}

cross::Transform AnimationComponent::GetLocalSpaceTransform(const std::string& boneName)
{
    auto skeletonComp = GetECSComponent<cross::SkeletonComponentG>();
    if (skeletonComp && skeletonComp.Read()->RunSkelt && skeletonComp.Read()->PosePtr)
    {
        if (auto skBoneHandle = skeletonComp.Read()->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(CEName{boneName.c_str()}))
        {
            if (auto poseBoneHandle = skeletonComp.Read()->PosePtr->GetPoseIndexFromFilteredBoneIndex(skBoneHandle))
            {
                auto transform = skeletonComp.Read()->PosePtr->GetLocalSpaceTransform(poseBoneHandle);
                cross::Transform{transform.GetTranslation(), transform.GetScale(), transform.GetRotation()};
            }
        }
    }
    return cross::Transform{};
}

}   // namespace cegf
