#pragma once

#include "GameFramework/Components/Component.h"
#include "CrossBase/Math/CrossMath.h"
#include "Runtime/GameWorld/DecalSystemG.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) DecalComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect) DecalComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) void SetDecalEnable(bool enable);
    CEFunction(Reflect, Cli, ScriptCallable) void SetDecalVisible(bool Visible);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetDecalEnable() const;
    
    CEFunction(Reflect, Cli, ScriptCallable) const std::string& GetDecalMaterial() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetDecalMaterial(const std::string& material);
    
    CEFunction(Reflect, C<PERSON>, ScriptCallable) uint32_t GetSortOrder() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetSortOrder(uint32_t sortOrder);
    
    CEFunction(Reflect, Cli, ScriptCallable) float GetFadeScreenSize() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetFadeScreenSize(float fadeScreenSize);
    
    CEFunction(Reflect, Cli, ScriptCallable) float GetFadeStartDelay() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetFadeStartDelay(float fadeStartDelay);
    
    CEFunction(Reflect, Cli, ScriptCallable) float GetFadeDuration() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetFadeDuration(float fadeDuration);
    
    CEFunction(Reflect, Cli, ScriptCallable) float GetFadeInDuration() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetFadeInDuration(float fadeInDuration);
    
    CEFunction(Reflect, Cli, ScriptCallable) float GetFadeInStartDelay() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetFadeInStartDelay(float fadeInStartDelay);
    
    CEFunction(Reflect, Cli, ScriptCallable) cross::Float3 GetDecalSize() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetDecalSize(const cross::Float3& decalSize);
    
    CEFunction(Reflect, Cli, ScriptCallable) cross::DecalConfig GetDecalConfig() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetDecalConfig(const cross::DecalConfig& config);
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask & bitMask) const override;
};
} // namespace cegf
