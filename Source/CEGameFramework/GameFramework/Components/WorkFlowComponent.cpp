#include "Runtime/Input/Core/InputKeys.h"
#include "GameFramework/Components/WorkFlowComponent.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/GameEngine.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"
#include "blueprint/details/blueprint_context.h"

#include "blueprint/details/node/blueprint_event_node.h"
#include "blueprint/details/blueprint_workspace.h"
#include "Resource/WorkflowGraphResource.h"

#include "GamePlayBaseFramework\meta\reflection\objects\make_user_object.hpp"

#include "logic/visual/blueprint/details/blueprint_workspace.h"
namespace cegf
{
WorkFlowComponent::WorkFlowComponent()
{
    mTickFunction->bCanEverTick = true;
}

WorkFlowComponent::WorkFlowComponent(GameObject* owner) : GameObjectComponent(owner)
{
    mTickFunction->bCanEverTick = true;
}

WorkFlowComponent::~WorkFlowComponent() {
    
}

void WorkFlowComponent::Init()
{
    GameObjectComponent::Init();
}

void WorkFlowComponent::StartGame()
{
    GameObjectComponent::StartGame();
    if (mWorkflowGraphResourcePtr)
    {
        auto blueprint = mWorkflowGraphResourcePtr->GetBlueprint();
        blueprint->BuildDynamicBindingObjects();
    }

    for (auto& ctx : mAllContext)
    {
        if (m_events_to_fire.find({"BeginPlay"}) == m_events_to_fire.end())
        {
            ctx->OnGlobalEvent("BeginPlay", gbf::machine::VValue{});
            ctx->Update();
        }
        else
        {
            Assert(false);
        }
    }
}
void WorkFlowComponent::EndGame()
{
    for (auto& ctx : mAllContext)
    {
        if (m_events_to_fire.find({"EndPlay"}) == m_events_to_fire.end())
        {
            ctx->OnGlobalEvent("EndPlay", gbf::machine::VValue{});
            ctx->Update();
        }
        else
        {
            Assert(false);
        }
    }
    GameObjectComponent::EndGame();
}

void WorkFlowComponent::Serialize(SerializeNode& node, SerializeContext& context) const
{
    node[SK_WORKFLOWRES] = mWorkFlowResGUID;

    GameObjectComponent::Serialize(node, context);
}

void WorkFlowComponent::GetReferenceResource(cross::ResourcePtr resource) const
{
    resource->AddReferenceResource(mWorkFlowResGUID);
}

bool WorkFlowComponent::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    bool ret = GameObjectComponent::Deserialize(in, context);

    if (!in.HasMember(SK_WORKFLOWRES))
    {
        return false;
    }
    mWorkFlowResGUID = in[SK_WORKFLOWRES].AsString();

    auto srcResourcePtr = gAssetStreamingManager->LoadSynchronously(mWorkFlowResGUID);
    if (srcResourcePtr == nullptr)
    {
        return false;
    }
    InitByWorkFlowResource(srcResourcePtr);
    return ret;
}

void WorkFlowComponent::Tick(float deltaTime)  
{
    for (auto& ctx : mAllContext)
    {
        for (auto&& [event_name, param] : m_events_to_fire)
        {
            ctx->OnGlobalEvent(event_name.EventName, param, event_name.SlotIndex);
        }
        if (m_events_to_fire.find({"Tick"}) == m_events_to_fire.end())
        {
            ctx->OnGlobalEvent("Tick", gbf::machine::VValue{deltaTime});
        }
        ctx->Update();
    }
    m_events_to_fire.clear();
}

void WorkFlowComponent::InitBPGlobalVariables(gbf::logic::UBlueprintContextPtr inContext)
{
    if (!inContext)
        return;
    inContext->GetGlobalMemoryScope()->CreateValue("ThisGameObjectPtr", gbf::reflection::make_raw_pointer_user_object(GetOwner()));
    inContext->GetGlobalMemoryScope()->CreateValue("GameWorldPtr", gbf::reflection::make_raw_pointer_user_object(GetOwner()->GetWorld()));
    inContext->GetGlobalMemoryScope()->CreateValue("GameEnginePtr", gbf::reflection::make_raw_pointer_user_object(gGameEngine));
}

void WorkFlowComponent::InitGraphContext()
{
    mAllContext.clear();
    auto blueprint =  mWorkflowGraphResourcePtr->GetBlueprint();
    for (size_t i = 0; i < blueprint->GetGraphCount(); i++)
    {
        auto* subGraph = blueprint->GetGraphByIndex(i);

        auto new_context = blueprint->CreateContext(std::dynamic_pointer_cast<gbf::logic::UBlueprintGraph>(subGraph->shared_from_this()));
        if (new_context)
        {
            InitBPGlobalVariables(new_context);
            mAllContext.emplace_back(new_context);
        }
    }
}

const gbf::logic::UBlueprintGraphGroup* WorkFlowComponent::GetBlueprint() const
{
    return mWorkflowGraphResourcePtr ? mWorkflowGraphResourcePtr->GetBlueprint() : nullptr;
}

void WorkFlowComponent::InitByWorkFlowResource(cross::ResourcePtr res)
{
    mWorkflowGraphResourcePtr = cross::TypeCast<cross::resource::WorkflowGraphResource>(res);
    InitGraphContext();
    mWorkFlowResGUID = res->GetGuid_Str();
}

void WorkFlowComponent::SetWorkflowRes(std::string resGUID)
{
    // check dirty
    if (mWorkFlowResGUID == resGUID)
    {
        return;
    }
    auto res = gResourceMgr.GetResource(resGUID.c_str());
    if (res)
    {
        InitByWorkFlowResource(res);
    }
    else
    {
        LOG_ERROR("Invalid workflow resource: guid {}, path {}", resGUID, gResourceMgr.ConvertGuidToPath(resGUID));
    }
    //gAssetStreamingManager->RequestAsyncLoad(resGUID, true, [this](cross::ResourcePtr inRes) {
    //    InitByWorkFlowResource(inRes);
    //});
}

void cegf::WorkFlowComponent::ImmediateEvent(const WFEventNameDesc& inEventName)
{
    for (auto& ctx : mAllContext)
    {
        ctx->OnImmediateEvent(inEventName.EventName, {}, inEventName.SlotIndex);
    }
}

void cegf::WorkFlowComponent::ReceiveDelegate(RttiBase* node, gbf::logic::BlueprintEventParamList param_list, size_t out_slot_index)
{
    if (node == nullptr)
    {
        return;
    }
    gbf::logic::UBlueprintEventNode* bp_event_node = dynamic_cast<gbf::logic::UBlueprintEventNode*>(node);

    if (bp_event_node == nullptr)
    {
        return;
    }
    for (auto& ctx : mAllContext)
    {
        if (ctx->GetMainGraph()->GetNodeById(bp_event_node->id()))
        {
            bp_event_node->RtPushNodeAsInstruction(ctx->GetMainCoroutine(), out_slot_index, param_list);
        }
    }
}

void cegf::WorkFlowComponent::ReceiveDelegate(RttiBase* node, gbf::machine::VValue _param, size_t out_slot_index)
{
    gbf::logic::BlueprintEventParamList param_list;
    param_list.push_back(std::move(_param));
    ReceiveDelegate(node, param_list, out_slot_index);
}
}

