#pragma once

#include "GameFramework/Components/Component.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) FoliageComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect) FoliageComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) void SetIntersection(bool enable);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetIntersection() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnable(bool enable);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetEnable() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetGlobalScale(float globalScale);
    CEFunction(Reflect, Cli, ScriptCallable) float GetGlobalScale() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetGlobalRangeScale(float globalScale);
    CEFunction(Reflect, Cli, ScriptCallable) float GetGlobalRangeScale() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetMaxRandomCulling(float maxRandomculling);
    CEFunction(Reflect, Cli, ScriptCallable) float GetMaxRandomCulling() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetDensity(float density);
    CEFunction(Reflect, Cli, ScriptCallable) float GetDensity() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetPCGReservedCapacity(UInt32 count);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetPCGReservedCapacity() const;
    CEFunction(Reflect, Cli, ScriptCallable) bool GetSubmeshVisible(UInt32 submeshIndex);
    CEFunction(Reflect, Cli, ScriptCallable) void SetSubmeshVisible(UInt32 submeshIndex, bool visible);
    CEFunction(Reflect, Cli, ScriptCallable) void SetLightCastShadow(bool castShadow);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetLightCastShadow() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEditorPrefabResource(const std::string& prefabPath);
    CEFunction(Reflect, Cli, ScriptCallable) size_t GetInstanceCount() const;
    CEFunction(Reflect, Cli, ScriptCallable) size_t GetInstanceLightCount() const;
    CEFunction(Reflect, Cli, ScriptCallable) cross::FoliageGenerationType GetFoliageGenerationType() const;
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
