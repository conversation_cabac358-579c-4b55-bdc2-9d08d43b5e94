#include "GameFramework/Camera/CameraComponent.h"
#include "GameFramework/GameObjects/Pawn.h"
#include "GameFramework/GameObjects/Controller.h"
namespace cegf
{

CameraComponent::CameraComponent()
{
    
}

CameraComponent::~CameraComponent()
{
    
}

void CameraComponent::Activate()
{
    if (mHasStarted)
    {
        SetCameraEnable(true);
    }
    GameObjectComponent::Activate();
}

void CameraComponent::Deactivate()
{
    SetCameraEnable(false);
    GameObjectComponent::Deactivate();
}

cross::CameraProjectionMode CameraComponent::GetProjectionMode() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetProjectionMode(comp.Read());
}

void CameraComponent::SetProjectionMode(cross::CameraProjectionMode mode)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    cameraSys->SetProjectionMode(comp.Write(), mode);
}

void CameraComponent::SetAspectRatio(float aspectRatio)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    cameraSys->SetPerspectiveAspect(comp.Write(), aspectRatio);
}

float CameraComponent::GetAspectRatio() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetPerspectiveAspect(comp.Read());
}

bool CameraComponent::GetCameraEnable() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetCameraEnable(comp.Read());
}

void CameraComponent::SetCameraEnable(bool enable)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    cameraSys->SetCameraEnable(comp.Write(), enable);
}

void CameraComponent::SetFocalLength(float focalLength)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    cameraSys->SetFocalLength(comp.Write(), focalLength);
}

float CameraComponent::GetFocalLength()
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetFocalLength(comp.Read());
}

void CameraComponent::SetFOV(float fieldOfView)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    cameraSys->SetPerspectiveFov(comp.Write(), fieldOfView);
}

float CameraComponent::GetFOV() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetFOV(comp.Read());
}

float CameraComponent::GetOrthoWidth() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetOrthoWidth(comp.Read());
}

float CameraComponent::GetOrthoHeight() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetOrthoHeight(comp.Read());
}

float CameraComponent::GetOrthoNearPlane() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetOrthoNearPlane(comp.Read());
}

float CameraComponent::GetOrthoFarPlane() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetOrthoFarPlane(comp.Read());
}

void CameraComponent::SetOrthogonal(float width, float height, float nearDistance, float farDistance)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    cameraSys->SetOrthogonal(comp.Write(), width, height, nearDistance, farDistance);
}

bool CameraComponent::SetOrthogonalWidth(float width)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetOrthogonalWidth(comp.Write(), width);
}

bool CameraComponent::SetOrthogonalHeight(float height)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetOrthogonalHeight(comp.Write(), height);
}

bool CameraComponent::SetOrthogonalNear(float nearDistance)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetOrthogonalNear(comp.Write(), nearDistance);
}

bool CameraComponent::SetOrthogonalFar(float farDistance)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetOrthogonalFar(comp.Write(), farDistance);
}

bool CameraComponent::SetAsMainCamera()
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetMainCamera(GetOwnerEntityID());
}


bool CameraComponent::SetPerspectiveNear(float nearDistance)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetPerspectiveNear(comp.Write(), nearDistance);
}

float CameraComponent::GetPerspectiveNear() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetPerspectiveNearPlane(comp.Read());
}

bool CameraComponent::SetPerspectiveFar(float farDistance)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetPerspectiveFar(comp.Write(), farDistance);
}

float CameraComponent::GetPerspectiveFar() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetPerspectiveFarPlane(comp.Read());
}

bool CameraComponent::SetMinFocalLength(float minFocalLength)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetMinFocalLength(comp.Write(), minFocalLength);
}

float CameraComponent::GetMinFocalLength() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetMinFocalLength(comp.Read());
}

bool CameraComponent::SetMaxFocalLength(float maxFocalLength)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetMaxFocalLength(comp.Write(), maxFocalLength);
}

float CameraComponent::GetMaxFocalLength() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetMaxFocalLength(comp.Read());
}

bool CameraComponent::SetSensorWidth(float sensorWidth)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetSensorWidth(comp.Write(), sensorWidth);
}

float CameraComponent::GetSensorWidth() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetSensorWidth(comp.Read());
}

bool CameraComponent::SetSensorHeight(float sensorHeight)
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->SetSensorHeight(comp.Write(), sensorHeight);
}

float CameraComponent::GetSensorHeight() const
{
    auto comp = GetECSComponent<cross::CameraComponentG>();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    return cameraSys->GetSensorHeight(comp.Read());
}

void CameraComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::CameraComponentG::GetDesc()->GetMaskBitIndex(), true);
}

cross::Float4x4 CameraComponent::GetProjectionMatrix() const
{
    auto reader = GetECSComponent<cross::CameraComponentG>().Read();
    auto cameraSys = GetSystem<cross::CameraSystemG>();
    switch (cameraSys->GetProjectionMode(reader))
    {
    case cross::CameraProjectionMode::Orthogonal:
        return cameraSys->GetOrthoProjMatrix(reader);
        break;
    case cross::CameraProjectionMode::Perspective:
    default:
        return cameraSys->GetProjMatrix(reader);
        break;
    }
}


void CameraComponent::GetCameraView(float deltaTime, ViewTargetInfo& outTargetInfo)
{
    Pawn* owningPawn = CheckCastTo<Pawn, GameObject>(GetOwner());
    if (owningPawn)
    {
        const Controller* owningController = owningPawn ? owningPawn->GetController() : nullptr;
        if (owningController && owningController->IsPlayerController())
        {
            cross::TRSQuaternionType pawnRotation = owningPawn->GetWorldRotation();
            cross::TRSQuaternionType componentRotation = owningPawn->GetWorldRotation();
            if (!componentRotation.Equal(pawnRotation))
            {
                owningPawn->SetWorldRotation(pawnRotation);
            }
        }
    }

    outTargetInfo.SetViewTranslation(GetOwner()->GetWorldTranslation());
    outTargetInfo.SetViewRotation(GetOwner()->GetWorldRotation());
    outTargetInfo.SetCameraComponent(this);
}

}

