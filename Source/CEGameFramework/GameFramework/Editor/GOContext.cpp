#include "GOContext.h"

#include <ValueConverter/ResourceClassRegisterFactory.h>

#include "WorkflowGraphResource.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "../GameFrameworkSystem.h"
#include "base/core/utils/byte_buffer.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/WorkFlowComponent.h"
#include "details/blueprint_workspace.h"
#include "details/blueprint_graph_group.h"
#include "GameObjects/AIController.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"
#include "ValueConverter/InspectorHelperResource.h"
namespace cegf
{

GameEnginePtr GOContext::GetCurrentGameEngine()
{
    if (!m_cross_game_engine_)
    {
        Assert(false);
        return nullptr;
    }
    auto GFSys = m_cross_game_engine_->GetGlobalSystem<GameFrameworkSystem>();
    return GFSys->GetGameEngine();
}

GameWorld* GOContext::GetCurrentGameWorld()
{
    if (!m_cross_game_world_)
    {
        Assert(false);
        return nullptr;
    }
    GameEnginePtr engine = GetCurrentGameEngine();
    GameWorld* world = engine->GetGameWorld(m_cross_game_world_->GetRuntimeID());
    if (!world)
    {
        Assert(false);
        return nullptr;
    }
    return world;
}

void GOContext::Init()
{
    Assert(m_cross_game_engine_);
    Assert(m_cross_game_world_);
    m_game_engine_ = GetCurrentGameEngine();
    m_game_world_ = GetCurrentGameWorld();
    EnumerateGOTypes();
}

GOContext::GOContext(cross::IGameWorld* in_game_world)
    : m_cross_game_world_(reinterpret_cast<cross::GameWorld*>(in_game_world))
{
    m_cross_game_engine_ = reinterpret_cast<cross::CrossEngine*>(cross::EngineGlobal::GetEngine());
    Init();
}

GOContext::GOContext(GOContext& inContext)
{
    m_cross_game_engine_ = reinterpret_cast<cross::CrossEngine*>(cross::EngineGlobal::GetEngine());
    m_cross_game_world_ = inContext.m_cross_game_world_;
    Init();
}

void GOContext::CreateGameObject(std::string className, cross::TRSVector3Type pos, GOHandle* ret)
{
    CreateGameObject(className, pos, cross::ecs::EntityID::InvalidHandle().GetValue(), ret);

}

void GOContext::CreateGameObject(std::string className, cross::TRSVector3Type pos, UInt64 EntityID, GOHandle* ret)
{
    cross::TRSVector3AType location = pos;
    cross::TRSQuaternionAType rot = cross::TRSQuaternionAType::Identity();

    CreateGameObjectParameters Param;

    if (ret)
    {
        if (ret->Parent)
        {
            Param.mParent = ret->Parent;
        }
        Param.mGameObjectName = ret->GOName;
    }
    Param.mLocalEntity = EntityID;

    cross::TRSVector3Type ori_T, ori_S;
    cross::TRSQuaternionType ori_Q;

    auto transSys = m_cross_game_world_->GetGameSystem<cross::TransformSystemG>();
    // save ori world translation
    if (Param.mLocalEntity != Param.mLocalEntity.InvalidHandle())
    {
        auto comp = m_cross_game_world_->GetComponent<cross::WorldTransformComponentG>(Param.mLocalEntity);
        ori_T = transSys->GetWorldTranslationT(comp.Read());
        ori_S = transSys->GetWorldScaleT(comp.Read());
        ori_Q = transSys->GetWorldRotationT(comp.Read());
    }

    auto newobj = m_game_world_->CreateGameObject(className, location, rot, cross::TRSVector3AType::One(), Param);

    // load ori world translation
    if (Param.mLocalEntity != Param.mLocalEntity.InvalidHandle())
    {
        auto comp = m_cross_game_world_->GetComponent<cross::WorldTransformComponentG>(Param.mLocalEntity);
        transSys->SetWorldTranslationT(comp.Write(), ori_T);
        transSys->SetWorldScaleT(comp.Write(), ori_S);
        transSys->SetWorldRotationT(comp.Write(), ori_Q);
    }

    if (ret == nullptr)
    {
        return;
    }
    ret->EntityID = newobj->GetObjectEntityID();
    ret->UsingWorkflow = false;
    ret->GOType = className;
    ret->GOName = newobj->GetName();
    ret->WorkflowGUID = "";
    ret->Location = location;
}

void GOContext::CreateGameObjectWithHandle(GOHandle inHandle, GOHandle* ret)
{
    if (inHandle.EntityID != cross::ecs::EntityID::InvalidHandle().GetValue())
    {
        // attach to entity
    }
    else if (inHandle.UsingWorkflow)
    {
        CreateGameObjectWithFlowFile(inHandle.WorkflowGUID, inHandle.Location, ret);
    }
    else
    {
        CreateGameObject(inHandle.GOType, inHandle.Location, ret);
    }
}


cross::ecs::ComponentBitMask GetComponentBitMask(const gbf::reflection::MetaClass* meta_class, std::shared_ptr<cegf::GameObjectComponent>* out_comp = nullptr)
{
    cross::ecs::ComponentBitMask bit_mask;
    auto vtable = meta_class->GetStorageVtableByType(gbf::reflection::StorageType::StorageRemoteShared);
    if (vtable->ctor_default_ == nullptr)
    {
        return bit_mask;
    }

    // 2. build comp ptr
    auto comp = static_pointer_cast<cegf::GameObjectComponent>(gbf::reflection::make_user_object_by_class(meta_class).Ref<cegf::GameObjectComponent>().shared_from_this());

    // collect bit mask, except GOSerializerComponentG
    comp->GetRelatedECSComponentBitMask(bit_mask);
    bit_mask.Set(cross::GOSerializerComponentG::GetDesc()->GetMaskBitIndex(), false);

    if (out_comp)
    {
        *out_comp = std::move(comp);
    }
    return bit_mask;
}

cross::ecs::ComponentBitMask GetComponentBitMask(const std::string& meta_class_name, std::shared_ptr<cegf::GameObjectComponent>* out_comp = nullptr)
{
    return GetComponentBitMask(gbf::reflection::query_meta_class_by_name(meta_class_name), out_comp);
}

void GOContext::TransferToGameObjectWithHandle(GOHandle inHandle)
{
    // prepare empty GameComponent's bit mask
    auto empty_bit_mask = GetComponentBitMask(gbf::reflection::query_meta_class<cegf::GameObjectComponent>());

    std::unordered_map<std::string, GameObjectComponentPtr> candidates_comp_names;
    std::unordered_set<std::string> excluded_comp_names = {
        gbf::reflection::query_meta_class<cegf::BoxComponent>()->name(),
        gbf::reflection::query_meta_class<cegf::SphereComponent>()->name(),
        gbf::reflection::query_meta_class<cegf::CapsuleComponent>()->name(),
    };

    for (auto comp_type_name : GCompTypeNames)
    {
        // already added
        if (candidates_comp_names.find(comp_type_name) != candidates_comp_names.end())
        {
            continue;
        }

        // already checked
        if (excluded_comp_names.find(comp_type_name) != excluded_comp_names.end())
        {
            continue;
        }

        // 1. get meta class
        std::shared_ptr<cegf::GameObjectComponent> comp;
        auto bit_mask = GetComponentBitMask(comp_type_name, &comp);
        if (bit_mask == empty_bit_mask || bit_mask == cross::ecs::ComponentBitMask{})
        {
            continue;
        }

        //3. check if ecs has all related components
        if (m_cross_game_world_->HasComponent(inHandle.EntityID, bit_mask))
        {
            auto meta_class = gbf::reflection::query_meta_class_by_name(comp_type_name);

            // 4. remove all added base class
            bool base_class_exist = false;
            std::deque<const gbf::reflection::MetaClass*> base_classes;

            for (size_t i = 0; i < meta_class->GetBaseCount(); i++)
            {
                base_classes.push_back(&meta_class->GetBase(i));
                excluded_comp_names.insert(meta_class->GetBase(i).name());
            }

            // search all base class recursively
            while (!base_classes.empty())
            {
                auto base = base_classes.front();
                base_classes.pop_front();

                // if found, remove it
                auto itr = candidates_comp_names.find(base->name());
                if (itr != candidates_comp_names.end())
                {
                    candidates_comp_names.erase(itr);
                }

                // add base of base to the queue
                for (size_t i = 0; i < base->GetBaseCount(); i++)
                {
                    base_classes.push_back(&base->GetBase(i));
                    excluded_comp_names.insert(base->GetBase(i).name());
                }
            }

            //auto comp = static_pointer_cast<cegf::GameObjectComponent>(gbf::reflection::make_user_object_by_class(meta_class).Ref<cegf::GameObjectComponent>().shared_from_this());
            candidates_comp_names.insert({std::move(comp_type_name), std::move(comp)});
        }
        else
        {
            excluded_comp_names.insert(comp_type_name);
        }
    }

    // prepare json for deserialize go serializer
    cross::SerializeNode json, json_go, json_components;
    cross::SerializeContext context;
    using _go_comp_type = cross::GOSerializerComponentG;

    for (auto& [name, component] : candidates_comp_names)
    {
        SerializeNode json_comp;
        component->Serialize(json_comp, context);

        json_comp[GameObject::SK_COMPONENTTYPE] = name;

        json_components.PushBack(std::move(json_comp));
    }

    // prepare go json
    {
        auto trans_comp = m_cross_game_world_->GetComponent<cross::LocalTransformComponentG>(inHandle.EntityID);
        auto trans_sys = m_cross_game_world_->GetGameSystem<cross::TransformSystemG>();
        json_go[GameObject::SK_LOCAL_ROTATION] = trans_sys->GetLocalRotationT(trans_comp.Read()).Serialize();
        json_go[GameObject::SK_LOCAL_TRANSLATION] = trans_sys->GetLocalTranslationT(trans_comp.Read()).Serialize();
        json_go[GameObject::SK_LOCAL_SCALE] = trans_sys->GetLocalScale(trans_comp.Read()).Serialize();
        json_go[GameObject::SK_METACLASSTYPE] = "cegf::GameObject";
        json_go[GameObject::SK_OBJECTNAME] = inHandle.GOName;
        json_go[GameObject::SK_COMPONENTS] = std::move(json_components);
    }

    json[_go_comp_type::SK_GAMEOBJECT] = std::move(json_go);

    // create go serializer
    m_cross_game_world_->CreateComponents<_go_comp_type>(inHandle.EntityID);
    auto comp = m_cross_game_world_->GetComponent<_go_comp_type>(inHandle.EntityID);

    _go_comp_type::PostDeserializeGOSComponent(json, comp.mComponent, m_cross_game_world_, inHandle.EntityID);
    
}

void GOContext::DuplicateGameObject(UInt64 srcEntityID, UInt64 targetEntityID)
{
    GameObject* srcGO = GetGameObject(srcEntityID);
    GameObject* targetGO = GetGameObject(targetEntityID);

    if (!srcGO || !targetGO)
    {
        return;
    }

    SerializeContext context;
    SerializeNode GOJson;
    srcGO->Serialize(GOJson, context);
    targetGO->Deserialize(GOJson, context);
}

void GOContext::DeleteGameObjectByEntity(UInt64 inEntityID)
{
    cross::ecs::EntityID entity = {inEntityID};
    if (entity == cross::ecs::EntityID::InvalidHandle())
    {
        return;
    }
    m_cross_game_world_->DestroyEntity(entity);
}

void GOContext::CreateGameObjectWithFlowFile(std::string flowFilePath, cross::TRSVector3Type pos, GOHandle* ret)
{
    // step0 : ensure that the resource is available when called
    // step 1 :: extract class type from resource
    cross::WorkflowGraphResourcePtr workflowResourceptr = cross::TypeCast<cross::resource::WorkflowGraphResource>(gAssetStreamingManager->LoadSynchronously(flowFilePath));

    std::string ClassName = workflowResourceptr->GetSuperClassName();

    // step 2: create object
    GameObjectPtr newobj = m_game_world_->CreateGameObject(ClassName, pos, cross::TRSQuaternionAType(0.f, 0.f, 0.f, 1.0f), cross::TRSVector3AType::One());

    // step 3: create and init workflow component
    auto newComp = newobj->AddComponent<cegf::WorkFlowComponent>();
    newComp->InitByWorkFlowResource(cross::TypeCast<cross::Resource>(workflowResourceptr));

    newobj->RunConstructionScript();

    if (ret == nullptr)
    {
        return;
    }
    ret->EntityID = newobj->GetObjectEntityID();
    ret->UsingWorkflow = true;
    ret->GOType = ClassName;
    ret->GOName = newobj->GetName();
    ret->WorkflowGUID = workflowResourceptr->GetGuid_Str();
    ret->Location = pos;
}

void GOContext::EnumerateGOTypes()
{
    GOTypeNames.clear();
    GCompTypeNames.clear();
    StructTypeNames.clear();
    ResourceTypeNames.clear();

    size_t class_num = gbf::reflection::meta_class_count();
    GOTypeNames.reserve(class_num);
    GCompTypeNames.reserve(class_num);
    StructTypeNames.reserve(class_num);

    for (size_t i = 0; i < class_num; i++)
    {
        auto classptr = gbf::reflection::meta_class_by_index(static_cast<int>(i));

        if (classptr->SearchBaseClass(gbf::reflection::query_meta_class<cegf::GameObject>()->id()))
        {
            GOTypeNames.push_back(classptr->name());
        }
        else if (classptr->SearchBaseClass(gbf::reflection::query_meta_class<cegf::GameObjectComponent>()->id()))
        {
            GCompTypeNames.push_back(classptr->name());
        }
        else {
            const std::string& prop_name = "StructFullName";
            if (classptr->HasStaticProperty(prop_name))
            {
                auto& prop = classptr->GetStaticProperty(prop_name);
                if (prop.IsReadable() && prop.kind() == gbf::reflection::ValueKind::kString)
                {
                    StructTypeNames.push_back(prop.Get().Ref<std::string>());
                }
            }
                
        }
    }

    // init resource type names
    auto& resources = cegf::ResourceClassRegisterFactory::Instance().GetResourceRefClasses();

    ResourceTypeNames.reserve(resources.size());
    for (auto itr = resources.begin();itr != resources.end(); itr++)
    {
        ResourceTypeNames.emplace_back(itr->first);
    }
}

GameObject* GOContext::GetGameObject(UInt64 inEntityID)
{
    cross::ecs::EntityID e = inEntityID;
    GameObject* obj = m_game_world_->GetGameObject(e);
    return obj;
}

Vector_GameObject_wrapper GOContext::GetGameObjectsWithComponent(std::string inComponentName)
{
    auto meta_class = gbf::reflection::query_meta_class_by_name(inComponentName);
    Vector_GameObject_wrapper ret;
    ret.holder.reserve(m_game_world_->GetRuntimeCreatedGameObjectCount());

    m_game_world_->TraverseGameObjects([&ret, meta_class](GameObjectPtr obj) 
    {
        if (obj->GetComponentByMetaClass(meta_class) != nullptr)
        {
            ret.holder.push_back(obj.get());
        }
    });

    return ret;
}

void GOContext::GetGameObjectComponentNames(UInt64 inEntityID, Vector_std_string_wrapper* ret)
{
    cross::ecs::EntityID e = inEntityID;
    GameObject* obj = m_game_world_->GetGameObject(e);
    if (obj == nullptr)
    {
        return;
    }

    const auto& allComps = obj->GetAllComponents();
    ret->holder.clear();
    ret->holder.reserve(allComps.size());

    for (const auto comp : allComps)
    {
        ret->holder.emplace_back(comp->GetMetaClass()->name());
    }
}

bool GOContext::GameObjectHasComponent(UInt64 inEntityID, std::string inComponentName)
{
    cross::ecs::EntityID e = inEntityID;
    GameObject* obj = m_game_world_->GetGameObject(e);
    if (obj == nullptr)
    {
        return false;
    }
    return obj->GetComponentByMetaClassName(inComponentName) != nullptr;
}
}   // namespace cegf
