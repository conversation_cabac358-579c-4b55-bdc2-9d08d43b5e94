#include "GameFramework/Tests/TestCharacter.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/Input/InputManager.h"

namespace cegf
{

void TestCharacter::Tick(float deltaTime)
{
    Character::Tick(deltaTime);
    if (mController)
    {
        //handle input
        cross::EngineGlobal& global = cross::EngineGlobal::Inst();
        cross::InputManager* inputMgr = global.GetInputManager();
        auto user = inputMgr->GetUser({0});
        float hTarget = user->GetKeyValue(cross::input::CEKeys::CursorX).x;
        if (hTarget != 0.0f)
        {
            AddControllerYawInput(hTarget * deltaTime);
        }
        float vTarget = user->GetKeyValue(cross::input::CEKeys::CursorY).x;
        if (vTarget != 0.0f)
        {
            AddControllerYawInput(vTarget * deltaTime);
        }

        if (hTarget || vTarget)
        {
            //LOG_DEBUG("TestCharacter::Tick =========== {} {}", hTarget, vTarget);
        }
    }
}

}

