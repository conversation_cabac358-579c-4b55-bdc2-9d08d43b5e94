namespace CrossEditor
{
    public struct Vector4ui
    {
        public uint X;
        public uint Y;
        public uint Z;
        public uint W;

        public Vector4ui(uint X, uint Y, uint Z, uint W)
        {
            this.X = X;
            this.Y = Y;
            this.Z = Z;
            this.W = W;
        }

        public override bool Equals(object Object)
        {
            if (Object is Vector4ui)
            {
                return this == (Vector4ui)Object;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return X.GetHashCode() ^ Y.GetHashCode() ^ Z.GetHashCode() ^ W.GetHashCode();
        }

        public static bool operator ==(Vector4ui v1, Vector4ui v2)
        {
            return (v1.X == v2.X) && (v1.Y == v2.Y) && (v1.Z == v2.Z) && (v1.W == v2.W);
        }

        public static bool operator !=(Vector4ui v1, Vector4ui v2)
        {
            return (v1.X != v2.X) || (v1.Y != v2.Y) || (v1.Z != v2.Z) || (v1.W != v2.W);
        }

        public static Vector4ui operator +(Vector4ui v1, Vector4ui v2)
        {
            return new Vector4ui(v1.X + v2.X, v1.Y + v2.Y, v1.Z + v2.Z, v1.W + v2.W);
        }
    }
}