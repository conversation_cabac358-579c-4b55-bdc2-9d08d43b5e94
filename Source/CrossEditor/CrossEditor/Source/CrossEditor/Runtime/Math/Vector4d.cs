using EditorUI;
using System;

namespace CrossEditor
{
    public struct Vector4d
    {
        public double X;
        public double Y;
        public double Z;
        public double W;

        public Vector4d(double X, double Y, double Z, double W)
        {
            this.X = X;
            this.Y = Y;
            this.Z = Z;
            this.W = W;
        }

        public Vector3d ToVector3()
        {
            double InverseW = 1.0 / this.W;
            return new Vector3d(this.X * InverseW, this.Y * InverseW, this.Z * InverseW);
        }

        public override bool Equals(object Object)
        {
            if (Object is Vector4d)
            {
                return this == (Vector4d)Object;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return X.GetHashCode() ^ Y.GetHashCode() ^ Z.GetHashCode() ^ W.GetHashCode();
        }

        public static bool operator ==(Vector4d v1, Vector4d v2)
        {
            return Math.Abs(v1.X - v2.X) < MathHelper.Epsilon &&
                   Math.Abs(v1.Y - v2.Y) < MathHelper.Epsilon &&
                   Math.Abs(v1.Z - v2.Z) < MathHelper.Epsilon &&
                   Math.Abs(v1.W - v2.W) < MathHelper.Epsilon;
        }

        public static bool operator !=(Vector4d v1, Vector4d v2)
        {
            return Math.Abs(v1.X - v2.X) >= MathHelper.Epsilon ||
                   Math.Abs(v1.Y - v2.Y) >= MathHelper.Epsilon ||
                   Math.Abs(v1.Z - v2.Z) >= MathHelper.Epsilon ||
                   Math.Abs(v1.W - v2.W) >= MathHelper.Epsilon;
        }

        public override string ToString()
        {
            return string.Format("({0}, {1}, {2}, {3})", X, Y, Z, W);
        }

        public double Magnitude()
        {
            return Math.Sqrt(Math.Pow(X, 2) + Math.Pow(Y, 2) + Math.Pow(Z, 2) + Math.Pow(W, 2));
        }
    }
}
