using Clicegf;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class GameObject
    {
        public Entity mEntity;
        public List<GameObjectComponent> mComponents;

        public GameObject()
        {
            mComponents = new List<GameObjectComponent>();
        }

        public bool HasComponent(Type Type)
        {
            return mComponents.Any(Comp => Comp.GetType() == Type);
        }
        public bool CheckDuplicate(Type type)
        {
            return HasComponent(type);
        }

        public int FindComponent(GameObjectComponent Component)
        {
            return mComponents.FindIndex(_comp => _comp == Component);
        }

        // should preferbly use this function
        public T GetComponent<T>() where T : GameObjectComponent
        {
            return (T)GetComponent(typeof(T));
        }
        public Clicegf.GameObject GetRuntimeGameObject()
        {
            GOContext GoContext = new GOContext(mEntity.World._WorldInterface);
            return GoContext.GetGameObject(mEntity.EntityID);
        }
        public void RuntimeAddComponentsAndReapplyProperties()
        {
            List<GameObjectComponent> SwappedComponent = mComponents;
            mComponents = new List<GameObjectComponent>();
            SwappedComponent.Sort(CompareByCompnentOrder);

            foreach (GameObjectComponent Component in SwappedComponent)
            {
                AddComponent(Component);
                Component.RuntimeReapplyProperties();
            }
        }

        public static int CompareByCompnentOrder(ComponentBase x, ComponentBase y)
        {
            {
                return x.ComponentOrder().CompareTo(y.ComponentOrder());
            }
        }

        public GameObjectComponent GetComponent(Type Type)
        {
            return mComponents.Find(_comp => _comp.GetType() == Type);
        }

        public T CreateComponent<T>() where T : GameObjectComponent, new()
        {
            T comp = new T();
            return (T)AddComponent(comp);
        }

        public virtual void OnComponentAddToEntity()
        {

        }

        public GameObjectComponent AddComponent(GameObjectComponent realComponent)
        {
            foreach (var mECSEditorComponent in realComponent.mECSEditorComponents.Values)
            {
                mEntity.AddComponent(mECSEditorComponent);
            }

            // some rare case (WorldPartitionProperty component) it has no native component, thus runtime duplicated is always false
            // cause some exceptions needs to be handled.
            bool isDuplicated = HasComponent(realComponent.GetType());
            bool runtimeDuplicated = CheckDuplicate(realComponent.GetType());
            if (realComponent != null && !isDuplicated && !runtimeDuplicated)
            {
                realComponent.mGameObject = this;
                realComponent.RuntimeAddComponent();
                mComponents.Add(realComponent);
                mComponents.Sort(GameObjectComponent.CompareByOrder);
                realComponent.OnComponentAddToEntity();

                Clicegf.PropertyChangedEvent evt = new Clicegf.PropertyChangedEvent();
                evt.PropertyName = realComponent.GetType().ToString();
                GetRuntimeGameObject().PostEditChangeProperty(evt);
                return realComponent;
            }
            else if (isDuplicated && runtimeDuplicated)
            {
                var comp = mComponents.Find(_comp => _comp.GetType() == realComponent.GetType());
                if (comp != null)
                {
                    return comp;
                }
            }
            return null;
        }

        public void RemoveComponent(GameObjectComponent Component)
        {
            if (Component != null)
            {
                if (Component.RuntimeHasComponent())
                {
                    Component.RuntimeRemoveComponent();
                }

                foreach (var mECSEditorComponent in Component.mECSEditorComponents.Values)
                {
                    mEntity.RemoveComponent(mECSEditorComponent);
                }

                mComponents.Remove(Component);

                Clicegf.PropertyChangedEvent evt = new Clicegf.PropertyChangedEvent();
                evt.PropertyName = Component.GetType().ToString();
                GetRuntimeGameObject().PostEditChangeProperty(evt);
            }
        }

        public void RefreshGameObjectComponents()
        {
            mComponents.Clear();

            HashSet<Type> EditorComps = new HashSet<Type>();

            GOContext GoContext = new GOContext(mEntity.World._WorldInterface);
            var compNames = new Vector_std_string_wrapper();
            GoContext.GetGameObjectComponentNames(mEntity.EntityID, compNames);

            for (int i = 0; i < compNames.holder.Count; i++)
            {
                var name = compNames.holder[i];
                if (CrossEngine.GetInstance().RuntimeToEditorTypeMapping.ContainsKey(name))
                {
                    EditorComps.Add(CrossEngine.GetInstance().RuntimeToEditorTypeMapping[name]);
                }
            }

            foreach (Type comptype in EditorComps)
            {
                GameObjectComponent comp = (GameObjectComponent)Activator.CreateInstance(comptype);
                comp.mGameObject = this;

                if (comp.RuntimeHasComponent())
                {
                    mComponents.Add(comp);
                }
            }
            mComponents.Sort(GameObjectComponent.CompareByOrder);
            foreach (GameObjectComponent comp in mComponents)
            {
                comp.SyncDataFromEngine();
            }
        }

        public int FindComponentIndex(GameObjectComponent Component)
        {
            return mComponents.FindIndex(_comp => _comp == Component);
        }

        public void MoveUp(GameObjectComponent Component)
        {
            int Index = FindComponentIndex(Component);
            if (Index == -1)
            {
                return;
            }
            if (Index == 0 || Index == 1)
            {
                return;
            }
            GameObjectComponent Component1 = mComponents[Index - 1];
            mComponents[Index - 1] = Component;
            mComponents[Index] = Component1;
        }

        public bool CanMoveUp(GameObjectComponent Component)
        {
            int Index = FindComponentIndex(Component);
            if (Index == -1)
            {
                return false;
            }
            if (Index == 0 || Index == 1)
            {
                return false;
            }
            return true;
        }

        public void MoveDown(GameObjectComponent Component)
        {
            int Index = FindComponentIndex(Component);
            if (Index == -1)
            {
                return;
            }
            if (Index == 0 || Index == mComponents.Count - 1)
            {
                return;
            }
            GameObjectComponent Component1 = mComponents[Index + 1];
            mComponents[Index + 1] = Component;
            mComponents[Index] = Component1;
        }

        public bool CanMoveDown(GameObjectComponent Component)
        {
            int Index = FindComponentIndex(Component);
            if (Index == -1)
            {
                return false;
            }
            if (Index == 0 || Index == mComponents.Count - 1)
            {
                return false;
            }
            return true;
        }

        static public GameObject GetGameObjectFromEntity(Entity inEntity)
        {
            if (inEntity == null || !inEntity.HasComponent(typeof(GOSerializerComponent)))
            {
                return null;
            }
            GOSerializerComponent comp = inEntity.GetComponent<GOSerializerComponent>();
            GameObject GameObject = comp.GameObject;
            return GameObject;
        }
    }
}