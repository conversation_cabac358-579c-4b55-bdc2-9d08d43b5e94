namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/TerrainComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class TerrainGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::TerrainComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<TerrainComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<TerrainComponent>();
            }

            mECSEditorComponents["TerrainComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["TerrainComponent"] = mGameObject.mEntity.GetComponent<TerrainComponent>();
        }
    }
}
