namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/SkyLightComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class SkyLightGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::SkyLightComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<SkyLightComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<SkyLightComponent>();
            }

            mECSEditorComponents["SkyLightComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["SkyLightComponent"] = mGameObject.mEntity.GetComponent<SkyLightComponent>();
        }
    }
}
