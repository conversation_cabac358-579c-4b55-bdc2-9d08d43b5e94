using CEngine;

namespace CrossEditor
{
    class CloudComponent : Component
    {
        static string[] _NativeNames = { "cross::CloudComponentG" };
        static int _ComponentOrder = 3;
        static int _GroupOrder = 2;


        public CloudComponentG ComponentData = new CloudComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "Cloud")]
        public CloudComponentG Cloud
        {
            get
            {
                if (Entity.World != null)
                {
                    CloudSystemG.GetCloudComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
                }
                return ComponentData;
            }
            set
            {
                ComponentData = value;
                CloudSystemG.SetCloudComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
            }
        }

        //public override void SyncDataFromEngine()
        //{
        //    PostProcessVolumeSystemG.GetPostProcessVolumeComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
        //}

        //public void OnComponentDataChanged()
        //{
        //    PostProcessVolumeSystemG.SetPostProcessVolumeComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
        //}

        public CloudComponent() { }

    }
}
