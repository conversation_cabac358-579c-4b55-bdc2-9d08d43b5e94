using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Mesh/HierachicalInstancedStaticModel Component", NeedRenderProperty = true)]
    class HierachicalInstancedStaticModelComponent : InstancedStaticModelComponent
	{
        static string[] _NativeNames = { "cross::HierachicalInstancedStaticModelComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        static new List<Type> _AssociateComponents = new List<Type> { typeof(InstancedStaticModelComponent) };

        static int _ComponentOrder = 2;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this mesh renderer.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;
            }
        }
    }
}
