using EditorUI;

namespace CrossEditor
{
    public delegate void SearchUISearchEventHandler(SearchUI Sender, string Pattern);
    public delegate void SearchUICancelEventHandler(SearchUI Sender);

    public class SearchUI
    {
        Panel _PanelBack;
        Edit _EditSearchPattern;
        Button _ButtonSearch;
        Button _ButtonCancel;

        public event SearchUISearchEventHandler SearchEvent;
        public event SearchUICancelEventHandler CancelEvent;

        public void Initialize()
        {
            _PanelBack = new Panel();
            _PanelBack.SetSize(500, 20);
            _PanelBack.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _PanelBack.PositionChangedEvent += OnPanelBackPositionChanged;

            _EditSearchPattern = new Edit();
            _EditSearchPattern.SetFontSize(16);
            _EditSearchPattern.Initialize(EditMode.Simple_SingleLine);
            _EditSearchPattern.LoadSource("");
            _EditSearchPattern.TextChangedEvent += OnEditTextChanged;
            _EditSearchPattern.CharInputedEvent += OnEditSearchCharInputed;
            _EditSearchPattern.KeyDownEvent += OnEditSearchKeyDown;
            _PanelBack.AddChild(_EditSearchPattern);
            _EditSearchPattern.SetPosition(0, 2, 200, 16);
            _EditSearchPattern.SetBackgroundColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            EditContextUI.GetInstance().RegisterEdit(_EditSearchPattern);

            _ButtonSearch = new Button();
            _ButtonSearch.SetImage(UIManager.LoadUIImage("Editor/Others/ButtonSearch.png"));
            _ButtonSearch.ClickedEvent += OnButtonSearchClicked;
            _ButtonSearch.SetPosition(0, 0, 20, 20);
            _PanelBack.AddChild(_ButtonSearch);

            _ButtonCancel = new Button();
            _ButtonCancel.SetImage(UIManager.LoadUIImage("Editor/UI/Docking/ButtonClose.png"));
            _ButtonCancel.ClickedEvent += OnButtonCancelClicked;
            _ButtonCancel.SetPosition(180, 0, 16, 16);
            _ButtonCancel.SetVisible(false);
            _EditSearchPattern.AddChild(_ButtonCancel);
        }

        public UIManager GetUIManager()
        {
            if (_PanelBack != null)
            {
                return _PanelBack.GetUIManager();
            }
            else
            {
                return UIManager.GetActiveUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public Panel GetPanelBack()
        {
            return _PanelBack;
        }

        public Edit GetEdit()
        {
            return _EditSearchPattern;
        }

        public string GetSearchPattern()
        {
            string SearchPattern = _EditSearchPattern.GetText();
            return SearchPattern;
        }

        public void ClearSearchPattern()
        {
            _EditSearchPattern.SetText("");
            _ButtonCancel.SetVisible(false);
        }

        public void TriggerCancelEvent()
        {
            if (CancelEvent != null)
            {
                CancelEvent(this);
            }
        }

        public void TriggerSearchEvent()
        {
            if (SearchEvent != null)
            {
                string SearchPattern = _EditSearchPattern.GetText();
                SearchEvent(this, SearchPattern);
            }
        }

        void OnEditTextChanged(Edit Sender)
        {
            string SearchPattern = _EditSearchPattern.GetText();
            if (SearchPattern == "")
            {
                _ButtonCancel.SetVisible(false);
                TriggerCancelEvent();
            }
            else
            {
                _ButtonCancel.SetVisible(true);
            }
        }

        void OnEditSearchCharInputed(Control Sender, char Char, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && (Char == '\r' || Char == '\n'))
            {
                TriggerSearchEvent();
            }
        }

        void OnEditSearchKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Escape)
            {
                ClearSearchPattern();
                TriggerCancelEvent();
            }
        }

        void OnButtonSearchClicked(Button Sender)
        {
            TriggerSearchEvent();
        }

        void OnButtonCancelClicked(Button Sender)
        {
            ClearSearchPattern();
            TriggerCancelEvent();
            Sender.SetVisible(false);
        }

        void OnPanelBackPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            int SpanX = 4;
            int Width = _PanelBack.GetWidth();
            int ButtonSearchWidth = 20;
            int ButtonSearchX = Width - ButtonSearchWidth;
            int EditSearchPatternWidth = ButtonSearchX - 2 * SpanX;
            int ButtonCancelX = EditSearchPatternWidth - 16;
            _EditSearchPattern.SetPosition(SpanX, 2, EditSearchPatternWidth, 16);
            _ButtonSearch.SetPosition(ButtonSearchX, 0, ButtonSearchWidth, 20);
            _ButtonCancel.SetPosition(ButtonCancelX, 0, 16, 16);
        }
    }
}
