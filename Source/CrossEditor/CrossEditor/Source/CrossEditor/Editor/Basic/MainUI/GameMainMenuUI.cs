using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace CrossEditor
{
    public class GameMainMenuUI
    {
        static GameMainMenuUI _Instance = new GameMainMenuUI();

        MainMenu _MainMenu;

        Texture _TextureCheckedMark;

        Menu _Menu_File;
        MenuItem _MenuItem_FileProjects;
        MenuItem _MenuItem_FileNewScene;
        MenuItem _MenuItem_FileOpenScene;
        MenuItem _MenuItem_FileSave;
        MenuItem _MenuItem_FileSaveAs;
        MenuItem _MenuItem_FileExit;

        Menu _Menu_Edit;
        MenuItem _MenuItem_EditUndo;
        MenuItem _MenuItem_EditRedo;
        MenuItem _MenuItem_EditCopy;
        MenuItem _MenuItem_EditPaste;
        MenuItem _MenuItem_EditDelete;
        MenuItem _MenuItem_EditDuplicate;
        MenuItem _MenuItem_EditorConfig;

        Menu _Menu_Assets;
        Menu _Menu_AssetsCreate;
        MenuItem _MenuItem_AssetsCreate;
        MenuItem _MenuItem_AssetsImportAsset;
        MenuItem _MenuItem_AssetsImportAssets;
        MenuItem _MenuItem_AssetsBatchCluster;

        Menu _Menu_Entity;

        Menu _Menu_Components;
        AddComponentMenu _AddComponentMenu;

        Menu _Menu_Generate;
        MenuItem _MenuItem_GenerateGenerateFoliage;
        MenuItem _MenuItem_GenerateGenerateMSDF;

        Menu _Menu_Debug;
        MenuItem _MenuItem_DebugPlayGameWithProjectConfig;
        MenuItem _MenuItem_DebugPlayGame;
        MenuItem _MenuItem_DebugPlayCurrentScene;
        MenuItem _MenuItem_BuildResourceList;
        MenuItem _MenuItem_ViewImGuiConsole;

        Menu _Menu_Build;
        MenuItem _MenuItem_BuildProject;

        Menu _Menu_View;
        MenuItem _MenuItem_ViewProject;
        MenuItem _MenuItem_ViewHierarchy;
        MenuItem _MenuItem_ViewInspector;
        MenuItem _MenuItem_ViewResourceInspector;
        MenuItem _MenuItem_ViewTerrain;
        MenuItem _MenuItem_ViewTod;
        MenuItem _MenuItem_ViewConsole;
        MenuItem _MenuItem_ViewWelcome;
        MenuItem _MenuItem_ViewFxView;
        MenuItem _MenuItem_ViewScene;
        MenuItem _MenuItem_ViewGame;
        MenuItem _MenuItem_ViewCaptureComparer;
        MenuItem _MenuItem_ViewREDVisualizer;
        MenuItem _MenuItem_ViewNavMeshBaker;
        MenuItem _MenuItem_ViewMSAPreview;
        MenuItem _MenuItem_ViewMEPreview;
        MenuItem _MenuItem_ViewNodeGraph;
        MenuItem _MenuItem_ViewStateMachine;
        MenuItem _MenuItem_ViewAnimStoryBoard;
        MenuItem _MenuItem_ViewCurve;
        MenuItem _MenuItem_ViewLienarColorCurve;
        MenuItem _MenuItem_ViewTimeline;
        MenuItem _MenuItem_ViewAnimTimeline;
        MenuItem _MenuItem_ViewWorldPartition;
        MenuItem _MenuItem_ViewCinematic;
        MenuItem _MenuItem_Tasks;
        MenuItem _MenuItem_ResetLayout;

        Menu _Menu_Options;
        MenuItem _MenuItem_OptionsScreenShotMode;
        MenuItem _MenuItem_OptionsOpenConfigDirectory;
        MenuItem _MenuItem_OptionsShowEditorEntities;
        MenuItem _MenuItem_OptionsShowAverageSceneLuminance;
        Menu _Menu_OptionsAverage;
        MenuItem _MenuItem_OptionsAverage;
        MenuItem _MenuItem_OptionsAverageLatest1Frames;
        MenuItem _MenuItem_OptionsAverageLatest20Frames;
        MenuItem _MenuItem_OptionsAverageLatest60Frames;
        MenuItem _MenuItem_OptionsConfig;

        Menu _Menu_Help;
        MenuItem _MenuItem_HelpTest;
        MenuItem _MenuItem_HelpAbout;

        public static GameMainMenuUI GetInstance()
        {
            return _Instance;
        }

        public UIManager GetUIManager()
        {
            if (_Menu_File != null)
            {
                return _Menu_File.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public bool Initialize(UIManager UIManager)
        {
            Control Root = UIManager.GetRoot();
            MainUI MainUI = MainUI.GetInstance();
            ShortcutConfig ShortcutConfig = ShortcutConfig.GetInstance();

            _TextureCheckedMark = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");

            _MenuItem_FileProjects = new MenuItem();
            _MenuItem_FileProjects.SetText("Projects...");
            _MenuItem_FileProjects.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemFileProjectsClicked(Sender); };

            _MenuItem_FileNewScene = new MenuItem();
            _MenuItem_FileNewScene.SetText("New Scene");
            _MenuItem_FileNewScene.SetShortcutKey(ShortcutConfig.GetShortcutString("FileNew"));
            _MenuItem_FileNewScene.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemFileNewSceneClicked(Sender); };

            _MenuItem_FileOpenScene = new MenuItem();
            _MenuItem_FileOpenScene.SetText("Open Scene...");
            _MenuItem_FileOpenScene.SetShortcutKey(ShortcutConfig.GetShortcutString("FileOpen"));
            _MenuItem_FileOpenScene.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemFileOpenSceneClicked(Sender); };

            _MenuItem_FileSave = new MenuItem();
            _MenuItem_FileSave.SetText("Save");
            _MenuItem_FileSave.SetShortcutKey(ShortcutConfig.GetShortcutString("FileSave"));
            _MenuItem_FileSave.SetImage(UIManager.LoadUIImage("Editor/Icons/File/Save.png"));
            _MenuItem_FileSave.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemFileSaveClicked(Sender); };

            _MenuItem_FileSaveAs = new MenuItem();
            _MenuItem_FileSaveAs.SetText("Save As");
            _MenuItem_FileSaveAs.SetShortcutKey(ShortcutConfig.GetShortcutString("FileSaveAs"));
            _MenuItem_FileSaveAs.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemFileSaveAsClicked(Sender); };


            _MenuItem_FileExit = new MenuItem();
            _MenuItem_FileExit.SetText("Exit");
            _MenuItem_FileExit.SetShortcutKey(ShortcutConfig.GetShortcutString("FileExit"));
            _MenuItem_FileExit.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemFileExitClicked(Sender); };

            _Menu_File = new Menu(UIManager);
            _Menu_File.Initialize();
            _Menu_File.SetText("File");
            _Menu_File.AddMenuItem(_MenuItem_FileProjects);
            _Menu_File.AddSeperator();
            _Menu_File.AddMenuItem(_MenuItem_FileNewScene);
            _Menu_File.AddMenuItem(_MenuItem_FileOpenScene);
            _Menu_File.AddSeperator();
            _Menu_File.AddMenuItem(_MenuItem_FileSave);
            _Menu_File.AddMenuItem(_MenuItem_FileSaveAs);
            //_Menu_File.AddMenuItem(_MenuItem_FileAutoSave);
            _Menu_File.AddSeperator();
            _Menu_File.AddSeperator();
            _Menu_File.AddMenuItem(_MenuItem_FileExit);

            _MenuItem_EditUndo = new MenuItem();
            _MenuItem_EditUndo.SetText("Undo");
            _MenuItem_EditUndo.SetShortcutKey(ShortcutConfig.GetShortcutString("EditUndo"));
            _MenuItem_EditUndo.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/Undo.png"));
            _MenuItem_EditUndo.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditUndoClicked(Sender); };

            _MenuItem_EditRedo = new MenuItem();
            _MenuItem_EditRedo.SetText("Redo");
            _MenuItem_EditRedo.SetShortcutKey(ShortcutConfig.GetShortcutString("EditRedo"));
            _MenuItem_EditRedo.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/Redo.png"));
            _MenuItem_EditRedo.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditRedoClicked(Sender); };

            _MenuItem_EditCopy = new MenuItem();
            _MenuItem_EditCopy.SetText("Copy");
            _MenuItem_EditCopy.SetShortcutKey(ShortcutConfig.GetShortcutString("EditCopy"));
            _MenuItem_EditCopy.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/Copy.png"));
            _MenuItem_EditCopy.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditCopyClicked(Sender); };

            _MenuItem_EditPaste = new MenuItem();
            _MenuItem_EditPaste.SetText("Paste");
            _MenuItem_EditPaste.SetShortcutKey(ShortcutConfig.GetShortcutString("EditPaste"));
            _MenuItem_EditPaste.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/Paste.png"));
            _MenuItem_EditPaste.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditPasteClicked(Sender); };

            _MenuItem_EditDelete = new MenuItem();
            _MenuItem_EditDelete.SetText("Delete");
            _MenuItem_EditDelete.SetShortcutKey(ShortcutConfig.GetShortcutString("EditDelete"));
            _MenuItem_EditDelete.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/Delete.png"));
            _MenuItem_EditDelete.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditDeleteClicked(Sender); };

            _MenuItem_EditDuplicate = new MenuItem();
            _MenuItem_EditDuplicate.SetText("Duplicate");
            _MenuItem_EditDuplicate.SetShortcutKey(ShortcutConfig.GetShortcutString("EditDuplicate"));
            _MenuItem_EditDuplicate.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditDuplicateClicked(Sender); };

            _MenuItem_EditorConfig = new MenuItem();
            _MenuItem_EditorConfig.SetText("Config...");
            _MenuItem_EditorConfig.SetShortcutKey(ShortcutConfig.GetShortcutString("EditConfig"));
            _MenuItem_EditorConfig.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemEditConfigClicked(Sender); };

            _Menu_Edit = new Menu(GetUIManager());
            _Menu_Edit.Initialize();
            _Menu_Edit.SetText("Edit");
            _Menu_Edit.AddMenuItem(_MenuItem_EditUndo);
            _Menu_Edit.AddMenuItem(_MenuItem_EditRedo);
            _Menu_Edit.AddSeperator();
            _Menu_Edit.AddMenuItem(_MenuItem_EditCopy);
            _Menu_Edit.AddMenuItem(_MenuItem_EditPaste);
            _Menu_Edit.AddSeperator();
            _Menu_Edit.AddMenuItem(_MenuItem_EditDelete);
            _Menu_Edit.AddSeperator();
            _Menu_Edit.AddMenuItem(_MenuItem_EditDuplicate);
            _Menu_Edit.AddSeperator();
            _Menu_Edit.AddMenuItem(_MenuItem_EditorConfig);

            _Menu_AssetsCreate = new Menu(GetUIManager());
            _Menu_AssetsCreate.Initialize();
            _MenuItem_AssetsCreate = new MenuItem();
            _MenuItem_AssetsCreate.SetText("Create");
            _MenuItem_AssetsCreate.SetMenu(_Menu_AssetsCreate);
            ProjectUI.GetInstance().BulidCreateMenuItems(_Menu_AssetsCreate);

            _MenuItem_AssetsImportAsset = new MenuItem();
            _MenuItem_AssetsImportAsset.SetText("Import Asset...");
            _MenuItem_AssetsImportAsset.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemAssetsImportAssetClicked(Sender); };

            _MenuItem_AssetsImportAssets = new MenuItem();
            _MenuItem_AssetsImportAssets.SetText("Import Assets...");
            _MenuItem_AssetsImportAssets.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemAssetsImportAssetsClicked(Sender); };

            _MenuItem_AssetsBatchCluster = new MenuItem();
            _MenuItem_AssetsBatchCluster.SetText("Batch Cluster...");
            _MenuItem_AssetsBatchCluster.ClickedEvent += (MenuItem Sender) => { OnMenuItemAssetsBatchCluterClicked(Sender); };

            Menu Menu_RecompileShaders = new Menu(GetUIManager());
            Menu_RecompileShaders.Initialize();

            MenuItem MenuItem_RecompileShader = new MenuItem();
            MenuItem_RecompileShader.SetText("Recompile Shaders");
            MenuItem_RecompileShader.SetMenu(Menu_RecompileShaders);

            MenuItem _MenuItem_AssetsImportFFSShaders = new MenuItem();
            _MenuItem_AssetsImportFFSShaders.SetText("Recompile All Pipeline Shaders");
            _MenuItem_AssetsImportFFSShaders.ClickedEvent += (MenuItem Sender) =>
            {
                // Get ShaderPathes
                string FFSShaderDirectoryPath = EditorUtilities.GetResourceDirectory() + "/PipelineResource/FFSRP/Shader/";
                MainUI.GetInstance().OnMenuItemAssetsRecompileShadersClicked(Sender, FFSShaderDirectoryPath);
            };


            MenuItem _MenuItem_AssetsImportProjectShaders = new MenuItem();
            _MenuItem_AssetsImportProjectShaders.SetText("Recompile All Project Shaders");
            _MenuItem_AssetsImportProjectShaders.ClickedEvent += (MenuItem Sender) =>
            {
                // Get ShaderPathes
                string ProjectDirectoryPath = MainUI.GetInstance().GetProjectDirectory();
                MainUI.GetInstance().OnMenuItemAssetsRecompileShadersClicked(Sender, ProjectDirectoryPath);
            };

            Menu_RecompileShaders.AddMenuItem(_MenuItem_AssetsImportFFSShaders);
            Menu_RecompileShaders.AddMenuItem(_MenuItem_AssetsImportProjectShaders);

            MenuItem MenuItem_RegenerateFx = new MenuItem();
            MenuItem_RegenerateFx.SetText("Regenerate Fx");
            MenuItem_RegenerateFx.ClickedEvent += (MenuItem Sender) =>
            {
                string FFSShaderDirectoryPath = EditorUtilities.GetResourceDirectory() + "/PipelineResource/FFSRP/";
                string ProjectDirectoryPath = MainUI.GetInstance().GetProjectDirectory();

                MainUI.GetInstance().OnMenuItemAssetsRegenerateFxClicked(Sender, new List<string> { FFSShaderDirectoryPath, ProjectDirectoryPath });
            };

            _Menu_Assets = new Menu(GetUIManager());
            _Menu_Assets.Initialize();
            _Menu_Assets.SetText("Assets");
            _Menu_Assets.AddMenuItem(_MenuItem_AssetsCreate);
            _Menu_Assets.AddMenuItem(_MenuItem_AssetsImportAsset);
            _Menu_Assets.AddMenuItem(_MenuItem_AssetsImportAssets);
            //_Menu_Assets.AddMenuItem(_MenuItem_AssetsBatchCluster);
            _Menu_Assets.AddMenuItem(MenuItem_RecompileShader);
            _Menu_Assets.AddMenuItem(MenuItem_RegenerateFx);

            _Menu_Entity = new Menu(GetUIManager());
            _Menu_Entity.Initialize();
            _Menu_Entity.SetText("Entity");
            HierarchyUI.GetInstance().BulidCreateEntityMenu(_Menu_Entity, "MainMenu");

            _Menu_Components = new Menu(GetUIManager());
            _Menu_Components.Initialize();
            _Menu_Components.SetText("Components");
            bool bMainMenu = true;

            _AddComponentMenu = Inspector_Entity.BuildAddComponentMenu(_Menu_Components, bMainMenu);

            _MenuItem_GenerateGenerateFoliage = new MenuItem();
            _MenuItem_GenerateGenerateFoliage.SetText("Generate Foliage");
            _MenuItem_GenerateGenerateFoliage.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemGenerateGenerateFoliageClicked(Sender); };

            _MenuItem_GenerateGenerateMSDF = new MenuItem();
            _MenuItem_GenerateGenerateMSDF.SetText("Generate MSDF");
            _MenuItem_GenerateGenerateMSDF.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemGenerateGenerateMSDFClicked(Sender); };

            _Menu_Generate = new Menu(GetUIManager());
            _Menu_Generate.Initialize();
            _Menu_Generate.SetText("Generate");
            _Menu_Generate.AddMenuItem(_MenuItem_GenerateGenerateFoliage);

            _Menu_Generate.AddMenuItem(_MenuItem_GenerateGenerateMSDF);


            _MenuItem_DebugPlayGameWithProjectConfig = new MenuItem();
            _MenuItem_DebugPlayGameWithProjectConfig.SetText("Play Game With Project Config");
            _MenuItem_DebugPlayGameWithProjectConfig.SetImage(UIManager.LoadUIImage("Editor/Icons/Debug/RunGame.png"));
            _MenuItem_DebugPlayGameWithProjectConfig.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemDebugPlayGameWithProjectConfigClicked(Sender); };

            _MenuItem_DebugPlayGame = new MenuItem();
            _MenuItem_DebugPlayGame.SetText("Play Game With Current Scene");
            _MenuItem_DebugPlayGame.SetImage(UIManager.LoadUIImage("Editor/Icons/Debug/RunGame.png"));
            _MenuItem_DebugPlayGame.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemDebugPlayGameClicked(Sender); };

            _MenuItem_DebugPlayCurrentScene = new MenuItem();
            _MenuItem_DebugPlayCurrentScene.SetText("Play Current Scene");
            _MenuItem_DebugPlayCurrentScene.SetImage(UIManager.LoadUIImage("Editor/Icons/Debug/PreviewScene.png"));
            _MenuItem_DebugPlayCurrentScene.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemDebugPlayCurrentSceneClicked(Sender); };

            _MenuItem_BuildResourceList = new MenuItem();
            _MenuItem_BuildResourceList.SetText("Build ResourceList");
            _MenuItem_BuildResourceList.SetShortcutKey(ShortcutConfig.GetShortcutString("Build Resource List"));
            _MenuItem_BuildResourceList.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemUpdateResourceClicked(Sender); };

            _MenuItem_BuildResourceList = new MenuItem();
            _MenuItem_BuildResourceList.SetText("Test SuperResolution");
            _MenuItem_BuildResourceList.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemTestSuperResolutionClicked(Sender); };

            _MenuItem_ViewImGuiConsole = new MenuItem();
            _MenuItem_ViewImGuiConsole.SetText("ImGui Console");
            _MenuItem_ViewImGuiConsole.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemImGuiConsoleClicked(Sender); };

            _Menu_Debug = new Menu(GetUIManager());
            _Menu_Debug.Initialize();
            _Menu_Debug.SetText("Debug");
            _Menu_Debug.AddMenuItem(_MenuItem_DebugPlayGameWithProjectConfig);
            _Menu_Debug.AddMenuItem(_MenuItem_DebugPlayGame);
            _Menu_Debug.AddMenuItem(_MenuItem_DebugPlayCurrentScene);
            _Menu_Debug.AddMenuItem(_MenuItem_BuildResourceList);
            _Menu_Debug.AddMenuItem(_MenuItem_ViewImGuiConsole);

            _MenuItem_BuildProject = new MenuItem();
            _MenuItem_BuildProject.SetText("Build Project");
            _MenuItem_BuildProject.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemBuildProjectClicked(Sender); };

            _Menu_Build = new Menu(GetUIManager());
            _Menu_Build.Initialize();
            _Menu_Build.SetText("Build");
            _Menu_Build.AddMenuItem(_MenuItem_BuildProject);

            _MenuItem_ViewProject = new MenuItem();
            _MenuItem_ViewProject.SetText("Resource");
            _MenuItem_ViewProject.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewProjectClicked(Sender); };

            _MenuItem_ViewHierarchy = new MenuItem();
            _MenuItem_ViewHierarchy.SetText("Entities");
            _MenuItem_ViewHierarchy.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewHierarchyClicked(Sender); };

            _MenuItem_ViewInspector = new MenuItem();
            _MenuItem_ViewInspector.SetText("Properties");
            _MenuItem_ViewInspector.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewInspectorClicked(Sender); };

            _MenuItem_ViewResourceInspector = new MenuItem();
            _MenuItem_ViewResourceInspector.SetText("ResourceProperties");
            _MenuItem_ViewResourceInspector.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewResourceInspectorClicked(Sender); };

            _MenuItem_ViewTerrain = new MenuItem();
            _MenuItem_ViewTerrain.SetText("Terrain");
            _MenuItem_ViewTerrain.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewTerrainClicked(Sender); };

            _MenuItem_ViewTod = new MenuItem();
            _MenuItem_ViewTod.SetText("TOD");
            _MenuItem_ViewTod.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewTodClicked(Sender); };

            _MenuItem_ViewConsole = new MenuItem();
            _MenuItem_ViewConsole.SetText("Output");
            _MenuItem_ViewConsole.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewConsoleClicked(Sender); };

            _MenuItem_ViewWelcome = new MenuItem();
            _MenuItem_ViewWelcome.SetText("Welcome");
            _MenuItem_ViewWelcome.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewWelcomeClicked(Sender); };

            _MenuItem_ViewFxView = new MenuItem();
            _MenuItem_ViewFxView.SetText("FxEditor");
            _MenuItem_ViewFxView.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewFxViewClicked(Sender); };

            _MenuItem_ViewScene = new MenuItem();
            _MenuItem_ViewScene.SetText("Scene");
            _MenuItem_ViewScene.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewSceneClicked(Sender); };

            _MenuItem_ViewGame = new MenuItem();
            _MenuItem_ViewGame.SetText("Game");
            _MenuItem_ViewGame.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewGameClicked(Sender); };

            _MenuItem_ViewCaptureComparer = new MenuItem();
            _MenuItem_ViewCaptureComparer.SetText("Capture");
            _MenuItem_ViewCaptureComparer.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewCaptureComparerClicked(Sender); };


            _MenuItem_ViewREDVisualizer = new MenuItem();
            _MenuItem_ViewREDVisualizer.SetText(nameof(REDVisualizer));
            _MenuItem_ViewREDVisualizer.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewREDVisualizerClicked(Sender); };


            _MenuItem_ViewNavMeshBaker = new MenuItem();
            _MenuItem_ViewNavMeshBaker.SetText("NavMeshBaker");
            _MenuItem_ViewNavMeshBaker.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewNavMeshBakerClicked(Sender); };

            _MenuItem_ViewMSAPreview = new MenuItem();
            _MenuItem_ViewMSAPreview.SetText("MSA Preview");
            _MenuItem_ViewMSAPreview.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewMSAPreivewClicked(Sender); };

            _MenuItem_ViewMEPreview = new MenuItem();
            _MenuItem_ViewMEPreview.SetText("Mesh Edit Preview");
            _MenuItem_ViewMEPreview.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewMEPreviewClicked(Sender); };

            _MenuItem_ViewNodeGraph = new MenuItem();
            _MenuItem_ViewNodeGraph.SetText("Node Graph");
            _MenuItem_ViewNodeGraph.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewNodeGraphClicked(Sender); };

            _MenuItem_ViewStateMachine = new MenuItem();
            _MenuItem_ViewStateMachine.SetText("State Machine");
            _MenuItem_ViewStateMachine.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewStateMachineClicked(Sender); };

            _MenuItem_ViewAnimStoryBoard = new MenuItem();
            _MenuItem_ViewAnimStoryBoard.SetText("AnimStoryBoard");
            _MenuItem_ViewAnimStoryBoard.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewAnimatorClicked(Sender); };

            _MenuItem_ViewCurve = new MenuItem();
            _MenuItem_ViewCurve.SetText("Curve");
            _MenuItem_ViewCurve.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewCurveClicked(Sender); };

            _MenuItem_ViewLienarColorCurve = new MenuItem();
            _MenuItem_ViewLienarColorCurve.SetText("LienarColor Curve");
            _MenuItem_ViewLienarColorCurve.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewLinearColorCurveClicked(Sender); };

            _MenuItem_ViewTimeline = new MenuItem();
            _MenuItem_ViewTimeline.SetText("Timeline");
            _MenuItem_ViewTimeline.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewTimelineClicked(Sender); };

            _MenuItem_ViewAnimTimeline = new MenuItem();
            _MenuItem_ViewAnimTimeline.SetText("Anim Timeline");
            _MenuItem_ViewAnimTimeline.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewAnimTimelineClicked(Sender); };

            _MenuItem_ViewCinematic = new MenuItem();
            _MenuItem_ViewCinematic.SetText("Cinematic");
            _MenuItem_ViewCinematic.ClickedEvent += (MenuItem Sender) => { OnMenuItemCinematic(Sender);/*MainUI.GetInstance().OnMenuItemViewCinematicClicked(Sender);*/ };

            _MenuItem_ViewWorldPartition = new MenuItem();
            _MenuItem_ViewWorldPartition.SetText("World Partition");
            _MenuItem_ViewWorldPartition.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemViewWorldPartition(Sender); };

            _MenuItem_Tasks = new MenuItem();
            _MenuItem_Tasks.SetText("Tasks");
            _MenuItem_Tasks.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemTasksClicked(Sender); };



            _Menu_View = new Menu(GetUIManager());
            _Menu_View.Initialize();
            _Menu_View.SetText("View");
            _Menu_View.AddMenuItem(_MenuItem_ViewProject);
            _Menu_View.AddSeperator();
            _Menu_View.AddMenuItem(_MenuItem_ViewHierarchy);
            _Menu_View.AddMenuItem(_MenuItem_ViewInspector);
            _Menu_View.AddMenuItem(_MenuItem_ViewResourceInspector);
            _Menu_View.AddMenuItem(_MenuItem_ViewTerrain);
            _Menu_View.AddMenuItem(_MenuItem_ViewTod);
            _Menu_View.AddMenuItem(_MenuItem_ViewREDVisualizer);
            _Menu_View.AddMenuItem(_MenuItem_ViewNavMeshBaker);
            _Menu_View.AddSeperator();
            _Menu_View.AddMenuItem(_MenuItem_ViewConsole);
            _Menu_View.AddSeperator();
            _Menu_View.AddMenuItem(_MenuItem_ViewWelcome);
            _Menu_View.AddMenuItem(_MenuItem_ViewFxView);
            _Menu_View.AddMenuItem(_MenuItem_ViewScene);
            _Menu_View.AddMenuItem(_MenuItem_ViewGame);
            _Menu_View.AddMenuItem(_MenuItem_ViewCaptureComparer);
            _Menu_View.AddSeperator();
            _Menu_View.AddMenuItem(_MenuItem_ViewMSAPreview);
            _Menu_View.AddMenuItem(_MenuItem_ViewMEPreview);
            _Menu_View.AddMenuItem(_MenuItem_ViewNodeGraph);
            _Menu_View.AddMenuItem(_MenuItem_ViewStateMachine);
            _Menu_View.AddMenuItem(_MenuItem_ViewAnimStoryBoard);
            _Menu_View.AddMenuItem(_MenuItem_ViewCurve);
            _Menu_View.AddMenuItem(_MenuItem_ViewLienarColorCurve);
            _Menu_View.AddMenuItem(_MenuItem_ViewTimeline);
            _Menu_View.AddMenuItem(_MenuItem_ViewAnimTimeline);
            _Menu_View.AddMenuItem(_MenuItem_ViewCinematic);
            _Menu_View.AddMenuItem(_MenuItem_ViewWorldPartition);
            _Menu_View.AddMenuItem(_MenuItem_Tasks);



            // add reflected view items;
            foreach (var item in CrossEngine.GetInstance().ViewDockingItems)
            {
                var type = item.Key;
                if (item.Value.ShowOrder > 0)
                {
                    var StaticGetInstanceMethod = type.GetMethod("GetInstance", BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy);
                    var instance = StaticGetInstanceMethod.Invoke(null, null);
                    var GetMenuItemMethod = type.GetMethod("GetMenuItem", BindingFlags.Public | BindingFlags.FlattenHierarchy | BindingFlags.Instance);
                    _Menu_View.AddMenuItem(GetMenuItemMethod.Invoke(instance, null) as MenuItem);
                }
            }


            _MenuItem_OptionsScreenShotMode = new MenuItem();
            _MenuItem_OptionsScreenShotMode.SetText("Screenshot Mode");
            _MenuItem_OptionsScreenShotMode.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemOptionsScreenShotModeClicked(Sender); };

            _MenuItem_OptionsOpenConfigDirectory = new MenuItem();
            _MenuItem_OptionsOpenConfigDirectory.SetText("Open Config Directory...");
            _MenuItem_OptionsOpenConfigDirectory.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemOptionsOpenConfigDirectoryClicked(Sender); };

            _MenuItem_OptionsShowEditorEntities = new MenuItem();
            _MenuItem_OptionsShowEditorEntities.SetText("Switch Editor Entities showing in Scene");
            _MenuItem_OptionsShowEditorEntities.ClickedEvent += (MenuItem Sender) => { HierarchyUI.GetInstance().OnMenuItemShowEditorEntitiesClicked(Sender); };

            _MenuItem_OptionsShowAverageSceneLuminance = new MenuItem();
            _MenuItem_OptionsShowAverageSceneLuminance.SetText("Show Average Scene Luminance");
            _MenuItem_OptionsShowAverageSceneLuminance.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemShowAverageSceneLuminanceClicked(Sender); };

            _MenuItem_OptionsAverageLatest1Frames = new MenuItem();
            _MenuItem_OptionsAverageLatest1Frames.SetText("Latest 1 Frames");
            _MenuItem_OptionsAverageLatest1Frames.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemOptionsAverageCount1Clicked(Sender); };

            _MenuItem_OptionsAverageLatest20Frames = new MenuItem();
            _MenuItem_OptionsAverageLatest20Frames.SetText("Latest 20 Frames");
            _MenuItem_OptionsAverageLatest20Frames.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemOptionsAverageCount20Clicked(Sender); };

            _MenuItem_OptionsAverageLatest60Frames = new MenuItem();
            _MenuItem_OptionsAverageLatest60Frames.SetText("Latest 60 Frames");
            _MenuItem_OptionsAverageLatest60Frames.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemOptionsAverageCount60Clicked(Sender); };

            _Menu_OptionsAverage = new Menu(GetUIManager());
            _Menu_OptionsAverage.Initialize();
            _MenuItem_OptionsAverage = new MenuItem();
            _MenuItem_OptionsAverage.SetText("Average");
            _MenuItem_OptionsAverage.SetMenu(_Menu_OptionsAverage);

            _Menu_OptionsAverage.AddMenuItem(_MenuItem_OptionsAverageLatest1Frames);
            _Menu_OptionsAverage.AddMenuItem(_MenuItem_OptionsAverageLatest20Frames);
            _Menu_OptionsAverage.AddMenuItem(_MenuItem_OptionsAverageLatest60Frames);

            _MenuItem_OptionsConfig = new MenuItem();
            _MenuItem_OptionsConfig.SetText("Config...");
            _MenuItem_OptionsConfig.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemOptionsConfigClicked(Sender); };

            _MenuItem_ResetLayout = new MenuItem();
            _MenuItem_ResetLayout.SetText("Reset Layout");
            _MenuItem_ResetLayout.ClickedEvent += (MenuItem sender) => { UserConfig.GetInstance().OnResetLayout(); };

            _Menu_Options = new Menu(GetUIManager());
            _Menu_Options.Initialize();
            _Menu_Options.SetText("Options");
            _Menu_Options.AddMenuItem(_MenuItem_OptionsScreenShotMode);
            _Menu_Options.AddMenuItem(_MenuItem_OptionsOpenConfigDirectory);
            _Menu_Options.AddMenuItem(_MenuItem_OptionsShowEditorEntities);
            _Menu_Options.AddMenuItem(_MenuItem_OptionsShowAverageSceneLuminance);
            _Menu_Options.AddMenuItem(_MenuItem_OptionsAverage);
            _Menu_Options.AddMenuItem(_MenuItem_OptionsConfig);
            _Menu_Options.AddMenuItem(_MenuItem_ResetLayout);

            _MenuItem_HelpTest = new MenuItem();
            _MenuItem_HelpTest.SetText("Test");
            _MenuItem_HelpTest.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemHelpTestClicked(Sender); };

            _MenuItem_HelpAbout = new MenuItem();
            _MenuItem_HelpAbout.SetText("About...");
            _MenuItem_HelpAbout.ClickedEvent += (MenuItem Sender) => { MainUI.GetInstance().OnMenuItemHelpAboutClicked(Sender); };

            _Menu_Help = new Menu(GetUIManager());
            _Menu_Help.Initialize();
            _Menu_Help.SetText("Help");
            _Menu_Help.AddMenuItem(_MenuItem_HelpTest);
            _Menu_Help.AddSeperator();
            _Menu_Help.AddMenuItem(_MenuItem_HelpAbout);

            _MainMenu = new MainMenu();
            _MainMenu.Initialize();
            _MainMenu.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _MainMenu.UpdateMenuEvent += OnMainMenuUpdateMenu;
            Root.AddChild(_MainMenu);

            _MainMenu.AddMenu(_Menu_File);
            _MainMenu.AddMenu(_Menu_Edit);
            _MainMenu.AddMenu(_Menu_Assets);
            _MainMenu.AddMenu(_Menu_Entity);
            _MainMenu.AddMenu(_Menu_Components);
            _MainMenu.AddMenu(_Menu_Generate);
            _MainMenu.AddMenu(_Menu_Debug);
            _MainMenu.AddMenu(_Menu_Build);
            _MainMenu.AddMenu(_Menu_View);
            _MainMenu.AddMenu(_Menu_Options);
            _MainMenu.AddMenu(_Menu_Help);

            GenerateReflectedToolBarItems();

            return true;
        }


        public void GenerateReflectedToolBarItems()
        {
            List<(string, Type)> toolbarItems = new List<(string, Type)>();
            foreach (var item in CrossEngine.GetInstance().ToolBarItems)
            {

                string DisplayUINames = item.Value.DisplayUINames;
                if (item.Value.UseDynamicUINames)
                {
                    var toolbar = (ToolBarMenuItem)Activator.CreateInstance(item.Key);
                    DisplayUINames = toolbar.GetDynamicUINames();
                }

                if (item.Value.ShowOrder > 0)
                    toolbarItems.Add((DisplayUINames, item.Key));
            }

            MenuItemUtils.CreateMenu(null, _MainMenu.GetUIManager(), toolbarItems,
                delegate ((List<string>, Type) entry)
                {
                    return (MenuItem)Activator.CreateInstance(entry.Item2);
                },
                delegate (Menu m, int level)
                {
                    if (level == 0)
                    {
                        _MainMenu.AddMenu(m);
                    }
                }
            );
        }

        public void Refresh()
        {
            if (_MainMenu != null)
            {
                Control Root = GetUIManager().GetRoot();
                Root.RemoveChild(_MainMenu);
                Initialize(GetUIManager());
                OnDeviceResize(Root.GetWidth(), Root.GetHeight());
            }
        }

        public void OnDeviceResize(int Width, int Height)
        {
            int TotalWidth = _MainMenu.GetTotalWidth();
            int Widht1 = Math.Min(TotalWidth, Width - 160);
            int MainMenuHeight = _MainMenu.GetHeight();
            _MainMenu.SetPosition(32, 0, Widht1, MainMenuHeight);
        }

        public void OnMenuItemAssetsBatchCluterClicked(MenuItem Sender)
        {
            List<string> fileList = new List<string>();
            string dir = MainUI.GetInstance().GetProjectDirectory() + "/Contents/Mesh/";
            foreach (string newPath in Directory.GetFiles(dir, "*.mesh.nda", SearchOption.AllDirectories))
            {
                if (newPath.Contains("WALK", StringComparison.OrdinalIgnoreCase) || newPath.Contains("focus", StringComparison.OrdinalIgnoreCase) || newPath.Contains("UnderWater", StringComparison.OrdinalIgnoreCase))
                {
                    string file = newPath.Replace(MainUI.GetInstance().GetProjectDirectory() + "/", "");
                    fileList.Add(file);
                }
            }

            for (int i = 0; i < fileList.Count; ++i)
            {
                Resource res = Resource.Get(fileList[i]);
                MeshAssetDataResource _Mesh = (MeshAssetDataResource)res;
                if (!_Mesh.HasCluster())
                {
                    _Mesh.MeshDataInfo.EnableVirtualGeometry = true;
                    CEResource.GenerateMeshClusters(_Mesh.ResourcePtr.GetPointer(), _Mesh.MeshDataInfo);
                    _Mesh.Save();
                }
                else
                {
                    if (_Mesh.MeshDataInfo.LodGroups.Count > 2)
                    {
                        _Mesh.MeshDataInfo.LodGroups[0].ScreenHeight = 0.5f;
                        _Mesh.MeshDataInfo.LodGroups[1].ScreenHeight = 0.3f;
                        CEResource.UpdateMeshLodSetting(_Mesh.ResourcePtr.GetPointer(), _Mesh.MeshDataInfo);
                        _Mesh.Save();
                    }
                }
                Console.WriteLine(String.Format("{0}/{1} - {2}.", i + 1, fileList.Count, fileList[i]));
            }
        }

        void OnMainMenuUpdateMenu(MainMenu Sender)
        {
            //_MenuItem_FileAutoSaveNoAutoSave.SetImage(null);
            //_MenuItem_FileAutoSaveAutoSaveIn1Minute.SetImage(null);
            //_MenuItem_FileAutoSaveAutoSaveIn5Minutes.SetImage(null);
            //_MenuItem_FileAutoSaveAutoSaveIn10Minutes.SetImage(null);
            //_MenuItem_FileAutoSaveAutoSaveIn30Minutes.SetImage(null);

            //int AutoSaveMinutes = EditorConfig.GetInstance().AutoSaveMinutes;
            //if (AutoSaveMinutes == 1)
            //{
            //    _MenuItem_FileAutoSaveAutoSaveIn1Minute.SetImage(_TextureCheckedMark);
            //}
            //else if (AutoSaveMinutes == 5)
            //{
            //    _MenuItem_FileAutoSaveAutoSaveIn5Minutes.SetImage(_TextureCheckedMark);
            //}
            //else if (AutoSaveMinutes == 10)
            //{
            //    _MenuItem_FileAutoSaveAutoSaveIn10Minutes.SetImage(_TextureCheckedMark);
            //}
            //else if (AutoSaveMinutes == 30)
            //{
            //    _MenuItem_FileAutoSaveAutoSaveIn30Minutes.SetImage(_TextureCheckedMark);
            //}
            //else
            //{
            //    _MenuItem_FileAutoSaveNoAutoSave.SetImage(_TextureCheckedMark);
            //}

            bool bCanUndo = EditOperationManager.GetInstance().CanUndo();
            bool bCanRedo = EditOperationManager.GetInstance().CanRedo();
            _MenuItem_EditUndo.SetEnable(bCanUndo);
            _MenuItem_EditRedo.SetEnable(bCanRedo);

            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            bool bCopyEnable = HierarchyUI.IsMenuItemCopyEnable();
            bool bPasteEnable = HierarchyUI.IsMenuItemPasteEnable();
            bool bDuplicateEnable = HierarchyUI.IsMenuItemDuplicateEnable();
            bool bDeleteEnable = HierarchyUI.IsMenuItemDeleteEnable();

            _MenuItem_EditCopy.SetEnable(bCopyEnable);
            _MenuItem_EditPaste.SetEnable(bPasteEnable);
            _MenuItem_EditDelete.SetEnable(bDeleteEnable);
            _MenuItem_EditDuplicate.SetEnable(bDuplicateEnable);

            Inspector_Entity.UpdateAddComponentMenu(_AddComponentMenu);

            _MenuItem_OptionsAverageLatest1Frames.SetImage(null);
            _MenuItem_OptionsAverageLatest20Frames.SetImage(null);
            _MenuItem_OptionsAverageLatest60Frames.SetImage(null);
            int AverageFrames = EditorConfig.GetInstance().AverageFrames;
            if (AverageFrames == 1)
            {
                _MenuItem_OptionsAverageLatest1Frames.SetImage(_TextureCheckedMark);
            }
            else if (AverageFrames == 20)
            {
                _MenuItem_OptionsAverageLatest20Frames.SetImage(_TextureCheckedMark);
            }
            else if (AverageFrames == 60)
            {
                _MenuItem_OptionsAverageLatest60Frames.SetImage(_TextureCheckedMark);
            }

            EditorScene EditorScene = EditorScene.GetInstance();
            bool bScreenShotMode = EditorScene.GetScreenShotMode();
            if (bScreenShotMode)
            {
                _MenuItem_OptionsScreenShotMode.SetImage(_TextureCheckedMark);
            }
            else
            {
                _MenuItem_OptionsScreenShotMode.SetImage(null);
            }
        }

        private void OnMenuItemCinematic(MenuItem Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Curve Files";
            PathInputUIFilterItem.Extensions.Add("nda");

            bool bContentsOnly = true;

            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "Save Curve File As", PathInputUIType.SaveFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                var CurveControllerRes = Clicross.ControllableUnitSystemG.EditorCreateCurveControllerRes(EditorScene.GetInstance().GetWorld()._WorldInterface as Clicross.GameWorld);
                Clicross.ResourceUtil.ResourceSaveToFile(CurveControllerRes, PathInputed);
                CurveControllerRes.Dispose();
                bool bResetScroll = false;
                ProjectUI.GetInstance().RefreshListView(bResetScroll);
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }
    }
}
