using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public delegate void OnPropertyNamedMapItemSelectEvent(string item);
    class Inspector_Property_NamedMap : Inspector_Property
    {
        public enum PropertyNamedMapChangeEvent { Add, Delete, Rename, ItemUpdate }

        public delegate void OnPropertyNamedMapChangedEvent(PropertyNamedMapChangeEvent Event, string key, string other_key);


        public event OnPropertyNamedMapChangedEvent OnPropertyNamedMapChanged = null;

        public event OnPropertyNamedMapItemSelectEvent OnPropertyNamedMapItemSelect = null;

        protected object _Dictionary;

        Type _DictionaryType;
        PropertyInfo _PropertyInfo_Count;
        MethodInfo _MethodInfo_GetMethod;
        MethodInfo _MethodInfo_SetMethod;
        MethodInfo _MethodInfo_EraseMethod;
        MethodInfo _MethodInfo_ContainsMethod;
        MethodInfo _MethodInfo_GetKeys;

        MethodInfo _MethodInfo_SetItemName;

        Type _ItemType;

        Button _ButtonAdd;

        List<Button> _OperationsButtonList;

        string _ItemKey;

        public Inspector_Property_NamedMap()
        {
            _OperationsButtonList = new List<Button>();
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _Dictionary = ObjectProperty.Object;

            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);

            _DictionaryType = _Dictionary.GetType();
            _PropertyInfo_Count = _DictionaryType.GetProperty("Count");
            _MethodInfo_GetMethod = _DictionaryType.GetMethod("get_Item");
            _MethodInfo_SetMethod = _DictionaryType.GetMethods().Single(x => x.Name == "Insert" && x.GetParameters().Length == 2);
            _MethodInfo_EraseMethod = _DictionaryType.GetMethods().Single(x => x.Name == "Erase" && x.GetParameters().Length == 1 && x.GetParameters()[0].ParameterType == typeof(string));
            _MethodInfo_ContainsMethod = _DictionaryType.GetMethod("ContainsKey");
            _MethodInfo_GetKeys = _DictionaryType.GetMethod("Keys");
            _ItemType = _MethodInfo_SetMethod.GetParameters()[1].ParameterType;
            _MethodInfo_SetItemName = _ItemType.GetMethod("SetName");

            if (_PropertyInfoAttribute.bFixedItems == false)
            {
                _ButtonAdd = new Button();
                _ButtonAdd.Initialize();
                _ButtonAdd.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonAdd.SetText("+");
                _ButtonAdd.SetFontSize(12);
                _ButtonAdd.SetTextOffsetY(1);
                _ButtonAdd.ClickedEvent += OnButtonAddClicked;
                Container.AddChild(_ButtonAdd);
            }

            if (OnPropertyNamedMapItemSelect != null)
            {
                AddChildButtons();
            }
            else
            {
                AddChildInspectors();
            }

        }

        protected int GetDictionaryCount(object Dictionary)
        {
            return (int)_PropertyInfo_Count.GetValue(Dictionary);
        }

        object GetDictionaryItem(object Dictionary, object Key)
        {
            return _MethodInfo_GetMethod.Invoke(Dictionary, new object[] { Key });
        }

        bool ContainesDictionaryKey(object Dictionary, object Key)
        {
            return (bool)_MethodInfo_ContainsMethod.Invoke(Dictionary, new object[] { Key });
        }
        void RemoveDictionayItem(object Dictionary, object Key)
        {
            _MethodInfo_EraseMethod.Invoke(Dictionary, new object[] { Key });
        }
        void SetDictionaryItem(object Dictionary, object Key, object Value)
        {
            _MethodInfo_SetMethod.Invoke(Dictionary, new object[] { Key, Value });
        }

        public override void WriteValue()
        {
            base.WriteValue();
            SetPropertyValue(_Dictionary);
        }

        protected void AddChildInspectors()
        {
            ClearChildInspectors();
            foreach (var item in (List<string>)_MethodInfo_GetKeys.Invoke(_Dictionary, null))
            {
                AddItemInspector(item);
            }
        }

        protected void AddChildButtons()
        {
            ClearChildInspectors();
            foreach (var item in (List<string>)_MethodInfo_GetKeys.Invoke(_Dictionary, null))
            {
                AddItemSelectButton(item);
            }
        }
        public Button CreateButtonOperations(Control Container)
        {
            Button ButtonOperations = new Button();
            ButtonOperations.Initialize();
            ButtonOperations.SetFontSize(12);
            ButtonOperations.SetText("v");
            ButtonOperations.SetTextOffsetY(1);
            ButtonOperations.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            ButtonOperations.SetToolTips("Operations");
            ButtonOperations.ClickedEvent += OnButtonOperationsClicked;
            Container.AddChild(ButtonOperations);
            return ButtonOperations;
        }

        protected void AddItemInspector(object Key)
        {
            string ChildPropertyTypeString = _ItemType.ToString();
            if (_PropertyInfoAttribute.ChildPropertyType != "")
            {
                ChildPropertyTypeString = _PropertyInfoAttribute.ChildPropertyType;
            }

            bool bIsEnum = _ItemType.IsEnum;

            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = Key;
            ObjectProperty.Name = Key.ToString();
            ObjectProperty.Type = _ItemType;
            ObjectProperty.PropertyInfoAttribute = _PropertyInfoAttribute;
            ObjectProperty.GetPropertyValueFunction = (Object, PropertyName, ValueExtraProperty) =>
            {
                return GetDictionaryItem(_Dictionary, Object);
            };
            ObjectProperty.SetPropertyValueFunction = (Object, PropertyName, PropertyValue, SubProperty) =>
            {
                SetDictionaryItem(_Dictionary, Object, PropertyValue);
                WriteValue();
                if (OnPropertyNamedMapChanged != null)
                {
                    OnPropertyNamedMapChanged(PropertyNamedMapChangeEvent.ItemUpdate, (string)Key, new string(""));
                }
            };

            Inspector_Property Inspector_Property = (Inspector_Property)InspectorManager.GetInstance().CreatePropertyInspector(ChildPropertyTypeString, bIsEnum);
            Inspector_Property.InspectProperty(ObjectProperty);
            Inspector_Property.DisableToolTips();
            AddChildInspector(Inspector_Property);
            if (_PropertyInfoAttribute.bFixedItems == false)
            {
                Button ButtonOperations = CreateButtonOperations(Inspector_Property.GetValueContainer());
                _OperationsButtonList.Add(ButtonOperations);
            }
        }

        void AddItemSelectButton(string Key)
        {
            var Container = GetValueContainer();

            Button ItemSelectButton = new Button();
            ItemSelectButton.Initialize();
            ItemSelectButton.SetTagString1(Key);
            ItemSelectButton.SetFontSize(12);
            ItemSelectButton.SetText(Key);
            ItemSelectButton.SetTextOffsetY(1);
            ItemSelectButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            ItemSelectButton.ClickedEvent += OnButtonItemSelectClicked;
            Container.AddChild(ItemSelectButton);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
            _LabelName.SetX(_CheckExpand.GetEndX());
            if (_ButtonToolTips != null)
            {
                _ButtonToolTips.SetX(_LabelName.GetX());
            }
            if (_ButtonAdd != null)
            {
                _ButtonAdd.SetPosition(0, SPAN_Y, 20, PROPERTY_FONT_SIZE);
                GetValueContainer().FloatToLeft(_ButtonAdd);
            }



            for (int i = 0; i < _OperationsButtonList.Count; ++i)
            {
                Button ButtonOperations = _OperationsButtonList[i];
                ButtonOperations.SetTagString1(_ChildInspectors[i].GetName());
                int ButtonOperationsY = (ButtonOperations.GetParent().GetHeight() - PROPERTY_FONT_SIZE) / 2;
                ButtonOperations.SetPosition(0, ButtonOperationsY, DEFAULT_HEIGHT, PROPERTY_FONT_SIZE);
                (_ChildInspectors[i] as Inspector_Property).GetValueContainer().FloatToLeft(ButtonOperations);
            }
        }

        public virtual void OnButtonOperationsClicked(Button Sender)
        {
            _ItemKey = Sender.GetTagString1();
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            MenuItem MenuItem_RenameItem = new MenuItem();
            MenuItem_RenameItem.SetText("Rename");
            MenuItem_RenameItem.ClickedEvent += OnMenuRenameClicked;

            MenuContextMenu.AddMenuItem(MenuItem_RenameItem);


            MenuItem MenuItem_DeleteItem = new MenuItem();
            MenuItem_DeleteItem.SetText("Delete");
            MenuItem_DeleteItem.ClickedEvent += OnMenuDeleteClicked;



            MenuContextMenu.AddMenuItem(MenuItem_DeleteItem);


            if (_PropertyInfoAttribute.bFixedItems)
            {
                MenuItem_RenameItem.SetEnable(false);
                MenuItem_DeleteItem.SetEnable(false);
            }
            else
            {
                if (_MethodInfo_SetItemName == null)
                {
                    MenuItem_RenameItem.SetEnable(false);
                }
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }


        void OnButtonItemSelectClicked(Button Sender)
        {
            _ItemKey = Sender.GetTagString1();
            if (OnPropertyNamedMapItemSelect != null)
            {
                OnPropertyNamedMapItemSelect(_ItemKey);
            }
        }


        protected object CloneDict(object Dict)
        {
            Type DictType = Dict.GetType();
            object Dict2 = Activator.CreateInstance(DictType);

            int Count1 = GetDictionaryCount(Dict);
            foreach (var item in (List<string>)_MethodInfo_GetKeys.Invoke(Dict, null))
            {
                SetDictionaryItem(Dict2, item, GetDictionaryItem(Dict, item));
            }
            return Dict2;
        }
        object NewItem(string ItemName)
        {
            if (_ItemType == typeof(string))
            {
                return "";
            }
            else
            {
                var obj = Activator.CreateInstance(_ItemType);
                if (_PropertyInfoAttribute.DefaultValue != null)
                {
                    obj = _PropertyInfoAttribute.DefaultValue;
                }
                _MethodInfo_SetItemName?.Invoke(obj, new object[] { ItemName });
                return obj;
            }
        }

        protected void OnButtonAddClicked(Button Sender)
        {
            Sender.CloseToolTips();
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Create Item", "Please input Item Name:", "");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Item name can not be empty!");
                    return;
                }
                if (ContainesDictionaryKey(_Dictionary, StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated Item name: " + StringInputed);
                    return;
                }
                RecordAndWriteValue(() =>
                {
                    _Dictionary = CloneDict(_Dictionary);
                    var Item = NewItem(StringInputed);

                    SetDictionaryItem(_Dictionary, StringInputed, Item);
                    SetPropertyValue(_Dictionary);
                });
                RefreshInspectorUI();
                if (OnPropertyNamedMapChanged != null)
                {
                    OnPropertyNamedMapChanged(PropertyNamedMapChangeEvent.Add, StringInputed, new string(""));
                }
                GetUIManager().SetFocusControl(null);
            };
            TextInputUI.ShowDialog();

        }

        protected void OnMenuDeleteClicked(MenuItem MenuItem)
        {
            GetUIManager().SetFocusControl(null);
            RecordAndWriteValue(() =>
            {
                _Dictionary = CloneDict(_Dictionary);
                RemoveDictionayItem(_Dictionary, _ItemKey);
                SetPropertyValue(_Dictionary);
            });
            RefreshInspectorUI();
            if (OnPropertyNamedMapChanged != null)
            {
                OnPropertyNamedMapChanged(PropertyNamedMapChangeEvent.Delete, _ItemKey, new string(""));
            }
        }

        protected void OnMenuRenameClicked(MenuItem MenuItem)
        {
            GetUIManager().SetFocusControl(null);
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Rename Item", "Please input a New Name:", _ItemKey);
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                GetUIManager().SetFocusControl(null);
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Item name can not be empty!");
                    return;
                }
                if (ContainesDictionaryKey(_Dictionary, StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated Item name: " + StringInputed);
                    return;
                }
                RecordAndWriteValue(() =>
                {
                    _Dictionary = CloneDict(_Dictionary);
                    var PreValue = GetDictionaryItem(_Dictionary, _ItemKey);
                    RemoveDictionayItem(_Dictionary, _ItemKey);
                    _MethodInfo_SetItemName?.Invoke(PreValue, new object[] { StringInputed });
                    SetDictionaryItem(_Dictionary, StringInputed, PreValue);
                    SetPropertyValue(_Dictionary);
                });
                RefreshInspectorUI();
                if (OnPropertyNamedMapChanged != null)
                {
                    OnPropertyNamedMapChanged(PropertyNamedMapChangeEvent.Rename, StringInputed, _ItemKey);
                }
            };
            TextInputUI.ShowDialog();


        }
        protected void RefreshInspectorUI()
        {
            OperationQueue.GetInstance().AddOperation(() =>
            {
                GetInspectorHandler().InspectObject();
                GetInspectorHandler().UpdateLayout();
            });
        }
    }
}
