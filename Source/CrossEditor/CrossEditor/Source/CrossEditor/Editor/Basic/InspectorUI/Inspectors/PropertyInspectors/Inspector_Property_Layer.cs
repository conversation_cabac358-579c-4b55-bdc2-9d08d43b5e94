using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Layer : Inspector_Property
    {
        protected LayerSetting _LayerSetting;
        protected ComboBoxEx _ComboBoxValue;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);

            _LayerSetting = CrossEngineApi.GetSettingManager().GetLayerSetting();

            Control Container = GetValueContainer();

            _ComboBoxValue = new ComboBoxEx();
            _ComboBoxValue.Initialize();
            _ComboBoxValue.GetValueEdit().SetFontSize(PROPERTY_FONT_SIZE);
            _ComboBoxValue.GetValueEdit().SetTextAlign(TextAlign.CenterLeft);
            _ComboBoxValue.ItemSelectedEvent += (Sender) =>
            {
                GetInspectorHandler().UpdateLayout();
            };

            var LayerInfos = _LayerSetting.GetAllocatedLayerInfos();
            foreach (var LayerInfo in LayerInfos)
            {
                _ComboBoxValue.AddItem(LayerInfo.LayerIndex.ToString() + ": " + LayerInfo.LayerName);
            }
            _ComboBoxValue.ItemSelectedEvent += OnComboBoxValueItemSelected;
            Container.AddChild(_ComboBoxValue);

            _ComboBoxValue.SetEnable(!_ObjectProperty.ReadOnly);

            if (_ObjectProperty.DefaultValue == null && !_ValueExtraProperty._bHasMultipleValues)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        public int GetComboBoxWidth()
        {
            int EditWidth;
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(MULTIPLE_VALUES_STRING);
            }
            else
            {
                int Index = _ComboBoxValue.GetSelectedItemIndex();
                if (Index != -1)
                {
                    EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(_ComboBoxValue.GetItemText(Index));
                }
                else
                {
                    EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast("    ");
                }
            }
            return EditWidth + 30;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int ComboWidth = Math.Max(GetComboBoxWidth(), DEFAULT_WIDTH);
            ComboWidth = Math.Min(ComboWidth, GetValueWidth() - 2 * SPAN_X);

            _ComboBoxValue.SetPosition(0, 2, ComboWidth, 16);
            GetValueContainer().FloatToLeft(_ComboBoxValue);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
            {
                PropertyValueString = MathHelper.NumberToString(PropertyValue);
            }

            uint LayerIndex = uint.Parse(PropertyValueString);

            string LayerName = _LayerSetting.GetLayerInfo(LayerIndex).LayerName;
            string LayerDisplayName = LayerIndex.ToString() + ": " + LayerName;

            _ComboBoxValue.SetSelectedItemByText(LayerDisplayName);
        }

        public override void WriteValue()
        {
            base.WriteValue();

            string ValueString = _ComboBoxValue.GetSelectedItemText();
            ValueString = ValueString.Substring(0, ValueString.IndexOf(':'));

            object NewValue = MathHelper.ParseNumber(ValueString, typeof(uint));

            SetPropertyValue(NewValue);
        }

        void OnComboBoxValueItemSelected(ComboBoxEx Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            RecordAndWriteValue();
        }
    }
}
