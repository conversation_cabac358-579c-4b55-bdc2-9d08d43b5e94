using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_AddPoint : EditOperation
    {
        readonly Dictionary<CurveManager, List<Point>> ModifiedCurves;

        public EditOperation_AddPoint(Dictionary<CurveManager, List<Point>> ModifiedCurves)
        {
            this.ModifiedCurves = ModifiedCurves;
        }

        public override void Redo()
        {
            foreach (var Pair in ModifiedCurves)
            {
                foreach (var Pt in Pair.Value)
                {
                    Pair.Key.AddPoint(Pt);
                }
            }

            CurveEditorUI.GetInstance().SetModified(true, ModifiedCurves.Keys);
        }

        public override void Undo()
        {
            foreach (var Pair in ModifiedCurves)
            {
                foreach (var Pt in Pair.Value)
                {
                    Pair.Key.DeletePoint(Pt);
                }
            }

            CurveEditorUI.GetInstance().SetModified(true, ModifiedCurves.Keys);
        }
    }
}
