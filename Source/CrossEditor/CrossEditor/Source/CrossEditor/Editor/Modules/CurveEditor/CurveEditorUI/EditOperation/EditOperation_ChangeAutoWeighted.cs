using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_ChangeAutoWeighted : EditOperation
    {
        List<SmoothPoint> ModifiedPointList;
        List<bool> OldAttrList;
        List<bool> NewAttrList;

        public EditOperation_ChangeAutoWeighted(List<SmoothPoint> ModifiedPointList, List<bool> OldAttrList, List<bool> NewAttrList)
        {
            this.ModifiedPointList = ModifiedPointList;
            this.OldAttrList = OldAttrList;
            this.NewAttrList = NewAttrList;
        }

        public override void Redo()
        {
            for (int i = 0; i < ModifiedPointList.Count; ++i)
            {
                ModifiedPointList[i].AutoWeighted = NewAttrList[i];
            }

            CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPointList));
        }

        public override void Undo()
        {
            for (int i = 0; i < ModifiedPointList.Count; ++i)
            {
                ModifiedPointList[i].AutoWeighted = OldAttrList[i];
            }

            CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPointList));
        }
    }
}
