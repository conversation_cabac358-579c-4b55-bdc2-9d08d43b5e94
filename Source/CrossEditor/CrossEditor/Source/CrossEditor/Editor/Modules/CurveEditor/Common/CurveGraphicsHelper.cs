using EditorUI;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CrossEditor
{
    public class CurveGraphicsHelper
    {
        public Axis Axis;

        public Control TrackControl;

        public int X, Y, Width, Height;

        public void Initialize(Axis Axis, Control TrackControl)
        {
            this.Axis = Axis;
            this.TrackControl = TrackControl;

            UpdateDrawRegion();
        }

        public void UpdateDrawRegion()
        {
            X = TrackControl.GetScreenX();
            Y = TrackControl.GetScreenY();
            Width = TrackControl.GetWidth();
            Height = TrackControl.GetHeight();
        }

        public bool ClipSegment(ref Vector2f P0, ref Vector2f P1)
        {
            return CurveMathHelper.CohenSutherland.Intersect(P0, P1, new RectangleF(X, Y, Width, Height), out P0, out P1);
        }
        public bool ClipSegment(ref Vector2m P0, ref Vector2m P1)
        {
            return CurveMathHelper.CohenSutherland.Intersect(P0, P1, new RectangleM(X, Y, Width, Height), out P0, out P1);
        }

        public List<Vector2m> GeneratePointsBySegments(List<(Vector2m, Vector2m)> Segments)
        {
            UpdateDrawRegion();
            List<Vector2m> Points = new List<Vector2m>();

            for (int i = 0; i < Segments.Count; ++i)
            {
                var Segment = Segments[i];
                decimal X1 = Segment.Item1.X;
                decimal X2 = Segment.Item2.X;
                decimal StartX = Axis.StartX;
                decimal EndX = Axis.EndX;
                if (X1 < X2 && (X1 >= EndX || X2 <= StartX) ||
                    X1 > X2 && (X1 <= StartX || X2 >= EndX))
                    continue;

                Vector2m P1 = WorldToScreen(Segment.Item1);
                Vector2m P2 = WorldToScreen(Segment.Item2);
                Points.Add(P1);
                Points.Add(P2);
            }

            return Points;
        }

        public void DrawSegment(UIManager UIManager, Color Color, float Width, Vector2m StartPoint, Vector2m EndPoint, float Height = 0)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(StartPoint);
            Vector2f P1 = WorldToScreen(EndPoint);

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawLineF(P0, P1, Width, ref Color);
        }

        public void DrawSegments(UIManager UIManager, Vector2m[] Points, int Count, float Width, Color Color)
        {
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();

            Vector2f[] vector2fArray = new Vector2f[Points.Length];
            Parallel.For(0, Points.Length, i =>
            {
                vector2fArray[i] = new Vector2f(
                    (float)Points[i].X,
                    (float)Points[i].Y
                );
            });
            EditorUICanvas.DrawLinesF(vector2fArray, Count, Width, Color);
        }

        public void DrawHalfLine(UIManager UIManager, Color Color, float Width, Vector2f StartPoint, int Angle)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(StartPoint);
            Vector2f P1;
            switch (Angle)
            {
                case 0:
                    P1 = new Vector2f(Width, P0.Y);
                    break;
                case 180:
                    P1 = new Vector2f(0f, P0.Y);
                    break;
                default:
                    P1 = new Vector2f(Width, P0.Y);
                    break;
            }

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawLineF(P0, P1, Width, ref Color);
        }

        public void DrawRectangle(UIManager UIManager, Color Color, float Width, Vector2f Center, float Size)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(Center);
            Vector2f[] Vertices =
            {
                new Vector2f(P0.X - Size, P0.Y - Size),
                new Vector2f(P0.X - Size, P0.Y + Size),
                new Vector2f(P0.X + Size, P0.Y + Size),
                new Vector2f(P0.X + Size, P0.Y - Size)
            };
            DrawPolygon(UIManager, Color, Width, Vertices);
        }

        public void FillRectangle(UIManager UIManager, Color Color, Vector2f Center, float Size)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(Center);

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillRectangle((int)(P0.X - Size), (int)(P0.Y - Size), (int)Size * 2, (int)Size * 2, ref Color);
        }

        public void DrawDiamond(UIManager UIManager, Color Color, float Width, Vector2f Center, float Size)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(Center);

            Vector2f[] DiamondPoints =
            {
                new Vector2f(P0.X, P0.Y - Size), // Top
                new Vector2f(P0.X + Size, P0.Y), // Right
                new Vector2f(P0.X, P0.Y + Size), // Down
                new Vector2f(P0.X - Size, P0.Y) // Left
            };

            DrawPolygon(UIManager, Color, Width, DiamondPoints);
        }

        public void FillDiamond(UIManager UIManager, Color Color, Vector2f Center, float Size)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(Center);

            Vector2f[] DiamondPoints =
            {
                new Vector2f(P0.X, P0.Y - Size), // Top
                new Vector2f(P0.X + Size, P0.Y), // Right
                new Vector2f(P0.X, P0.Y + Size), // Down
                new Vector2f(P0.X - Size, P0.Y) // Left
            };

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillPolygonF(DiamondPoints, ref Color);
        }

        public void DrawTriangle(UIManager UIManager, Color Color, float Width, Vector2f Center, float Size)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(Center);

            Vector2f[] TrianglePoints =
            {
                new Vector2f(P0.X, P0.Y - Size), // Top
                new Vector2f( (float)( P0.X + Size * Math.Sqrt(3) / 2 ), P0.Y + Size/2.0f), // Right
                new Vector2f( (float)( P0.X - Size * Math.Sqrt(3) / 2 ), P0.Y + Size/2.0f), // Left
            };

            DrawPolygon(UIManager, Color, Width, TrianglePoints);
        }

        public void FillTriangle(UIManager UIManager, Color Color, Vector2f Center, float Size)
        {
            UpdateDrawRegion();

            Vector2f P0 = WorldToScreen(Center);

            Vector2f[] TrianglePoints =
            {
                new Vector2f(P0.X, P0.Y - Size), // Top
                new Vector2f( (float)( P0.X + Size * Math.Sqrt(3) / 2 ), P0.Y + Size/2.0f), // Right
                new Vector2f( (float)( P0.X - Size * Math.Sqrt(3) / 2 ), P0.Y + Size/2.0f), // Left
            };

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillPolygonF(TrianglePoints, ref Color);
        }

        public void DrawSphere(UIManager UIManager, Color Color, float Width, Vector2f Center, float Size)
        {
            Vector2f P0 = WorldToScreen(Center);

            int Segment = 20;
            double Delta = 2f * Math.PI / Segment;
            double Theta = 0;
            List<Vector2f> SpherePoints = new List<Vector2f>();
            for (int i = 0; i < Segment; ++i)
            {
                Vector2f NextPoint = new Vector2f()
                {
                    X = P0.X + (float)Math.Cos(Theta) * Size,
                    Y = P0.Y + (float)Math.Sin(Theta) * Size
                };
                SpherePoints.Add(NextPoint);
                Theta += Delta;
            }
            DrawPolygon(UIManager, Color, Width, SpherePoints.ToArray());
        }

        public void FillSphere(UIManager UIManager, Color Color, Vector2f Center, float Size)
        {
            Vector2f P0 = WorldToScreen(Center);

            int Segment = 500;
            double Delta = 2f * Math.PI / Segment;
            double Theta = 0;
            List<Vector2f> SpherePoints = new List<Vector2f>();
            for (int i = 0; i < Segment; ++i)
            {
                Vector2f NextPoint = new Vector2f()
                {
                    X = P0.X + (float)Math.Cos(Theta) * Size,
                    Y = P0.Y + (float)Math.Sin(Theta) * Size
                };
                SpherePoints.Add(NextPoint);
                Theta += Delta;
            }
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillPolygonF(SpherePoints.ToArray(), ref Color);
        }

        public void DrawPolygon(UIManager UIManager, Color Color, float Width, Vector2m[] Vertices)
        {
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            for (int i = 0; i < Vertices.Length; ++i)
            {
                int j = (i + 1) % Vertices.Length;
                EditorUICanvas.DrawLineF(Vertices[i], Vertices[j], Width, ref Color);
            }
        }
        public void DrawPolygon(UIManager UIManager, Color Color, float Width, Vector2f[] Vertices)
        {
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            for (int i = 0; i < Vertices.Length; ++i)
            {
                int j = (i + 1) % Vertices.Length;
                EditorUICanvas.DrawLineF(Vertices[i], Vertices[j], Width, ref Color);
            }
        }

        public void DrawFloat(UIManager UIManager, float Float, Font Font, Color Color, Vector2f Location)
        {
            UpdateDrawRegion();
            Font.DrawString(UIManager, string.Format("{0:0.####}", Float), ref Color, (int)Math.Round(Location.X), (int)Math.Round(Location.Y), 1.0f);
        }

        public void DrawDecimal(UIManager UIManager, decimal Value, Font Font, Color Color, Vector2f Location)
        {
            UpdateDrawRegion();
            Font.DrawString(UIManager, string.Format("{0:0.######}", Value), ref Color, (int)Math.Round(Location.X), (int)Math.Round(Location.Y), 1.0f);
        }

        public Vector2m WorldToScreen(Vector2m Point)
        {
            return WorldToScreen(Width, Height, Point);
        }

        public Vector2m WorldToScreen(float ScreenWidth, float ScreenHeight, Vector2m Point)
        {
            decimal DistanceX = (decimal)ScreenWidth / Axis.UnitWidth * Axis.UnitX;
            decimal DistanceY = (decimal)ScreenHeight / Axis.UnitHeight * Axis.UnitY;

            Vector2m ScreenPoint = new Vector2m
            {
                X = DistanceX == 0m ? 0m : (Point.X - Axis.StartX) / DistanceX * (decimal)ScreenWidth + X,
                Y = DistanceY == 0m ? 0m : (1 - (Point.Y - Axis.StartY) / DistanceY) * (decimal)ScreenHeight + Y
            };

            return ScreenPoint;
        }

        public Vector2m ScreenToWorld(Vector2m Point)
        {
            return ScreenToWorld(Width, Height, Point);
        }

        public Vector2m ScreenToWorld(float ScreenWidth, float ScreenHeight, Vector2m Point)
        {
            decimal WidthRatio = (Point.X - X) / (decimal)ScreenWidth;
            decimal HeightRatio = 1 - (Point.Y - Y) / (decimal)ScreenHeight;

            Vector2m WorldPoint = new Vector2m
            {
                X = (Axis.EndX - Axis.StartX) * WidthRatio + Axis.StartX,
                Y = (Axis.EndY - Axis.StartY) * HeightRatio + Axis.StartY
            };

            return WorldPoint;
        }

        public decimal GetNearestX(decimal X, decimal UnitX)
        {
            if (UnitX != 0)
                return GetNearest(X, UnitX);
            else
                return GetNearest(X, Axis.UnitX);
        }

        public decimal GetNearestY(decimal Y, decimal UnitY)
        {
            if (UnitY != 0)
                return GetNearest(Y, UnitY);
            else
                return GetNearest(Y, Axis.UnitY);
        }

        public decimal GetNearest(decimal Origin, decimal Unit)
        {
            decimal Prev = Math.Floor(Origin / Unit) * Unit;
            decimal Next = Prev + Unit;

            return Origin - Prev < Next - Origin ? Prev : Next;
        }

        public RectangleF GetBound()
        {
            return new RectangleF(X, Y, Width, Height);
        }
    }
}
