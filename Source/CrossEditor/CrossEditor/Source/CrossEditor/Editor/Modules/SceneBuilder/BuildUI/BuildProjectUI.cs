using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;


namespace CrossEditor
{
    class BuildProjectUI : DialogUI
    {
        static BuildProjectUI _Instance = new BuildProjectUI();

        Panel mMainPanel;
        ScrollView mScrollView;
        Panel mScrollPanel;
        Panel mButtonPanel;
        Button mSaveComfigButton;
        Button mRunButton;
        Panel mProgressPanel;
        ProgressBar mProgressBar;
        Label mProgressLabel;
        Button mCancelButton;

        List<Inspector> mInspectors = new List<Inspector>();

        int mWidth;
        int mHeight;
        InspectorHandler mInspectorHandler;

        public static BuildProjectUI GetInstance()
        {
            return _Instance;
        }

        public BuildProjectUI()
        {
            mWidth = 800;
            mHeight = 500;
            mInspectorHandler = new InspectorHandler();
            mInspectorHandler.InspectObject = UpdateConfig;
            mInspectorHandler.UpdateLayout = UpdateLayout;
            mInspectorHandler.ReadValue = ReadValue;
        }

        public void Initialize(UIManager UIManager)
        {
            base.Initialize(UIManager, "BuildProject", mWidth, mHeight);

            mMainPanel = new Panel();
            mMainPanel.Initialize();
            mMainPanel.SetSize(_PanelDialog.GetWidth(), _PanelDialog.GetHeight());
            _PanelDialog.AddChild(mMainPanel);

            mScrollView = new ScrollView();
            mScrollView.Initialize();
            mScrollView.SetBackgroundColor(Color.FromRGBA(30, 30, 30, 255));
            mScrollView.SetPosition(20, 40, mWidth - 40, mHeight - 130);
            mScrollView.GetHScroll().SetEnable(false);
            mMainPanel.AddChild(mScrollView);
            mScrollPanel = mScrollView.GetScrollPanel();

            mButtonPanel = new Panel();
            mButtonPanel.Initialize();
            mButtonPanel.SetPosition(20, mHeight - 70, mWidth - 40, 50);
            mMainPanel.AddChild(mButtonPanel);

            mSaveComfigButton = new Button();
            mSaveComfigButton.Initialize();
            mSaveComfigButton.SetFontSize(16);
            mSaveComfigButton.SetText("Save Config");
            mSaveComfigButton.SetPosition(20, 10, 100, 30);
            mSaveComfigButton.ClickedEvent += OnSaveButtonClicked;
            mButtonPanel.AddChild(mSaveComfigButton);

            mRunButton = new Button();
            mRunButton.Initialize();
            mRunButton.SetText("Run");
            mRunButton.SetFontSize(16);
            mRunButton.SetPosition(140, 10, 100, 30);
            mRunButton.ClickedEvent += OnRunButtonClicked;
            mButtonPanel.AddChild(mRunButton);

            mProgressPanel = new Panel();
            mProgressPanel.Initialize();
            mProgressPanel.SetPosition(20, mHeight - 70, mWidth - 40, 50);
            mMainPanel.AddChild(mProgressPanel);

            mProgressBar = new ProgressBar();
            mProgressBar.Initialize();
            mProgressBar.SetPosition(0, 20, mWidth - 360, 40);
            mProgressPanel.AddChild(mProgressBar);

            mProgressLabel = new Label();
            mProgressLabel.Initialize();
            mProgressLabel.SetFontSize(16);
            mProgressLabel.SetPosition(mWidth - 340, 20, 200, 40);
            mProgressPanel.AddChild(mProgressLabel);

            mCancelButton = new Button();
            mCancelButton.Initialize();
            mCancelButton.SetText("Cancel");
            mCancelButton.SetFontSize(16);
            mCancelButton.SetPosition(mWidth - 120, 20, 80, 40);
            mCancelButton.ClickedEvent += OnCancelButtonClicked;
            mProgressPanel.AddChild(mCancelButton);
            // update config
            UpdateConfig();
            // add global update
            SceneRuntime.GetInstance().EditorGlobalUpdateEvent += OnEditorGlobalUpdate;
        }

        public void UpdateConfig()
        {
            mInspectors.Clear();
            mScrollPanel.ClearChildren();
            BuildConfig config = EditorConfigManager.GetInstance().GetConfig<BuildConfig>();
            if (config == null) return;
            Type tp = config.GetType();
            PropertyInfo[] properties = tp.GetProperties();
            foreach (PropertyInfo propertyInfo in properties)
            {
                AddPropertyInspector(config, propertyInfo);
            }
            UpdateLayout();
        }

        public void UpdateBuildState()
        {
            bool isBuilding = BuildProject.GetInstance().IsBuilding();
            mButtonPanel.SetVisible(!isBuilding);
            mProgressBar.SetVisible(isBuilding);
        }

        public void UpdateLayout()
        {
            int width = mScrollView.GetWidth();
            int Y = 0;
            foreach (Inspector inspector in mInspectors)
            {
                inspector.UpdateLayout(width, ref Y);
            }
            if (Y > mScrollView.GetHeight())
            {
                width = mScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                Y = 0;
                foreach (Inspector inspector in mInspectors)
                {
                    inspector.UpdateLayout(width, ref Y);
                }
            }
            int height = Y;
            mScrollPanel.SetSize(width, height);
            mScrollView.UpdateScrollBar();
        }

        void AddPropertyInspector(object obj, PropertyInfo PropertyInfo)
        {
            string PropertyTypeString = PropertyInfo.PropertyType.ToString();
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            Type PropertyType = PropertyInfo.PropertyType;
            bool bIsEnum = PropertyType.IsEnum;
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = obj;
            ObjectProperty.Name = PropertyInfo.Name;
            ObjectProperty.Type = PropertyType;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            ObjectProperty.GetPropertyValueFunction = delegate (object Object, string name, ValueExtraProperty ValueExtraProperty) { return PropertyInfo.GetValue(Object); };
            ObjectProperty.SetPropertyValueFunction = delegate (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) { PropertyInfo.SetValue(Object, PropertyValue); }; ;
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, bIsEnum);
            Inspector_Property.InspectProperty(ObjectProperty);
            Inspector_Property.SetContainer(mScrollPanel);
            Inspector_Property.SetInspectorHandler(mInspectorHandler);
            mInspectors.Add(Inspector_Property);
        }

        void OnSaveButtonClicked(Button Sender)
        {
            BuildConfig config = EditorConfigManager.GetInstance().GetConfig<BuildConfig>();
            config.Dump();
        }

        void OnRunButtonClicked(Button Sender)
        {
            BuildConfig config = EditorConfigManager.GetInstance().GetConfig<BuildConfig>();
            if (config.BuildMode == BuildMode.Asset)
                BuildProject.GetInstance().BuildAsset();
            else if (config.BuildMode == BuildMode.Engine)
                BuildProject.GetInstance().BuildEngine();
            else
                BuildProject.GetInstance().BuildAll();
        }

        void OnCancelButtonClicked(Button Sender)
        {
            BuildProject.GetInstance().BuildAbort();
        }

        void OnEditorGlobalUpdate(Device Sender, long TimeElapsed)
        {
            if (!mMainPanel.GetVisible_Recursively())
                return;
            bool isBuilding = BuildProject.GetInstance().IsBuilding();
            mButtonPanel.SetVisible(!isBuilding);
            mProgressPanel.SetVisible(isBuilding);
            if (isBuilding)
            {
                double progress = BuildProject.GetInstance().GetProgress();
                mProgressBar.SetProgress((float)progress);
                if (BuildProject.GetInstance().IsBuildingAsset() && BuildProject.GetInstance().IsBuildingEngine())
                {
                    mProgressLabel.SetText(string.Format("BuildAll:{0}%", (progress * 100).ToString("0.00")));
                }
                else if (BuildProject.GetInstance().IsBuildingAsset())
                {
                    mProgressLabel.SetText(string.Format("BuildAsset:{0}%", (progress * 100).ToString("0.00")));
                }
                else
                {
                    mProgressLabel.SetText(string.Format("BuildEngine:{0}%", (progress * 100).ToString("0.00")));
                }
            }
        }

        void ReadValue()
        {
            foreach (Inspector inspector in mInspectors)
            {
                inspector.ReadValue();
            }
        }
    }
}
