using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_MSAPreviewAsset : Inspector
    {
        protected MsaPreviewSingleAsset _MsaPreviewAsset;

        protected Button _ButtonBar;
        protected Label _LabelName;

        protected Panel _SaveButtonSeparator;
        protected Button _ButtonSaveAsset;

        public Inspector_MSAPreviewAsset()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _MsaPreviewAsset = (MsaPreviewSingleAsset)Object;

            _ButtonBar = new Button();
            _ButtonBar.Initialize();
            _ButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.ClickedEvent += OnButtonBarClicked;
            _SelfContainer.AddChild(_ButtonBar);

            InitializeCheckExpand();
            _ButtonBar.AddChild(_CheckExpand);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetText(_MsaPreviewAsset.GetName());
            _LabelName.SetFontSize(16);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            _ButtonBar.AddChild(_LabelName);

            _SaveButtonSeparator = new Panel();
            _SaveButtonSeparator.Initialize();
            _SaveButtonSeparator.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);
            _ChildContainer.AddChild(_SaveButtonSeparator);

            _ButtonSaveAsset = new Button();
            _ButtonSaveAsset.Initialize();
            _ButtonSaveAsset.SetText("Save Asset");
            _ButtonSaveAsset.SetBorderColor(Color.FromRGBA(81, 82, 84, 255));
            _ButtonSaveAsset.SetFontSize(18);
            _ButtonSaveAsset.SetTextOffsetY(2);
            _ButtonSaveAsset.ClickedEvent += OnButtonSaveAssetClicked;
            _ChildContainer.AddChild(_ButtonSaveAsset);

            RefreshChildInspectors();

            OperationQueue.GetInstance().AddOperation(() =>
            {
                OnButtonRefreshMenuClicked(null);
            });
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        protected virtual void RefreshChildInspectors()
        {
            ClearChildInspectors();
            List<PropertyInfo> Properties = MSAPreviewContext.GetInstance().GetPreviewAssetProperties(_MsaPreviewAsset);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AddPropertyInspector(PropertyInfo, _MsaPreviewAsset);
            }
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            PropertyInfo PropertyInfo = PreviewContext.GetPreviewAssetProperty(_MsaPreviewAsset, PropertyName);

            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }

            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            if (PreviewContext.SetPreviewAssetProperty(_MsaPreviewAsset, PropertyName, PropertyValue))
            {
                // need refresh inspectors after setting PropertyInfo
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    OnButtonRefreshMenuClicked(null);
                });
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            _ButtonBar.SetPosition(0, 0, Width, 24);
            _SelfContainer.SetPosition(0, Y, Width, 24);
            Y += 24;

            _CheckExpand.SetPosition(SPAN_X, 2, 20, 20);
            int LabelNameX = _CheckExpand.GetEndX() + SPAN_X;
            int LabelNameWidth = Width - SPAN_X - LabelNameX;
            _LabelName.SetPosition(LabelNameX, 4, LabelNameWidth, 20);

            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            _SaveButtonSeparator.SetPosition(SPAN_X, Y1, Width - 2 * SPAN_X, 1);
            _ButtonSaveAsset.SetPosition(SPAN_X, Y1 + 5, Width - 2 * SPAN_X, 30);
            _ChildContainer.SetHeight(Y1 + 40);
            Y += 40;
        }

        public Inspector_Property FindChildInspector(string PropertyName)
        {
            foreach (Inspector Inspector in _ChildInspectors)
            {
                Inspector_Property Inspector_Property = Inspector as Inspector_Property;
                if (Inspector_Property != null)
                {
                    string CurPropertyName = Inspector_Property.GetPropertyName();
                    if (CurPropertyName == PropertyName)
                    {
                        return Inspector_Property;
                    }
                }
            }

            return null;
        }

        protected void OnButtonSaveAssetClicked(Button Sender)
        {
            _SaveButtonSeparator.SetVisible(false);
            _ButtonSaveAsset.SetVisible(false);
            MSAPreviewContext.GetInstance().AttemptSaveAsset(_MsaPreviewAsset);
        }

        protected void OnButtonBarClicked(Button Sender)
        {
            bool bChecked = _CheckExpand.GetChecked();
            SetCheckExpand(!bChecked);
        }

        protected virtual void OnButtonRefreshMenuClicked(Button Sender)
        {
            _SaveButtonSeparator.SetVisible(false);
            _ButtonSaveAsset.SetVisible(false);

            if (_MsaPreviewAsset.GetDirty() &&
               _MsaPreviewAsset.GetMsaItemType() != MsaHierItemType.SkeletalMeshAsset &&
               _MsaPreviewAsset.GetMsaItemType() != MsaHierItemType.StaticMeshAsset)
            {
                _SaveButtonSeparator.SetVisible(true);
                _ButtonSaveAsset.SetVisible(true);
            }

            GetInspectorHandler().UpdateLayout();
        }
    }
}
