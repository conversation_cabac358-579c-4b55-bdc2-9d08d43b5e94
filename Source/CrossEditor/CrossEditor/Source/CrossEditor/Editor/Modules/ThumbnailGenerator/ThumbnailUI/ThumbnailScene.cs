using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class ThumbnailScene
    {
        static ThumbnailScene _Instance = new ThumbnailScene();
        const string SphereModel = "EngineResource/Model/Sphere.nda";
        const string CubeMapPreviewMaterialPath = "EngineResource/Material/SkyBox.nda";
        int _X;
        int _Y;
        int _Width;
        int _Height;

        World _World;
        Clicross.IRenderWindow _Renderwindow;
        int _ImageScene = -1;
        bool _bRunFirstUpdate = false;
        RenderPipelineSetting PipelineSetting;
        string _ThumbnailRequest = "";
        // Camera
        Entity _CameraEntity;
        Transform _CameraEntity_Transform;
        Camera _CameraEntity_Camera;
        Vector3f _DefaultCameraPos = new Vector3f(0.0f, 0.0f, -30.0f);
        // SkyBox
        Entity _SkyBox;
        // Light
        Entity _LightEntity;
        Transform _LightEntity_Transform;
        Light _LightEntity_Light;
        // Preview entity
        public Entity _CreatedEntity;
        Vector3f _Scale;
        // Preview target
        public string _PreviewNdaPath = "";
        Material _CubeMapPreviewMaterial;
        Material _MaterialPreviewMaterial;
        string _MeshPreviewMaterialPath;
        Material _MeshPreviewMaterial;
        PreviewRefSkeleton _PreviewSkeleton = new PreviewRefSkeleton(new MsaPreviewSingleAsset());
        public int FrameCounter = 0;
        float ObjectHeight = 100000.0f;
        public static ThumbnailScene GetInstance()
        {
            return _Instance;
        }

        ThumbnailScene()
        {
            _X = 0;
            _Y = 0;
            _Width = 256;
            _Height = 256;
            _ImageScene = 0;
            _Scale = new Vector3f(1.0f, 1.0f, 1.0f);
        }

        public UIManager GetUIManager()
        {
            return ThumbnailUI.GetInstance().GetUIManager();
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public static string GetThumbnailPath(string NdaPath)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            return ProjectDirectory + "/Intermediate/Thumb/" + NdaPath + ".thumb.nda";
        }

        public static string GetThumbnailPNGPath(string NdaPath)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            return ProjectDirectory + "/Intermediate/Thumb/" + NdaPath + ".thumb.png";
        }

        public static bool NeedGenerateThumbnail(string Path, string ThumbnailPath)
        {
            // For debug
            if (ThumbnailUI.bShowThumbnailWindow)
            {
                return true;
            }

            if (FileHelper.IsFileExists(ThumbnailPath) == false)
            {
                return true;
            }
            string Path1 = EditorUtilities.StandardFilenameToEditorFilename(Path);
            long PathModifyTime = FileHelper.GetFileModifyTime(Path1);
            long ThumbnailPathModifyTime = FileHelper.GetFileModifyTime(ThumbnailPath);
            if (PathModifyTime > ThumbnailPathModifyTime)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public void SetupPerspectiveCamera()
        {
            _CameraEntity_Camera.PerspectiveAspect = CalculateAspect();
            _CameraEntity_Camera.PerspectiveFov = (float)Math.PI / 5.0f;
            _CameraEntity_Camera.PerspectiveNear = 1.0f;
            _CameraEntity_Camera.PerspectiveFar = 100000.0f;
        }

        public void SetupOrthogonalCamera()
        {
            _CameraEntity_Camera.Enable = true;
            _CameraEntity_Camera.Mode = CameraProjectionMode.Orthogonal;
            _CameraEntity_Camera.OrthogonalNear = 0.1f;
            _CameraEntity_Camera.OrthogonalFar = 20480.0f;
        }

        public void SetupTheScene()
        {
            if (_World.Root == null)
            {
                _World.Root = new Entity(_World);
            }
            var settingMgr = CrossEngineApi.GetSettingManager();
            var setting = settingMgr.GetRenderPipelineSettingForEditor();

            _World.Root.EntityID = Clicross.GameWorldInterface.World_GetRootEntity(_World._WorldInterface);
            string Name = Clicross.GameWorldInterface.World_GetEntityName(_World._WorldInterface, _World.Root.EntityID);
            _CameraEntity = _World.CreateEntity();
            _CameraEntity_Transform = _CameraEntity.CreateComponent<Transform>();
            _CameraEntity_Camera = _CameraEntity.CreateComponent<Camera>();
            SetupPerspectiveCamera();
            //Runtime.Entity_SetSerializeAble(_World._World, _CameraEntity.EntityID, false);
            Clicross.GameWorldInterface.World_JointToRoot(_World._WorldInterface, _CameraEntity.EntityID);


            if (setting.UseRenderPipeline != "UseFFSRP")
            {
                _LightEntity = _World.CreateEntity();
                _LightEntity_Transform = _LightEntity.CreateComponent<Transform>();
                float RotationX = 45;
                float RotationY = 45;
                float RotationZ = 0;
                _LightEntity_Transform.Rotation = new Vector3f(RotationX, RotationY, RotationZ);
                _LightEntity_Light = _LightEntity.CreateComponent<Light>();
                _LightEntity_Light.mLight.mEnable = true;
                _LightEntity_Light.mLight.mType = CEngine.LightType.Directional;
                _LightEntity_Light.mLight.mColor = new CEngine.Float3(1.0f, 1.0f, 1.0f);
                _LightEntity_Light.mLight.mIntensity = 5.0f;
                Clicross.GameWorldInterface.World_JointToRoot(_World._WorldInterface, _LightEntity.EntityID);

            }
            _DefaultCameraPos.Y = ObjectHeight;
            _CameraEntity_Transform.Translation = _DefaultCameraPos;
            _World.Root.SetName(Name.ToString());
            ManipulatorManager.GetInstance().SetManipulatorType(ManipulatorType.LocalTranslator);
            switch (setting.UseRenderPipeline)
            {
                case "UseBuiltinRP":
                    PipelineSetting = RenderPipelineEditor.CastTo<BuiltinRenderPipelineSetting>(setting, false);
                    break;
                case "UseFFSRP":
                    PipelineSetting = RenderPipelineEditor.CastTo<FFSRenderPipelineSetting>(setting, false);
                    var ffsSetting = (FFSRenderPipelineSetting)PipelineSetting;
                    ffsSetting.mTemporalAntiAliasingSetting.enable = false;
                    break;
                default:
                    break;
            }
            _MeshPreviewMaterialPath = PipelineSetting.DefaultMaterial;
            settingMgr.NotifyRenderPipelineSettingChanged();
            _CameraEntity_Camera.SetMainCamera();
        }


        public void Initialize(CrossEngine CrossEngine, Clicross.IRenderWindow RenderWindow)
        {
            var settingMgr = CrossEngineApi.GetSettingManager();
            var setting = settingMgr.GetRenderPipelineSettingForEditor();

            _World = CrossEngine.CreateWorld("WorldThumbnail11", 3);
            _World.Initialize();
            if (setting.UseRenderPipeline == "UseFFSRP")
            {
                Clicross.GameWorldInterface.World_LoadScene(_World._WorldInterface, "PipelineResource/FFSRP/DefaultWorld/Thumbnailworld.world");
            }

            CrossEngine.Bind(_World, RenderWindow);
            _Renderwindow = RenderWindow;
        }

        public void SetPosition(int X, int Y, int Width, int Height)
        {
            _X = X;
            _Y = Y;
            if (Width != _Width || Height != _Height)
            {
                _Width = Width;
                _Height = Height;
                UIManager UIManager = GetUIManager();
                EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
                EditorUICanvas.ResizeSceneImage(_ImageScene, _World, _Width, _Height);
            }
        }

        public void DrawScene()
        {
            UIManager UIManager = GetUIManager();
            if (UIManager != null)
            {
                EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
                Color ColorWhite = new Color(1.0f, 1.0f, 1.0f, 1.0f);
                EditorUICanvas.DrawImage(_ImageScene, _X, _Y, _Width, _Height, ref ColorWhite);
            }
        }

        public void SaveThumbnail(string ThumbnailPath)
        {
            _ThumbnailRequest = ThumbnailPath;
        }

        public World GetWorld()
        {
            return _World;
        }

        public void Update()
        {
            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            if (CrossEngineApiPINVOKE.IsWorldLoaded(_World.GetNativePointer()))
            {
                if (_ImageScene == 0)
                {
                    _ImageScene = EditorUICanvas.CreateSceneImage(_World, _Width, _Height);
                }
                Device Device = GetDevice();
                int DeviceWidth = Device.GetWidth();
                int DeviceHeight = Device.GetHeight();
                SetPosition(0, 0, DeviceWidth, DeviceHeight);

                if (!_bRunFirstUpdate)
                {
                    SetupTheScene();
                    _bRunFirstUpdate = true;
                }

                FrameCounter++;
                // Previously 3.
                // Since now mesh asset data resources can be streamable, more frames should be waited
                // to ensure the streaming process is completed and get the right thumbnail images
                const int gMaxQueuedFrame = 10;
                if (FrameCounter > gMaxQueuedFrame)
                {
                    PreviewNda();
                    if (_ThumbnailRequest.Length > 0)
                    {
                        bool saveAlpha = true;
                        EditorUICanvas.SaveImage(_Renderwindow, _ImageScene, _ThumbnailRequest, saveAlpha);
                        _ThumbnailRequest = "";
                    }
                }
            }
        }

        float CalculateAspect()
        {
            Device Device = GetDevice();
            int DeviceWidth = Device.GetWidth();
            int DeviceHeight = Device.GetHeight();
            return DeviceWidth / (float)DeviceHeight;
        }

        public void PreviewNda()
        {
            if (_PreviewNdaPath.Length > 0)
            {
                _Scale = new Vector3f(1.0f, 1.0f, 1.0f);
                SetupPerspectiveCamera();
                InnerPreviewNda(_PreviewNdaPath);
            }
        }
        public void ClearScene()
        {
            if (_CreatedEntity != null)
            {
                _CreatedEntity.RuntimeRemove();
                _CreatedEntity = null;
            }
            if (_CubeMapPreviewMaterial != null)
            {
                _CubeMapPreviewMaterial = null;
            }
            if (_MaterialPreviewMaterial != null)
            {
                _MaterialPreviewMaterial = null;
            }
            if (_MeshPreviewMaterial != null)
            {
                _MeshPreviewMaterial = null;
            }

            //CEngine.ClassIDType ObjectClassID = (CEngine.ClassIDType)Runtime.Resource_GetResourceType(_PreviewNdaPath);
            //if (ObjectClassID != CEngine.ClassIDType.CLASS_Material && ObjectClassID != CEngine.ClassIDType.CLASS_Fx)
            {
                ResourceManager.Instance().TryReloadResource(_PreviewNdaPath);
            }

            CrossEngine.GetInstance()._CrossEngineInterface.FlushRendering();
        }

        void InnerPreviewNda(string NdaPath)
        {

            CEngine.ClassIDType ObjectClassID = (CEngine.ClassIDType)Clicross.ResourceUtil.ResourceGetClassID(NdaPath);
            if (ObjectClassID > 0)
            {
                _CameraEntity_Transform.Translation = _DefaultCameraPos;

                if (ObjectClassID == CEngine.ClassIDType.CLASS_MeshAssetDataResource)
                {
                    _MeshPreviewMaterial = new Material(Clicross.resource.Material.MaterialCreateMaterial(_MeshPreviewMaterialPath));
                    PreviewMesh(NdaPath, _MeshPreviewMaterial);
                }
                else if (ObjectClassID == CEngine.ClassIDType.CLASS_Material || ObjectClassID == CEngine.ClassIDType.CLASS_Fx)
                {
                    PreviewMesh(SphereModel, Resource.Get(NdaPath, false));
                }
                else if (ObjectClassID == CEngine.ClassIDType.CLASS_Texture2D ||
                    ObjectClassID == CEngine.ClassIDType.CLASS_TextureCube ||
                    ObjectClassID == CEngine.ClassIDType.CLASS_Texture3D ||
                    ObjectClassID == CEngine.ClassIDType.CLASS_Texture2DVirtual)
                {
                    CEngine.EditorPrimitiveSystemG.DrawTextureInGizmo(_World.GetNativePointer(), NdaPath);
                }
                else if (ObjectClassID == CEngine.ClassIDType.CLASS_SkeletonResource)
                {
                    _CameraEntity_Transform.Translation = new Vector3f(0.0f, 100.0f, -200.0f);
                    _PreviewSkeleton.Load(NdaPath);
                    _PreviewSkeleton.DrawSkeletonFrameWork(_World._World, -1);
                }
            }
        }

        void PreviewMesh(string MeshPath, Resource materialInterface)
        {
            if (_CreatedEntity == null)
            {
                _CreatedEntity = _World.CreateEntity();
                _CreatedEntity.CreateComponent<Transform>();
                _CreatedEntity.CreateComponent<ModelComponent>();
                Clicross.GameWorldInterface.World_JointToRoot(_World._WorldInterface, _CreatedEntity.EntityID);
            }
            ModelComponent Mesh = (ModelComponent)_CreatedEntity.GetComponent(typeof(ModelComponent));
            Transform Transform = (Transform)_CreatedEntity.GetComponent(typeof(Transform));
            List<Model> ModelList = new List<Model>();
            ModelList.Add(new Model(MeshPath));
            Mesh.Models = ModelList;

            uint lodcount = CEngine.ModelSystemG.GetLODCount(_CreatedEntity.World._World, _CreatedEntity.EntityID, 0);
            for (uint j = 0; j < lodcount; j++)
            {
                uint SubMeshCount = CEngine.ModelSystemG.GetSubModelCount(_CreatedEntity.World._World, _CreatedEntity.EntityID, 0, j);
                for (int i = 0; i < SubMeshCount; i++)
                {
                    Clicross.ModelSystemG.Model_SetMaterialInstance(_CreatedEntity.World._WorldInterface, _CreatedEntity.EntityID, materialInterface.ResourcePtr as Clicross.resource.MaterialInterface, i, 0);
                }
            }
            Clicross.ResourceAABB aabb = Clicross.GameWorldInterface.Resource_GetAABBFromMeshAssetData(MeshPath);
            Vector3f Min = new Vector3f((float)aabb.min.x, (float)aabb.min.y, (float)aabb.min.z);
            Vector3f Max = new Vector3f((float)aabb.max.x, (float)aabb.max.y, (float)aabb.max.z);
            if (_CreatedEntity != null)
            {
                float LengthX = Max.X - Min.X;
                float LengthY = Max.Y - Min.Y;
                float LengthZ = Max.Z - Min.Z;
                float TranslationX = -Max.X + LengthX / 2.0f;
                float TranslationY = -Max.Y + LengthY / 2.0f;
                float TranslationZ = -Max.Z + LengthZ / 2.0f;
                float MaxLength = Math.Max(LengthX, Math.Max(LengthY, LengthZ));
                float Scale = 12.5f / MaxLength;
                float ScaleX = Scale * _Scale.X;
                float ScaleY = Scale * _Scale.Y;
                float ScaleZ = Scale * _Scale.Z;
                Transform.Translation = new Vector3f(TranslationX * Scale, TranslationY * Scale + ObjectHeight, TranslationZ * Scale);
                Transform.Scale = new Vector3f(ScaleX, ScaleY, ScaleZ);
            }
        }

        void SetSkyBoxVisability(bool bEnable)
        {
            if (bEnable && _SkyBox == null)
            {
                _SkyBox = _World.CreateEntity();
                _SkyBox.CreateComponent<ModelComponent>();
                _SkyBox.CreateComponent<Transform>();
                Transform Transform = (Transform)_SkyBox.GetComponent(typeof(Transform));
                Transform.Scale = new Vector3f(10000, 10000, 10000);
                List<Model> ModelList = new List<Model>();
                ModelList.Add(new Model(SphereModel, "EngineResource/Material/SkyBox.nda"));
                ModelComponent Model = (ModelComponent)_SkyBox.GetComponent(typeof(ModelComponent));
                Model.Models = ModelList;
            }
            else if (!bEnable && _SkyBox != null)
            {
                _SkyBox.RuntimeRemove();
                _SkyBox = null;
            }
        }
    }
}
