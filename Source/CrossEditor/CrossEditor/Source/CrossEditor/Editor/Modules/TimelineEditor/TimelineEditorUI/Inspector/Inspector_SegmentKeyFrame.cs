using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class Inspector_SegmentKeyFrame : Inspector
    {
        AnimSegmentKeyFrame _Object;

        public Inspector_SegmentKeyFrame()
        { }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Object = Object as AnimSegmentKeyFrame;

            ClearChildInspectors();
            Type inspecteredType = _Object.BindObject.GetType();

            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(inspecteredType);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                if (PropertyInfo.Name != "Enable")
                {
                    AddPropertyInspector(PropertyInfo, _Object);
                }
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = _Object.BindObject.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
                return PropertyInfo.GetValue(_Object.BindObject);
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = _Object.BindObject.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                var AnimAsset = _Object.OwnerTrack.GetTagObject() as PreviewAnimAsset;
                var Data = _Object.BindObject as SlotTrackResSegment;
                var Name = Data.Name;

                bool bShouldSet = true;
                if (PropertyInfo.PropertyType == typeof(string) &&
                    PropertyInfo.GetValue(_Object.BindObject).Equals(PropertyValue))
                    bShouldSet = false;

                PropertyInfo.SetValue(_Object.BindObject, PropertyValue);

                var index = _Object.OwnerTrack.GetKeyFrameIndex(_Object);

                if (bShouldSet)
                    MSAPreviewContext.GetInstance().SetSubDataByName(AnimAsset,
                        MSAPreviewContext.AnimSubDataType.Segment, index.ToString(), Data);
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }
    }
}
