using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class SeqAnimSectionTrack : Track
    {
        public PreviewSkeletonAsset PreviewRunSkelt = null;
        public LevelSeqAnimationTrack AnimTrackRef { get; private set; } = null;
        public Entity Previewing { get; private set; }
        public int TrackIndex = -1;
        PathInputUIEx PathInputUI;

        public void Initialize(ScaleUI ScaleUI, Track Parent, IntPtr AnimTrackPtr, object TagObject)
        {
            AnimTrackRef = new LevelSeqAnimationTrack(AnimTrackPtr, false);
            base.Initialize(ScaleUI, Parent, AnimTrackRef.TrackName, TagObject);

            ItemHeight = 40;

            _ButtonAddTrack.SetText("+ Animation");

            Previewing = TagObject as Entity;
            string skeletonAssetPath = SkeletonSystemG.GetSkeletonAssetPath(Previewing.World.GetNativePointer(), Previewing.EntityID);
            PreviewRunSkelt = new PreviewSkeletonAsset();
            PreviewRunSkelt.Load(skeletonAssetPath);

            InitPathInputUI();

            KeyFrames.Clear();
            for (uint i = 0; i < AnimTrackRef.AnimSectionCount(); i++)
            {
                var pSection = AnimTrackRef.GetAnimSectionAt(i);
                if (pSection != IntPtr.Zero)
                {
                    AddKeyFrame(new LevelSeqAnimationSection(pSection, false));
                }
            }
        }

        void InitPathInputUI()
        {
            var PathInputUIFilterItem = new PathInputUIFilterItem()
            {
                Name = "Animation Files",
                Extensions = new List<string> { "nda" }
            };

            PathInputUI = new PathInputUIEx();
            var ContentsDirectory = EditorUtilities.AddEditorDrives(PathInputUI, true);
            PathInputUI.Initialize(GetUIManager(), "Open Animation Asset", PathInputUIType.OpenFile, PathInputUIFilterItem, ContentsDirectory);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                AddNewAnimSection(PathInputed);
            };
        }

        public void AddNewAnimSection(string filepath)
        {
            try
            {
                var AnimAsset = Resource.Get(filepath, false) as PreviewAnimSeqAsset;
                if (AnimAsset is null)
                {
                    throw new Exception("The selected file is NOT a AnimSequenceAsset");
                }

                CompatibleFailInfo CompatibleFailedInfo = new CompatibleFailInfo();
                if (PreviewRunSkelt.IsCompatible(AnimAsset, ref CompatibleFailedInfo) != MsaCompatibleType.Success)
                {
                    throw new Exception("The selected animation is NOT compatible.");
                }

                decimal Key = _ScaleUI.GetCurrentUI().GetHeadLocation();
                float StartTime = (float)Key;
                var pSection = AnimTrackRef.AddNewAnimSection(StartTime, AnimAsset.Path);
                AddKeyFrame(new LevelSeqAnimationSection(pSection, false));
            }
            catch (Exception ex)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Warning", ex.Message);
            }
        }

        public override void Draw(UIManager UIManager)
        {
            Color BackColor = GetIsSelected() ? SelectedColor : UnSelectedColor;

            RectangleF Bound = GetBound();
            int X = (int)Bound.X;
            int Y = (int)Bound.Y;
            int Width = (int)Bound.Width;
            int Height = (int)Bound.Height;

            if (Y == 0) return;

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillRectangle(X, Y, Width, Height, ref BackColor);

            int DrawSpan = 2;
            int DrawX = _Panel.GetScreenX();
            int DrawY = GetTrackItem().GetScreenY() + DrawSpan;
            int DrawWidth = _ScaleUI.GetWidth();
            int DrawHeight = ItemHeight - DrawSpan * 2;
            Curve.Draw(UIManager, _ScaleUI.GetStart(), _ScaleUI.GetEnd(), DrawX, DrawY, DrawWidth, DrawHeight, DrawSpan);
            DrawSections();
        }

        public void DrawSections()
        {
            foreach (var Section in KeyFrames)
            {
                Section.Draw();
            }
        }

        public override object GetValue(float Key)
        {
            return 1.0f;
        }

        protected override void OnButtonAddTrackClicked(Button Sender)
        {
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        static public void CloneAnimationSection(LevelSeqAnimationSection inSection, LevelSeqAnimationSection outSection)
        {
            if (outSection is null) outSection = new LevelSeqAnimationSection();

            outSection.SectionName = inSection.SectionName;
            outSection.SectionStart = inSection.SectionStart;
            outSection.SectionEnd = inSection.SectionEnd;
            outSection.AnimSequencePath = inSection.AnimSequencePath;
            outSection.SlotName = inSection.SlotName;
            // outSection.MatchedBoneName = inSection.MatchedBoneName;
            // outSection.MatchTranslationXZ = inSection.MatchTranslationXZ;
            // outSection.MatchTranslationY = inSection.MatchTranslationY;
        }

        static public LevelSeqAnimationSection CloneAnimationSection(LevelSeqAnimationSection Section)
        {
            LevelSeqAnimationSection newSection = null;
            CloneAnimationSection(Section, newSection);
            return newSection;
        }


        void AddKeyFrame(LevelSeqAnimationSection section)
        {
            var NewKeyFrame = new SeqAnimSectionKeyFrame(section, this);
            //NewKeyFrame.MoveEvent += (Sender, Args) => { Curve.Points.Sort(); };
            KeyFrames.Add(NewKeyFrame);
        }

        public override KeyFrame RemoveKeyFrame(KeyFrame KeyFrame)
        {
            base.RemoveKeyFrame(KeyFrame);
            if (KeyFrame is SeqAnimSectionKeyFrame SectionKeyFrame)
            {
                Entity LevelSequenceEntity = CinematicUI.GetInstance().GetLevelSequenceEntity();
                ControllableUnitSystemG.RemoveCurveControllerAnimSection(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID,
                    Previewing.GetEntityIdStruct(), TrackIndex, SectionKeyFrame.SectionID);
            }
            return null;
        }

        public override void ClearKeyFrames()
        {
            KeyFrames.Clear();
            AnimTrackRef.Clear();
        }

        public void ModifyValue(SeqAnimSectionKeyFrame Key, LevelSeqAnimationSection Value)
        {
            CloneAnimationSection(Value, Key.Section);
        }
    }
}