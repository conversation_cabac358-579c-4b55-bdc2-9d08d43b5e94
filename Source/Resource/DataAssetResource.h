#pragma once 

#include "Resource/Resource.h"
#include "virtual_machine/vmcore_define.hpp"

namespace cross{ 
namespace resource{
    enum class CEMeta(Cli,Puerts) DataAssetItemType
    {
        Bool = int(gbf::reflection::ValueKind::kBoolean),
        Integer = int(gbf::reflection::ValueKind::kInteger),
        Real = int(gbf::reflection::ValueKind::kReal),
        String = int(gbf::reflection::ValueKind::kString),
        UserObject = int(gbf::reflection::ValueKind::kUser),
    };

    enum class CEMeta(Cli, Puerts) DataAssetItemTypeCategory
    {
        //BasicType = int(gbf::logic::VariableCategory::BasicType),
        BasicType = 0,
        Struct = 1,
        Enum = 2,
        Asset = 3,
        Color = 4
    };

    struct Resource_API CEMeta(Cli, Puerts) DataAssetItem
    {
        CEMeta(Cli, Puerts)
        DataAssetItem() = default;

        CEFunction(Reflect, ScriptCallable)
        int GetDataItemSize()
        {
            return static_cast<int>(Child.size());
        }

        CEMeta(Cli, Serialize)
        CEFunction(Reflect, ScriptCallable)
        DataAssetItem GetDataItem(const std::string& name) const
        {
            auto it = Child.find(name);
            if (it != Child.end())
            {
                return it->second;
            }
            return DataAssetItem{};
        }

        CEMeta(Cli, Serialize) CEProperty(ScriptReadWrite) std::string Name;
        CEMeta(Cli, Serialize) CEProperty(ScriptReadWrite) DataAssetItemType Type = DataAssetItemType::Bool;
        CEMeta(Cli, Serialize) CEProperty(ScriptReadWrite) uint64_t TypeId;
        CEMeta(Cli, Serialize) CEProperty(ScriptReadWrite) DataAssetItemTypeCategory Category = DataAssetItemTypeCategory::BasicType;
        CEMeta(Cli, Serialize) CEProperty(ScriptReadWrite) std::string Value;
        CEMeta(Cli, Serialize) CEProperty(ScriptReadWrite) std::string DefaultValue;
        CEMeta(Cli, Serialize) std::map<std::string, DataAssetItem> Child;       // Only used by DataAssetItemTypeCategory::Struct

        CE_Serialize_Deserialize
    };

    struct Resource_API DataAssetResourceInfo
    {
        CEMeta(Serialize) ClassIDType BaseClassTypeId;
        CEMeta(Serialize) std::string BaseMetaClass;
        CEMeta(Serialize) std::string BaseGuid;
        CEMeta(Serialize) std::map<std::string, DataAssetItem> DataItems;
        std::map<std::string, DataAssetItem> mWorkFlowDataItems;

        CE_Serialize_Deserialize
    };

    bool sync_item(DataAssetItem& work_flow_item, DataAssetItem& asset_item);
    bool sync_items(std::map<std::string, DataAssetItem>& work_flow_items, std::map<std::string, DataAssetItem>& asset_items);


    class Resource_API CEMeta(Reflect, Script, Cli, WorkflowType) DataAssetResource : public Resource
    {
    public:
        //@brief GetClassIDStatic
        static int GetClassIDStatic() { return ClassIDType::ClassID(DataAssetResource); }
        //@brief Constructor
        CEFunction(Reflect, ScriptCallable, Puerts)
        DataAssetResource() = default;
        //@brief Destructor
        virtual ~DataAssetResource() = default;

        bool Serialize(const std::string& path);
        bool Deserialize(DeserializeNode const& s) override;

        CEFunction(Reflect, ScriptCallable)
        DataAssetItem GetDataItem(const std::string& name) const
        {
            auto it = mInfo.DataItems.find(name);
            if (it != mInfo.DataItems.end())
            {
                return it->second;
            }
            return DataAssetItem{};
        }

        const ClassIDType& GetBaseTypeId() const
        {
            return mInfo.BaseClassTypeId;
        }

        const std::string& GetBaseMetaClass() const
        {
            return mInfo.BaseMetaClass;
        }

        void SetBaseMetaClass(const std::string& metaClass)
        {
            mInfo.BaseMetaClass = metaClass;
        }

        const std::string& GetBaseGuid() const
        {
            return mInfo.BaseGuid;
        }

        void SetBaseGuid(const std::string& guid)
        {
            mInfo.BaseGuid = guid;
        }

        const std::map<std::string, DataAssetItem>& GetItems() const
        {
            return mInfo.DataItems;
        }

        void Save(const std::string& path, const std::map<std::string, DataAssetItem>& dataItems);
        void RefreshByWorkflow();

         const std::map<std::string, DataAssetItem>& GetWorkFlowResourceItem();
        
    private:
        /* Create struct item recursively */
        void CreateItemByStructProp(uint64_t typeId, DataAssetItem & parentDataItem);

    private:
        DataAssetResourceInfo mInfo;
        
    };
}
}