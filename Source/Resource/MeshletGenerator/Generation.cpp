//*********************************************************
//
// Copyright (c) Microsoft. All rights reserved.
// This code is licensed under the MIT License (MIT).
// THIS CODE IS PROVIDED *AS IS* WITHOUT WARRANTY OF
// ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING ANY
// IMPLIED WARRANTIES OF FITNESS FOR A PARTICULAR
// PURPOSE, MERCHANTABILITY, OR NON-INFRINGEMENT.
//
//*********************************************************
#include "Generation.h"
#include "Utilities.h"
#include "Math/CrossMath.h"
#include "Threading/Task.h"

#include <DirectXMath.h>

#include <algorithm>
#include <unordered_set>

using namespace DirectX;

///
// External Interface

namespace internal
{
    template <typename T>
    void Meshletize(
        uint32_t maxVerts, uint32_t maxPrims,
        const T* indices, uint32_t indexCount,
        const XMFLOAT3* positions, uint32_t vertexCount,
        T vertexStart,
        std::vector<InlineMeshlet<T>>& output,
        std::vector<MeshletGroup>* meshletGroups,
        uint32_t& handleGroupIndex);
}

void Meshletize(
    uint32_t maxVerts, uint32_t maxPrims,
    const uint16_t* indices, uint32_t indexCount,
    const XMFLOAT3* positions, uint32_t vertexCount,
    uint16_t vertexStart,
    std::vector<InlineMeshlet<uint16_t>>& output,
    std::vector<MeshletGroup>* meshletGroups,
    uint32_t& handleGroupIndex
)
{
    return internal::Meshletize(maxVerts, maxPrims, indices, indexCount, positions, vertexCount, vertexStart, output, meshletGroups, handleGroupIndex);
}

void Meshletize(
    uint32_t maxVerts, uint32_t maxPrims,
    const uint32_t* indices, uint32_t indexCount,
    const XMFLOAT3* positions, uint32_t vertexCount,
    uint32_t vertexStart,
    std::vector<InlineMeshlet<uint32_t>>& output,
    std::vector<MeshletGroup>* meshletGroups,
    uint32_t& handleGroupIndex
)
{
    return internal::Meshletize(maxVerts, maxPrims, indices, indexCount, positions, vertexCount, vertexStart, output, meshletGroups, handleGroupIndex);
}

DirectX::XMVECTOR MergeBoundingSphere(DirectX::XMVECTOR& sphere1, DirectX::XMVECTOR& sphere2) 
{
    DirectX::XMVECTOR result = g_XMZero;
    cross::Float3 center1(XMVectorGetX(sphere1), XMVectorGetY(sphere1), XMVectorGetZ(sphere1));
    cross::BoundingSphere sp1(center1, XMVectorGetW(sphere1));
    cross::Float3 center2(XMVectorGetX(sphere2), XMVectorGetY(sphere2), XMVectorGetZ(sphere2));
    cross::BoundingSphere sp2(center2, XMVectorGetW(sphere2));
    cross::BoundingSphere merged;
    cross::BoundingSphere::CreateMerged(merged, sp1, sp2);
    auto mergedCenter = merged.GetCenter();
    result = XMVectorSetX(result, mergedCenter.x);
    result = XMVectorSetY(result, mergedCenter.y);
    result = XMVectorSetZ(result, mergedCenter.z);
    result = XMVectorSetW(result, merged.GetRadius());
    return result;
}

///
// Helpers

// Sort in reverse order to use vector as a queue with pop_back.
bool CompareScores(const std::pair<uint32_t, float>& a, const std::pair<uint32_t, float>& b)
{
    return a.second > b.second;
}

XMVECTOR ComputeNormal(XMFLOAT3* tri)
{
    XMVECTOR p0 = XMLoadFloat3(&tri[0]);
    XMVECTOR p1 = XMLoadFloat3(&tri[1]);
    XMVECTOR p2 = XMLoadFloat3(&tri[2]);

    XMVECTOR v01 = p0 - p1;
    XMVECTOR v02 = p0 - p2;

    return XMVector3Normalize(XMVector3Cross(v01, v02));
}

// Compute number of triangle vertices already exist in the meshlet
template <typename T>
uint32_t ComputeReuse(const InlineMeshlet<T>& meshlet, T (&triIndices)[3])
{
    uint32_t count = 0;

    for (uint32_t i = 0; i < static_cast<uint32_t>(meshlet.UniqueVertexIndices.size()); ++i)
    {
        for (uint32_t j = 0; j < 3u; ++j)
        {
            if (meshlet.UniqueVertexIndices[i] == triIndices[j])
            {
                ++count;
            }
        }
    }

    return count;
}

// Computes a candidacy score based on spatial locality, orientational coherence, and vertex re-use within a meshlet.
template <typename T>
float ComputeScore(const InlineMeshlet<T>& meshlet, XMVECTOR sphere, XMVECTOR normal, T (&triIndices)[3], XMFLOAT3* triVerts, bool computeReuse = true)
{
    const float reuseWeight = 0.334f;
    float locWeight = 0.333f;
    float oriWeight = 0.333f;
    if (!computeReuse)
    {
        locWeight = 0.5f;
        oriWeight = 0.5f;
    }
    
    XMVECTOR vXMOne = g_XMOne;

    // Vertex reuse
    uint32_t reuse; 
    XMVECTOR reuseScore = vXMOne;
    if (computeReuse)
    {
        reuse = ComputeReuse(meshlet, triIndices);
        reuseScore = vXMOne - (XMVectorReplicate(static_cast<float>(reuse)) / 3.0f);
    }

    // Distance from center point
    XMVECTOR maxSq = g_XMZero;
    for (uint32_t i = 0; i < 3u; ++i)
    {
        XMVECTOR v = sphere - XMLoadFloat3(&triVerts[i]);
        maxSq = XMVectorMax(maxSq, XMVector3Dot(v, v));
    }
    XMVECTOR r = XMVectorSplatW(sphere);
    XMVECTOR r2 = r * r;
    XMVECTOR locScore = XMVectorLog(maxSq / r2 + vXMOne);

    // Angle between normal and meshlet cone axis
    XMVECTOR n = ComputeNormal(triVerts);
    XMVECTOR d = XMVector3Dot(n, normal);
    XMVECTOR oriScore = (-d + vXMOne) / 2.0f;

    XMVECTOR b;
    if (computeReuse)
    {
        b = reuseWeight * reuseScore + locWeight * locScore + oriWeight * oriScore;
    }
    else
    {
        b = locWeight * locScore + oriWeight * oriScore;
    }

    return XMVectorGetX(b);
}

// Determines whether a candidate triangle can be added to a specific meshlet; if it can, does so.
template <typename T>
bool AddToMeshlet(uint32_t maxVerts, uint32_t maxPrims, InlineMeshlet<T>& meshlet, T (&tri)[3], uint32_t triangleIndex)
{
    // Are we already full of vertices?
    if (meshlet.UniqueVertexIndices.size() == maxVerts)
        return false;

    // Are we full, or can we store an additional primitive?
    if (meshlet.PrimitiveIndices.size() == maxPrims)
        return false;

    static const uint32_t Undef = uint32_t(-1);
    uint32_t indices[3] = { Undef, Undef, Undef };
    uint32_t newCount = 3;

    for (uint32_t i = 0; i < meshlet.UniqueVertexIndices.size(); ++i)
    {
        for (uint32_t j = 0; j < 3; ++j)
        {
            if (meshlet.UniqueVertexIndices[i] == tri[j])
            {
                indices[j] = i;
                --newCount;
            }
        }
    }

    // Will this triangle fit?
    if (meshlet.UniqueVertexIndices.size() + newCount > maxVerts)
        return false;

    // Add unique vertex indices to unique vertex index list
    for (uint32_t j = 0; j < 3; ++j)
    {
        if (indices[j] == Undef)
        {
            indices[j] = static_cast<uint32_t>(meshlet.UniqueVertexIndices.size());
            meshlet.UniqueVertexIndices.push_back(tri[j]);
        }
    }

    // Add the new primitive 
    typename InlineMeshlet<T>::PackedTriangle prim = {};
    prim.i0 = indices[0];
    prim.i1 = indices[1];
    prim.i2 = indices[2];

    meshlet.PrimitiveIndices.push_back(prim);
    meshlet.TriangleIndexSet.insert(triangleIndex);
    meshlet.TriangleIndices.push_back(triangleIndex);

    return true;
}

template <typename T>
bool IsMeshletFull(uint32_t maxVerts, uint32_t maxPrims, const InlineMeshlet<T>& meshlet)
{
    assert(meshlet.UniqueVertexIndices.size() <= maxVerts);
    assert(meshlet.PrimitiveIndices.size() <= maxPrims);

    return meshlet.UniqueVertexIndices.size() == maxVerts
        || meshlet.PrimitiveIndices.size() == maxPrims;
}

inline uint32_t MurmurFinalize32(uint32_t Hash)
{
    Hash ^= Hash >> 16;
    Hash *= 0x85ebca6b;
    Hash ^= Hash >> 13;
    Hash *= 0xc2b2ae35;
    Hash ^= Hash >> 16;
    return Hash;
}

uint32_t Murmur32(std::initializer_list<uint32_t> InitList)
{
    uint32_t Hash = 0;
    for (auto Element : InitList)
    {
        Element *= 0xcc9e2d51;
        Element = (Element << 15) | (Element >> (32 - 15));
        Element *= 0x1b873593;

        Hash ^= Element;
        Hash = (Hash << 13) | (Hash >> (32 - 13));
        Hash = Hash * 5 + 0xe6546b64;
    }

    return MurmurFinalize32(Hash);
}

uint32_t HashPosition(const XMFLOAT3& Position)
{
    union { float f; uint32_t i; } x;
    union { float f; uint32_t i; } y;
    union { float f; uint32_t i; } z;

    x.f = Position.x;
    y.f = Position.y;
    z.f = Position.z;

    return Murmur32( {
        Position.x == 0.0f ? 0u : x.i,
        Position.y == 0.0f ? 0u : y.i,
        Position.z == 0.0f ? 0u : z.i
        } );
}

///
// Implementation 

template <typename T>
void internal::Meshletize(
    uint32_t maxVerts, uint32_t maxPrims,
    const T* indices, uint32_t indexCount,
    const XMFLOAT3* positions, uint32_t vertexCount,
    T vertexStart,
    std::vector<InlineMeshlet<T>>& output,
    std::vector<MeshletGroup>* meshletGroups,
    uint32_t& handleGroupIndex
)
{
    const uint32_t triCount = indexCount / 3;

    // Build a primitive adjacency list
    std::vector<uint32_t> adjacency;
    adjacency.resize(indexCount);

    // Find point reps (unique positions) in the position stream
    // Create a mapping of non-unique vertex indices to point reps
    std::vector<T> pointRep;
    pointRep.resize(vertexCount);

    std::unordered_map<size_t, T> uniquePositionMap;
    uniquePositionMap.reserve(vertexCount);

    BuildAdjacencyList(indices, indexCount, positions, vertexCount, vertexStart, adjacency.data(), pointRep, uniquePositionMap);

    // Rest our outputs
    output.clear();
    output.emplace_back();
    auto* curr = &output.back();

    // Bitmask of all triangles in mesh to determine whether a specific one has been added.
    std::vector<bool> checklist;
    checklist.resize(triCount);

    std::vector<float> candidateScore;
    candidateScore.resize(triCount);

    std::vector<XMFLOAT3> m_positions;
    std::vector<XMFLOAT3> normals;
    std::vector<std::pair<uint32_t, float>> candidates;
    std::unordered_set<uint32_t> candidateCheck;

    XMVECTOR psphere, normal;
    psphere = g_XMZero;
    normal = g_XMIdentityR0;

    // Arbitrarily start at triangle zero.
    uint32_t triIndex = 0;
    candidates.push_back(std::make_pair(triIndex, 0.0f));
    candidateCheck.insert(triIndex);
    
    // Continue adding triangles until 
    while (!candidates.empty())
    {
        uint32_t index = candidates.back().first;
        candidates.pop_back();

        T tri[3];
        //=
        //{
        //    indices[index * 3] + vertexStart,
        //    indices[index * 3 + 1] + vertexStart,
        //    indices[index * 3 + 2] + vertexStart,
        //};
        tri[0] = indices[index * 3] + vertexStart;
        tri[1] = indices[index * 3 + 1] + vertexStart;
        tri[2] = indices[index * 3 + 2] + vertexStart;

        assert(tri[0] < vertexCount);
        assert(tri[1] < vertexCount);
        assert(tri[2] < vertexCount);

        // Try to add triangle to meshlet
        if (AddToMeshlet(maxVerts, maxPrims, *curr, tri, index))
        {
            // Success! Mark as added.
            checklist[index] = true;

            // Add m_positions & normal to list
            XMFLOAT3 points[3] =
            {
                positions[tri[0]],
                positions[tri[1]],
                positions[tri[2]],
            };

            m_positions.push_back(points[0]);
            m_positions.push_back(points[1]);
            m_positions.push_back(points[2]);

            XMFLOAT3 Normal;
            XMStoreFloat3(&Normal, ComputeNormal(points));
            normals.push_back(Normal);

            // Compute new bounding sphere & normal axis
            psphere = MinimumBoundingSphere(m_positions.data(), static_cast<uint32_t>(m_positions.size()));
            
            XMVECTOR nsphere = MinimumBoundingSphere(normals.data(), static_cast<uint32_t>(normals.size()));
            normal = XMVector3Normalize(nsphere);

            // Find and add all applicable adjacent triangles to candidate list
            const uint32_t adjIndex = index * 3;

            uint32_t adj[3] =
            {
                adjacency[adjIndex],
                adjacency[adjIndex + 1],
                adjacency[adjIndex + 2],
            };

            for (uint32_t i = 0; i < 3u; ++i)
            {
                // Invalid triangle in adjacency slot
                if (adj[i] == -1)
                    continue;
                
                // Already processed triangle
                if (checklist[adj[i]])
                    continue;

                // Triangle already in the candidate list
                if (candidateCheck.count(adj[i]))
                    continue;

                candidates.push_back(std::make_pair(adj[i], FLT_MAX));
                candidateCheck.insert(adj[i]);
            }

            // Re-score remaining candidate triangles
            for (uint32_t i = 0; i < static_cast<uint32_t>(candidates.size()); ++i)
            {
                uint32_t candidate = candidates[i].first;

                T triIndices[3];
                //=
                //{
                //    indices[candidate * 3] + vertexStart,
                //    indices[candidate * 3 + 1] + vertexStart,
                //    indices[candidate * 3 + 2] + vertexStart,
                //};
                triIndices[0] = indices[candidate * 3] + vertexStart;
                triIndices[1] = indices[candidate * 3 + 1] + vertexStart;
                triIndices[2] = indices[candidate * 3 + 2] + vertexStart;

                assert(triIndices[0] < vertexCount);
                assert(triIndices[1] < vertexCount);
                assert(triIndices[2] < vertexCount);

                XMFLOAT3 triVerts[3] =
                {
                    positions[triIndices[0]],
                    positions[triIndices[1]],
                    positions[triIndices[2]],
                };

                candidates[i].second = ComputeScore(*curr, psphere, normal, triIndices, triVerts);
            }

            // Determine whether we need to move to the next meshlet.
            if (IsMeshletFull(maxVerts, maxPrims, *curr))
            {
                m_positions.clear();
                normals.clear();
                candidateCheck.clear();

                // Use one of our existing candidates as the next meshlet seed.
                if (!candidates.empty())
                {
                    candidates[0] = candidates.back();
                    candidates.resize(1);
                    candidateCheck.insert(candidates[0].first);
                }

                curr->BoundingSphere = psphere;
                output.emplace_back();
                curr = &output.back();
            }
            else
            {
                std::sort(candidates.begin(), candidates.end(), &CompareScores);
            }
        }
        else
        {
            if (candidates.empty())
            {
                m_positions.clear();
                normals.clear();
                candidateCheck.clear();

                curr->BoundingSphere = psphere;
                output.emplace_back();
                curr = &output.back();
            }
        }

        // Ran out of candidates; add a new seed candidate to start the next meshlet.
        if (candidates.empty())
        {
            bool cal = false;
            if (!cal)
            {
                while (triIndex < triCount && checklist[triIndex])
                    ++triIndex;

                if (triIndex == triCount)
                    break;

                candidates.push_back(std::make_pair(triIndex, 0.0f));
                candidateCheck.insert(triIndex);
            }
            else
            {
                // compute candidate score and leave the highest score
                triIndex = 0;
                float lowestScore = std::numeric_limits<float>::max();
                uint32_t highestCandidate = 0;
                cross::threading::ParallelFor(static_cast<SInt32>(triCount), [=, &candidateScore](auto triIndex) {
                    // while (triIndex < triCount){
                    // uint32_t triIndex = index;
                    if (!checklist[triIndex])
                    {
                        uint32_t candidate = triIndex;

                        T triIndices[3];
                        //= {
                        //    indices[candidate * 3],
                        //    indices[candidate * 3 + 1],
                        //    indices[candidate * 3 + 2],
                        //};
                        triIndices[0] = indices[candidate * 3] + vertexStart;
                        triIndices[1] = indices[candidate * 3 + 1] + vertexStart;
                        triIndices[2] = indices[candidate * 3 + 2] + vertexStart;

                        assert(triIndices[0] < vertexCount);
                        assert(triIndices[1] < vertexCount);
                        assert(triIndices[2] < vertexCount);

                        XMFLOAT3 triVerts[3] = {
                            positions[triIndices[0]],
                            positions[triIndices[1]],
                            positions[triIndices[2]],
                        };

                        float score = ComputeScore(*curr, psphere, normal, triIndices, triVerts, true);
                        candidateScore[triIndex] = score;
                        // if (score < lowestScore)
                        //{
                        //     lowestScore = score;
                        //     highestCandidate = candidate;
                        // }
                    }
                    else
                    {
                        candidateScore[triIndex] = std::numeric_limits<float>::max();
                    }
                    //++triIndex;
                });
                for (uint32_t i = 0; i < triCount; ++i)
                {
                    if (candidateScore[i] < lowestScore)
                    {
                        lowestScore = candidateScore[i];
                        highestCandidate = i;
                    }
                }

                if (highestCandidate != 0)
                {
                    candidates.push_back(std::make_pair(highestCandidate, 0.0f));
                    candidateCheck.insert(triIndex);
                }
                else
                {
                    break;
                }
            }
        }
    }

    // The last meshlet may have never had any primitives added to it - in which case we want to remove it.
    if (output.back().PrimitiveIndices.empty())
    {
        output.pop_back();
    }

    if (meshletGroups != nullptr)
    {
        // Calculate external edges for cluster
        uint32_t numExternalEdges = 0;
        for (auto& meshlet : output)
        {
            meshlet.ExternalEdges.resize(3 * meshlet.PrimitiveIndices.size());
            memset(meshlet.ExternalEdges.data(), 0, 3 * meshlet.PrimitiveIndices.size());
            for (int primIdx = 0; primIdx < meshlet.PrimitiveIndices.size(); ++primIdx)
            {
                auto _triIndex = meshlet.TriangleIndices[primIdx];
                uint32_t edges[3] = {3 * _triIndex, 3 * _triIndex + 1, 3 * _triIndex + 2};
                for (int i = 0; i < 3; ++i)
                {
                    auto adjTriIdx = adjacency[edges[i]];
                    // this meshlet doesn't contain the adjacency triangle of the edge, so this is a external edge
                    if (meshlet.TriangleIndexSet.find(adjTriIdx) == meshlet.TriangleIndexSet.end() && meshlet.ExternalEdges[3 * primIdx + i] == 0)
                    {
                        meshlet.ExternalEdges[3 * primIdx + i] = 1;
                        meshlet.NumExternalEdge += 1;
                    }
                }
            }
            numExternalEdges += meshlet.NumExternalEdge;
        }

        auto getEdgeVertIndex = [](int edgeIndex, InlineMeshlet<T>& meshlet, T& vert0, T& vert1) {
            uint32_t primIndex = edgeIndex / 3;
            uint32_t offset = edgeIndex % 3;
            auto& prim = meshlet.PrimitiveIndices[primIndex];
            uint32_t idxs[3] = {prim.i0, prim.i1, prim.i2};
            uint32_t i0 = idxs[offset];
            uint32_t i1 = idxs[(offset + 1) % 3];
            vert0 = meshlet.UniqueVertexIndices[i0];
            vert1 = meshlet.UniqueVertexIndices[i1];
        };

        auto cmpFloat3 = [](const XMFLOAT3& a, const XMFLOAT3& b) { return a.x == b.x && a.y == b.y && a.z == b.z; };

        struct ExternalEdge
        {
            uint32_t MeshletIndex;
            int EdgeIndex;
        };
        std::vector<ExternalEdge> externalEdges;
        std::unordered_map<uint32_t, std::vector<uint32_t>> externalEdgeHash;
        externalEdges.resize(numExternalEdges);
        uint32_t externalEdgeOffset = 0;
        for (uint32_t meshletIndex = 0; meshletIndex < output.size(); ++meshletIndex)
        {
            auto& meshlet = output[meshletIndex];
            for (int edgeIndex = 0; edgeIndex < meshlet.ExternalEdges.size(); ++edgeIndex)
            {
                if (meshlet.ExternalEdges[edgeIndex])
                {
                    T vert0, vert1;
                    getEdgeVertIndex(edgeIndex, meshlet, vert0, vert1);

                    // calculate hash for this edge
                    XMFLOAT3 position0 = *(positions + vert0);
                    XMFLOAT3 position1 = *(positions + vert1);
                    uint32_t hash0 = HashPosition(position0);
                    uint32_t hash1 = HashPosition(position1);
                    uint32_t hash = Murmur32({hash0, hash1});

                    externalEdges[externalEdgeOffset] = {meshletIndex, edgeIndex};
                    externalEdgeHash[hash].push_back(externalEdgeOffset);
                    ++externalEdgeOffset;
                }
            }
        }

        // Build meshlet adjacency
        for (uint32_t meshletIndex = 0; meshletIndex < output.size(); ++meshletIndex)
        {
            auto& meshlet = output[meshletIndex];
            for (int i = 0; i < meshlet.ExternalEdges.size(); ++i)
            {
                if (meshlet.ExternalEdges[i])
                {
                    T vert0, vert1;
                    getEdgeVertIndex(i, meshlet, vert0, vert1);

                    // calculate hash for this edge
                    XMFLOAT3 position0 = *(positions + vert0);
                    XMFLOAT3 position1 = *(positions + vert1);
                    uint32_t hash0 = HashPosition(position0);
                    uint32_t hash1 = HashPosition(position1);
                    uint32_t hash = Murmur32({hash1, hash0});

                    if (externalEdgeHash.find(hash) != externalEdgeHash.end())
                    {
                        for (auto edgeIndex : externalEdgeHash[hash])
                        {
                            ExternalEdge& externalEdge = externalEdges[edgeIndex];
                            if (externalEdge.MeshletIndex == meshletIndex)
                                continue;
                            auto& externalMeshlet = output[externalEdge.MeshletIndex];
                            if (externalMeshlet.ExternalEdges[externalEdge.EdgeIndex])
                            {
                                T externalVert0, externalVert1;
                                getEdgeVertIndex(externalEdge.EdgeIndex, externalMeshlet, externalVert0, externalVert1);
                                XMFLOAT3 externalPosition0 = *(positions + externalVert0);
                                XMFLOAT3 externalPosition1 = *(positions + externalVert1);
                                if (cmpFloat3(position0, externalPosition1) && cmpFloat3(position1, externalPosition0))
                                {
                                    if (meshlet.AdjacentMeshlet.find(externalEdge.MeshletIndex) == meshlet.AdjacentMeshlet.end())
                                    {
                                        meshlet.AdjacentMeshlet[externalEdge.MeshletIndex] = 0;
                                    }
                                    meshlet.AdjacentMeshlet[externalEdge.MeshletIndex] += 1;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Build cluster groups
        //std::vector<MeshletGroup> meshgrps;
        std::vector<std::pair<uint32_t, float>> _candidates;
        std::unordered_set<uint32_t> _candidateCheck;
        std::vector<bool> _checklist;
        _checklist.resize(output.size(), false);
        psphere = g_XMZero;

        meshletGroups->push_back(MeshletGroup());
        MeshletGroup* curGrp = &meshletGroups->back();

        // Arbitrarily start at first.
        uint32_t meshletIndex = 0;
        _candidates.push_back(std::make_pair(meshletIndex, 0.0f));
        _candidateCheck.insert(meshletIndex);
        while (!_candidates.empty())
        {
            uint32_t index = _candidates.back().first;
            _candidates.pop_back();

            if (curGrp->Children.size() < 32)
            {
                auto& meshlet = output[index];
                if (curGrp->Children.size() == 0)
                {
                    psphere = meshlet.BoundingSphere;
                }
                else
                {
                    // merge bounding shpere
                    psphere = MergeBoundingSphere(psphere, meshlet.BoundingSphere);
                }

                _checklist[index] = true;
                curGrp->Children.push_back(index);

                for (auto& _pair : meshlet.AdjacentMeshlet)
                {
                    auto adjIndex = _pair.first;
                    if (_checklist[adjIndex])
                        continue;

                    if (_candidateCheck.count(adjIndex))
                        continue;
                    auto& adjBoundingSphere = output[adjIndex].BoundingSphere;
                    float distance = XMVectorGetX(XMVector3Length(psphere - adjBoundingSphere));
                    float localityScore = log(XMVectorGetW(psphere) / distance + 1.0f);
                    float score = _pair.second * 1.0f + localityScore;
                    _candidates.push_back(std::make_pair(adjIndex, score));
                    _candidateCheck.insert(adjIndex);
                }
                std::sort(_candidates.begin(), _candidates.end(), &CompareScores);
            }
            else
            {
                _candidateCheck.clear();
                if (!_candidates.empty())
                {
                    _candidates[0] = _candidates.back();
                    _candidates.resize(1);
                    _candidateCheck.insert(_candidates[0].first);
                }

                // new group
                curGrp->BoundingSphere = psphere;
                meshletGroups->push_back(MeshletGroup());
                curGrp = &meshletGroups->back();
            }

            // ran out of candidate
            if (_candidates.empty())
            {
                while (meshletIndex < output.size() && _checklist[meshletIndex])
                    ++meshletIndex;

                if (meshletIndex == output.size())
                    break;

                _candidates.push_back(std::make_pair(meshletIndex, 0.0f));
                _candidateCheck.insert(meshletIndex);
            }
        }
        // last group
        curGrp->BoundingSphere = psphere;

        // build meshlet group hierarchy
        // TODO(dobeye): more reasonable, think of spatial locality
        auto numGroupLeft = meshletGroups->size();
        //uint32_t handleGroupIndex = 0;
        psphere = g_XMZero;
        meshletGroups->push_back(MeshletGroup());
        curGrp = &meshletGroups->back();
        curGrp->MipLevel = 1;
        while (numGroupLeft > 1)
        {
            auto& group = (*meshletGroups)[handleGroupIndex];
            if (curGrp->GroupChildren.size() < 8 && curGrp->MipLevel == group.MipLevel + 1)
            {
                if (curGrp->GroupChildren.size() == 0)
                {
                    psphere = group.BoundingSphere;
                }
                else
                {
                    // merge bounding shpere
                    psphere = MergeBoundingSphere(psphere, group.BoundingSphere);
                }

                group.ParentIndex = uint32_t(meshletGroups->size()) - 1;
                curGrp->GroupChildren.push_back(handleGroupIndex);
                ++handleGroupIndex;
                numGroupLeft = meshletGroups->size() - handleGroupIndex;
            }
            else
            {
                // new group
                curGrp->BoundingSphere = psphere;
                meshletGroups->push_back(MeshletGroup());
                curGrp = &meshletGroups->back();
                curGrp->MipLevel = group.MipLevel + 1;
            }
        }
        ++handleGroupIndex;
        curGrp->BoundingSphere = psphere;
    }
}
