#include "EnginePrefix.h"
#include "AssetStreaming.h"
#include "ResourceManager.h"
#include "FetchResource.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/EngineGlobal.h"

#include <thread>
#include <future>

DECLARE_CPU_TIMING_GROUP(GroupAssetStreaming);

namespace cross
{
    std::string ConvertAssetPath(const std::string& inAssetPath)
    {
        if (inAssetPath == "default_texture")
        {
            return gResourceMgr.GetDefaultTexturePath();
        }
        else if (inAssetPath == "black_texture")
        {
            return gResourceMgr.GetBlackTexturePath();
        }
        else
        {
            return inAssetPath;
        }
    }

    AssetStreamingManager* AssetStreamingManager::Get()
    {
        static AssetStreamingManager instance;
        return &instance;
    }

    AssetStreamingPtr AssetStreamingManager::RequestAsyncLoad(const std::string& assetPath, bool needPost, std::function<void(ResourcePtr)>&& completionEvent, StreamingPriority priority)
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "RequestAsyncLoad");

        auto streamingPtr = AssetStreamingHandle::Create(gResourceMgr.ConvertGuidToPath(assetPath), std::move(completionEvent), needPost);
        if (streamingPtr->CheckAndCompleteStreaming())
        {
            Assert(streamingPtr->GetResource());
        }
        else
        {
            AddAsyncLoadRequests(streamingPtr, priority);
        }

        return streamingPtr;
    }

    bool AssetStreamingManager::CancelLoadRequest(AssetStreamingPtr streamingPtr)
    {
        std::scoped_lock lock(mRequestQueuesMutex);

        if (AssetStreamingContext::StreamingStage::Queued != streamingPtr->GetContext()->GetStreamingStage())
        {
            return false;
        }

        for (UInt32 priority = 0; priority != static_cast<UInt32>(AssetStreamingManager::StreamingPriority::Num); priority++)
        {
            std::queue<AssetStreamingPtr> requests;
            while (!mRequestQueues[priority].empty())
            {
                if (mRequestQueues[priority].front() != streamingPtr)
                {
                    requests.push(mRequestQueues[priority].front());
                }
                mRequestQueues[priority].pop();
            }

            Assert(mRequestQueues[priority].empty());
            mRequestQueues[priority].swap(requests);
        }

        return true;
    }

    ResourcePtr AssetStreamingManager::LoadSynchronously(const std::string& assetPath, bool needPostDeserialize)
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "LoadSynchronously");

        // such as MeshAssetData line:1240,we need return null ptr
        if (&cross::EngineGlobal::Inst())
        {
            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor || 
                EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeHeadless)
            {
                if (!gResourceMgr.CheckFileByGuid(assetPath))
                {
                    LOG_ERROR("Miss File {0}", gResourceMgr.ConvertGuidToPath(assetPath));
                    return ResourcePtr(nullptr);
                }
            }
        }

        auto filePath = gResourceMgr.ConvertGuidToPath(assetPath);

        if (auto res = gResourceMgr.Find(filePath.c_str()))
        {
            return res;
        }

      
        // if the returned result is a guid, means the path is incorrect
        if (CrossUUID::CheckVaid(filePath) || filePath.empty())
        {
            LOG_ERROR("Miss File {0}", filePath);
            return ResourcePtr(nullptr);
        }

        auto streamingPtr = RequestAsyncLoad(filePath, needPostDeserialize);
        streamingPtr->WaitForStreaming();

        return streamingPtr->GetResource();
    }

    ResourcePtr AssetStreamingManager::GetResource(const std::string& inAssetPath, bool forceLoad, bool needPostDeserialize)
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "GetResource");

        if (forceLoad && EngineGlobal::GetSettingMgr()->GetUseAsyncLoading() && EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpType::AppStartUpTypeCrossEditor)
        {
            //Assert(threading::TaskSystem::IsInAsyncThread());
        }

        const auto assetPath = ConvertAssetPath(gResourceMgr.ConvertGuidToPath(inAssetPath));
        std::string theabsolutePath{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(assetPath, theabsolutePath);
        std::string relativeFileName{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetRelativePath(theabsolutePath, relativeFileName);

        if (relativeFileName == "")
        {
            LOG_ERROR("Faile to get relativeFileName for file {} {}", inAssetPath, assetPath);
        }
        ResourcePtr resourcePtr;
        {
            std::scoped_lock lock(mRequestQueuesMutex);
            resourcePtr = gResourceMgr.Find(relativeFileName.c_str());
            if (!resourcePtr)
            {
                resourcePtr = gResourceMgr.FindDeserialized(relativeFileName.c_str());
            }
        }

        if (forceLoad && !resourcePtr)
        {
            //LOG_ERROR("Missing \"{}\", break here to check for asset dependencies.", relativeFileName.c_str());

            resourcePtr = gResourceMgr.GetResource(relativeFileName.c_str());
            if (resourcePtr)
            {
                if (needPostDeserialize)
                {
                    resourcePtr->PostDeserializeForUse();
                }
                gResourceMgr.AddResource(relativeFileName.c_str(), resourcePtr);
            }
            else
            {
                LOG_ERROR("Cannot load \"{}\".", relativeFileName.c_str());
            }

            return resourcePtr;
        }

        return resourcePtr;
    }

    UInt32 AssetStreamingManager::GetNumInflightLoadRequests()
    {
        std::scoped_lock lock(mRequestQueuesMutex);

        UInt32 num = static_cast<UInt32>(mRequests.size());

        for (UInt32 priority = 0; priority != static_cast<UInt32>(AssetStreamingManager::StreamingPriority::Num); priority++)
        {
            num += static_cast<UInt32>(mRequestQueues[priority].size());
        }

        return num;
    }

    void AssetStreamingManager::AddAsyncLoadRequests(AssetStreamingPtr streamingPtr, StreamingPriority priority)
    {
        std::scoped_lock lock(mRequestQueuesMutex);

        mRequestQueues[static_cast<UInt32>(priority)].push(streamingPtr);

        if (EngineGlobal::GetSettingMgr()->GetUseAsyncLoading())
        {
            if (mNumActiveTasks < mMaxNumStreamingTasks)
            {
                mNumActiveTasks++;

                threading::Async({}, [this](auto)
                {
                    ProcessLoadRequests();
                });
            }
        }
        else
        {
            ProcessLoadRequests();
        }
    }

    ResourcePtr AssetStreamingManager::TryLoadResource(const std::string& assetPath, AssetStreamingPtr parent)
    {
        std::unique_lock uiqueLock(mRequestQueuesMutex);

        if (auto resourcePtr = gResourceMgr.Find(assetPath.c_str()))
        {
            return resourcePtr;
        }

        if (auto iter = mRequests.find(assetPath); iter != mRequests.end())
        {
            if (iter->second.mState == RequestState::State::Deserialize)
            {
                if (iter->second.mEvent)
                {
                    threading::Dispatch({iter->second.mEvent}, [this, parent](auto)
                        {
                            AddAsyncLoadRequests(parent, StreamingPriority::High);
                        });
                }
                else
                {
                    threading::Dispatch([this, parent](auto)
                        {
                            AddAsyncLoadRequests(parent, StreamingPriority::High);
                        });
                }
                return ResourcePtr{};
            }
            else
            {
                Assert(iter->second.mResource);
                parent->GetContext()->PushToPostDeserializeListThreadSafe(assetPath, iter->second.mResource);
                return iter->second.mResource;
            }
        }
        else
        {
            mRequests.emplace(assetPath, RequestState{ threading::DispatchAndHold([](auto) {}), RequestState::State::Deserialize });
            uiqueLock.unlock();

            auto resourcePtr = gResourceMgr.GetResource(assetPath.c_str());
            Assert(resourcePtr);
            if (resourcePtr)
            {
                parent->GetContext()->PushToPostDeserializeListThreadSafe(assetPath, resourcePtr);

                std::scoped_lock scopedLock(mRequestQueuesMutex);
                if (auto it = mRequests.find(assetPath); it != mRequests.end())
                {
                    if(it->second.mEvent)
                        it->second.mEvent->ReleaseTask();
                    it->second.mState = RequestState::State::PostDeserialize;
                    it->second.mResource = resourcePtr;
                }
            }
            else
            {
                mRequests.erase(assetPath);
            }
            return resourcePtr;
        }
    }

    threading::TaskEventPtr cross::AssetStreamingManager::StartLoadResourceRequest(std::shared_ptr<threading::TaskEventArray> predecessors, const std::string& assetPath, AssetStreamingPtr parent)
    {
        std::unique_lock lock(mRequestMutex);
        // if asset is loaded 
        if (auto resourcePtr = gResourceMgr.Find(assetPath.c_str()))
        {
            return threading::Async<ResourcePtr>(*predecessors.get(), [=](auto) {
                parent->GetContext()->IncrementalUpdateProcessingProgress(1);
                return resourcePtr;
            });
        }

        // if asset is in loading
        if (auto iter = mRequests.find(assetPath); iter != mRequests.end())
        {
            // cooperate with CompleteLoadRequest, we only need post deserialize once;
            if (iter->second.mState != RequestState::State::Deserialize)
            {
                parent->GetContext()->PushToPostDeserializeListThreadSafe(assetPath, iter->second.mResource);
            }
            auto post_depend_tasks = std::make_shared<threading::TaskEventArray>();
            predecessors->Traverse([&post_depend_tasks](const auto& taskEventPtr)
            {
                post_depend_tasks->Add(taskEventPtr);
                return false;
            });
            post_depend_tasks->Add(iter->second.mEvent);

            return threading::Async<ResourcePtr>(*post_depend_tasks.get(), [this, assetPath, parent](auto) {
                std::unique_lock lock(mRequestMutex);
                // if asset is loaded
                if (auto resourcePtr = gResourceMgr.Find(assetPath.c_str()))
                {
                    parent->GetContext()->IncrementalUpdateProcessingProgress(1);
                    return resourcePtr;
                }
                auto iter = mRequests.find(assetPath);
                if (iter == mRequests.end())
                {
                    Assert(false);
                }
                parent->GetContext()->IncrementalUpdateProcessingProgress(1);
                return iter->second.mResource;
            });
        }
        else
        {
            mRequests.emplace(assetPath, RequestState{ threading::Async<ResourcePtr>(*predecessors.get(),[this, assetPath, parent](auto) {
                auto resourcePtr = gResourceMgr.GetResource(assetPath.c_str());
                if (resourcePtr)
                {
                    if (!IsScriptFile(assetPath) && parent->NeedPostDeserialize())
                    {
                        resourcePtr->PostDeserializeForUse();
                    }
                    
                    if (IsScriptFile(assetPath))
                    {
                        parent->GetContext()->PushToPostDeserializeListThreadSafe(assetPath, resourcePtr);
                    }

                    std::scoped_lock lock(mRequestMutex);
                    auto iter = mRequests.find(assetPath);
                    {
                        Assert(iter != mRequests.end());
                        // tell that this resource is deserilized;
                        iter->second.mState = RequestState::State::PostDeserialize;
                        iter->second.mResource = resourcePtr;
                    }

                    gResourceMgr.AddResource(assetPath.c_str(), resourcePtr);
                }
                else
                {
                    LOG_ERROR("Cannot find resource: {}", assetPath);
                }
                parent->GetContext()->IncrementalUpdateProcessingProgress(1);
                return resourcePtr;
                }), RequestState::State::Deserialize });

            return mRequests[assetPath].mEvent;
        }
    }

    [[maybe_unused]]
    void AssetStreamingManager::CompleteLoadRequest(const std::string& assetPath, ResourcePtr resource, bool performPostDeserialize)
    {
        LOG_INFO("start complete load request {}", assetPath);
        auto perform = false;
        {
            std::scoped_lock lock(mRequestMutex);

            if (auto iter = mRequests.find(assetPath); iter != mRequests.end())
            {
                // make sure the resource is deserialzied;
                Assert(iter->second.mResource && iter->second.mState == RequestState::State::PostDeserialize);
                perform = true;
                mRequests.erase(iter);

                // important!! @yazhenyuan
                // there is a miss match between addResource and postDeserialize, since it could add to resource manager, 
                // while the resource is never postDeserialized since the performPostDerialize is not set true.
            }
            else
            {
                Assert(resource == gResourceMgr.Find(assetPath.c_str()));
                perform = true;
                LOG_INFO("A rare case deteted {} {} {}", assetPath, perform, performPostDeserialize);
            }
        }

        LOG_INFO("end complete load request {}",assetPath);
    }

    void AssetStreamingManager::ProcessLoadRequests()
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "ProcessLoadRequests");

        while (auto streamingPtr = GetNextRequest())
        {
            if (streamingPtr->GetContext()->Tick())
            {
                //LOG_INFO("Loading Asset {} success, triggering", streamingPtr->GetContext()->GetName());
                //streamingPtr->GetContext()->Trigger();
            }
            else
            {
                LOG_ERROR("Loading Asset {} failed to trigger", streamingPtr->GetContext()->GetName());
            }
        }
    }

    AssetStreamingPtr AssetStreamingManager::GetNextRequest()
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "GetNextRequest");

        std::scoped_lock lock(mRequestQueuesMutex);

        for (UInt32 priority = 0; priority != static_cast<UInt32>(AssetStreamingManager::StreamingPriority::Num); priority++)
        {
            if (!mRequestQueues[priority].empty())
            {
                auto streamingPtr = mRequestQueues[priority].front();
                mRequestQueues[priority].pop();

                if (streamingPtr->GetContext()->GetStreamingStage() == AssetStreamingContext::StreamingStage::Queued)
                {
                    streamingPtr->GetContext()->SetStreamingStage(AssetStreamingContext::StreamingStage::Start);
                }

                return streamingPtr;
            }
        }

        mNumActiveTasks--;
        return nullptr;
    }

    bool AssetStreamingContext::Tick()
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "Tick");

        auto Start = std::chrono::steady_clock::now();

        std::unordered_map<std::string, std::pair<std::unordered_set<std::string>, std::unordered_set<std::string>>> file_dependecy;
        std::unordered_set<std::string> files_with_dependency_resolved;

        if (GetStreamingStage() == StreamingStage::Start)
        {
            SCOPED_CPU_TIMING(GroupAssetStreaming, "ResolveDependencies");

            SetStreamingStage(StreamingStage::ResolvingDependencies);

            resource::FetchResource::CheckAndFetch(mAssetPath);

            ResourceMetaHeader header{};

            auto Pushdependency = [&](const  ResourceMetaHeader& header, const std::string& asset, std::queue<std::string> & pendingImports, bool need_store_dependency)
            {
                for (const auto& path : header.mDependency)
                {
                    if (!path.empty())
                    {
                        auto filepath = gResourceMgr.ConvertGuidToPath(path);
                        if (IsScriptFile(filepath) || IsNDAFile(filepath))
                        {
                            std::string relativeFileName{};
                            std::string theabsolutePath{};

                            const auto convertfilePath = ConvertAssetPath(filepath);
                            cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(convertfilePath, theabsolutePath);
                            cross::EngineGlobal::Inst().GetFileSystem()->GetRelativePath(theabsolutePath, relativeFileName);

                            pendingImports.push(relativeFileName);
                            if (need_store_dependency)
                            {
                                file_dependecy[asset].first.insert(relativeFileName);
                                file_dependecy[relativeFileName].second.insert(asset);
                            }
                        }
                    }
                }
            };

            if (gResourceMgr.GetResourceMetaHeader(mAssetPath, header))
            {
                std::queue<std::string> pendingImports;
                // key: file, value <sons(dependents), parents (be depdended on)>
                std::unordered_map<std::string, ResourceMetaHeader> mCachedHeader;
                
                file_dependecy[mAssetPath] = std::pair<std::unordered_set<std::string>, std::unordered_set<std::string>>();
                
                Pushdependency(header, mAssetPath, pendingImports, true);

                while (!pendingImports.empty())
                {
                    auto relativeFileName = pendingImports.front();
                    mImports.push_back(relativeFileName);

                    ResourceMetaHeader localHeader{};
                    bool hasHeader = false;

                    if (mCachedHeader.count(relativeFileName))
                    {
                        hasHeader = true;
                        localHeader = mCachedHeader[relativeFileName];
                    }
                    else
                    {
                        hasHeader = gResourceMgr.GetResourceMetaHeader(relativeFileName, localHeader);
                    }

                    if (hasHeader)
                    {
                        // re push the dependency, so we we have move the dependency result upfront(since we iterate from back) 
                        mCachedHeader[relativeFileName] = localHeader;

                        // only when this file is not visited (dependency is empty)
                        bool need_store_depends = file_dependecy.count(relativeFileName) == 0 || file_dependecy[relativeFileName].first.size() == 0;

                        if (file_dependecy.count(relativeFileName) == 0)
                        {
                            file_dependecy[relativeFileName] = std::pair<std::unordered_set<std::string>, std::unordered_set<std::string>>();
                        }

                        Pushdependency(localHeader, relativeFileName, pendingImports, need_store_depends);
                    }
                    
                    if (file_dependecy[relativeFileName].first.empty())
                    {
                        files_with_dependency_resolved.insert(relativeFileName);
                    }


                    pendingImports.pop();
                }
            }

            if (file_dependecy[mAssetPath].first.empty())
            {
                files_with_dependency_resolved.insert(mAssetPath);
            }
        }

        if (GetStreamingStage() == StreamingStage::ResolvingDependencies || GetStreamingStage() == StreamingStage::Processing)
        {
            SetStreamingStage(StreamingStage::Processing);
            
            mCachedResources.reserve(mImports.size());

            mLoadedAssetsCount = 0;
            mRequestedAssetsCount = static_cast<int>(file_dependecy.size());
            SetProcessingPercentage(0.0f);

            auto dispatch_dependecy = file_dependecy;

            while (!files_with_dependency_resolved.empty())
            {
                SCOPED_CPU_TIMING(GroupAssetStreaming, "LoadResource");

                std::unordered_set<std::string> new_resolved_dependency_files;

                for (auto& assetPath : files_with_dependency_resolved)
                {
                    if (mLoadingTasksMap.count(assetPath))
                    {
                        continue;
                    }

                    auto depend_tasks = std::make_shared<threading::TaskEventArray>();

                    // construct task dependencies
                    for (auto& deps : file_dependecy[assetPath].first)
                    {
                        depend_tasks->Add(mLoadingTasksMap[deps]);
                    }

                    auto task = gAssetStreamingManager->StartLoadResourceRequest(depend_tasks, assetPath, mHandle->shared_from_this());

                    if (!mHandle->NeedPostDeserialize())
                    {
                        mPostDeserializeList.push_back(std::pair{assetPath, ResourcePtr(nullptr)});
                    }

                    mLoadingTasksMap[assetPath] = task;

                    if (!IsScriptFile(assetPath))
                    {
                        mPosttaskSet.insert(assetPath);
                    }

                    {
                        for (auto& itr : dispatch_dependecy[assetPath].second)
                        {
                            dispatch_dependecy[itr].first.erase(assetPath);
                            if (dispatch_dependecy[itr].first.empty())
                            {
                                new_resolved_dependency_files.insert(itr);
                            }
                        }
                    }
                }

                files_with_dependency_resolved = new_resolved_dependency_files;
            }

            dispatch_dependecy.clear();

            // capture streamingPtr to prevent the context being destroyed when out release the ptr;
            mFinalizeTask = threading::Async({ mLoadingTasksMap[mAssetPath] }, [=, streamingPtr = mHandle->shared_from_this()](const auto&)
                {
                    for (auto& itr : mLoadingTasksMap)
                    {
                        mCachedResources[itr.first]= itr.second->GetReturnValue<ResourcePtr>();
                    }

                    if (!streamingPtr->NeedPostDeserialize())
                    {
                        for(auto itr = mPostDeserializeList.begin(); itr != mPostDeserializeList.end();)
                        {
                            if (mCachedResources.count(itr->first) != 0 && mCachedResources[itr->first])
                            {
                                itr->second = mCachedResources[itr->first];
                                itr++;
                            }
                            else
                            {
                                itr = mPostDeserializeList.erase(itr);
                            }
                        }
                    }
                    
                    SCOPED_CPU_TIMING(GroupAssetStreaming, "LoadResource");
                    if (const auto& resourcePtr = mCachedResources[mAssetPath])
                    {
                        Assert(resourcePtr);
                        mHandle->SetResource(resourcePtr);
                    }
                    else
                    {
                        LOG_ERROR("Failed to load {}", mAssetPath);
                    }

                    {
                        SetStreamingStage(StreamingStage::PostDeserialize);

                        std::vector<std::string>().swap(mImports);
                        SetProcessingPercentage(100);
                    }

                    auto End = std::chrono::steady_clock::now();

                    LOG_INFO("Loading {} cost {} ms", mAssetPath, std::chrono::duration_cast<std::chrono::milliseconds>(End - Start).count());
                    streamingPtr->GetContext()->CallCompletionEvent();
                    streamingPtr->GetContext()->Trigger();
                
                });

            file_dependecy.clear();
        }

        return true;
    }

    void cross::AssetStreamingContext::PushToPostDeserializeListThreadSafe(std::string assetName, ResourcePtr resource)
    {
        // only gather this list when post deserialize is needed
        if (mHandle->NeedPostDeserialize())
        {
            std::scoped_lock lock(mPostDeserizlizeListMutex);
            mPostDeserializeList.emplace_back(assetName, resource);
        }
    }

    void cross::AssetStreamingContext::EraseCompletedRequest() 
    {
        std::scoped_lock lock(AssetStreamingManager::Get()->mRequestMutex);
        for (auto& itr : mPosttaskSet)
        {
            auto& mRequests = AssetStreamingManager::Get()->mRequests;
            auto const& assetPath = itr;
            if (auto iter = mRequests.find(assetPath); iter != mRequests.end())
            {
                mRequests.erase(iter);
            }
        }
    }

    void cross::AssetStreamingContext::PostDeserialize()
    {
        EraseCompletedRequest();

        for (auto& itr : mPostDeserializeList)
        {
            if (IsScriptFile(itr.first) && itr.second && itr.second->NeedPostDeserialized())
            {
                itr.second->PostDeserializeForUse();
            }
        }
    }

    void AssetStreamingContext::CallCompletionEvent() const
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "CompletionEvent");

        if (mCompletionEvent)
        {
            mCompletionEvent(mHandle->GetResource());
        }
    }

    void AssetStreamingContext::IncrementalUpdateProcessingProgress(int count)
    {
        int loadedAssetsCount = mLoadedAssetsCount.fetch_add(count) + count;
        //LOG_DEBUG("Update streaming process of {}: {} / {}", mAssetPath, loadedAssetCount, mRequestedAssetsCount);
        SetProcessingPercentage(1.0f * loadedAssetsCount / mRequestedAssetsCount * 100.0f);
    }

    AssetStreamingHandle::AssetStreamingHandle(const std::string& assetPath, std::function<void(ResourcePtr)> completionEvent, bool doPostDeserialze)
        : mStreamingContext(std::make_unique<AssetStreamingContext>(this, assetPath, std::move(completionEvent)))
        , mDoPostDeserialze(true) // @mindalv always set mDoPostDeserialze true. Do postdeserialize at Async thread for all resource
    {
        mResource = gResourceMgr.Find(assetPath.c_str());

        if (mResource)
        {
            mStreamingContext->SetStreamingStage(AssetStreamingContext::StreamingStage::Finish);
        }
        else
        {
            mStreamingContext->SetStreamingStage(AssetStreamingContext::StreamingStage::Queued);
        }
    }

    void AssetStreamingHandle::WaitForStreaming()
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "WaitForStreaming");

        //Assert(threading::TaskSystem::IsInGameThread());

        while (!CheckAndCompleteStreaming())
        {
            GetContext()->Wait();
        }
    }
    
    bool AssetStreamingHandle::CheckAndCompleteStreaming()
    {
        if (mStreamingContext->GetStreamingStage() == AssetStreamingContext::StreamingStage::PostDeserialize)
        {
            PostDeserialize();
            Assert(mStreamingContext->GetStreamingStage() == AssetStreamingContext::StreamingStage::Finish);

            return true;
        }

        return mStreamingContext->GetStreamingStage() == AssetStreamingContext::StreamingStage::Finish;
    }

    void AssetStreamingHandle::PostDeserialize()
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "PostDeserialize");

        GetContext()->PostDeserialize();

        if (mDoPostDeserialze)
        {
           std::vector<std::pair<std::string, ResourcePtr>>().swap(GetContext()->GetPostDeserializeList());
        }
        GetContext()->SetStreamingStage(AssetStreamingContext::StreamingStage::Finish);
        GetContext()->CallCompletionEvent();
    }
}
