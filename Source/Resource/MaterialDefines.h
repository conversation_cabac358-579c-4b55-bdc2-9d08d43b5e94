#pragma once
#include "PlatformDefs.h"
#include "CEMetaMacros.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "Resource/MaterialDefines.generated.h"
#include "Resource/resourceforward.h"
#include "NativeGraphicsInterface/NGI.h"

namespace cross {
enum class CEMeta(Serialize, Cli) MaterialDomain : SInt32
{
    /** The material's attributes describe a 3d surface. */
    Surface,
    /** The material's attributes describe a deferred decal, and will be mapped onto the decal's frustum. */
    MeshDecal,
    /** The material's attributes describe a light's distribution. */
    // LightFunction,
    /** The material's attributes describe a 3d volume. */
    // Volume,
    /** The material will be used in a custom post process pass. */
    PostProcess,
    Foliage,
    /** The material will be used for UMG or Slate UI */
    // UI,
    /** The material will be used for runtime virtual texture (Deprecated). */
    // RuntimeVirtualTexture,
};

/**
 * Specifies the overal rendering/shading model for a material
 * @warning Check UMaterialInstance::Serialize if changed!
 */
enum class CEMeta(Serialize, Cli) MaterialShadingModel : SInt32
{
    Standard = 0,
    Unlit = 1,
    TwosidedFoliage = 2,
    ImposterFoliage = 3,
    Subsurface = 4,
    // Unlit,
    // DefaultLit,
    // Subsurface,
    // PreintegratedSkin,
    // ClearCoat,
    // SubsurfaceProfile,
    // TwoSidedFoliage,
    // Hair,
    // Cloth,
    // Eye,
    // SingleLayerWater,
    // ThinTranslucent,
    // Strata,
    /** Shading model will be determined by the Material Expression Graph,
        by utilizing the 'Shading Model' MaterialAttribute output pin. */
    // FromMaterialExpression,
};

/**
 * The blending mode for materials
 * @warning This is mirrored in Lightmass, be sure to update the blend mode structure and logic there if this changes.
 * @warning Check UMaterialInstance::Serialize if changed!!
 */
enum class CEMeta(Serialize, Cli) MaterialBlendMode : SInt32
{
    Opaque = 0,
    Masked = 1,
    Translucent = 2,
    Additive = 3,
    Modulate = 4,
    AlphaComposite = 5,
    // AlphaHoldout,
    // TranslucentColoredTransmittance,
};

enum class CEMeta(Serialize, Cli) TerrainMode : SInt32
{
    Land,
    Ocean,
    WeightBlend
};

enum class CEMeta(Serialize, Cli) BlendableLocation : SInt32
{
    AfterToneMapping,
    BeforeToneMapping,
    ReplacingToneMapper,
    BeforeTranslucency,
    SSRInput,
};

struct CEMeta(Cli) MaterialParameter : public gbf::reflection::RttiBase
{
    CEMeta(Cli)
    bool Enable;

    CEMeta(Cli)
    std::string ParameterName;

    CEMeta(Cli)
    std::string DisplayName;

    float SortPriority;

    uint32_t ReferenceCount;
};

struct CEMeta(Cli) MaterialParameterBool : public MaterialParameter
{
    CEMeta(Cli)
    bool Value;
};

struct CEMeta(Cli) MaterialParameterScalar : public MaterialParameter
{
    CEMeta(Cli)
    float Value;

    CEMeta(Cli)
    float SliderMin;

    CEMeta(Cli)
    float SliderMax;
};

struct CEMeta(Cli) MaterialParameterVector : public MaterialParameter
{
    CEMeta(Cli, EditorPropertyInfo(PropertyType = "Float4AsColor"))
    Float4 Value;
};

struct CEMeta(Cli) MaterialParameterTexture : public MaterialParameter
{
    CEMeta(Cli, EditorPropertyInfo(PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Texture))
    std::string Value;
};

struct CEMeta(Cli) MaterialParameterTextureVirtual : public MaterialParameter
{
    CEMeta(Cli, EditorPropertyInfo(PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_TextureVirtual))
    std::string Value;
};

struct CEMeta(Cli) MaterialParameterGroup
{
    CEMeta(Cli)
    std::string Name;

    CEMeta(Cli)
    std::vector<MaterialParameter*> Parameters;
};

struct CEMeta(Cli) MaterialPassState
{
    CE_Serialize_Deserialize;

    CEProperty(Cli, EditorPropertyInfo(PropertyType = "Struct"))
    NGIBlendStateDescForEditor BlendStateDesc;
    CEProperty(Cli, EditorPropertyInfo(PropertyType = "Struct"))
    NGIDepthStencilStateDesc DepthStencilStateDesc;
    CEProperty(Cli, EditorPropertyInfo(PropertyType = "Struct"))
    NGIRasterizationStateDesc RasterizationStateDesc;
    CEProperty(Cli, EditorPropertyInfo(PropertyType = "Struct"))
    NGIDynamicStateDesc DynamicStateDesc;
    CEProperty(Cli)
    UInt32 RenderGroup;
};

struct CEMeta(Cli) RenderStateInfo
{
    CE_Serialize_Deserialize;

    CEMeta(Serialize, Cli, EditorPropertyInfo(PropertyType = "List"))
    std::vector<MaterialPassState> mRenderStates;
    CEMeta(Serialize, Cli, EditorPropertyInfo(PropertyType = "List"))
    std::vector<std::string> mPassID;
};

struct CEMeta(Serialize, Cli) MaterialDefines
{
    CEGeneratedCode(MaterialDefines);
    CE_Serialize_Deserialize;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    MaterialDomain Domain;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material", DisplayName = "Blend Mode"))
    MaterialBlendMode BlendMode;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material", DisplayName = "Shading Model"))
    MaterialShadingModel ShadingModel;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material", DisplayName = "Two Sided"))
    bool TwoSided;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material", DisplayName = "Use Material Attributes"))
    bool UseMaterialAttributes = false;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material", ValueMin = "-200", ValueMax = "200", DisplayName = "Render Group Bias"))
    int RenderGroupBias;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Translucency", DisplayName = "Force Disable Exponential Fog"))
    bool ForceDisableExponentialFog;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Translucency", DisplayName = "Force Disable Volumetric Fog"))
    bool ForceDisableVolumetricFog;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Translucency", DisplayName = "Force Disable Cloud Fog"))
    bool ForceDisableCloudFog;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Translucency", DisplayName = "Force Disable SSR"))
    bool ForceDisableSSR;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Translucency", DisplayName = "Enable Separate Translucency"))
    bool EnableSeparateTranslucency;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Usage", DisplayName = "Used With Skeletal Mesh"))
    bool UsedWithSkeletalMesh;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Usage", DisplayName = "Used With Local Space Particle"))
    bool UsedWithLocalSpaceParticle;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Usage", DisplayName = "Used With Global Space Particle"))
    bool UsedWithGlobalSpaceParticle;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Usage", DisplayName = "Used With Terrain"))
    bool UsedWithTerrain;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Terrain", DisplayName = "Terrain Mode"))
    TerrainMode TerrainMode;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "PostProcess", DisplayName = "Blendable Location"))
    BlendableLocation PostProcessBlendableLocation;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "PostProcess", DisplayName = "Output Alpha"))
    bool PostProcessEnableOutputAlpha;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "PostProcess", DisplayName = "Blendable Priority"))
    float PostProcessBlendablePriority;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "PostProcess", DisplayName = "Is Blendable"))
    bool PostProcessIsBlendable;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Debug", DisplayName = "Enable Debug Symbol"))
    bool EnableDebugSymbol;

    CEMeta(Serialize, Cli, EditorPropertyInfo(bHide = true))
    bool EnableAdvancedMode;

    CEMeta(Cli, EditorPropertyInfo(bHide = true))
    RenderStateInfo AdvancedRenderStates;
};

namespace material_literal {
#define MATERIAL_LITERAL(LITERAL) constexpr auto LITERAL = #LITERAL;
    MATERIAL_LITERAL(debug_symbol);
    MATERIAL_LITERAL(compatible_symbol);
}   // namespace material_literal

struct CEMeta(Serialize, Cli) MaterialInstanceDefines
{
    CEGeneratedCode(MaterialInstanceDefines);
    CE_Serialize_Deserialize;

    CEMeta(Cli, EditorPropertyInfo(Category = "Material", bReadOnly = false, PropertyType = "StringAsResource", FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material, ObjectClassID2 = ClassIDType.CLASS_Fx))
    std::string ParentMaterial;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    bool BlendModeEnable = false;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    MaterialBlendMode BlendMode;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    bool ShadingModelEnable = false;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    MaterialShadingModel ShadingModel;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    bool TwoSidedEnable = false;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material"))
    bool TwoSided;

    CEMeta(Serialize, Cli, EditorPropertyInfo(Category = "Material", ValueMin = "- 200", ValueMax = "200"))
    int RenderGroupBias = 0;

    CEMeta(Cli, EditorPropertyInfo(bHide = true))
    std::vector<MaterialParameterGroup> ParameterGroups;
};

/** defines how MipValue is used */
enum class CEMeta(Serialize, Cli) TextureMipValueMode
{
    /* Use hardware computed sample's mip level with automatic anisotropic filtering support. */
    None,

    /* Explicitly compute the sample's mip level. Disables anisotropic filtering. */
    MipLevel,

    /* Bias the hardware computed sample's mip level. Disables anisotropic filtering. */
    MipBias,

    /* Explicitly compute the sample's DDX and DDY for anisotropic filtering. */
    Derivative,
};

enum class CEMeta(Serialize, Cli) TextureFilter
{
    Nearest,
    Bilinear,
    Trilinear,
    Anisotropic,
};

enum class CEMeta(Serialize, Cli) TextureAddressMode
{
    Wrap,
    Clamp,
    Mirror,
};

struct CEMeta(Serialize, Cli) Resource_API SamplerState
{
    CE_Serialize_Deserialize;

    CEMeta(Serialize, Cli)
    TextureMipValueMode MipValueMode = TextureMipValueMode::None;

    CEMeta(Serialize, Cli)
    TextureFilter Filter = TextureFilter::Trilinear;

    CEMeta(Serialize, Cli, EditorPropertyInfo(ValueMin = "1", ValueMax = "16", ValueStep = "1"))
    UInt8 AnisotropicLevel = 8;

    CEMeta(Serialize, Cli)
    TextureAddressMode AddressMode = TextureAddressMode::Wrap;

    friend bool operator==(const SamplerState& a, const SamplerState& b)
    {
        return a.MipValueMode == b.MipValueMode && a.Filter == b.Filter && a.AddressMode == b.AddressMode;
    }
};

struct CEMeta(Serialize, Cli) MaterialFunctionDefines
{
    CEGeneratedCode(MaterialFunctionDefines);
    CE_Serialize_Deserialize;

    CEMeta(Serialize, Cli)
    bool ExposeToLibrary = true;
};

constexpr const char* MATERIAL_ERROR_FLAG_NAME = "MATERIAL_ERROR_FLAG";

}   // namespace cross