#pragma once
#include "EnginePrefix.h"
#include "Resource/Resource.h"
#include "Texture/Texture.h"

namespace cross::resource
{
    struct FontAtlas
    {
        CEMeta(Serialize) std::string type;
        CEMeta(Serialize) int distanceRange;
        CEMeta(Serialize) float size;
        CEMeta(Serialize) int width;
        CEMeta(Serialize) int height;
        CEMeta(Serialize) std::string yOrigin;

        CE_Serialize_Deserialize;
    };

    struct FontMetrics
    {
        CEMeta(Serialize) float emSize;
        CEMeta(Serialize) float lineHeight;
        CEMeta(Serialize) float ascender;
        CEMeta(Serialize) float descender;
        CEMeta(Serialize) float underlineY;
        CEMeta(Serialize) float underlineThickness;

        CE_Serialize_Deserialize;
    };

    struct FontBound
    {
        CEMeta(Serialize) float left;
        CEMeta(Serialize) float bottom;
        CEMeta(Serialize) float right;
        CEMeta(Serialize) float top;

        CE_Serialize_Deserialize;
    };

    struct FontGlyph
    {
        CEMeta(Serialize) int unicode;
        CEMeta(Serialize) float advance;
        CEMeta(Serialize) FontBound planeBounds;
        CEMeta(Serialize) FontBound atlasBounds;

        CE_Serialize_Deserialize;
    };

    struct FontKerning
    {
        CEMeta(Serialize) int unicode1;
        CEMeta(Serialize) int unicode2;
        CEMeta(Serialize) float advance;

        CE_Serialize_Deserialize;
    };

    struct FontInfo
    {
        CEMeta(Serialize) std::string name;
        CEMeta(Serialize) FontAtlas atlas;
        CEMeta(Serialize) FontMetrics metrics;
        CEMeta(Serialize) std::vector<FontGlyph> glyphs;
        CEMeta(Serialize) std::vector<FontKerning> kerning;

        CE_Serialize_Deserialize;
    };

    class Resource_API CEMeta(Script) FontResource : public Resource
    {
    public:
        static int GetClassIDStatic()
        {
            return ClassID(FontResource);
        }

        FontResource() {
            INIT_RTTI_IN_CONSTRUCTOR
        }

        ~FontResource() {}

        bool Serialize(const std::string& path);

        bool Deserialize(DeserializeNode const& s) override;

    public:
        TexturePtr GetMSDFTexture() { return mMSDFTexture; }

        void SetMSDFTexture(TexturePtr texture) { mMSDFTexture = texture; }

        FontInfo& GetFontInfo() { return mFontInfo; }

        const FontInfo& GetFontInfo() const { return mFontInfo; }

        void SetFontInfoByJson(DeserializeNode const& s);

        const std::string& GetFontName() { return mFontInfo.name; }

        void SetFontName(std::string name) { mFontInfo.name = name; }

        void Clear();

    private:
        FontInfo mFontInfo;
        TexturePtr mMSDFTexture;

        static constexpr std::string_view FONT_INFO_KEY = "FontInfo";
        static constexpr std::string_view MSDF_TEXTURE_KEY = "MSDFTexture";
    };
}