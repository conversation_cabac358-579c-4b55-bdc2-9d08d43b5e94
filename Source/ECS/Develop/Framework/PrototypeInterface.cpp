#include "EnginePrefix.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/PrototypeInterface.h"

// prototype interface
namespace cross::ecs
{
    PrototypePtr PrototypeRegistry::GetOrCreatePrototype(const ComponentDesc** types, size_t count, bool bIsRenderStore)
    {
        if (!types || count == 0)
            return PrototypePtr();

        ComponentBitMask mask;
        for (size_t i = 0; i < count; i++)
        {
            auto* desc = *(types + i);
            mask.Set(desc->GetMaskBitIndex(), true);
        }

        return GetOrCreatePrototypeImp(mask, !bIsRenderStore);
    }

    PrototypePtr PrototypeRegistry::GetOrCreatePrototypeImp(ComponentBitMask& mask, bool isGameType)
    {
        auto& typeMap = isGameType ? mGameTypeMap : mRenderTypeMap;

        mask.Set(mIdComponentBitIndex, true);

        if (isGameType)
        {
            mask.Set(mMetaComponentBitIndex, true);
        }

        UInt64 hash = mask.CalculateHash();
        if (hash == 0)
        {
            LOG_ERROR("ECS::Prototype is Invalid");
            Assert(false);
            return PrototypePtr();
        }

        {
            std::lock_guard<std::mutex> lockGurad(*mMutex);
            auto it = typeMap.find(hash);
            if (it != typeMap.end())
                return it->second;
        }

        auto compCount = mask.GetPopCount();
        PrototypePtr newType = Prototype::AllocatePrototype(hash, isGameType, (UInt16)compCount, mask);

        SInt16 curIndex = 0;
        for (auto it = mask.GetBitIndexIteratorBegin(); it != mask.GetBitIndexIteratorEnd(); it++)
        {
            SInt32 bitIndex = it.GetBitIndex();
            Assert(bitIndex >= 0);

            auto compDesc = isGameType ? mFramework->GetGameComponentDescByIndex((UInt32)bitIndex)
                : mFramework->GetRenderComponentDescByIndex((UInt32)bitIndex);

            if (isGameType)
                Assert(compDesc->IsGameComponent());
            else
                Assert(compDesc->IsRenderComponent());

            newType->Components[curIndex].Desc = compDesc;
            newType->Components[curIndex].Hash = compDesc->Name.GetHash32();
            newType->Components[curIndex].Index = UInt8(curIndex);

            if (compDesc->GetMaskBitIndex() == mIdComponentBitIndex)
            {
                newType->IDComponentIndex = curIndex;
            }
            else if (compDesc->GetMaskBitIndex() == mMetaComponentBitIndex)
            {
                newType->MetaComponentIndex = curIndex;
            }

            curIndex++;
        }
        Assert(curIndex == (SInt16)compCount);

        InitPrototypeCapacity(newType);

        {
            std::lock_guard<std::mutex> lockGurad(*mMutex);
            //double check
            auto it = typeMap.find(hash);
            if (it != typeMap.end())
            {
                Prototype::Release(newType);
                return it->second;
            }

            PrototypePtr prototypePtr{ newType };
            [[maybe_unused]] auto result = typeMap.emplace(hash, prototypePtr);
            DEBUG_ASSERT(result.second);
            return prototypePtr;
        }
    }

    void PrototypeRegistry::Init(ComponentDescMap& componentsRegistry, Framework& frameworkInstance)
    {
        mFramework = &frameworkInstance;

        mIdComponentBitIndex = EntityIDComponent::GetDesc()->GetMaskBitIndex();
        mMetaComponentBitIndex = EntityMetaComponentG::GetDesc()->GetMaskBitIndex();


        ComponentBitMask mask = mFramework->GetIDComponentDesc()->GetMask();
        mEmptyPrototypeR = GetOrCreatePrototypeImp(mask, false);

        mask.Set(mFramework->GetMetaComponentDesc()->GetMaskBitIndex(), true);
        mEmptyPrototypeG = GetOrCreatePrototypeImp(mask, true);
    }

    PrototypePtr PrototypeRegistry::MergePrototype(std::initializer_list<const PrototypePtr>& prototypes)
    {
        size_t sz = prototypes.size();
        if (sz == 0)
        {
            return PrototypePtr();
        }
        else if (sz == 1)
        {
            return *prototypes.begin();
        }
        else
        {
            ComponentBitMask mask;
            for (const auto& prototype : prototypes)
            {
                for (UInt16 i = 0; i < prototype->ComponentCount; i++)
                {
                    auto& compDesc = prototype->Components[i];
                    mask.Set(compDesc.Desc->GetMaskBitIndex(), true);
                }
            }

            const PrototypePtr& prototype0 = *prototypes.begin();
            if (prototype0->ComponentMask == mask)
                return prototype0;

            return GetOrCreatePrototypeImp(mask, prototype0->IsGameType);
        }
    }

    PrototypeRegistry::PrototypeRegistry()
    {
        mMutex = std::make_unique<std::mutex>();
    }

    PrototypePtr PrototypeRegistry::CalculatePrototypeSub(PrototypePtr const& srcType, PrototypePtr const& subType)
    {
        static ComponentDesc* idCompDesc{ EntityIDComponent::GetDesc() };
        static ComponentDesc* metaCompDesc{ EntityMetaComponentG::GetDesc() };

        Assert(srcType->IsGameType == subType->IsGameType);

        ComponentBitMask mask = srcType->ComponentMask;

        for (UInt32 i = 0; i < subType->ComponentCount; i++)
        {
            auto* desc = subType->Components[i].Desc;
            if (desc != idCompDesc || desc != metaCompDesc)
            {
                mask.Set(desc->GetMaskBitIndex(), false);
            }
        }

        return GetOrCreatePrototypeImp(mask, srcType->IsGameType);
    }

    bool PrototypeRegistry::PrototypeHasComponent(PrototypePtr const& prototype, const ComponentDesc* compDesc)
    {
        //DEBUG_ASSERT(compDesc);
        if (compDesc && prototype->IsGameType == compDesc->IsGameComponent())
        {
            auto maskBitIndex = compDesc->GetMaskBitIndex();
            return prototype->ComponentMask[maskBitIndex];
        }
        return false;
    }

    auto PrototypeRegistry::ProtyotypeGetComponentInfo(PrototypePtr const& prototype, const ComponentDesc* typeInfo) -> ComponentInfo*
    {
        DEBUG_ASSERT(typeInfo);
        if (!typeInfo)
        {
            return nullptr;
        }

        for (UInt16 loop = 0; loop < prototype->ComponentCount; ++loop)
        {
            auto& dataInfo = prototype->Components[loop];
            if (dataInfo.Desc->Name == typeInfo->Name)
            {
                return std::addressof(dataInfo);
            }
        }

        return nullptr;
    }

    void PrototypeRegistry::InitPrototypeCapacity(PrototypePtr prototype)
    {
        /// Estimate a capacity first
        // 1. calculate the accumulation of all components size

        UInt32 allCompDataSize = 0;
        for (UInt16 i = 0; i < prototype->ComponentCount; i++)
        {
            allCompDataSize += prototype->Components[i].Desc->Size;
        }
#if ENABLE_GUARDED_COMPONENT_ACCESS
        allCompDataSize += prototype->ComponentCount * sizeof(AtomicDataRaceGuardPtr);
#endif
        Assert(allCompDataSize < 65535);
        prototype->ComponentDataSize = (UInt16)allCompDataSize;

        // 2. The estimation is the division of Data Part Size of the Chunk & the total component size
        constexpr auto DataBlockSize = Chunk::ChunkSize - sizeof(Chunk);
        UInt32 capacity = DataBlockSize / allCompDataSize + 1;

        // 3. Without considering the alignment, the estimation of the capacity is larger than but very near to the result.
        // So we search the result
        UInt32 chunkSize{ 0 };
#if ENABLE_GUARDED_COMPONENT_ACCESS
        std::vector<UInt32> offsets(prototype->ComponentCount + 1, 0);
#else
        std::vector<UInt32> offsets(prototype->ComponentCount, 0);
#endif
        do
        {
            capacity--;
            chunkSize = ChunkSizeWithCapacity(prototype, capacity, offsets);
        } while (DataBlockSize <= chunkSize);

        // 4. Now we get the Capacity and the offset of each component data
        Assert(prototype->CapacityInChunk == 0);
        prototype->CapacityInChunk = static_cast<UInt16>(capacity);
        for (UInt32 loop = 0; loop < prototype->ComponentCount; ++loop)
        {
            prototype->Components[loop].Offset = offsets[loop];
        }
#if ENABLE_GUARDED_COMPONENT_ACCESS
        prototype->GuardOffset = offsets[prototype->ComponentCount];
#endif
    }

    UInt32 PrototypeRegistry::ChunkSizeWithCapacity(PrototypePtr prototype, UInt32 capacity, std::vector<UInt32>& result)
    {
        DEBUG_ASSERT(prototype && prototype->ComponentCount > 0);
        UInt32 size = sizeof(Chunk);

        // Allocate bit block array if count > 1 after chunk itself
        if (capacity > BitArray::BitsPerBlock)
        {
            auto/*UInt*/ blockCount = capacity / BitArray::BitsPerBlock + (capacity % BitArray::BitsPerBlock ? 1 : 0);
            size += (blockCount * sizeof(BitArray::Block)) * (ToUnderlying(RuntimeMaskType::Count) - 1); // same with definition of chunk(-1)
        }

        for (UInt16 loop = 0; loop < prototype->ComponentCount; ++loop)
        {
            auto& dataInfo = prototype->Components[loop];
            auto typeInfo = dataInfo.Desc;
            DEBUG_ASSERT(typeInfo);

            // adjust aligment for each component
            size = AlignUp(size, typeInfo->Alignment);

            // mark offset
            result[loop] = size;

            // caculate total component size with capacity
            size += typeInfo->Size * capacity;
        }

#if ENABLE_GUARDED_COMPONENT_ACCESS
        size = AlignUp(size, alignof(AtomicDataRaceGuardPtr));
        result[prototype->ComponentCount] = size;
        size += prototype->ComponentCount * sizeof(AtomicDataRaceGuardPtr) * prototype->ComponentCount * capacity;
#endif

        return size;
    }

}