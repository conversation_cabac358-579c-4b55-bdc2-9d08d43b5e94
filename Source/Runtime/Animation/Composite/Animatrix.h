#pragma once

#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Resource/Animation/Composite/AnimatrixRes.h"
#include "CEAnimation/Composite/AnimCompositeBase.h"
#include "CEAnimation/Composite/AnimSegment.h"
#include "CEAnimation/Composite/AnimSectionedTrack.h"

namespace cross::anim
{
    class IAnimSequence;
    class AnimatrixInstance;

    /*
     * Any property you're adding to Animatrix and parent class has to be considered for Child Asset
     *
     * Animatrix Tick happens in 2 phases
     *
     * first is to update weight of current montage only
     * this will make sure that all nodes will get up-to-date weight information
     * when update comes in for them
     *
     * second is evaluate pose with first phase's weight
     */
    class Animatrix : public AnimCompositeBase
    {
    public:
        //// ~AnimCompositeBase Interface Begin~
        //// 

        // base name of animatrix asset(.nda file)
        // eg. Run_Animatrix.nda --> name = "Run_Animatrix"
        virtual CEName GetName() const override { return mAnimatrixResPtr->mAssetName; }

        // relative path of animatrix asset(.nda file)
        virtual std::string GetAssetPath() const override { return mAnimatrixResPtr->GetName(); }

        virtual const CEName& GetGroupName() const override;

        virtual bool HasRootMotion() const override;

        virtual bool IsNotifyAvailable() const override { return mNotifies.size() > 0; }

        virtual bool IsValidSlot(const CEName& slotName) const override;

        virtual bool ContainRecursive(std::vector<const IAnimSequence*>& inAccumulatedList) const override { return false; }

        virtual void GetPose(
            RootSpacePose& outPose,
            AnimExtractContext<TrackUnWrapperH>& extractContext,
            const CEName& slotName) const override;

    protected:
        virtual AnimTrack const* GetTrack(CEName const& slotAnimTrack) const override;

        virtual const CEName& GetSyncGroupName() const override { return mAnimatrixResPtr->mSyncGroupName; }

        virtual bool AttachTo(const Skeleton* inSkeleton) override;

        virtual bool IsSkeletonAttached(Skeleton const* specifiedSkelt = nullptr) const override;

        ////
        //// ~AnimCompositeBase Interface End~

    public:

        bool IsSectionedTrack(CEName const& slotAnimTrack) const;

        bool IsDefaultTrack(CEName const& slotAnimTrack) const;

    protected:
        Animatrix(const AnimatrixResPtr& inAnimatrixResPtr);

        bool InitializeSlot(CEName const& inSlotName, std::vector<AnimSeqPtr>& inRunAnims);

    protected:
        // 
        AnimatrixResPtr mAnimatrixResPtr{ nullptr };
        // simple slot data, each slot contains anim track
        CENameMap<CEName, AnimTrack> mDefTracks;
        // sectioned slot data, each slot got at least 1 section
        CENameMap<CEName, AnimSectionedTrack> mSecTracks;

        friend class AnimFactory;
        friend class AnimatrixInstance;
#if CROSSENGINE_EDITOR
        friend class AnimatrixModifier;
#endif
    };

    using AnimatrixPtr = std::shared_ptr<Animatrix>;

    class AnimatrixInstance : public AnimCmpInstanceBase
    {
    private:
        AnimatrixInstance(Animatrix* inAnimShell);

        static std::shared_ptr<AnimatrixInstance> Create(Animatrix* inAnimShell)
        {
            class MakeSharedEnabler : public AnimatrixInstance
            {
            public:
                MakeSharedEnabler(Animatrix* inAnimShell) : AnimatrixInstance(inAnimShell) {}
            };

            return std::make_shared<MakeSharedEnabler>(inAnimShell);
        }

    public:

        //// ~AnimCmpInstanceBase inherit Begin~
        //// 

        virtual float GetTimeRemainingToEndPos() const override { return 0.0f; }

        virtual bool IsValid() const  override { return !IsBlendOuted(); }

        virtual void Terminate() override;

        virtual bool IsLoopingAllowed() const override { return false; }

        // 1st tick in animator, move cursor
        virtual void Advance(float deltaTime) override;

        // 2nd tick in Graph, override cursor moving at 1st tick when using sync group
        virtual void Update(AnimExecUpdateRecord& execRecord, AnimExecUpdateContext& updateContext) override;

        virtual void Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& slotName) override;

        virtual void ExtractDataArbitraryDeltaTime(const CEName& slotName, AnimExtractContext<TrackUnWrapperH>& extractContext) override;

        virtual const AnimSyncMarkerData& GetSyncMarkerData(CEName const& inSlotName = "") const override;

        virtual SyncMarkerUpdateRecord& GetMarkerUpdateRecord(CEName slotName = "") override;

    protected:
        // 
        inline Animatrix* GetMatrixShell() { return IsValid() ? mAnimatrixPtr.get() : nullptr; }

        virtual AnimCompositeBase const* GetInstanceShell() const override { return IsValid() ? mAnimatrixPtr.get() : nullptr; }

        virtual bool AttachTo(const Skeleton* inSkeleton) override;

    public:
        /* Select the longest length from activated tracks or use first def track's length */
        float GetRunLength() const;
        // 
        float GetRunLength(CEName const& inSlotName) const;
        /*  */
        inline bool IsAnySlotActivated() const { return mLongestRefTrack != nullptr; }
        //
        virtual bool ActivateSingleSlot(CEName const& inSlotName) override;
        //
        virtual bool IsSlotActivated(CEName const& inSlotName) const override;
        // 
        virtual bool IsSlotCompleted(CEName const& inSlotName) const override;
        // 
        virtual void GetActivatedSlots(std::vector<AnimReferenceTrackBase const*>& outSlots) const override;
        //
        virtual AnimReferenceTrackBase const* GetActivatedSlot(CEName const& inSlotName) const override;

        ////
        //// ~AnimCmpInstanceBase inherit End~

    protected:
        //
        AnimReferenceTrackBase* GetActivatedSlot(CEName const& inSlotName);

    protected:
        // reference to Animatrix Shell
        AnimatrixPtr mAnimatrixPtr{ nullptr };
        // active simple slot data, each slot contains anim track, SIZE <= shell DefTracks' size 
        std::vector<AnimReferenceSecTrack> mActivatedSectionTracks;
        // active sectioned slot data, each slot got at least 1 section, SIZE <= shell SecTracks' size
        std::vector<AnimReferenceDefTrack> mActivatedDefaultTracks;
        // active section with longest runtime length.
        // if no slot node extracted by ANIM_GRAPH, keep below property = null
        AnimReferenceTrackBase* mLongestRefTrack = nullptr;
        // Previous Position for this Exec while no activated slot in instance
        TrackUnWrapperH mPrevPos{0.f};
        // Current Position for this Exec while no activated slot in instance
        TrackUnWrapperH mCurPos{0.f};

        friend class AnimFactory;
    };

    using AnimatrixInstancePtr = std::shared_ptr<AnimatrixInstance>;
}
