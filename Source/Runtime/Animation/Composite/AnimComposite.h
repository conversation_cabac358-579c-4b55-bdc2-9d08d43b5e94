#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Resource/Animation/Composite/AnimCompositeRes.h"
#include "CEAnimation/Composite/AnimCompositeBase.h"

namespace cross::anim {

class AnimCompositeInstance;

/* Proxy object passed around anim Resource object NO-SHARE used only with particular skeleton
 * persist runtime info like link-up for sequence only, NO-RELTV runtime info with anim-res holding by instance
 * each segment's loop_count = 1 in composite, while no blend between segments
 * simple sequence combine && played in order
 */
class AnimComposite : public AnimCompositeBase, public IAnimSequence
{
public:
    AnimComposite(const AnimCmpResPtr& inAnimCmpResPtr, std::vector<AnimSeqPtr>& inRunAnims);

    AnimComposite(const AnimSeqResPtr& inSeqResPtr, const AnimSeqPtr& inSeqPtr, const RawH& inStartPos, const RawH& inEndPos);

    virtual ~AnimComposite() = default;

public:
    //// ~IAnimSequence Interface Begin~
    ////

    virtual SInt32 GetFrameCount() const override { return mTrack.GetRunFrames(); }

    virtual RawH GetRunLength() const override { return {mTrack.GetRunLength()}; }

    virtual SInt32 GetFrameAtTime(const RawH& time) const override { return -1; }

    virtual RawH GetTimeAtFrame(const SInt32 frame) const override { return {0.0f}; }

    virtual float GetSampleRate() const override { return -1.0f; }

    virtual ReferenceSkeleton const& GetReferenceSkeleton() const override
    {
        if (mTrack.AnimSegments.size() == 0)
        {
            static ReferenceSkeleton refSkelt;
            return refSkelt;
        }

        return mTrack.AnimSegments[0].SequencePtr->GetReferenceSkeleton();
    }

    // base name of composite asset(.nda file)
    // eg. Run_Composite.nda --> name = "Run_Composite"
    virtual CEName GetName() const override { return IsDynamic() ? (mDynamicSeqResPtr->GetAssetName()) : (mCompositeResPtr->GetAssetName()); }

    // relative path of composite asset(.nda file)
    virtual std::string GetAssetPath() const override { return IsDynamic() ? (mDynamicSeqResPtr->GetName()) : (mCompositeResPtr->GetName()); }

    virtual bool HasRootMotion() const override { return mTrack.HasRootMotion(); }

    virtual NodeTransform ExtractRootMotionFromRange(const RawH& startPos, const RawH& endPos) const override;

    virtual NodeTransform ExtractRootMotionFromDeltaTime(const RawH& startPos, float deltaTime, bool bAllowLoop) const override;

    virtual bool ContainRecursive(std::vector<const IAnimSequence*>& inAccumulatedList) const override;

    virtual const std::vector<sync::AnimSyncMarker>& GetSyncMarkers() const override { return mSyncMarkerData.SyncMarkers; }

    /* Composite could be an element of Track.
     *	This should be called from segment get-notify while a particular track ref this asset
     */
    virtual void ExtractNotifiesFromRange(const RawH& startPos, const RawH& endPos, std::vector<AnimNotifyEventReference>& outActiveNotifies) const override;

    /* Composite could be an element of Track.
     *  This should be called from segment get-notify while a particular track ref this asset
     */
    virtual void ExtractNotifiesFromDeltaTime(const RawH& startPos, const float deltaTime, const bool bAllowLooping, std::vector<AnimNotifyEventReference>& outActiveNotifies) const override;

    virtual bool IsNotifyAvailable() const override { return (mTrack.IsNotifyAvailable()) || (mNotifies.size() > 0); }

    virtual void ExtractAnimCurves(const RawH& currentTime, AnimCurveData& outCurveData) const override;

    /* Composite could be an element of Track.
     *  This should be called from segment get-pose while a particular track ref this asset
     */
    virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<RawH>& extractContext) const override;

    /* Get bone transform of specific Bone for the Time given, must be called after SampleCache updated */
    void GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const override;

    ////
    //// ~IAnimSequence Interface End~

public:
    //// ~AnimCompositeBase Interface Begin~
    ////

    virtual bool IsDynamic() const override { return mCompositeResPtr == nullptr && mDynamicSeqResPtr != nullptr; }

    virtual bool IsValidSlot(const CEName& slotName) const override { return (mTrack.SlotName == slotName) && (mTrack.AnimSegments.size() > 0); }

    virtual const CEName& GetGroupName() const override 
    { 
        // Todo(xtnwang) : extracted from skeleton's ctrl
        //if (!IsDynamic())
        //    return mCompositeResPtr->mGroupName;

        return AnimSlotGroup::sDefaultGroupName;
    }

    /* Composite could be an executable anim asset.
     * This should be called from a particular animator while time cursor already be TrackUnWrapperH pos
     */
    virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& slotName) const override;

protected:
    virtual AnimTrack const* GetTrack(const CEName& slotName) const override { return IsValidSlot(slotName) ? &mTrack : nullptr; }

    virtual AnimTrack const* GetTrack() const { return &mTrack; }

    virtual const CEName& GetSyncGroupName() const override { return mCompositeResPtr->mSyncGroupName; }

    virtual bool IsSkeletonAttached(Skeleton const* specifiedSkelt = nullptr) const override
    {
        auto itr = std::find_if(mTrack.AnimSegments.begin(), mTrack.AnimSegments.end(), [specifiedSkelt](auto& elem) 
        { 
            return elem.SequencePtr->IsSkeletonAttached(specifiedSkelt) == false; 
        });
        return itr == mTrack.AnimSegments.end();
    }

    virtual bool AttachTo(const Skeleton* inSkeleton) override;

    ////
    //// ~AnimCompositeBase Interface End~

protected:
    /* Pointer to the asset can be treat as CompositeAnim.
     *  if set, you should not to edit any data in mDynamicSeqsResPtr, keep it empty
     */
    const AnimCmpResPtr mCompositeResPtr{nullptr};

    /* if not empty, means the CompositeAnim is dynmaic created by anim sequence but not Disk File.
     * will also hold resource reference count here.
     */
    const AnimSeqResPtr mDynamicSeqResPtr{nullptr};
    std::unique_ptr<SlotTrackRes> mDynamicSlotTrackRes{nullptr};

    /** Serializable data that stores anims holding by segment **/
    AnimTrack mTrack;

    //
    AnimSyncMarkerData mSyncMarkerData;

    friend class AnimFactory;
    friend class AnimCompositeInstance;

#if CROSSENGINE_EDITOR
    friend class AnimCompositeModifier;
#endif
};

using AnimCmpPtr = std::shared_ptr<AnimComposite>;

class AnimCompositeInstance : public AnimCmpInstanceBase
{
protected:
    AnimCompositeInstance(AnimComposite* inCompositeShell, bool isTrigger, bool allowLooping = true, float playRate = 1.0f);

    static std::shared_ptr<AnimCompositeInstance> Create(AnimComposite* inCompositeShell, bool isTrigger, bool allowLooping = true, float playRate = 1.0f)
    {
        class MakeSharedEnabler : public AnimCompositeInstance
        {
        public:
            MakeSharedEnabler(AnimComposite* inCompositeShell, bool isTrigger, bool allowLooping = true, float playRate = 1.0f)
                : AnimCompositeInstance(inCompositeShell, isTrigger, allowLooping, playRate)
            {}
        };

        return std::make_shared<MakeSharedEnabler>(inCompositeShell, isTrigger, allowLooping, playRate);
    }

public:
    //// ~AnimCmpInstanceBase inherit Begin~
    ////

    virtual void Replay() override
    {
        mRefTrack.Reset();
        AnimCmpInstanceBase::Replay();
    }

    virtual float GetTimeRemainingToEndPos() const override
    {
        return (std::abs)(mRefTrack.GetCursor() - mAnimCompositePtr->GetRunLength());
    }

    virtual bool IsValid() const override { return !IsBlendOuted(); }

    virtual void Terminate() override;

    virtual bool IsLoopingAllowed() const override { return mAllowLooping; }

    virtual void SetCursor(float fromTime, float toTime) override;

    // 1st tick in animator, move cursor
    virtual void Advance(float deltaTime) override;

    // 2nd tick in Graph, override cursor moving at 1st tick when using sync group
    virtual void Update(AnimExecUpdateRecord& execRecord, AnimExecUpdateContext& updateContext) override;

    virtual void Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& slotName) override;

    virtual void ExtractDataArbitraryDeltaTime(const CEName& slotName, AnimExtractContext<TrackUnWrapperH>& extractContext) override;

    virtual const AnimSyncMarkerData& GetSyncMarkerData(CEName const& inSlotName = "") const override { return mRefTrack.GetSyncTrackMarkerData(); }

    virtual SyncMarkerUpdateRecord& GetMarkerUpdateRecord(CEName slotName = "") override { return mRefTrack.GetSyncMarkerUpdateRecord(); }

protected:
    inline AnimComposite* GetCompositeShell() { return IsValid() ? mAnimCompositePtr.get() : nullptr; }

    virtual AnimCompositeBase const* GetInstanceShell() const override { return IsValid() ? mAnimCompositePtr.get() : nullptr; }

    virtual bool AttachTo(const Skeleton* inSkeleton) override;

public:
    virtual bool ActivateSingleSlot(CEName const& inSlotName) override { return true; }
    //
    virtual bool IsSlotActivated(CEName const& inSlotName) const override { return inSlotName == GetSlotName(); }
    //
    virtual bool IsSlotCompleted(CEName const& inSlotName) const override { return mRefTrack.IsCompleted(); }
    //
    virtual void GetActivatedSlots(std::vector<AnimReferenceTrackBase const*>& outSlots) const override;
    //
    virtual AnimReferenceTrackBase const* GetActivatedSlot(CEName const& inSlotName) const override;

    ////
    //// ~AnimCmpInstanceBase inherit End~

public:
    CEName GetSlotName() const
    {
        Assert(mAnimCompositePtr != nullptr);
        return mAnimCompositePtr->GetTrack()->SlotName;
    }

protected:
    // It will handle wrapping if mAllowLooping is true
    const bool mAllowLooping{true};
    // reference to Composite Shell
    AnimCmpPtr mAnimCompositePtr{nullptr};
    // composite should got one and only track instance through its life cycle
    mutable AnimReferenceDefTrack mRefTrack;

    friend class AnimFactory;
};

using AnimCmpInstancePtr = std::shared_ptr<AnimCompositeInstance>;
}   // namespace cross::anim
