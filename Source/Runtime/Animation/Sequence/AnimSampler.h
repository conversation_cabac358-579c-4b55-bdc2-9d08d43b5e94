#pragma once

#include "Resource/Resource.h"
#include "CEAnimation/Transform/AnimPose.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "CEAnimation/AnimBase.h"

namespace cross::anim
{
	class IAnimSampler
	{
	public:
		enum class SampleDirection
		{
			Forward = 0,
			Backward = 1
		};

	public:
		/* When enable Root Motion, we will extract RootTransform while getting animation pose.
		 * Explain:
		 * If an animation has Root Motion and we do not extract Root Motion Transform, a vertex will be transformed by following procedures :
		 *			V * BindPoseInvMatrix * RootBoneTransform * LocalTransform * WorldTransform；
		 *
		 * This means that LocalTransform will not change when Vertex moves driven by Root Motion . 
		 * If one mesh has a physical component bound to it, that will remain still when Vertex moves, leading to invalidation of physics. 
		 * So we need to extract Root Motion Transform and apply it to LocalTransform.
		 * 
		 * We will extract root motion every frame, so Root Motion Transform is actually a DeltaTransform, that's to say:
		 *			DeltaT * LastFrameBoneTransform = CurFrameBoneTransform
		 * 
		 * Mathematically speaking, what we need to do is to decompose RootBoneTransform in formula mentioned above.There are four possible scenarios:
		 *			(1) delta_1 * delta_2 * ......*delta_n * Lock
		 *			(2) delta_n * delta_n-1 * ......*delta_1 * Lock
		 *			(3) Lock * delta_n * delta_n-1 * ......*delta_1
		 *			(4) Lock * delta_1 * delta_2 * ......*delta_n
		 *
		 * Where Lock is RootTransform root bone will be locked to when extracting root motion.
		 * 
		 * If we use (1) (2), the formula will be:
					V * BindPoseInvMatrix * (delta * Lock * LocalTransform) * WorldTransform, 
		 * this requires us to apply LockTransform to LocalTransform when extracting RootMotion every first time, which is so Tedious!!!
		 * 
		 * If we use (3), the formula will be:
					V * BindPoseInvMatrix * (Lock * delta_n * delta_n-1 * ......* delta_1) * LocalTransform * WorldTransform
			==>
					RootBoneTransform_n-1 = Lock * delta_n-1 * delta_n-2 * ......* delta_1，
					RootBoneTransform_n = Lock * delta_n * delta_n-1 * ......* delta_1
			==>
					LockInv * RootBoneTransform_n-1 = delta_n-1 * delta_n-2 * ......* delta_1，
					LockInv * RootBoneTransform_n = delta_n * delta_n-1 * ......*delta_1
																	  = delta_n * LockInv *  RootBoneTransform_n-1
			==> 
					delta_n = GetRelativeTransform(parent, child)，where parent = LockInv *  RootBoneTransform_n-1，child = LockInv *  RootBoneTransform_n；

		 * By doing so,  LocalTransform will change every frame according to (delta_n * LocalTransform_n-1 = LocalTransform_n),
		 * root bone will locked to LockTransform, the formula will be:
		 *			V * BindPoseInvMatrix * Lock * (delta_n * delta_n-1 * ......* delta_1 * LocalTransform_0) * WorldTransform
		 * 
		 * If we use (4), the formula will be:
		 * 			LockInv * RootBoneTransform_n = delta_1 * delta_2 * ......* delta_n
		 *														      = LockInv *  RootBoneTransform_n-1 * delta_n
		 * This means: 
		 *			when LocalTransform_n-1 = delta_1 * delta_2 * ......* delta_n-1 * LocalTransform_0, 
		 *			we need to decompose LocalTransform_n-1 into two parts: (delta_1 * delta_2 * ......* delta_n-1) and LocalTransform_0, and then wo need to 
		 *			ensure LocalTransform_n = (delta_1 * delta_2 * ......* delta_n-1) * delta_n * LocalTransform_0, that's too much complicated!!!
		 * 
		 * In Conclusion, we choose formula (3).
		 */
		virtual void Interpolate(
			NodeTransform& outRootMotionTrans, 
			const RawH& previousTime,
			const RawH& currentTime,
			SampleDirection sampleDirect = SampleDirection::Forward) = 0;

		/*
		 * Update SampleCache by currentTime, and then return pose at currentTime
		 */
		virtual void Interpolate(RootSpacePose& outPose, const RawH& currentTime, SampleDirection sampleDirect = SampleDirection::Forward) = 0;
		virtual void Interpolate(RootSpacePose& outPose, const UInt32 currentFrameIndex, SampleDirection sampleDirect = SampleDirection::Forward) = 0;

		/* Get bone transform of specific Bone for the Time given, must be called after SampleCache updated */
		virtual void GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const = 0;

        const NodeTransform& GetRootLockTransform() const { return mRootLockTransform; }

	protected:
		// record current sample time
		float	mCurSampleTime{ 0.f };
		// record current sample direction
		SampleDirection mCurSampleDirect = SampleDirection::Forward;

		// for root locking when extracting root motion
		cross::NodeTransform mRootLockTransform;

		// for root motion extracting
		cross::NodeTransform mLastFrameRootTrans;
		cross::NodeTransform mCurFrameRootTrans;
	};
}
