#include "EnginePrefix.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Resource/ResourceManager.h"
#include "Runtime/Animation/Animator/Animator.h"

#include "Resource/Animation/Animator/AnimatorResource.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimAssembler.h"
#include "Runtime/Animation/Composite/AnimComposite.h"
#include "Runtime/Animation/Composite/Animatrix.h"
#include "Runtime/Animation/AnimFactory.h"
#include "Resource/MeshAssetDataResource.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/TransformSystemG.h"

#include "CECommon/Utilities/CrossUtility.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Allocator/FrameAllocator.h"

namespace cross::anim {

const Skeleton* Animator::GetSkeleton() const
{
    auto skComp = mGameWorld->GetComponent<SkeletonComponentG>(mOwnerEntity);
    if (!skComp.IsValid())
    {
        AssertMsg(false, "You need add skeleton component for this entity first");
    }

    if (!skComp.Read(ecs::ComponentAccessFlag::SubComponentAccess1)->SkeletonAssetPtr)
    {
        AssertMsg(false, "You need set skeleton path of skeleton component first");
    }

    return skComp.Read(ecs::ComponentAccessFlag::SubComponentAccess1)->RunSkelt.get();
}

ParameterPtr Animator::GetParameterByName(const CEName& name) {
    if (GetParameterIndexByName(name) == -1)
    {
        return nullptr;
    }
    return mParameters[GetParameterIndexByName(name)];
}

Animator::Animator(GameWorld* inWorld, ecs::EntityID inEntityID, const AnimFactory* inAnimFactory)
{
    mGameWorld = inWorld;
    mOwnerEntity = inEntityID;
    mCurFactory = inAnimFactory;

    // initialize update context
    mUpdateContext.AnimatorPtr = this;

    // initialize extract context
    if (mExtractContext.BonesRequiredPtr == nullptr)
    {
        mExtractContext.BonesRequiredPtr = new std::vector<SkBoneHandle>();
    }
    mExtractContext.AnimatorPtr = this;
}

Animator::~Animator()
{
    SAFE_DELETE(mExtractContext.BonesRequiredPtr);
}

bool Animator::Initialize(AnimatorResPtr& inAnimatorRes)
{
    // Grab parameters from animator resource
    ParameterWithSerializer paramSerializer;
    auto const& animatorParameters = inAnimatorRes->GetParameters();
    std::for_each(animatorParameters.cbegin(), animatorParameters.cend(), [&](auto& elem) 
    { 
        mParameters.push_back(ParameterPtr(paramSerializer.Clone(elem.get()))); 
    });

    BuildParamIndexMap();

    // Init curve data
    mAnimCurveData.IntendedCurves = inAnimatorRes->GetIntendedCurveNames();

    const SerializeNode& storyBoardJson = inAnimatorRes->GetStoryBoardContent();
    mSbPtr = std::make_shared<AnimStoryBoard>(this, CEName{storyBoardJson[ANIMGRAPH_NAME].AsString().c_str()});
    // Set root motion mode for story board
    Assert(storyBoardJson.HasMember("RootMotionMode"));
    mSbPtr->SetRootMotionMode(
        RootMotion::OperateExtractMode(storyBoardJson["RootMotionMode"]["ExtractMode"].AsString()), 
        RootMotion::OperateApplyMode(storyBoardJson["RootMotionMode"]["ApplyMode"].AsString()));
    // Assemble story board
    if (!AnimAssembler::AssembleStoryBoard(mSbPtr.get(), storyBoardJson))
    {
        return false;
    }

    // Initialize story board
    AnimInitContext initContext;
    initContext.AnimatorPtr = this;
    if (mSbPtr)
    {
        mSbPtr->Initialize(initContext);
        return true;
    }

    return false;
}

void Animator::BuildParamIndexMap()
{
    mNameToParamIndexMap.clear();
    for (size_t iParam = 0; iParam < mParameters.size(); iParam++)
    {
        mNameToParamIndexMap.insert({mParameters[iParam]->Name(), iParam});

        // add all params to symbol table
        mParamSymbolTable.UpdateTableByParam(mParameters[iParam].get());
    }

    /* expr examples */
    /*
    {
        const std::string expression_list[]
            = {
                  "mand(b, s[] == 1, v4[] == 4);",
                  "sum(v2)",
                  "2* sum(v3)",
                  "b = true",
                  "b == true",
                  "s := 'abc'",
                  "s == 'abc'",
                  "var res := 'cde'",
                  "min(f, i) > 10",
                  "f := 3*i",
                  "(s like '*123*') or ('a123b' ilike s)",
                  "sgn(+1.2^3.4 * i / -5.6 * f) > {-7.8^9 / -10.11 * i }",
                  "(i + f)",
                  "2 * (i + f)",
                  "(2 * i + 2 * f)",
                  "((1.23 * i^2) / f) - 123.123",
                  "sin(2 * i) + cos(pi * f)",
                  "sqrt(111.111 - sin(2 * i) + cos(pi * f) / 333.333)",
                  "(i^2 / sin(2 * pi * i)) - f / 2",
                  "clamp(-1.0, sin(2 * pi * i) + cos(f / 2 * pi), +1.0)",
                  "max(3.33, min(sqrt(1 - sin(2 * i) + cos(pi * f) / 3), 1.11))"
        };

        for (auto& expr : expression_list)
        {
            ParamExpression exprInstance{ expr, Expression::None };
            exprInstance.RegisterSymbolTable(mParamSymbolTable);
            if (!exprInstance.Compile())
            {
                //AssertMsg(false, "Expression compiling failed");
                LOG_ERROR("Expression compiling failed");
            }

            auto res = exprInstance.Evaluate();
        }
    }
    */
}

void Animator::PreUpdate(float deltaSeconds)
{
    UpdateAnimInstance(deltaSeconds);
}

void Animator::Update(float deltaSeconds, RootSpacePose& outPose)
{
    SCOPED_CPU_TIMING(GroupAnimation, "AnimationUpdate");

    // step 1 Update anim instances
    PreUpdate(deltaSeconds);

    // step 2 Update anim graph
    UpdateGraph(deltaSeconds);

    // step 3 Init pose and flag
    outPose.ResetToRefPose();

    // step 4 Evaluate Animator(get pose, extract root motion & notify & curve)
    EvaluatePose(outPose);
}

void Animator::OnPreUpdateLODChanged(const SInt32 previousLODIndex, const SInt32 newLODIndex) {}

void Animator::UpdateAnimInstance(float deltaSeconds)
{
    for (auto& instancePtr : mAnimInstances)
    {
        // update cursor & section  & weight
        if (instancePtr && instancePtr->IsValid())
            instancePtr->Advance(deltaSeconds);
    }
}

void Animator::UpdateGraph(float deltaSeconds)
{
    // clear extracted notify info this frame
    mNotifyQueue.Reset();

    if (mSbPtr)
    {
        // reset update context
        mUpdateContext.CurrentWeight = 1.0f;
        mUpdateContext.DeltaTime = deltaSeconds;
        mSbPtr->Update(mUpdateContext);
    }

    // Do not extract root motion and notify from mAnimInstances here.
    // Because we will do this in graph when mAnimInstances are used in SlotNode.
    // When one is used, it will be added into AnimSync and we will extract these info at corresponding graph node
}

void Animator::EvaluatePose(RootSpacePose& outPose)
{
    mExtractContext.AnimNotifyQueuePtr = &mNotifyQueue;
    mExtractContext.RootMotionParamsPtr = &mRootMotionParams;
    mExtractContext.AnimCurveDataPtr = &mAnimCurveData;
    mExtractContext.IsAnySlotPlaying = &mIsAnySlotPlaying;
    if (mSbPtr)
        mSbPtr->Evaluate(outPose, mExtractContext);

    // Do not extract pose from mAnimInstances here.
    // The same reason for root motion and notify extracting.
    // Only when they are used, can we extract these info.
}

void Animator::PostUpdate()
{
    if (mSbPtr != nullptr)
        mSbPtr->PostUpate();

    // reset root motion info this frame
    mRootMotionParams.Reset();
    // clear extracted curve info this frame
    mAnimCurveData.Reset();

    mIsAnySlotPlaying = false;

    for (auto iter = mAnimInstances.begin(); iter != mAnimInstances.end();)
    {
        // remove invalid anim instances
        if ((*iter)->IsValid() == false)
            iter = mAnimInstances.erase(iter);
        // post update valid anim instances
        else
        {
            (*iter)->PostUpdate();
            ++iter;
        }
    }
}

bool Animator::IsPlayingRootMotion() const
{
    if (mSbPtr)
    {
        if (mSbPtr->GetRootMotionApplyMode() == RootMotion::Apply)
        {
            if (mSbPtr->GetRootMotionExtractMode() == RootMotion::DoNotExtract)
            {
                return false;
            }
            else if (mSbPtr->GetRootMotionExtractMode() == RootMotion::ExtractFromSlotOnly)
            {
                return IsAnySlotPlaying();
            }
            else if (mSbPtr->GetRootMotionExtractMode() == RootMotion::ExtractFromEverything)
            {
                return true;
            }
        }
        else
        {
            return false;
        }
    }

    return false;
}

RootMotionParams Animator::ConsumeRootMotion()
{
    return mRootMotionParams.ApplyRootMotion();
}

Float4x4A Animator::GetWorldTransform()
{
    auto transformSystem = mGameWorld->GetGameSystem<cross::TransformSystemG>();
    auto compHandle = mGameWorld->GetComponent<cross::WorldTransformComponentG>(mOwnerEntity);

    return transformSystem->GetWorldMatrix(compHandle.Read());
}

void Animator::SetWorldTransform(const Float3& trans, const Float3& scale, const Quaternion& quat)
{
    auto transformSystem = mGameWorld->GetGameSystem<cross::TransformSystemG>();
    auto compHandle = mGameWorld->GetComponent<cross::WorldTransformComponentG>(mOwnerEntity);

    transformSystem->SetWorldTranslation(compHandle.Write(), trans);
    transformSystem->SetWorldRotation(compHandle.Write(), quat);
    transformSystem->SetWorldScale(compHandle.Write(), scale);
}

void Animator::PushNewPlayedAnim(AnimCmpInstanceBasePtr newAnimInstance, bool stopOthers)
{
    if (stopOthers)
    {
        // stop all anim-matrix & anim-composite at the same group
        auto const& groupName = newAnimInstance->GetGroupName();
        auto blendInTime = newAnimInstance->GetBlendInReferenceTime();
        for (auto& instancePtr : mAnimInstances)
        {
            if (instancePtr->IsValid() && groupName == instancePtr->GetGroupName())
            {
                instancePtr->Stop(blendInTime, true);
            }
        }
    }

    newAnimInstance->Play();
    mAnimInstances.push_back(newAnimInstance);
}

AnimCmpInstanceBasePtr Animator::GetAnimInstanceForID(SInt32 InstanceID) const
{
    for (auto& inst : mAnimInstances)
    {
        if (inst->GetInstanceID() == InstanceID)
            return inst;
    }
    return nullptr;
}

cross::skeleton::CEName Animator::GetStbName() const 
{
    if (mSbPtr)
    {
        return mSbPtr->GetName();
    }
    return "";
}

bool Animator::IsAnySlotPlaying() const
{
    return mIsAnySlotPlaying;
}
}   // namespace cross::anim
