#include "EnginePrefix.h"
#include "Runtime/Animation/AnimFactory.h"
#include "CEAnimation/AnimRuntime.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_PlayCompositeNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_NodeUtils.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"
#include "Resource/ResourceManager.h"

namespace cross::anim {

REGISTER_NODE_TYPE(AnimGraph_PlayCompositeNode)

AnimGraph_PlayCompositeNode::AnimGraph_PlayCompositeNode(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
    : AnimGraph_PlayAnimBaseNode(inOwner, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap)
    , mPlayRateParamLink(1.f)
{
    ExtractMember(mLoopingAnim, inNodeJson, ANIMGRAPH_NODE_COMPOSITE_LOOP);

    ExtractMember(mAnimAssetPath, inNodeJson, ANIMGRAPH_NODE_COMPOSITE_PATH);
    mAnimAssetPath = gResourceMgr.ConvertPathToGuid(mAnimAssetPath);
}

void AnimGraph_PlayCompositeNode::Initialize(const AnimInitContext& inContext)
{
    // grab anim sequence instance in the very beginning
    auto animator = inContext.AnimatorPtr;
    auto factory = animator->GetFactory();

    FactoryErrorCode::Type errorCode;
    mAnimExecPtr = factory->CreateAnimExec(mAnimAssetPath.c_str(), errorCode, animator, false, mLoopingAnim, static_cast<float>(mPlayRateParamLink));

    if (!(mAnimExecPtr && errorCode == FactoryErrorCode::Type::Success))
    {
        LOG_ERROR("PlayCompositeNode load failed");
        return;
    }

    mAnimExecPtr->SetAnimSyncParams(AnimSyncParams(mGroupName, mGroupRole, mSyncMethod));
    
    // after anim sequence created, reset node then
    AnimGraph_PlayAnimBaseNode::Initialize(inContext);
}

void AnimGraph_PlayCompositeNode::Update(const AnimUpdateContext& inContext)
{
    AnimGraph_PlayAnimBaseNode::Update(inContext);

    // Grab composite instance here
    auto compInstPtr = CompositeInstancePtr();

    if (mAnimExecPtr != nullptr && mAnimExecPtr->IsValid())
    {
        AnimExecUpdateRecord updateRecord(compInstPtr, mNodeGlobalWeight);
        inContext.SyncMgrPtr->AddAnimExecUpdateRecord(updateRecord);
    }
}

void AnimGraph_PlayCompositeNode::ResetNodeInternal()
{
    if (CompositeInstancePtr())
        CompositeInstancePtr()->Replay();
}

bool AnimGraph_PlayCompositeNode::PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks) 
{
    if (!AnimGraph_PlayAnimBaseNode::PostAssemble(inNodeJson, inParamLinks))
        return false;

    // if first param link is instanced, PLAY RATE in this node will extracted by link instead
    if (inParamLinks.size() && inParamLinks[0] != nullptr)
        mPlayRateParamLink = ParamLinkUtils::GetParamLinkInstance<AnimGraph_FloatParamLink>(inParamLinks[0]);

    return true;
}

AnimGraph_BaseNode* AnimGraph_PlayCompositeNode::Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
{
    return new AnimGraph_PlayCompositeNode(inOwner, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap);
}

}   // namespace cross::anim
