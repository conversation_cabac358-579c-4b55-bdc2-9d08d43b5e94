#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_SwitchPosesByIntNode.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"

namespace cross::anim {

REGISTER_NODE_TYPE(AnimGraph_SwitchPosesByIntNode)

AnimGraph_SwitchPosesByIntNode::AnimGraph_SwitchPosesByIntNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson,
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
    : AnimGraph_SwitchPosesBaseNode(inAnimGraph, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap)
    , mActiveValueParamLink(0)
{
}

void AnimGraph_SwitchPosesByIntNode::Update(const AnimUpdateContext& inContext)
{
    // update param link first
    AnimGraph_BaseNode::Update(inContext);

    // get active pose index
    mCurActivePoseIndex = mActiveValueParamLink;

    AnimGraph_SwitchPosesBaseNode::Update(inContext);
}

AnimGraph_BaseNode* AnimGraph_SwitchPosesByIntNode::Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
{
    return new AnimGraph_SwitchPosesByIntNode(inOwner, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap);
}

bool AnimGraph_SwitchPosesByIntNode::PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks) 
{
    if (!AnimGraph_SwitchPosesBaseNode::PostAssemble(inNodeJson, inParamLinks))
        return false;

    // if first param link is instanced, ACTIVE POSE INDEX in this node will extracted by link instead
    if (inParamLinks[0] != nullptr)
        mActiveValueParamLink = ParamLinkUtils::GetParamLinkInstance<AnimGraph_IntParamLink>(inParamLinks[0]);

    return true;
}
}   // namespace cross::anim