#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_PlayAnimBaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_ParameterLink.h"
#include "Runtime/Animation/BlendSpace/AnimBlendSpace.h"

namespace cross::anim {

// Play AnimBlendSpace node
class AnimGraph_PlayBlendSpaceNode : public AnimGraph_PlayAnimBaseNode
{
    enum class ParamType
    {
        Vec2 = 0,
        X_And_Y
    };

public:
    AnimGraph_PlayBlendSpaceNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    //// ~AnimGraph_BaseNode Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

protected:
    virtual void ResetNodeInternal() override;

    virtual bool PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks) override;

    ////
    //// ~AnimGraph_BaseNode Interface End~

private:
    inline AnimBlendSpace* BlendSpacePtr()
    {
        return TYPE_CAST(AnimBlendSpace*, mAnimExecPtr.get());
    }

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

private:
    AnimGraph_FloatParamLink mPlayRateParamLink;

    AnimGraph_Vec2ParamLink mPositionParamLink;

    AnimGraph_FloatParamLink mXParamLink;
    AnimGraph_FloatParamLink mYParamLink;

    ParamType mParamType;
    friend class AnimAssembler;
};

}   // namespace cross::anim
