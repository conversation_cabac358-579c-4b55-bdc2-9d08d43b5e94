#pragma once
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_BaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "CEAnimation/AnimBase.h"
#include "CEAnimation/Transform/AnimBlend.h"

namespace cross::skeleton {
struct ReferenceSkeleton;
}

namespace cross::anim {

struct PerBoneBlendInfo
{
    // use BlendPoseLink(UInt32 index) to get pose link
    UInt32 BlendPoseIndex{0};

    // BasePoseWeight = 1 - BlendPoseWeight
    float BlendPoseWeight{0.0f};
};

struct LayeredFilter
{
    CEName mBoneName{""};
    SInt32 mDepth{0};
};

struct BlendLayer
{
    std::vector<LayeredFilter> mFilters;
    float mBlendWeight{1.0f};
};

class AnimGraph_BlendByLayeredFilterNode : public AnimGraph_BaseNode
{
public:
    AnimGraph_BlendByLayeredFilterNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    //// ~AnimGraph_BaseNode Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

protected:
    virtual void ResetNodeInternal() override;

    ////
    //// ~AnimGraph_BaseNode Interface End~

private:
    inline AnimGraph_LocalPoseLink* BasePoseLink()
    {
        Assert(mPoseLinks.size() > 0);
        return TYPE_CAST(AnimGraph_LocalPoseLink*, mPoseLinks[0]);
    }

    inline AnimGraph_LocalPoseLink* BlendPoseLink(UInt32 index)
    {
        Assert(mPoseLinks.size() > index + 1);
        return TYPE_CAST(AnimGraph_LocalPoseLink*, mPoseLinks[index + 1]);
    }

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

protected:
    std::vector<BlendLayer> mLayers;

private:
    const Animator* mAnimator{nullptr};

    std::vector<PerBoneBlendInfo> mPerBoneBlendInfo;
    std::vector<RootSpacePose> mCachePose;

    friend class AnimAssembler;
};

}   // namespace cross::anim
