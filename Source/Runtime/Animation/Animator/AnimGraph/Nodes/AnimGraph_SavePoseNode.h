#pragma once
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_BaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"

namespace cross::anim {

class AnimGraph_SavePoseNode : public AnimGraph_BaseNode
{
public:
    AnimGraph_SavePoseNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    //// ~AnimGraph_BaseNode inherit Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    virtual void PostUpdate() override;

    ////
    //// ~AnimGraph_BaseNode inherit End~

    const CEName& GetPoseName() const
    {
        return mPoseName;
    }

    void UpdateSavePoseNode();

    void ResetNodeFlag()
    {
        mEvaluateFlag = false;
        mPostUpdateFlag = false;
    }

private:
    inline AnimGraph_LocalPoseLink* PoseLink()
    {
        Assert(mPoseLinks.size() > 0);
        return TYPE_CAST(AnimGraph_LocalPoseLink*, mPoseLinks[0]);
    }

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

private:
    CEName mPoseName{""};

    RootSpacePose mSavedPose;
    RootMotionParams mSavedRootMotion;

    std::vector<AnimUpdateContext> mContexts;

    bool mInitFlag{false};
    bool mEvaluateFlag{false};
    bool mPostUpdateFlag{false};

    friend class AnimAssembler;
};

}   // namespace cross::anim