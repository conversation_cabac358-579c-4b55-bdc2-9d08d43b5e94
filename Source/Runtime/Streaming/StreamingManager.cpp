#include "Streaming/StreamingManager.h"
#include "Streaming/StreamingTask.h"
#include "Streaming/StreamingUtils.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "NativeGraphicsInterface/Statistics.h"

namespace cross
{

StreamingManager& StreamingManager::GetInstance()
{
    static StreamingManager instance;
    return instance;
}

/*-----------------------------------------------------------------------------
    StreamingManager implementation.
-----------------------------------------------------------------------------*/

/** Constructor, initializing all members and  */
StreamingManager::StreamingManager()
    : CurrentUpdateStreamingRenderAssetIndex(0)
    , AsyncWork(nullptr)
    , CurrentPendingLODCopyRequestIdx(0)
    , ProcessingStage(0)
    , NumRenderAssetProcessingStages(5)
    , bUseDynamicStreaming(false)
    , BoostPlayerTextures(3.0f)
    , MemoryMargin(0)
    , EffectiveStreamingPoolSize(0)
    , MemoryOverBudget(0)
    , MaxEverRequired(0)
    , bPauseRenderAssetStreaming(false)
    , LastWorldUpdateTime(StreamingUtils::IsEditor() ? -FLT_MAX : 0)   // In editor, visibility is not taken into consideration.
    , LastWorldUpdateTime_LODCalcTask(LastWorldUpdateTime)
{
    // Forbid streaming in engine headless mode
    Settings.EnableStreaming &= !StreamingUtils::IsHeadless();

    // Read settings
    MemoryMargin = Settings.MemoryMargin;

    //int PoolSizeIniSetting = 0;
    //GConfig->GetInt(TEXT("TextureStreaming"), TEXT("PoolSize"), PoolSizeIniSetting, GEngineIni);
    //GConfig->GetBool(TEXT("TextureStreaming"), TEXT("UseDynamicStreaming"), bUseDynamicStreaming, GEngineIni);
    //GConfig->GetFloat(TEXT("TextureStreaming"), TEXT("BoostPlayerTextures"), BoostPlayerTextures, GEngineIni);
    //GConfig->GetBool(TEXT("TextureStreaming"), TEXT("NeverStreamOutRenderAssets"), GNeverStreamOutRenderAssets, GEngineIni);

    // -NeverStreamOutRenderAssets
    if (StreamingUtils::IsEditor())
    {
        // this would not be good or useful in the editor
        Settings.NeverStreamOut = false;
    }
    if (Settings.NeverStreamOut)
    {
        LOG_INFO("Meshes will NEVER stream out!");
    }

    // Convert from MByte to byte.
    MemoryMargin *= 1024 * 1024;

    // TODO: NumStreamedLODs_StaticMesh, NumStreamedLODs_SkeletalMesh, NumStreamedLODs_LandscapeMeshMobile
    NumStreamedLODs_StaticMesh.reserve(1);
    NumStreamedLODs_StaticMesh.emplace_back(INT32_MAX);

    // setup the streaming resource flush function pointer
    //GFlushStreamingFunc = [this]() { BlockTillAllRequestsFinished(); };

    ProcessingStage = 0;
    //AsyncWork = threading::TTask<StreamingCalculationTask>::Create({}).HoldTask(this);
    AsyncWorkTask = StreamingCalculationTask(this);

    AsyncUnsafeStreamingRenderAssets.reserve(StreamingResourcesDefaultReservedSize);

    // Subscibe world life event
    auto* worldSys = TYPE_CAST(WorldSystemG*, EngineGlobal::GetEngine()->GetGlobalSystem<WorldSystemG>());
    if (worldSys)
    {
        worldSys->SubscribeEvent<WorldLifeEvent>(this);
    }
}

StreamingManager::~StreamingManager()
{
}

void StreamingManager::Shutdown()
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingManager::Shutdown");

    std::scoped_lock ScopeLock(CriticalSection);

    // Unsubscibe world life event
    auto* worldSys = TYPE_CAST(WorldSystemG*, EngineGlobal::GetEngine()->GetGlobalSystem<WorldSystemG>());
    if (worldSys)
    {
        worldSys->Unsubscribe<WorldLifeEvent>(this);
    }

    // Wait for async tasks to complete
    if (StreamingRenderAssetsSyncEvent && !StreamingRenderAssetsSyncEvent->Complete())
    {
        Assert(threading::TaskSystem::IsInGameThread());
        StreamingRenderAssetsSyncEvent->WaitForCompletion();
        StreamingRenderAssetsSyncEvent.Reset();
    }

    if (AsyncWork)
    {
        AsyncWork->WaitForCompletion();
        AsyncWork.Reset();
    }

    if (RenderAssetInstanceAsyncWork)
    {
        RenderAssetInstanceAsyncWork->WaitForCompletion();
        RenderAssetInstanceAsyncWork.Reset();
    }

    // Clear the stats
    DisplayedStats.Reset();
    // STAT(DisplayedStats.Apply();)
}

std::vector<StreamingResource>& StreamingManager::GetStreamingRenderAssetsAsyncSafe()
{
    if (StreamingRenderAssetsSyncEvent && !StreamingRenderAssetsSyncEvent->Complete())
    {
        Assert(threading::TaskSystem::IsInGameThread());
        StreamingRenderAssetsSyncEvent->WaitForCompletion();
    }

    return AsyncUnsafeStreamingRenderAssets;
}

/**
 * Cancels the timed Forced resources (i.e used the Kismet action "Stream In Textures").
 */
void StreamingManager::CancelForcedResources()
{
    std::scoped_lock ScopeLock(CriticalSection);

    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    // Update textures/meshes that are Forced on a timer.
    for (int Idx = 0; Idx < StreamingRenderAssets.size(); ++Idx)
    {
        StreamingResource& StreamingRenderAsset = StreamingRenderAssets[Idx];

        // Make sure this streaming texture/mesh hasn't been marked for removal.
        if (StreamingRenderAsset.RenderAsset)
        {
            // Remove any prestream requests from textures/meshes
            float TimeLeft = (float)(StreamingRenderAsset.RenderAsset->ForceLODLevelsToBeResidentTimestamp - time::CurrentMs() / 1000.0);
            if (TimeLeft >= 0.0f)
            {
                StreamingRenderAsset.RenderAsset->SetForceLODLevelsToBeResident(-1.0f);
                StreamingRenderAsset.InstanceRemovedTimestamp = -FLT_MAX;
                StreamingRenderAsset.RenderAsset->InvalidateLastRenderTimeForStreaming();
#if STREAMING_LOG_CANCELFORCED
                UE_LOG(LogContentStreaming, Log, TEXT("Canceling forced texture: %s (had %.1f seconds left)"), *StreamingRenderAsset.Texture->GetFullName(), TimeLeft);
#endif
            }
        }
    }

    // Reset the streaming system, so it picks up any changes to the asset right away.
    ProcessingStage = 0;
}

/** Don't stream world resources for the next NumFrames. */
void StreamingManager::SetDisregardWorldResourcesForFrames(int NumFrames)
{
    //@TODO: We could perhaps increase the priority factor for character textures...
}

/**
 *	Try to stream out texture/mesh LOD-levels to free up more memory.
 *	@param RequiredMemorySize	- Additional texture memory required
 *	@return						- Whether it succeeded or not
 **/
bool StreamingManager::StreamOutRenderAssetData(SInt64 RequiredMemorySize)
{
    std::scoped_lock ScopeLock(CriticalSection);

    const SInt64 MaxTempMemoryAllowed = static_cast<SInt64>(Settings.MaxTempMemoryAllowed) * 1024 * 1024;
    const bool CachedPauseTextureStreaming = bPauseRenderAssetStreaming;
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    // Pause texture streaming to prevent sending load requests.
    bPauseRenderAssetStreaming = true;
    SyncStates(true);

    // Sort texture/mesh, having those that should be dropped first.
    std::vector<int> PrioritizedRenderAssets;
    PrioritizedRenderAssets.reserve(StreamingRenderAssets.size());
    for (int Idx = 0; Idx < StreamingRenderAssets.size(); ++Idx)
    {
        StreamingResource& StreamingRenderAsset = StreamingRenderAssets[Idx];
        // Only texture for which we can drop LODs.
        if (StreamingRenderAsset.IsMaxResolutionAffectedByGlobalBias() && (!Settings.bFullyLoadMeshes/* || !StreamingRenderAsset.IsMesh()*/))
        {
            PrioritizedRenderAssets.emplace_back(Idx);
        }
    }
    std::sort(PrioritizedRenderAssets.begin(), PrioritizedRenderAssets.end(), CompareStreamingAssetByRetentionPriority(StreamingRenderAssets));

    SInt64 TempMemoryUsed = 0;
    SInt64 MemoryDropped = 0;

    // Process all texture/mesh, starting with the ones we least want to keep
    for (int PriorityIndex = static_cast<int>(PrioritizedRenderAssets.size() - 1); PriorityIndex >= 0 && MemoryDropped < RequiredMemorySize; --PriorityIndex)
    {
        int RenderAssetIndex = PrioritizedRenderAssets[PriorityIndex];
        if (!(RenderAssetIndex >= 0 && RenderAssetIndex < StreamingRenderAssets.size()))
            continue;

        StreamingResource& StreamingRenderAsset = StreamingRenderAssets[RenderAssetIndex];
        if (!StreamingRenderAsset.RenderAsset)
            continue;

        const int MinimalSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.MinAllowedLODs);
        const int CurrentSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.ResidentLODs);

        if (StreamingRenderAsset.RenderAsset->StreamOut(StreamingRenderAsset.MinAllowedLODs))
        {
            MemoryDropped += CurrentSize - MinimalSize;
            TempMemoryUsed += MinimalSize;

            StreamingRenderAsset.UpdateStreamingStatus();

            if (TempMemoryUsed >= MaxTempMemoryAllowed)
            {
                // Queue up the process on the render thread and wait for everything to complete.
                // TODO:
                //DispatchRenderingCommandWithToken(FlushResourceCommand)([](FRHICommandListImmediate& RHICmdList) {
                //    RHICmdList.ImmediateFlush(EImmediateFlushType::FlushRHIThreadFlushResources);
                //    RHICmdList.FlushResources();
                //});
                threading::FlushRenderingCommands();
                TempMemoryUsed = 0;
            }
        }
    }

    bPauseRenderAssetStreaming = CachedPauseTextureStreaming;
    LOG_INFO("Streaming out texture memory! Saved {} MB.", float(MemoryDropped) / 1024.0f / 1024.0f);
    return true;
}

SInt64 StreamingManager::GetPoolSize() const
{
    return Settings.MemoryPoolSize;
}

void StreamingManager::IncrementalUpdate(float Percentage, bool bUpdateDynamicComponents)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingManager::IncrementalUpdate");

    SInt64 NumStepsLeftForIncrementalBuild = 50/*CVarStreamingNumStaticComponentsProcessedPerFrame.GetValueOnGameThread()*/;
    if (NumStepsLeftForIncrementalBuild <= 0)   // When 0, don't allow incremental updates.
    {
        NumStepsLeftForIncrementalBuild = std::numeric_limits<SInt64>::max();
    }
}

void StreamingManager::TickFastResponseAssets()
{
    if (!0/*CVarStreamingAllowFastForceResident.GetValueOnGameThread()*/)
    {
        return;
    }

    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    for (auto It = FastResponseRenderAssets.begin(); It != FastResponseRenderAssets.end(); It++)
    {
        StreamableResource* RenderAsset = *It;

        if (!RenderAsset || !RenderAsset->bIgnoreStreamingLODBias || (!RenderAsset->bForceLODlevelsToBeResident && RenderAsset->ForceLODLevelsToBeResidentTimestamp < EngineGlobal::GetEngine()->GetRealTimeSeconds()))
        {
            // No longer qualified for fast response
            It = FastResponseRenderAssets.erase(It);
            continue;
        }

        if (RenderAsset->GetLastRenderTimeForStreaming() < LastWorldUpdateTime_LODCalcTask)
        {
            // Not visible
            continue;
        }

        const int StreamingIdx = RenderAsset->StreamingIndex;
        StreamingResource& Asset = StreamingRenderAssets[StreamingIdx];

        Assert(RenderAsset == Asset.RenderAsset);

        Asset.UpdateStreamingStatus();

        if (Asset.ResidentLODs == Asset.RequestedLODs && Asset.ResidentLODs < Asset.MaxAllowedLODs)
        {
            RenderAsset->StreamIn(Asset.MaxAllowedLODs, true);
            RenderAsset->bHasStreamingUpdatePending = true;
            Asset.bHasUpdatePending = true;
            VisibleFastResponseRenderAssetIndices.emplace(StreamingIdx);

            for (int Idx = 0; Idx < PendingLODCopyRequests.size(); ++Idx)
            {
                FPendingLODCopyRequest& PendingRequest = PendingLODCopyRequests[Idx];

                if (PendingRequest.RenderAsset == RenderAsset)
                {
                    PendingRequest.RenderAsset = nullptr;
                }
            }

            Asset.UpdateStreamingStatus();
            //TrackRenderAssetEvent(&Asset, RenderAsset, true, this);
        }
    }
}

void StreamingManager::ProcessRemovedRenderAssets()
{
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    for (int AssetIndex : RemovedRenderAssetIndices)
    {
        // Remove swap all elements, until this entry has a valid texture/mesh.
        // This handles the case where the last element was also removed.
        // TODO: previous while
        while ((AssetIndex >= 0 && AssetIndex < StreamingRenderAssets.size()) && !StreamingRenderAssets[AssetIndex].RenderAsset)
        {
            //LOG_DEBUG("Streaming manager remove resource {}", (StreamingRenderAssets.begin() + AssetIndex)->RenderAsset->GetFullName());
            StreamingRenderAssets.erase(StreamingRenderAssets.begin() + AssetIndex);
        }

        if ((AssetIndex >= 0 && AssetIndex < StreamingRenderAssets.size()))
        {
            // Update the texture with its new index.
            int& StreamingIdx = StreamingRenderAssets[AssetIndex].RenderAsset->StreamingIndex;

            if (VisibleFastResponseRenderAssetIndices.erase(StreamingIdx))
            {
                VisibleFastResponseRenderAssetIndices.emplace(AssetIndex);
            }

            StreamingIdx = AssetIndex;
        }
        //Assert(StreamingRenderAssets[AssetIndex].RenderAsset->StreamingIndex == AssetIndex);
    }
    RemovedRenderAssetIndices.clear();

    // TODO: Align index
    for (int index = 0; index < StreamingRenderAssets.size(); index++)
    {
        StreamingRenderAssets[index].RenderAsset->StreamingIndex = index;
    }
}

void StreamingManager::ProcessAddedRenderAssets()
{
    // Add new textures or meshes.
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();
    StreamingRenderAssets.reserve(StreamingRenderAssets.size() + PendingStreamingRenderAssets.size());
    for (int Idx = 0; Idx < PendingStreamingRenderAssets.size(); ++Idx)
    {
        StreamableResource* Asset = PendingStreamingRenderAssets[Idx];
        // Could be null if it was removed after being added.
        if (Asset)
        {
            Asset->StreamingIndex = static_cast<int>(StreamingRenderAssets.size());
            const int* NumStreamedLODs = nullptr;
            const int NumLODGroups = GetNumStreamedLODsArray(Asset->GetRenderAssetType(), NumStreamedLODs);
            //new (StreamingRenderAssets) StreamingResource(Asset, NumStreamedLODs, NumLODGroups, Settings);
            StreamingRenderAssets.emplace_back(StreamingResource(Asset, NumStreamedLODs, NumLODGroups, Settings));
            LOG_DEBUG("Streaming manager add resource {} with LOD count {}", Asset->GetResource()->GetName(), Asset->GetLODCount());
        }
    }
    PendingStreamingRenderAssets.clear();
}

void StreamingManager::UpdatePendingStates(bool bUpdateDynamicComponents)
{
    CheckUserSettings();

    ProcessRemovedRenderAssets();
    ProcessAddedRenderAssets();

    // Fully complete all pending update static data (newly loaded levels).
    // Dynamic bounds are not updated here since the async task uses the async view generated from the last frame.
    // this makes the current dynamic data fully dirty, and it will get refreshed iterativelly for the next full update.
    IncrementalUpdate(1.f, bUpdateDynamicComponents);
}

/**
 * Adds new textures/meshes and level data on the gamethread (while the worker thread isn't active).
 */
void StreamingManager::PrepareAsyncTask(bool bProcessEverything)
{
    StreamingCalculationTask& AsyncTask = AsyncWorkTask;

    // TODO:
    //FTextureMemoryStats Stats;
    //RHIGetTextureMemoryStats(Stats);

    // TODO: Track memory allocated by mesh LODs

    const auto& deviceStats = GetNGIDevice().GetGpuResourceStatistics()->mDeviceResources;
    // MBytes to bytes
    SInt64 totalGraphicsMemory = static_cast<SInt64>(deviceStats.available) * 1024ll * 1024ll;
    SInt64 streamingMemorySize = static_cast<SInt64>(deviceStats.allocated) * 1024ll * 1024ll;

    // When processing all textures, we need unlimited budget so that textures get all at their required states.
    // Same when forcing stream-in, for which we want all used textures to be fully loaded
    if (Settings.VRAMPoolSize > 0 && !bProcessEverything && !Settings.bFullyLoadUsedMeshes)
    {
        const SInt64 TempMemoryBudget = static_cast<SInt64>(Settings.MaxTempMemoryAllowed) * 1024 * 1024;
        AsyncTask.Reset(totalGraphicsMemory, streamingMemorySize, Settings.VRAMPoolSize, TempMemoryBudget, MemoryMargin);
    }
    else
    {
        // Temp must be smaller since membudget only updates if it has a least temp memory available.
        AsyncTask.Reset(0, streamingMemorySize, INT64_MAX, INT64_MAX / 2, 0);
    }

    // Copy desired LODs from the last render frame
    // Now a new streaming task is about to kick in and thus lock the latest complete version of visible resources for task execution
    mResourceRequestLODMapInFlight.clear();
    mResourceRequestLODMapInFlight = mResourceRequestLODMapStaged;

    AsyncTask.StreamingData.Init(CurrentViewInfos, LastWorldUpdateTime);

    LastWorldUpdateTime_LODCalcTask = LastWorldUpdateTime;
}

void StreamingManager::AddStreamingRenderAsset(StreamableResource* InAsset)
{
    std::scoped_lock ScopeLock(CriticalSection);

    // Adds the new texture/mesh to the Pending list, to avoid reallocation of the thread-safe StreamingRenderAssets array.
    Assert(InAsset->StreamingIndex == INDEX_NONE);
    PendingStreamingRenderAssets.emplace_back(InAsset);
    InAsset->StreamingIndex = static_cast<int>(PendingStreamingRenderAssets.size() - 1);
    Assert(InAsset->StreamingIndex == PendingStreamingRenderAssets.back()->StreamingIndex);

    // Mark as pending update while the streamer has not determined the required resolution (unless paused)
    InAsset->bHasStreamingUpdatePending = !bPauseRenderAssetStreaming;

    // Notify that this texture/mesh ptr is valid.
    ReferencedRenderAssets.emplace(InAsset);
}

/**
 * Removes a texture/mesh from the streaming manager.
 */
void StreamingManager::RemoveStreamingRenderAsset(StreamableResource* RenderAsset)
{
    std::scoped_lock ScopeLock(CriticalSection);
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    const int Idx = RenderAsset->StreamingIndex;

    // Remove it from the Pending list if it is there.
    if ((Idx >= 0 && Idx < PendingStreamingRenderAssets.size()) && PendingStreamingRenderAssets[Idx] == RenderAsset)
    {
        PendingStreamingRenderAssets[Idx] = nullptr;
    }
    else if (Idx >= 0 && Idx < StreamingRenderAssets.size() && StreamingRenderAssets[Idx].RenderAsset == RenderAsset)
    {
        StreamingRenderAssets[Idx].RenderAsset = nullptr;
        RemovedRenderAssetIndices.emplace_back(Idx);

        FastResponseRenderAssets.erase(RenderAsset);
        VisibleFastResponseRenderAssetIndices.erase(Idx);
    }

    RenderAsset->StreamingIndex = INDEX_NONE;
    RenderAsset->bHasStreamingUpdatePending = false;

    // Remove reference to this texture/mesh.
    ReferencedRenderAssets.erase(RenderAsset);

    auto entitiesIt = mRegisteredEntitiesMap.find(RenderAsset);
    if (entitiesIt != mRegisteredEntitiesMap.end())
    {
        auto& entities = entitiesIt->second;
        for (auto entityIt = entities.begin(); entityIt != entities.end(); entityIt++)
        {
            auto& entity = *entityIt;
            auto it = mEntityToResourceLookUpMap.find(entity);
            if (it != mEntityToResourceLookUpMap.end())
            {
                mEntityToResourceLookUpMap.erase(it);
            }
        }
        mRegisteredEntitiesMap.erase(entitiesIt);
    }

    mResourceRequestLODMapInFlight.erase(RenderAsset);
    mResourceRequestLODMapStaged.erase(RenderAsset);
    mResourceRequestLODMap.erase(RenderAsset);

    ProcessRemovedRenderAssets();
}

bool StreamingManager::IsFullyStreamedIn(StreamableResource* RenderAsset)
{
    Assert(RenderAsset);

    const StreamingResource* StreamingAsset = GetStreamingRenderAsset(RenderAsset);
    const StreamableResourceState& AssetState = RenderAsset->GetStreamableResourceState();

    if (StreamingAsset)
    {
        int NumLODsToConsiderAsFull = AssetState.LODCount - RenderAsset->GetCachedLODBias();
        return AssetState.ResidentLODCount >= NumLODsToConsiderAsFull;
    }

    return false;
}

/**
 * Mark the textures/meshes with a timestamp. They're about to lose their location-based heuristic and we don't want them to
 * start using LastRenderTime heuristic for a few seconds until they are garbage collected!
 *
 * @param RemovedRenderAssets	List of removed textures or meshes.
 */
void StreamingManager::SetRenderAssetsRemovedTimestamp(const RemovedRenderAssetArray& RemovedRenderAssets)
{
    const double CurrentTime = time::CurrentMs() / 1000.0;
    //for (int Idx = 0; Idx < RemovedRenderAssets.size(); ++Idx)
    //{
    //    // When clearing references to textures/meshes, those textures/meshes could be already deleted.
    //    // This happens because we don't clear texture/mesh references in RemoveStreamingRenderAsset.
    //    const StreamableResource* Asset = RemovedRenderAssets[Idx];
    //    if (!ReferencedRenderAssets.contains(Asset))
    //        continue;

    //    StreamingResource* StreamingRenderAsset = GetStreamingRenderAsset(Asset);
    //    if (StreamingRenderAsset)
    //    {
    //        StreamingRenderAsset->InstanceRemovedTimestamp = CurrentTime;
    //    }
    //}
}

void StreamingManager::SyncStates(bool bCompleteFullUpdateCycle)
{
    // Finish the current update cycle.
    while (ProcessingStage != 0 && bCompleteFullUpdateCycle)
    {
        UpdateResourceStreaming(0, false);
    }

    // Wait for async tasks
    AsyncWork->WaitForCompletion();
    RenderAssetInstanceAsyncWork->WaitForCompletion();

    // Update any pending states, including added/removed textures/meshes.
    // Doing so when ProcessingStage != 0 risk invalidating the indices in the async task used in StreamRenderAssets().
    // This would in practice postpone some of the load and cancel requests.
    UpdatePendingStates(false);
}

/**
 * Returns the corresponding StreamingResource for a texture or mesh.
 */
StreamingResource* StreamingManager::GetStreamingRenderAsset(const StreamableResource* RenderAsset)
{
    std::scoped_lock ScopeLock(CriticalSection);

    if (RenderAsset && (RenderAsset->StreamingIndex >= 0 && RenderAsset->StreamingIndex < AsyncUnsafeStreamingRenderAssets.size()))
    {
        StreamingResource* StreamingRenderAsset = &AsyncUnsafeStreamingRenderAssets[RenderAsset->StreamingIndex];
        Assert(RenderAsset->StreamingIndex == StreamingRenderAsset->RenderAsset->StreamingIndex);

        // If the texture/mesh don't match, this means the texture/mesh is pending in PendingStreamingRenderAssets, for which no StreamingResource* is yet allocated.
        // If this is not acceptable, the caller should first synchronize everything through SyncStates
        return StreamingRenderAsset->RenderAsset == RenderAsset ? StreamingRenderAsset : nullptr;
    }
    else
    {
        return nullptr;
    }
}

/**
 * Updates streaming for an individual texture/mesh, taking into account all view infos.
 *
 * @param RenderAsset	Texture or mesh to update
 */
void StreamingManager::UpdateIndividualRenderAsset(StreamableResource* RenderAsset)
{
    std::scoped_lock ScopeLock(CriticalSection);

    if (!IsStreamingEnabled() || !RenderAsset)
        return;

    // Because we want to priorize loading of this texture,
    // don't process everything as this would send load requests for all textures.
    SyncStates(false);

    StreamingResource* StreamingRenderAsset = GetStreamingRenderAsset(RenderAsset);
    if (!StreamingRenderAsset)
        return;

    const int* NumStreamedLODs;
    const int NumLODGroups = GetNumStreamedLODsArray(StreamingRenderAsset->RenderAssetType, NumStreamedLODs);

    StreamingRenderAsset->UpdateDynamicData(NumStreamedLODs, NumLODGroups, Settings);

    if (StreamingRenderAsset->bForceFullyLoad)   // Somewhat expected at this point.
    {
        StreamingRenderAsset->WantedLODs = StreamingRenderAsset->BudgetedLODs = StreamingRenderAsset->MaxAllowedLODs;
    }

    StreamingRenderAsset->StreamWantedLODs(*this);
}

bool StreamingManager::FastForceFullyResident(StreamableResource* RenderAsset)
{
    Assert(threading::TaskSystem::IsInGameThread());
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    // NOTE: FastResponseRenderAssets usually apply to dynamic objects with fast LOD switching
    //if (false/*CVarStreamingAllowFastForceResident.GetValueOnGameThread()*/ && IsStreamingEnabled() && !bPauseRenderAssetStreaming && RenderAsset && RenderAsset->bIgnoreStreamingLODBias &&
    //    (RenderAsset->bForceLODlevelsToBeResident || RenderAsset->ForceLODLevelsToBeResidentTimestamp >= time::CurrentMs() / 1000.0) &&
    //    (RenderAsset->StreamingIndex >= 0 && RenderAsset->StreamingIndex < StreamingRenderAssets.size()) &&
    //    StreamingRenderAssets[RenderAsset->StreamingIndex].RenderAsset == RenderAsset)
    if (false)
    {
        const int StreamingIdx = RenderAsset->StreamingIndex;
        StreamingResource& Asset = StreamingRenderAssets[StreamingIdx];

        Asset.UpdateStreamingStatus();

        if (Asset.ResidentLODs < Asset.MaxAllowedLODs)
        {
            FastResponseRenderAssets.emplace(Asset.RenderAsset);
            return true;
        }
    }
    return false;
}

// How many workgroups we want to use for ParellelRenderAsset updates.
// Splits the work up a bit more so we don't get as many waits.
// Though adds overhead to GameThread if too high.
static int GParallelRenderAssetsNumWorkgroups = 2;

/**
 * Not thread-safe: Updates a portion (as indicated by 'StageIndex') of all streaming textures,
 * allowing their streaming state to progress.
 *
 * @param Context			Context for the current stage (frame)
 * @param StageIndex		Current stage index
 * @param NumUpdateStages	Number of texture update stages
 */
void StreamingManager::UpdateStreamingRenderAssets(int StageIndex, int NumUpdateStages, bool bAsync)
{
    if (StageIndex == 0)
    {
        CurrentUpdateStreamingRenderAssetIndex = 0;
        InflightRenderAssets.clear();
    }

    int StartIndex = CurrentUpdateStreamingRenderAssetIndex;
    int EndIndex = static_cast<int>(AsyncUnsafeStreamingRenderAssets.size()) * (StageIndex + 1) / NumUpdateStages;

    if (0/*!mEnableStats && GAllowParallelUpdateStreamingRenderAssets*/)
    {
        struct FPacket
        {
            FPacket() = default;
            FPacket(int InStartIndex, int InEndIndex, std::vector<StreamingResource>& InStreamingRenderAssets)
                : StartIndex(InStartIndex)
                , EndIndex(InEndIndex)
                , StreamingRenderAssets(&InStreamingRenderAssets)
            {}
            int StartIndex;
            int EndIndex;
            std::vector<StreamingResource>* StreamingRenderAssets;
            std::vector<int> LocalInflightRenderAssets;
            char FalseSharingSpacerBuffer[64/*PLATFORM_CACHE_LINE_SIZE*/];   // separate Packets to avoid false sharing
        };
        // In order to pass the InflightRenderAssets and DeferredTickCBAssets to each parallel for loop we need to break this into a number of workgroups.
        // Cannot be too large or the overhead of consolidating all the arrays will take too long.  Cannot be too small or the parallel for will not have
        // enough work.  Can be adjusted by CVarStreamingParallelRenderAssetsNumWorkgroups.
        int Num = EndIndex - StartIndex;
        // GetNumWorkerThreads()
        int NumThreadTasks = std::min<int>(std::max(1, threading::TaskSystem::Get()->GetNumAsyncThreads() * GParallelRenderAssetsNumWorkgroups), Num - 1);
        // make sure it never goes to zero, as a divide by zero could happen below
        NumThreadTasks = std::max<int>(NumThreadTasks, 1);
        std::vector<FPacket> Packets;
        Packets.reserve(NumThreadTasks);   // Go ahead and reserve space up front
        int Start = StartIndex;
        int NumRemaining = Num;
        int NumItemsPerGroup = (Num / NumThreadTasks) + 1;
        for (int i = 0; i < NumThreadTasks; ++i)
        {
            int NumAssetsToProcess = std::min<int>(NumRemaining, NumItemsPerGroup);
            Packets.emplace_back(FPacket(Start, Start + NumAssetsToProcess, AsyncUnsafeStreamingRenderAssets));
            Start += NumAssetsToProcess;
            NumRemaining -= NumAssetsToProcess;
            if (NumRemaining <= 0)
            {
                break;
            }
        }

        threading::ParallelFor(
            static_cast<int>(Packets.size()),
            [this, &Packets, &bAsync](int PacketIndex) {
                QUICK_SCOPED_CPU_TIMING("RenderAssetStreaming");

                for (int Index = Packets[PacketIndex].StartIndex; Index < Packets[PacketIndex].EndIndex; ++Index)
                {
                    StreamingResource& StreamingRenderAsset = (*Packets[PacketIndex].StreamingRenderAssets)[Index];

                    // Is this texture/mesh marked for removal? Will get cleanup once the async task is done.
                    if (!StreamingRenderAsset.RenderAsset)
                    {
                        continue;
                    }

                    const int* NumStreamedLODs;
                    const int NumLODGroups = GetNumStreamedLODsArray(StreamingRenderAsset.RenderAssetType, NumStreamedLODs);

                    StreamingRenderAsset.UpdateDynamicData(
                        NumStreamedLODs, NumLODGroups, Settings);   // We always use the Deferred CBs when doing the ParallelFor since those CBs are not thread safe.

                    // Make a list of each texture/mesh that can potentially require additional UpdateStreamingStatus
                    if (StreamingRenderAsset.RequestedLODs != StreamingRenderAsset.ResidentLODs)
                    {
                        Packets[PacketIndex].LocalInflightRenderAssets.emplace_back(Index);
                    }
                }
            });

        for (FPacket Packet : Packets)
        {
            InflightRenderAssets.insert(InflightRenderAssets.end(), Packet.LocalInflightRenderAssets.begin(), Packet.LocalInflightRenderAssets.end());
            Packet.LocalInflightRenderAssets.clear();
        }
        Packets.clear();
    }
    else
    {
        for (int Index = StartIndex; Index < EndIndex; ++Index)
        {
            StreamingResource& StreamingRenderAsset = AsyncUnsafeStreamingRenderAssets[Index];
            Assert(Index == StreamingRenderAsset.RenderAsset->StreamingIndex);
            //FPlatformMisc::Prefetch(&StreamingRenderAsset + 1);

            // Is this texture/mesh marked for removal? Will get cleanup once the async task is done.
            if (!StreamingRenderAsset.RenderAsset)
                continue;

            //STAT(int PreviousResidentLODs = StreamingRenderAsset.ResidentLODs;)

            const int* NumStreamedLODs;
            const int NumLODGroups = GetNumStreamedLODsArray(StreamingRenderAsset.RenderAssetType, NumStreamedLODs);

            StreamingRenderAsset.UpdateDynamicData(NumStreamedLODs, NumLODGroups, Settings);

            // Make a list of each texture/mesh that can potentially require additional UpdateStreamingStatus
            if (StreamingRenderAsset.RequestedLODs != StreamingRenderAsset.ResidentLODs)
            {
                InflightRenderAssets.emplace_back(Index);
            }

            if (Settings.EnableStats)
            {
                //if (StreamingRenderAsset.ResidentLODs > PreviousResidentLODs)
                //{
                //    GatheredStats.LODIOBandwidth += StreamingRenderAsset.GetSize(StreamingRenderAsset.ResidentLODs) - StreamingRenderAsset.GetSize(PreviousResidentLODs);
                //}
            }
        }
    }
    CurrentUpdateStreamingRenderAssetIndex = EndIndex;
}

//static TAutoConsoleVariable<int> CVarTextureStreamingAmortizeCPUToGPUCopy(TEXT("r.Streaming.AmortizeCPUToGPUCopy"), 0,
//                                                                            TEXT("If set and r.Streaming.MaxNumTexturesToStreamPerFrame > 0, limit the number of 2D textures ") TEXT("streamed from CPU memory to GPU memory each frame"),
//                                                                            ECVF_Scalability | ECVF_ExcludeFromPreview);
//
//static TAutoConsoleVariable<int> CVarTextureStreamingMaxNumTexturesToStreamPerFrame(TEXT("r.Streaming.MaxNumTexturesToStreamPerFrame"), 0,
//                                                                                      TEXT("Maximum number of 2D textures allowed to stream from CPU memory to GPU memory each frame. ")
//                                                                                          TEXT("<= 0 means no limit. This has no effect if r.Streaming.AmortizeCPUToGPUCopy is not set"),
//                                                                                      ECVF_Scalability | ECVF_ExcludeFromPreview);

//static FORCEINLINE bool ShouldAmortizeLODCopies()
//{
//    return CVarTextureStreamingAmortizeCPUToGPUCopy.GetValueOnGameThread() && CVarTextureStreamingMaxNumTexturesToStreamPerFrame.GetValueOnGameThread() > 0;
//}

/**
 * Stream textures/meshes in/out, based on the priorities calculated by the async work.
 * @param bProcessEverything	Whether we're processing all textures in one go
 */
void StreamingManager::StreamRenderAssets(bool bProcessEverything)
{
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();
    const StreamingCalculationTask& AsyncTask = AsyncWorkTask;

    // Note that render asset indices referred by the async task could be outdated if UpdatePendingStates() was called between the
    // end of the async task work, and this call to StreamRenderAssets(). This happens when SyncStates(false) is called.

    if (!bPauseRenderAssetStreaming || bProcessEverything)
    {
        for (int AssetIndex : AsyncTask.GetCancelationRequests())
        {
            if ((AssetIndex >= 0 && AssetIndex < StreamingRenderAssets.size()) && !VisibleFastResponseRenderAssetIndices.contains(AssetIndex))
            {
                StreamingRenderAssets[AssetIndex].CancelStreamingRequest();
            }
        }

        //if (!bProcessEverything && ShouldAmortizeLODCopies())
        //{
        //    // Ignore remaining requests since they may be outdated already
        //    PendingLODCopyRequests.clear();
        //    CurrentPendingLODCopyRequestIdx = 0;

        //    // Make copies of the requests so that they can be processed later
        //    for (int AssetIndex : AsyncTask.GetLoadRequests())
        //    {
        //        if (StreamingRenderAssets.IsValidIndex(AssetIndex) && StreamingRenderAssets[AssetIndex].RenderAsset && !VisibleFastResponseRenderAssetIndices.Contains(AssetIndex))
        //        {
        //            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
        //            StreamingRenderAsset.CacheStreamingMetaData();
        //            new (PendingLODCopyRequests) FPendingLODCopyRequest(StreamingRenderAsset.RenderAsset, AssetIndex);
        //        }
        //    }
        //}
        //else
        {
            for (int AssetIndex : AsyncTask.GetLoadRequests())
            {
                if ((AssetIndex >= 0 && AssetIndex < StreamingRenderAssets.size()) && !VisibleFastResponseRenderAssetIndices.contains(AssetIndex))
                {
                    StreamingRenderAssets[AssetIndex].StreamWantedLODs(*this);
                }
            }
        }
    }

    for (int AssetIndex : AsyncTask.GetPendingUpdateDirties())
    {
        if ((AssetIndex >= 0 && AssetIndex < StreamingRenderAssets.size()) && !VisibleFastResponseRenderAssetIndices.contains(AssetIndex))
        {
            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
            const bool bNewState = StreamingRenderAsset.HasUpdatePending(bPauseRenderAssetStreaming, AsyncTask.HasAnyView());

            // Always update the texture/mesh and the streaming texture/mesh together to make sure they are in sync.
            StreamingRenderAsset.bHasUpdatePending = bNewState;
            if (StreamingRenderAsset.RenderAsset)
            {
                StreamingRenderAsset.RenderAsset->bHasStreamingUpdatePending = bNewState;
            }
        }
    }

    // Reset BudgetLODBias and MaxAllowedLODs before we forget. Otherwise, new requests may stream out forced LODs
    for (auto It = VisibleFastResponseRenderAssetIndices.begin(); It != VisibleFastResponseRenderAssetIndices.end(); It++)
    {
        const int Idx = *It;

        if ((Idx >= 0 && Idx < StreamingRenderAssets.size()))
        {
            StreamingResource& Asset = StreamingRenderAssets[Idx];
            Asset.BudgetLODBias = 0;
            Asset.bIgnoreStreamingLODBias = true;

            // If optional LODs is available but not counted, they will be counted later in UpdateDynamicData
            if (Asset.RenderAsset)
            {
                Asset.MaxAllowedLODs = std::max<SInt8>(Asset.MaxAllowedLODs, Asset.RenderAsset->GetStreamableResourceState().NonStreamingLODCount/*NumNonOptionalLODs*/);
            }
        }
    }

    VisibleFastResponseRenderAssetIndices.clear();
}

void StreamingManager::ProcessPendingLODCopyRequests()
{
    if (!ShouldAmortizeLODCopies())
    {
        return;
    }

    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();
    int NumRemainingRequests = 0/*CVarTextureStreamingMaxNumTexturesToStreamPerFrame.GetValueOnGameThread()*/;

    while (NumRemainingRequests && CurrentPendingLODCopyRequestIdx < PendingLODCopyRequests.size())
    {
        const FPendingLODCopyRequest& Request = PendingLODCopyRequests[CurrentPendingLODCopyRequestIdx++];

        if (Request.RenderAsset)
        {
            StreamingResource* StreamingRenderAsset = nullptr;

            if ((Request.CachedIdx >= 0 && Request.CachedIdx < StreamingRenderAssets.size()) && StreamingRenderAssets[Request.CachedIdx].RenderAsset == Request.RenderAsset)
            {
                StreamingRenderAsset = &StreamingRenderAssets[Request.CachedIdx];
            }
            else if (ReferencedRenderAssets.contains(Request.RenderAsset))
            {
                // Texture is still valid but its index has been changed
                Assert(Request.RenderAsset->StreamingIndex >= 0 && Request.RenderAsset->StreamingIndex < StreamingRenderAssets.size());
                StreamingRenderAsset = &StreamingRenderAssets[Request.RenderAsset->StreamingIndex];
            }

            if (StreamingRenderAsset)
            {
                StreamingRenderAsset->StreamWantedLODsUsingCachedData(*this);
                --NumRemainingRequests;
            }
        }
    }
}

void StreamingManager::CheckUserSettings()
{
    if (true/*CVarStreamingUseFixedPoolSize.GetValueOnGameThread() == 0*/)
    {
        const int PoolSizeSetting = -1/*CVarStreamingPoolSize.GetValueOnGameThread()*/;

        SInt64 TexturePoolSize = Settings.MemoryPoolSize;
        if (true/*PoolSizeSetting == -1*/)
        {
            // TODO:
            //FTextureMemoryStats Stats;
            //RHIGetTextureMemoryStats(Stats);
            //if (StreamingSettings::GetStreamingSettings().PoolSizeVRAMPercentage > 0 && Stats.TotalGraphicsMemory > 0)
            //{
            //    TexturePoolSize = Stats.TotalGraphicsMemory * StreamingSettings::GetStreamingSettings().PoolSizeVRAMPercentage / 100;
            //}
            GetNGIDevice().GetGpuResourceStatistics()->mTotalSize;
        }
        else
        {
            TexturePoolSize = SInt64(PoolSizeSetting) * 1024ll * 1024ll;
        }

        if (TexturePoolSize != Settings.MemoryPoolSize)
        {
            LOG_INFO("Texture pool size now {} MB", int(TexturePoolSize / 1024 / 1024));
            //CSV_METADATA(TEXT("StreamingPoolSizeMB"), *WriteToString<32>(int32(TexturePoolSize / 1024 / 1024)));
            Settings.MemoryPoolSize = static_cast<int>(TexturePoolSize);
        }
    }
}

void StreamingManager::SetLastUpdateTime()
{
    // Update the last update time.
    float WorldTime = 0;


    if (WorldTime > 0)
    {
        LastWorldUpdateTime = WorldTime - .5f;
    }
    else if (StreamingUtils::IsEditor())
    {
        LastWorldUpdateTime = -FLT_MAX;   // In editor, visibility is not taken into consideration unless in PIE.
    }
}

void StreamingManager::UpdateStats()
{
    float DeltaStatTime = (float)(GatheredStats.Timestamp - DisplayedStats.Timestamp);
    if (DeltaStatTime > 1e-8)
    {
        GatheredStats.IOBandwidth = DeltaStatTime > 1e-8 ? static_cast<SInt64>(GatheredStats.IOBandwidth / DeltaStatTime) : 0;
    }
    DisplayedStats = GatheredStats;
    GatheredStats.CallbacksCycles = 0;
    GatheredStats.IOBandwidth = 0;
    MemoryOverBudget = DisplayedStats.OverBudget;
    MaxEverRequired = std::max<SInt64>(MaxEverRequired, DisplayedStats.RequiredPool);
}

void StreamingManager::UpdateCSVOnlyStats()
{
    DisplayedStats = GatheredStats;
}

void StreamingManager::LogViewLocationChange()
{
#if STREAMING_LOG_VIEWCHANGES
    static bool bWasLocationOveridden = false;
    bool bIsLocationOverridden = false;
    for (int ViewIndex = 0; ViewIndex < CurrentViewInfos.size(); ++ViewIndex)
    {
        FStreamingViewInfo& ViewInfo = CurrentViewInfos[ViewIndex];
        if (ViewInfo.bOverrideLocation)
        {
            bIsLocationOverridden = true;
            break;
        }
    }
    if (bIsLocationOverridden != bWasLocationOveridden)
    {
        UE_LOG(LogContentStreaming, Log, TEXT("Texture streaming view location is now %s."), bIsLocationOverridden ? TEXT("OVERRIDDEN") : TEXT("normal"));
        bWasLocationOveridden = bIsLocationOverridden;
    }
#endif
}

/**
 * Main function for the texture streaming system, based on texture priorities and asynchronous processing.
 * Updates streaming, taking into account all view infos.
 *
 * @param DeltaTime				Time since last call in seconds
 * @param bProcessEverything	[opt] If true, process all resources with no throttling limits
 */

//static TAutoConsoleVariable<int> CVarUseBackgroundThreadPool(TEXT("r.Streaming.UseBackgroundThreadPool"), 1, TEXT("If true, use the background thread pool for LOD calculations."));

//class FUpdateStreamingRenderAssetsTask
//{
//    StreamingManager* Manager;
//    int StageIdx;
//    int NumUpdateStages;
//
//public:
//    FUpdateStreamingRenderAssetsTask(StreamingManager* InManager, int InStageIdx, int InNumUpdateStages)
//        : Manager(InManager)
//        , StageIdx(InStageIdx)
//        , NumUpdateStages(InNumUpdateStages)
//    {}
//    static FORCEINLINE TStatId GetStatId() { RETURN_QUICK_DECLARE_CYCLE_STAT(FUpdateStreamingRenderAssetsTask, STATGROUP_TaskGraphTasks); }
//    static FORCEINLINE ENamedThreads::Type GetDesiredThread() { return ENamedThreads::AnyNormalThreadNormalTask; }
//    static FORCEINLINE ESubsequentsMode::Type GetSubsequentsMode() { return ESubsequentsMode::TrackSubsequents; }
//    void DoTask(ENamedThreads::Type CurrentThread, const FGraphEventRef& MyCompletionGraphEvent)
//    {
//        FOptionalTaskTagScope Scope(ETaskTag::EParallelGameThread);
//        Manager->UpdateStreamingRenderAssets(StageIdx, NumUpdateStages, true);
//    }
//};

void StreamingManager::UpdateResourceStreaming(float DeltaTime, bool bProcessEverything /*=false*/)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingManager::UpdateResourceStreaming");

    std::scoped_lock ScopeLock(CriticalSection);
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    //CSV_SCOPED_TIMING_STAT_EXCLUSIVE(RenderAssetStreaming);
    //CSV_SCOPED_SET_WAIT_STAT(RenderAssetStreaming);

    const bool bUseThreadingForPerf = true/*FApp::ShouldUseThreadingForPerformance()*/;

    LogViewLocationChange();
    //STAT(DisplayedStats.Apply();)

    //CSV_CUSTOM_STAT(TextureStreaming, StreamingPool, ((float)(DisplayedStats.RequiredPool + (StreamingSettings::GetStreamingSettings().PoolSizeVRAMPercentage > 0 ? 0 : DisplayedStats.NonStreamingLODs))) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, SafetyPool, ((float)DisplayedStats.SafetyPool) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, TemporaryPool, ((float)DisplayedStats.TemporaryPool) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, CachedLODs, ((float)DisplayedStats.CachedLODs) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, WantedLODs, ((float)DisplayedStats.WantedLODs) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, ResidentMeshMem, ((float)DisplayedStats.ResidentMeshMem) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, StreamedMeshMem, ((float)DisplayedStats.StreamedMeshMem) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, NonStreamingLODs, ((float)DisplayedStats.NonStreamingLODs) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set);
    //CSV_CUSTOM_STAT(TextureStreaming, PendingStreamInData, ((float)DisplayedStats.PendingRequests) / (1024.0f * 1024.0f), ECsvCustomStatOp::Set)

    // FIXME:
    //if (RenderAssetInstanceAsyncWork && !RenderAssetInstanceAsyncWork->Complete())
    //{
    //    RenderAssetInstanceAsyncWork->WaitForCompletion();
    //}

    if (NumRenderAssetProcessingStages <= 0 || bProcessEverything)
    {
        if (AsyncWork && !AsyncWork->Complete())
        {   // Is the AsyncWork is running for some reason? (E.g. we reset the system by simply setting ProcessingStage to 0.)
            AsyncWork->WaitForCompletion();
        }

        ProcessingStage = 0;
        NumRenderAssetProcessingStages = Settings.FramesForFullUpdate;

        // Update Thread Data
        SetLastUpdateTime();
        UpdateStreamingRenderAssets(0, 1, false);

        UpdatePendingStates(true);
        PrepareAsyncTask(bProcessEverything/* || Settings.bStressTest*/);
        AsyncWork = threading::TTask<StreamingCalculationTaskFunction>::Create({}).HoldTask(&AsyncWorkTask);
        AsyncWork->ReleaseTask(threading::TaskSystem::GetCurrentThreadID());

        TickFastResponseAssets();

        StreamRenderAssets(bProcessEverything);

//        STAT(GatheredStats.SetupAsyncTaskCycles = 0);
//        STAT(GatheredStats.UpdateStreamingDataCycles = 0);
//        STAT(GatheredStats.StreamRenderAssetsCycles = 0);
//        STAT(GatheredStats.CallbacksCycles = 0);
        if (Settings.EnableStats)
        {
            UpdateStats();
        }
        if (Settings.EnableCSVStats)
        {
            UpdateCSVOnlyStats();
        }
    }
    else if (ProcessingStage == 0)
    {
        //STAT(GatheredStats.SetupAsyncTaskCycles = -(int)FPlatformTime::Cycles();)

        NumRenderAssetProcessingStages = Settings.FramesForFullUpdate;

        if (AsyncWork && !AsyncWork->Complete())
        {   // Is the AsyncWork is running for some reason? (E.g. we reset the system by simply setting ProcessingStage to 0.)
            AsyncWork->WaitForCompletion();
        }

        // Here we rely on dynamic components to be updated on the last stage, in order to split the workload.
        UpdatePendingStates(false);
        PrepareAsyncTask(bProcessEverything/* || Settings.bStressTest*/);
        AsyncWork = threading::TTask<StreamingCalculationTaskFunction>::Create({}).HoldTask(&AsyncWorkTask);
        AsyncWork->ReleaseTask(threading::ThreadID::AsyncThread/*CVarUseBackgroundThreadPool.GetValueOnGameThread() ? GBackgroundPriorityThreadPool : GThreadPool*/);
        TickFastResponseAssets();
        ++ProcessingStage;

        //STAT(GatheredStats.SetupAsyncTaskCycles += FPlatformTime::Cycles();)
    }
    else if (ProcessingStage <= NumRenderAssetProcessingStages)
    {
        //STAT(int StartTime = (int32)FPlatformTime::Cycles();)

        if (PendingStreamingRenderAssets.size() > 0 && 1/*CVarProcessAddedRenderAssetsAfterAsyncWork.GetValueOnGameThread()*/ && AsyncWork->Complete())
        {
            // This will add to the StreamingRenderAssets array potentially reallocating it, but if the Async task has completed, that should be safe.
            // As we're only adding items, existing indicies in InflightRenderAssets etc will still be valid.
            ProcessAddedRenderAssets();
        }

        if (ProcessingStage == 1)
        {
            SetLastUpdateTime();
        }

        TickFastResponseAssets();

        // Optimization: overlapping UpdateStreamingRenderAssets() and IncrementalUpdate();
        // Restrict this optimization to platforms tested to have a win;
        // Platforms tested and results (ave exec time of UpdateResourceStreaming):
        //   PS4 Pro - from ~0.55 ms/frame to ~0.15 ms/frame
        //   XB1 X - from ~0.45 ms/frame to ~0.17 ms/frame
        const bool bOverlappedExecution = bUseThreadingForPerf && !StreamingUtils::IsEditor() /*CVarStreamingOverlapAssetAndLevelTicks.GetValueOnGameThread()*/;
        if (false/*bOverlappedExecution*/)
        {
            if (StreamingRenderAssetsSyncEvent && StreamingRenderAssetsSyncEvent->Complete())
            {
                StreamingRenderAssetsSyncEvent.Reset();
            }

            StreamingRenderAssetsSyncEvent = threading::Dispatch<threading::ThreadID::GameThreadLocal>({}, [this, DeltaTime = DeltaTime](auto) {
                QUICK_SCOPED_CPU_TIMING("EParallelGameThread");
                UpdateStreamingRenderAssets(ProcessingStage - 1, NumRenderAssetProcessingStages, true);
            });
        }
        else
        {
            UpdateStreamingRenderAssets(ProcessingStage - 1, NumRenderAssetProcessingStages);
        }

        IncrementalUpdate(1.f / (float)std::max(NumRenderAssetProcessingStages - 1, 1), true);   // -1 since we don't want to do anything at stage 0.
        ++ProcessingStage;

        //STAT(GatheredStats.UpdateStreamingDataCycles = FMath::Max<uint32>(ProcessingStage > 2 ? GatheredStats.UpdateStreamingDataCycles : 0, FPlatformTime::Cycles() - StartTime);)
    }
    else if (AsyncWork->Complete())
    {
        //STAT(GatheredStats.StreamRenderAssetsCycles = -(int32)FPlatformTime::Cycles();)

        if (PendingStreamingRenderAssets.size() > 0 && 1/*CVarProcessAddedRenderAssetsAfterAsyncWork.GetValueOnGameThread()*/)
        {
            // This will add to the StreamingRenderAssets array potentially reallocating it, but if the Async task has completed, that should be safe.
            // As we're only adding items, existing indicies in InflightRenderAssets etc will still be valid.
            ProcessAddedRenderAssets();
        }

        // Since this step is lightweight, tick each texture inflight here, to accelerate the state changes.
        for (int TextureIndex : InflightRenderAssets)
        {
            StreamingRenderAssets[TextureIndex].UpdateStreamingStatus();
        }

        TickFastResponseAssets();

        StreamRenderAssets(bProcessEverything);
        // Release the old view now as the destructors can be expensive. Now only the dynamic manager holds a ref.
        AsyncWorkTask.ReleaseAsyncViews();
        IncrementalUpdate(1.f / (float)std::max(NumRenderAssetProcessingStages - 1, 1), true);   // Just in case continue any pending update.

        ProcessingStage = 0;

        if (Settings.EnableStats)
        {
            UpdateStats();
        }
        if (Settings.EnableCSVStats)
        {
            UpdateCSVOnlyStats();
        }
    }

    if (!bProcessEverything)
    {
        ProcessPendingLODCopyRequests();
    }

    // FIXME:
    //RenderAssetInstanceAsyncWork.Reset();
    //RenderAssetInstanceAsyncWork = threading::TTask<StreamingUpdateTask>::Create({}).HoldTask();
    //if (bUseThreadingForPerf)
    //{
    //    RenderAssetInstanceAsyncWork->ReleaseTask(threading::ThreadID::AsyncThread);
    //}
    //else
    //{
    //    RenderAssetInstanceAsyncWork->ReleaseTask(threading::TaskSystem::GetCurrentThreadID());
    //}
}

/**
 * Blocks till all pending requests are fulfilled.
 *
 * @param TimeLimit		Optional time limit for processing, in seconds. Specifying 0 means infinite time limit.
 * @param bLogResults	Whether to dump the results to the log.
 * @return				Number of streaming requests still in flight, if the time limit was reached before they were finished.
 */
int StreamingManager::BlockTillAllRequestsFinished(float TimeLimit /*= 0.0f*/, bool bLogResults /*= false*/)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingManager::BlockTillAllRequestsFinished");

    std::scoped_lock ScopeLock(CriticalSection);
    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();

    double StartTime = time::CurrentMs() / 1000.0;

    while (!IsAssetStreamingSuspended())
    {
        // Optionally synchronize the states of async work before we wait for outstanding work to be completed.
        if (false/*CVarSyncStatesWhenBlocking.GetValueOnGameThread() != 0*/)
        {
            SyncStates(true);
        }

        int NumOfInFlights = 0;

        for (StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
        {
            const bool bValid = StreamingRenderAsset.UpdateStreamingStatus().IsValidForStreamingRequest();
            if (bValid && StreamingRenderAsset.RequestedLODs != StreamingRenderAsset.ResidentLODs)
            {
                ++NumOfInFlights;
            }
        }

        if (NumOfInFlights && (TimeLimit == 0 || (float)(time::CurrentMs() / 1000.0 - StartTime) < TimeLimit))
        {
            threading::FlushRenderingCommands();
            std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<SInt64>(StreamableResource::StreamingSleepDeltaTimeInSeconds * 1000)));
        }
        else
        {
            if (bLogResults)
            {
                float BlockedMillis = (float)(time::CurrentMs() / 1000.0 - StartTime) * 1000;
                if (BlockedMillis > 0.1f)
                {
                    LOG_INFO("Blocking on mesh streaming: {} ms ({} still in flight)", BlockedMillis, NumOfInFlights);
                }
            }
            return NumOfInFlights;
        }
    }
    return 0;
}

void StreamingManager::GetObjectReferenceBounds(const void* RefObject, std::vector<BoundingBox>& AssetBoxes)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingManager::GetEntityReferenceBounds");

    std::scoped_lock ScopeLock(CriticalSection);

    const StreamableResource* RenderAsset = reinterpret_cast<const StreamableResource*>(RefObject);
    if (RenderAsset)
    {
        auto meshAssetDataRes = TypeCast<resource::MeshAssetDataResource>(RenderAsset->GetResource());
        if (meshAssetDataRes)
        {
            AssetBoxes.emplace_back(meshAssetDataRes->GetAssetData()->GetBoundingBox());
        }
    }
}

//#if !CROSSENGINE_RELEASE

//bool StreamingManager::HandleDumpTextureStreamingStatsCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    float CurrentOccupancyPct = 0.f;
//    float TargetOccupancyPct = 0.f;
//    if (DisplayedStats.StreamingPool > 0)
//    {
//        CurrentOccupancyPct = 100.f * (DisplayedStats.WantedLODs / (float)DisplayedStats.StreamingPool);
//        TargetOccupancyPct = 100.f * (DisplayedStats.RequiredPool / (float)DisplayedStats.StreamingPool);
//    }
//
//    float StreamingPoolMB = DisplayedStats.StreamingPool / 1024.f / 1024.f;
//
//    // uses csv stats for access in test builds
//    Ar.Logf(TEXT("--------------------------------------------------------"));
//    Ar.Logf(TEXT("Texture Streaming Stats:"));
//    Ar.Logf(TEXT("Total Pool Size (aka RenderAssetPool) = %.2f MB"), DisplayedStats.RenderAssetPool / 1024.f / 1024.f);
//    Ar.Logf(TEXT("Non-Streaming LODs = %.2f MB"), DisplayedStats.NonStreamingLODs / 1024.f / 1024.f);
//    Ar.Logf(TEXT("Remaining Streaming Pool Size = %.2f MB"), StreamingPoolMB);
//    Ar.Logf(TEXT("Streaming Assets, Current/Pool = %.2f / %.2f MB (%d%%)"), DisplayedStats.WantedLODs / 1024.f / 1024.f, StreamingPoolMB, FMath::RoundToInt(CurrentOccupancyPct));
//    Ar.Logf(TEXT("Streaming Assets, Target/Pool =  %.2f / %.2f MB (%d%%)"), DisplayedStats.RequiredPool / 1024.f / 1024.f, StreamingPoolMB, FMath::RoundToInt(TargetOccupancyPct));
//    Ar.Logf(TEXT("--------------------------------------------------------"));
//
//    return true;
//}
//
//bool StreamingManager::HandleListStreamingRenderAssetsCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();
//
//    SyncStates(true);
//
//    const bool bShouldOnlyListUnkownRef = FParse::Command(&Cmd, TEXT("UNKOWNREF"));
//    EStreamableRenderAssetType ListAssetType = EStreamableRenderAssetType::None;
//    {
//        std::string AssetTypeStr;
//        if (FParse::Value(Cmd, TEXT("AssetType="), AssetTypeStr))
//        {
//            if (AssetTypeStr == TEXT("Texture"))
//            {
//                ListAssetType = EStreamableRenderAssetType::Texture;
//            }
//            else if (AssetTypeStr == TEXT("StaticMesh"))
//            {
//                ListAssetType = EStreamableRenderAssetType::StaticMesh;
//            }
//            else if (AssetTypeStr == TEXT("SkeletalMesh"))
//            {
//                ListAssetType = EStreamableRenderAssetType::SkeletalMesh;
//            }
//        }
//    }
//
//    // Sort texture/mesh by names so that the state can be compared between runs.
//    TMap<std::string, int32> SortedRenderAssets;
//    for (int Idx = 0; Idx < StreamingRenderAssets.size(); ++Idx)
//    {
//        const StreamingResource& StreamingRenderAsset = StreamingRenderAssets[Idx];
//        if (!StreamingRenderAsset.RenderAsset)
//            continue;
//        if (bShouldOnlyListUnkownRef && !StreamingRenderAsset.bUseUnkownRefHeuristic)
//            continue;
//
//        SortedRenderAssets.Add(StreamingRenderAsset.RenderAsset->GetFullName(), Idx);
//    }
//
//    SortedRenderAssets.KeySort(TLess<std::string>());
//
//    for (TMap<std::string, int32>::TConstIterator It(SortedRenderAssets); It; ++It)
//    {
//        const StreamingResource& StreamingRenderAsset = StreamingRenderAssets[It.Value()];
//        const StreamableResource* RenderAsset = StreamingRenderAsset.RenderAsset;
//        const StreamableResourceState ResourceState = RenderAsset->GetStreamableResourceState();
//        const EStreamableRenderAssetType AssetType = StreamingRenderAsset.RenderAssetType;
//
//        if (ListAssetType != EStreamableRenderAssetType::None && ListAssetType != AssetType)
//        {
//            continue;
//        }
//
//        UE_LOG(LogContentStreaming, Log, TEXT("%s [%d] : %s"), StreamingResource::GetStreamingAssetTypeStr(AssetType), It.Value(), *RenderAsset->GetFullName());
//
//        const int CurrentLODIndex = ResourceState.LODCountToAssetFirstLODIdx(ResourceState.NumResidentLODs);
//        const int WantedLODIndex = ResourceState.LODCountToAssetFirstLODIdx(StreamingRenderAsset.GetPerfectWantedLODs());
//        const int MaxAllowedLODIndex = ResourceState.LODCountToAssetFirstLODIdx(StreamingRenderAsset.MaxAllowedLODs);
//
//        FTexturePlatformData** TexturePlatformData = Cast<UTexture>(RenderAsset) ? const_cast<UTexture*>(Cast<UTexture>(RenderAsset))->GetRunningPlatformData() : nullptr;
//        if (AssetType == EStreamableRenderAssetType::Texture && TexturePlatformData && *TexturePlatformData)
//        {
//            const UTexture2D* Texture2D = Cast<UTexture2D>(RenderAsset);
//            const UVolumeTexture* VolumeTexture = Cast<UVolumeTexture>(RenderAsset);
//            const UTexture2DArray* Texture2DArray = Cast<UTexture2DArray>(RenderAsset);
//            const TIndirecstd::vector<struct FTexture2DLODMap>& TextureLODs = (*TexturePlatformData)->LODs;
//
//            auto OutputLODsString = [&](int OutputIndex) -> std::string {
//                const FTexture2DLODMap& OutputLODs = TextureLODs[OutputIndex];
//                if (Texture2D)
//                {
//                    return std::string::Printf(TEXT("%dx%d"), OutputLODs.SizeX, OutputLODs.SizeY);
//                }
//                else if (VolumeTexture)
//                {
//                    return std::string::Printf(TEXT("%dx%dx%d"), OutputLODs.SizeX, OutputLODs.SizeY, OutputLODs.SizeZ);
//                }
//                else if (Texture2DArray)
//                {
//                    return std::string::Printf(TEXT("%dx%d*%d"), OutputLODs.SizeX, OutputLODs.SizeY, OutputLODs.SizeZ);
//                }
//                else   // Unkown type fallback
//                {
//                    return std::string::Printf(TEXT("%d?%d?%d"), OutputLODs.SizeX, OutputLODs.SizeY, OutputLODs.SizeZ);
//                }
//            };
//
//            if (StreamingRenderAsset.LastRenderTime != UE_MAX_FLT)
//            {
//                UE_LOG(LogContentStreaming,
//                       Log,
//                       TEXT("    Current=%s  Wanted=%s MaxAllowed=%s LastRenderTime=%.3f BudgetBias=%d Group=%s"),
//                       *OutputLODsString(CurrentLODIndex),
//                       *OutputLODsString(WantedLODIndex),
//                       *OutputLODsString(MaxAllowedLODIndex),
//                       StreamingRenderAsset.LastRenderTime,
//                       StreamingRenderAsset.BudgetLODBias,
//                       UTexture::GetTextureGroupString(static_cast<TextureGroup>(StreamingRenderAsset.LODGroup)));
//            }
//            else
//            {
//                UE_LOG(LogContentStreaming,
//                       Log,
//                       TEXT("    Current=%s Wanted=%s MaxAllowed=%s BudgetBias=%d Group=%s"),
//                       *OutputLODsString(CurrentLODIndex),
//                       *OutputLODsString(WantedLODIndex),
//                       *OutputLODsString(MaxAllowedLODIndex),
//                       StreamingRenderAsset.BudgetLODBias,
//                       UTexture::GetTextureGroupString(static_cast<TextureGroup>(StreamingRenderAsset.LODGroup)));
//            }
//        }
//        else
//        {
//            const float LastRenderTime = StreamingRenderAsset.LastRenderTime;
//            const UStaticMesh* StaticMesh = Cast<UStaticMesh>(RenderAsset);
//            std::string LODGroupName = TEXT("Unknown");
//#    if WITH_EDITORONLY_DATA
//            if (StaticMesh)
//            {
//                LODGroupName = StaticMesh->LODGroup.ToString();
//            }
//#    endif
//            UE_LOG(LogContentStreaming,
//                   Log,
//                   TEXT("    CurrentLOD=%d WantedLOD=%d MaxAllowedLOD=%d NumLODs=%d NumForcedLODs=%d LastRenderTime=%s BudgetBias=%d Group=%s"),
//                   CurrentLODIndex,
//                   WantedLODIndex,
//                   MaxAllowedLODIndex,
//                   ResourceState.MaxNumLODs,
//                   StreamingRenderAsset.NumForcedLODs,
//                   LastRenderTime == UE_MAX_FLT ? TEXT("NotTracked") : *std::string::Printf(TEXT("%.3f"), LastRenderTime),
//                   StreamingRenderAsset.BudgetLODBias,
//                   *LODGroupName);
//        }
//    }
//    return true;
//}
//
//bool StreamingManager::HandleResetMaxEverRequiredRenderAssetMemoryCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    Ar.Logf(TEXT("OldMax: %u MaxEverRequired Reset."), MaxEverRequired);
//    ResetMaxEverRequired();
//    return true;
//}

//bool StreamingManager::HandleCancelRenderAssetStreamingCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    UTexture::CancelPendingTextureStreaming();
//    UStaticMesh::CancelAllPendingStreamingActions();
//    USkeletalMesh::CancelAllPendingStreamingActions();
//    return true;
//}

//bool StreamingManager::HandleNumStreamedLODsCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    std::string NumTextureString(FParse::Token(Cmd, 0));
//    std::string NumLODsString(FParse::Token(Cmd, 0));
//    std::string LODGroupType(FParse::Token(Cmd, false));
//    int LODGroup = (NumTextureString.Len() > 0) ? FCString::Atoi(*NumTextureString) : MAX_int32;
//    int NumLODs = (NumLODsString.Len() > 0) ? FCString::Atoi(*NumLODsString) : MAX_int32;
//    if ((LODGroupType == TEXT("") || LODGroupType == TEXT("Texture")) && LODGroup >= 0 && LODGroup < TEXTUREGROUP_MAX)
//    {
//        FTextureLODGroup& TexGroup = UDeviceProfileManager::Get().GetActiveProfile()->GetTextureLODSettings()->GetTextureLODGroup(TextureGroup(LODGroup));
//        if (NumLODs >= -1 && NumLODs <= MAX_TEXTURE_LOD_COUNT)
//        {
//            TexGroup.NumStreamedLODs = NumLODs;
//        }
//        Ar.Logf(TEXT("%s.NumStreamedLODs = %d"), UTexture::GetTextureGroupString(TextureGroup(LODGroup)), TexGroup.NumStreamedLODs);
//    }
//    else if (LODGroupType == TEXT("StaticMesh"))
//    {
//        // TODO
//        Ar.Logf(TEXT("NumStreamedLODs command is not implemented for static mesh yet"));
//    }
//    else if (LODGroupType == TEXT("SkeletalMesh"))
//    {
//        // TODO
//        Ar.Logf(TEXT("NumStreamedLODs command is not implemented for skeletal mesh yet"));
//    }
//    else
//    {
//        Ar.Logf(TEXT("Usage: NumStreamedLODs LODGroupIndex <N> [Texture|StaticMesh|SkeletalMesh]"));
//    }
//    return true;
//}
//
//bool StreamingManager::HandleTrackRenderAssetCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    std::string AssetName(FParse::Token(Cmd, 0));
//    if (TrackRenderAsset(AssetName))
//    {
//        Ar.Logf(TEXT("Textures or meshes containing \"%s\" are now tracked."), *AssetName);
//    }
//    return true;
//}
//
//bool StreamingManager::HandleListTrackedRenderAssetsCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    std::string NumAssetString(FParse::Token(Cmd, 0));
//    int NumAssets = (NumAssetString.Len() > 0) ? FCString::Atoi(*NumAssetString) : -1;
//    ListTrackedRenderAssets(Ar, NumAssets);
//    return true;
//}

//bool StreamingManager::HandleDebugTrackedRenderAssetsCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    // The ENABLE_RENDER_ASSET_TRACKING macro is defined in ContentStreaming.cpp and not available here. This code does not compile any more.
//#    ifdef ENABLE_TEXTURE_TRACKING_BROKEN
//    int NumTrackedTextures = GTrackedTextureNames.size();
//    if (NumTrackedTextures)
//    {
//        for (int StreamingIndex = 0; StreamingIndex < StreamingTextures.size(); ++StreamingIndex)
//        {
//            StreamingResource& StreamingTexture = StreamingTextures[StreamingIndex];
//            if (StreamingTexture.Texture)
//            {
//                // See if it matches any of the texture names that we're tracking.
//                std::string TextureNameString = StreamingTexture.Texture->GetFullName();
//                const TCHAR* TextureName = *TextureNameString;
//                for (int TrackedTextureIndex = 0; TrackedTextureIndex < NumTrackedTextures; ++TrackedTextureIndex)
//                {
//                    const std::string& TrackedTextureName = GTrackedTextureNames[TrackedTextureIndex];
//                    if (FCString::Stristr(TextureName, *TrackedTextureName) != NULL)
//                    {
//                        FTrackedTextureEvent* LastEvent = NULL;
//                        for (int LastEventIndex = 0; LastEventIndex < GTrackedTextures.size(); ++LastEventIndex)
//                        {
//                            FTrackedTextureEvent* Event = &GTrackedTextures[LastEventIndex];
//                            if (FCString::Strcmp(TextureName, Event->TextureName) == 0)
//                            {
//                                LastEvent = Event;
//                                break;
//                            }
//                        }
//
//                        if (LastEvent)
//                        {
//                            Ar.Logf(TEXT("Texture: \"%s\", ResidentLODs: %d/%d, RequestedLODs: %d, WantedLODs: %d, DynamicWantedLODs: %d, StreamingStatus: %d, StreamType: %s, Boost: %.1f"),
//                                    TextureName,
//                                    LastEvent->NumResidentLODs,
//                                    StreamingTexture.Texture->GetNumLODs(),
//                                    LastEvent->NumRequestedLODs,
//                                    LastEvent->WantedLODs,
//                                    LastEvent->DynamicWantedLODs.ComputeLOD(&StreamingTexture, LODBias, false),
//                                    LastEvent->StreamingStatus,
//                                    GStreamTypeNames[StreamingTexture.GetStreamingType()],
//                                    StreamingTexture.BoostFactor);
//                        }
//                        else
//                        {
//                            Ar.Logf(TEXT("Texture: \"%s\", StreamType: %s, Boost: %.1f"), TextureName, GStreamTypeNames[StreamingTexture.GetStreamingType()], StreamingTexture.BoostFactor);
//                        }
//                        for (int HandlerIndex = 0; HandlerIndex < TextureStreamingHandlers.size(); HandlerIndex++)
//                        {
//                            FStreamingHandlerTextureBase* TextureStreamingHandler = TextureStreamingHandlers[HandlerIndex];
//                            float MaxSize = 0;
//                            float MaxSize_VisibleOnly = 0;
//                            FFloatLODLevel HandlerWantedLODs = TextureStreamingHandler->GetWantedSize(*this, StreamingTexture, HandlerDistance);
//                            Ar.Logf(TEXT("    Handler %s: WantedLODs: %d, PerfectWantedLODs: %d, Distance: %f"),
//                                    TextureStreamingHandler->HandlerName,
//                                    HandlerWantedLODs.ComputeLOD(&StreamingTexture, LODBias, false),
//                                    HandlerWantedLODs.ComputeLOD(&StreamingTexture, LODBias, true),
//                                    HandlerDistance);
//                        }
//
//                        for (int LevelIndex = 0; LevelIndex < ThreadSettings.LevelData.size(); ++LevelIndex)
//                        {
//                            StreamingManager::FThreadLevelData& LevelData = ThreadSettings.LevelData[LevelIndex].Value;
//                            std::vector<FStreamableTextureInstance4>* TextureInstances = LevelData.ThreadTextureInstances.Find(StreamingTexture.Texture);
//                            if (TextureInstances)
//                            {
//                                for (int InstanceIndex = 0; InstanceIndex < TextureInstances->size(); ++InstanceIndex)
//                                {
//                                    const FStreamableTextureInstance4& TextureInstance = (*TextureInstances)[InstanceIndex];
//                                    for (int i = 0; i < 4; i++)
//                                    {
//                                        Ar.Logf(TEXT("    Instance: %f,%f,%f Radius: %f Range: [%f, %f] TexelFactor: %f"),
//                                                TextureInstance.BoundsOriginX[i],
//                                                TextureInstance.BoundsOriginY[i],
//                                                TextureInstance.BoundsOriginZ[i],
//                                                TextureInstance.BoundingSphereRadius[i],
//                                                FMath::Sqrt(TextureInstance.MinDistanceSq[i]),
//                                                SqrtKeepMax(TextureInstance.MaxDistanceSq[i]),
//                                                TextureInstance.TexelFactor[i]);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }
//#    endif   // ENABLE_RENDER_ASSET_TRACKING
//
//    return true;
//}

//bool StreamingManager::HandleUntrackRenderAssetCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    std::string AssetName(FParse::Token(Cmd, 0));
//    if (UntrackRenderAsset(AssetName))
//    {
//        Ar.Logf(TEXT("Textures or meshes containing \"%s\" are no longer tracked."), *AssetName);
//    }
//    return true;
//}
//
//bool StreamingManager::HandleStreamOutCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    std::string Parameter(FParse::Token(Cmd, 0));
//    SInt64 FreeMB = (Parameter.Len() > 0) ? FCString::Atoi(*Parameter) : 0;
//    if (FreeMB > 0)
//    {
//        bool bSucceeded = StreamOutRenderAssetData(FreeMB * 1024 * 1024);
//        Ar.Logf(TEXT("Tried to stream out %llu MB of texture/mesh data: %s"), FreeMB, bSucceeded ? TEXT("Succeeded") : TEXT("Failed"));
//    }
//    else
//    {
//        Ar.Logf(TEXT("Usage: StreamOut <N> (in MB)"));
//    }
//    return true;
//}
//
//bool StreamingManager::HandlePauseRenderAssetStreamingCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//
//    bPauseRenderAssetStreaming = !bPauseRenderAssetStreaming;
//    Ar.Logf(TEXT("Render asset streaming is now \"%s\"."), bPauseRenderAssetStreaming ? TEXT("PAUSED") : TEXT("UNPAUSED"));
//    return true;
//}
//
//bool StreamingManager::HandleStreamingManagerMemoryCommand(const TCHAR* Cmd, FOutputDevice& Ar, UWorld* InWorld)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();
//
//    SyncStates(true);
//
//    uint MemSize = sizeof(StreamingManager);
//    MemSize += StreamingRenderAssets.GetAllocatedSize();
//    MemSize += PendingStreamingRenderAssets.GetAllocatedSize() + RemovedRenderAssetIndices.GetAllocatedSize();
//    MemSize += AsyncWork->GetTask().StreamingData.GetAllocatedSize();
//
//    Ar.Logf(TEXT("StreamingManagerTexture: %.2f KB used"), MemSize / 1024.0f);
//
//    return true;
//}
//
//bool StreamingManager::HandleLODGroupsCommand(const TCHAR* Cmd, FOutputDevice& Ar)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//    SyncStates(true);
//
//    struct FTextureGroupStats
//    {
//        // Streaming texture stats
//        int NumStreamingTextures = 0;
//        uSInt64 CurrentTextureSize = 0;
//        uSInt64 WantedTextureSize = 0;
//        uSInt64 MaxTextureSize = 0;
//        // Non Streaming texture stats
//        int NumNonStreamingTextures = 0;
//        uSInt64 NonStreamingSize = 0;
//        // No resource texture
//        int NumNoResourceTextures = 0;
//    };
//    FTextureGroupStats TextureGroupStats[TEXTUREGROUP_MAX];
//
//    // Gather stats.
//    for (TObjectIterator<UTexture> It; It; ++It)
//    {
//        UTexture* Texture = *It;
//        Assert(Texture);
//
//        FTextureGroupStats& LODStats = TextureGroupStats[Texture->LODGroup];
//
//        const EPixelFormat PixelFormat = [&]() -> EPixelFormat {
//            if (Texture->GetRunningPlatformData() && *Texture->GetRunningPlatformData())
//            {
//                return (*Texture->GetRunningPlatformData())->PixelFormat;
//            }
//            else if (Texture->GetResource() && Texture->GetResource()->TextureRHI)
//            {
//                return Texture->GetResource()->TextureRHI->GetFormat();
//            }
//            else
//            {
//                return PF_Unknown;
//            }
//        }();
//
//        // No resource no size taken
//        if (!Texture->GetResource())
//        {
//            LODStats.NumNoResourceTextures++;
//        }
//        else if (Texture->IsStreamable())
//        {
//            StreamingResource* StreamingTexture = GetStreamingRenderAsset(Texture);
//            if (ensure(StreamingTexture))
//            {
//                LODStats.NumStreamingTextures++;
//                LODStats.CurrentTextureSize += StreamingTexture->GetSize(StreamingTexture->ResidentLODs);
//                ;
//                LODStats.WantedTextureSize += StreamingTexture->GetSize(StreamingTexture->WantedLODs);
//                LODStats.MaxTextureSize += StreamingTexture->GetSize(StreamingTexture->MaxAllowedLODs);
//            }
//        }
//        else
//        {
//            LODStats.NumNonStreamingTextures++;
//            LODStats.NonStreamingSize += Texture->CalcTextureMemorySizeEnum(TMC_ResidentLODs);
//        }
//    }
//
//    // Output stats.
//    {
//        UE_LOG(LogContentStreaming, Log, TEXT("Texture memory usage:"));
//        FTextureGroupStats TotalStats;
//        for (int GroupIndex = 0; GroupIndex < TEXTUREGROUP_MAX; ++GroupIndex)
//        {
//            FTextureGroupStats& Stat = TextureGroupStats[GroupIndex];
//            if (Stat.NumStreamingTextures || Stat.NumNonStreamingTextures || Stat.NumNoResourceTextures)
//            {
//                TotalStats.NumStreamingTextures += Stat.NumStreamingTextures;
//                TotalStats.NumNonStreamingTextures += Stat.NumNonStreamingTextures;
//                TotalStats.CurrentTextureSize += Stat.CurrentTextureSize;
//                TotalStats.WantedTextureSize += Stat.WantedTextureSize;
//                TotalStats.MaxTextureSize += Stat.MaxTextureSize;
//                TotalStats.NonStreamingSize += Stat.NonStreamingSize;
//                TotalStats.NumNoResourceTextures += Stat.NumNoResourceTextures;
//                UE_LOG(LogContentStreaming,
//                       Log,
//                       TEXT("%34s: NumStreamingTextures=%4d { Current=%8.1f KB, Wanted=%8.1f KB, OnDisk=%8.1f KB }, NumNonStreaming=%4d { Size=%8.1f KB }, NumWithNoResource=%4d"),
//                       UTexture::GetTextureGroupString((TextureGroup)GroupIndex),
//                       Stat.NumStreamingTextures,
//                       Stat.CurrentTextureSize / 1024.0f,
//                       Stat.WantedTextureSize / 1024.0f,
//                       Stat.MaxTextureSize / 1024.0f,
//                       Stat.NumNonStreamingTextures,
//                       Stat.NonStreamingSize / 1024.0f,
//                       Stat.NumNoResourceTextures);
//            }
//        }
//        UE_LOG(LogContentStreaming,
//               Log,
//               TEXT("%34s: NumStreamingTextures=%4d { Current=%8.1f KB, Wanted=%8.1f KB, OnDisk=%8.1f KB }, NumNonStreaming=%4d { Size=%8.1f KB }, NumWithNoResource=%4d"),
//               TEXT("Total"),
//               TotalStats.NumStreamingTextures,
//               TotalStats.CurrentTextureSize / 1024.0f,
//               TotalStats.WantedTextureSize / 1024.0f,
//               TotalStats.MaxTextureSize / 1024.0f,
//               TotalStats.NumNonStreamingTextures,
//               TotalStats.NonStreamingSize / 1024.0f,
//               TotalStats.NumNoResourceTextures);
//    }
//    return true;
//}

//bool StreamingManager::HandleInvestigateRenderAssetCommand(const TCHAR* Cmd, FOutputDevice& Ar, UWorld* InWorld)
//{
//    std::scoped_lock ScopeLock(CriticalSection);
//    std::vector<StreamingResource>& StreamingRenderAssets = GetStreamingRenderAssetsAsyncSafe();
//    SyncStates(true);
//
//    std::string InvestigateAssetName(FParse::Token(Cmd, 0));
//    if (InvestigateAssetName.Len())
//    {
//        FAsyncRenderAssetStreamingData& StreamingData = AsyncWork->GetTask().StreamingData;
//        StreamingData.Init(CurrentViewInfos, LastWorldUpdateTime);
//        StreamingData.ComputeViewInfoExtras(Settings);
//        StreamingData.UpdateBoundSizes_Async(Settings);
//
//        for (int AssetIndex = 0; AssetIndex < StreamingRenderAssets.size(); ++AssetIndex)
//        {
//            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
//            std::string AssetName = StreamingRenderAsset.RenderAsset->GetFullName();
//            if (AssetName.Contains(InvestigateAssetName))
//            {
//                StreamableResource* RenderAsset = StreamingRenderAsset.RenderAsset;
//                if (!RenderAsset)
//                    continue;
//                const EStreamableRenderAssetType AssetType = StreamingRenderAsset.RenderAssetType;
//                const StreamableResourceState ResourceState = RenderAsset->GetStreamableResourceState();
//                UTexture* Texture = Cast<UTexture>(RenderAsset);
//                UStaticMesh* StaticMesh = Cast<UStaticMesh>(RenderAsset);
//                int CurrentLODIndex = ResourceState.LODCountToAssetFirstLODIdx(StreamingRenderAsset.ResidentLODs);
//                int WantedLODIndex = ResourceState.LODCountToAssetFirstLODIdx(StreamingRenderAsset.GetPerfectWantedLODs());
//                int MaxLODIndex = ResourceState.LODCountToAssetFirstLODIdx(StreamingRenderAsset.MaxAllowedLODs);
//
//                UE_LOG(LogContentStreaming, Log, TEXT("%s: %s"), StreamingResource::GetStreamingAssetTypeStr(AssetType), *AssetName);
//                std::string LODGroupName = Texture ? UTexture::GetTextureGroupString((TextureGroup)StreamingRenderAsset.LODGroup) : TEXT("Unknown");
//#    if WITH_EDITORONLY_DATA
//                if (StaticMesh)
//                {
//                    LODGroupName = StaticMesh->LODGroup.ToString();
//                }
//#    endif
//                // put this up in some shared code somewhere in FGenericPlatformMemory
//                const TCHAR* BucketNames[] = {TEXT("Largest"), TEXT("Larger"), TEXT("Default"), TEXT("Smaller"), TEXT("Smallest"), TEXT("Tiniest")};
//                if ((int32)FPlatformMemory::GetMemorySizeBucket() < UE_ARRAY_COUNT(BucketNames))
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  LOD group:       %s [Bucket=%s]"), *LODGroupName, BucketNames[(int32)FPlatformMemory::GetMemorySizeBucket()]);
//                }
//                else
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  LOD group:       %s [Unkown Bucket]"), *LODGroupName);
//                }
//                if (Texture && Texture->GetRunningPlatformData() && *Texture->GetRunningPlatformData())
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Format:          %s"), GPixelFormats[(*Texture->GetRunningPlatformData())->PixelFormat].Name);
//                }
//                if (RenderAsset->bGlobalForceLODLevelsToBeResident)
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Force all LODs:  bGlobalForceLODLevelsToBeResident"));
//                }
//                else if (RenderAsset->bForceLODlevelsToBeResident)
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Force all LODs:  bForceLODlevelsToBeResident"));
//                }
//                else if (RenderAsset->ShouldLODLevelsBeForcedResident())
//                {
//                    float TimeLeft = (float)(RenderAsset->ForceLODLevelsToBeResidentTimestamp - FApp::GetCurrentTime());
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Force all LODs:  %.1f seconds left"), FMath::Max(TimeLeft, 0.0f));
//                }
//                else if (StreamingRenderAsset.bForceFullyLoadHeuristic)
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Force all LODs:  bForceFullyLoad"));
//                }
//                else if (ResourceState.MaxNumLODs == 1)
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Force all LODs:  No LOD-maps"));
//                }
//
//                if (Texture && Texture->GetRunningPlatformData() && *Texture->GetRunningPlatformData())
//                {
//                    const TIndirecstd::vector<struct FTexture2DLODMap>& TextureLODs = (*Texture->GetRunningPlatformData())->LODs;
//                    UTexture2D* Texture2D = Cast<UTexture2D>(Texture);
//                    UVolumeTexture* VolumeTexture = Cast<UVolumeTexture>(Texture);
//                    UTexture2DArray* Texture2DArray = Cast<UTexture2DArray>(Texture);
//                    if (Texture2D)
//                    {
//                        UE_LOG(LogContentStreaming, Log, TEXT("  Current size [2D LODs]: %dx%d [%d]"), TextureLODs[CurrentLODIndex].SizeX, TextureLODs[CurrentLODIndex].SizeY, StreamingRenderAsset.ResidentLODs);
//                        UE_LOG(LogContentStreaming, Log, TEXT("  Wanted size [2D LODs]:  %dx%d [%d]"), TextureLODs[WantedLODIndex].SizeX, TextureLODs[WantedLODIndex].SizeY, StreamingRenderAsset.GetPerfectWantedLODs());
//                    }
//                    else if (VolumeTexture)
//                    {
//                        UE_LOG(LogContentStreaming,
//                               Log,
//                               TEXT("  Current size [3D LODs]: %dx%dx%d [%d]"),
//                               TextureLODs[CurrentLODIndex].SizeX,
//                               TextureLODs[CurrentLODIndex].SizeY,
//                               TextureLODs[CurrentLODIndex].SizeZ,
//                               StreamingRenderAsset.ResidentLODs);
//                        UE_LOG(LogContentStreaming,
//                               Log,
//                               TEXT("  Wanted size [3D LODs]:  %dx%dx%d [%d]"),
//                               TextureLODs[WantedLODIndex].SizeX,
//                               TextureLODs[WantedLODIndex].SizeY,
//                               TextureLODs[CurrentLODIndex].SizeZ,
//                               StreamingRenderAsset.GetPerfectWantedLODs());
//                    }
//                    else if (Texture2DArray)
//                    {
//                        UE_LOG(LogContentStreaming,
//                               Log,
//                               TEXT("  Current size [2D Array LODs]: %dx%d*%d [%d]"),
//                               TextureLODs[CurrentLODIndex].SizeX,
//                               TextureLODs[CurrentLODIndex].SizeY,
//                               TextureLODs[CurrentLODIndex].SizeZ,
//                               StreamingRenderAsset.ResidentLODs);
//                        UE_LOG(LogContentStreaming,
//                               Log,
//                               TEXT("  Wanted size [2D Array LODs]:  %dx%d*%d [%d]"),
//                               TextureLODs[WantedLODIndex].SizeX,
//                               TextureLODs[WantedLODIndex].SizeY,
//                               TextureLODs[CurrentLODIndex].SizeZ,
//                               StreamingRenderAsset.GetPerfectWantedLODs());
//                    }
//                }
//                else
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Current LOD index: %d"), CurrentLODIndex);
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Wanted LOD index: %d"), WantedLODIndex);
//                }
//                UE_LOG(LogContentStreaming, Log, TEXT("  Allowed LODs:        %d-%d [%d]"), StreamingRenderAsset.MinAllowedLODs, StreamingRenderAsset.MaxAllowedLODs, ResourceState.MaxNumLODs);
//                UE_LOG(LogContentStreaming, Log, TEXT("  LoadOrder Priority:  %d"), StreamingRenderAsset.LoadOrderPriority);
//                UE_LOG(LogContentStreaming, Log, TEXT("  Retention Priority:  %d"), StreamingRenderAsset.RetentionPriority);
//                UE_LOG(LogContentStreaming, Log, TEXT("  Boost factor:        %.1f"), StreamingRenderAsset.BoostFactor);
//
//                std::string BiasDesc;
//                {
//                    int CumuBias = 0;
//                    if (!Settings.bUseAllLODs)
//                    {
//                        // UI specific Bias : see UTextureLODSettings::CalculateLODBias(), included in CachedCombinedLODBias.
//                        extern int GUITextureLODBias;
//                        if (StreamingRenderAsset.LODGroup == TEXTUREGROUP_UI && GUITextureLODBias)
//                        {
//                            BiasDesc += std::string::Printf(TEXT(" [UI:%d]"), GUITextureLODBias);
//                            CumuBias += GUITextureLODBias;
//                        }
//
//                        // LOD group Bias : see UTextureLODSettings::CalculateLODBias(), included in CachedCombinedLODBias
//                        const FTextureLODGroup& LODGroupInfo = UDeviceProfileManager::Get().GetActiveProfile()->GetTextureLODSettings()->TextureLODGroups[StreamingRenderAsset.LODGroup];
//                        if (LODGroupInfo.LODBias)
//                        {
//                            if (FPlatformProperties::RequiresCookedData())
//                            {
//                                BiasDesc += std::string::Printf(TEXT(" [LODGroup.Bias:0(%d)]"), LODGroupInfo.LODBias);
//                            }
//                            else
//                            {
//                                BiasDesc += std::string::Printf(TEXT(" [LODGroup.Bias:%d]"), LODGroupInfo.LODBias);
//                                CumuBias += LODGroupInfo.LODBias;
//                            }
//                        }
//
//                        // LOD group MaxResolution clamp : see UTextureLODSettings::CalculateLODBias(), included in CachedCombinedLODBias
//                        const int LODCountBeforeMaxRes = ResourceState.MaxNumLODs - (StreamingRenderAsset.LODGroup == TEXTUREGROUP_UI ? GUITextureLODBias : 0) -
//                                                           (FPlatformProperties::RequiresCookedData() ? 0 : (LODGroupInfo.LODBias + (Texture ? Texture->LODBias : 0)));
//                        const int MaxResBias = LODCountBeforeMaxRes - (LODGroupInfo.MaxLODLODCount + 1);
//                        if (MaxResBias > 0)
//                        {
//                            BiasDesc += std::string::Printf(TEXT(" [LODGroup.MaxRes:%d]"), MaxResBias);
//                            CumuBias += MaxResBias;
//                        }
//
//                        // Asset LODBias : see UTextureLODSettings::CalculateLODBias(), included in CachedCombinedLODBias
//                        if (Texture && Texture->LODBias)
//                        {
//                            if (FPlatformProperties::RequiresCookedData())
//                            {
//                                BiasDesc += std::string::Printf(TEXT(" [Asset.Bias:0(%d)]"), Texture->LODBias);
//                            }
//                            else
//                            {
//                                BiasDesc += std::string::Printf(TEXT(" [Asset.Bias:%d]"), Texture->LODBias);
//                                CumuBias += Texture->LODBias;
//                            }
//                        }
//                    }
//
//                    // RHI max resolution and optional LODs : see StreamingResource::UpdateDynamicData().
//                    {
//                        const int OptLODBias = ResourceState.MaxNumLODs - CumuBias - RenderAsset->GetStreamableResourceState().NumNonOptionalLODs;
//                        if (StreamingRenderAsset.OptionalLODsState != StreamingResource::EOptionalLODsState::OMS_HasOptionalLODs && OptLODBias > 0)
//                        {
//                            BiasDesc += std::string::Printf(TEXT(" [Asset.Opt:%d]"), OptLODBias);
//                            CumuBias += OptLODBias;
//                        }
//
//                        const int RHIMaxResBias = ResourceState.MaxNumLODs - CumuBias - GMaxTextureLODCount;
//                        if (RHIMaxResBias > 0)
//                        {
//                            BiasDesc += std::string::Printf(TEXT(" [RHI.MaxRes:%d]"), RHIMaxResBias);
//                            CumuBias += RHIMaxResBias;
//                        }
//                    }
//
//                    // Memory budget bias
//                    // Global Bias : see StreamingResource::UpdateDynamicData().
//                    if (StreamingRenderAsset.IsMaxResolutionAffectedByGlobalBias() && !Settings.bUsePerTextureBias && Settings.GlobalLODBias)
//                    {
//                        BiasDesc += std::string::Printf(TEXT(" [Global:%d]"), Settings.GlobalLODBias);
//                        CumuBias += Settings.GlobalLODBias;
//                    }
//
//                    if (StreamingRenderAsset.BudgetLODBias)
//                    {
//                        BiasDesc += std::string::Printf(TEXT(" [Budget:%d]"), StreamingRenderAsset.BudgetLODBias);
//                        CumuBias += StreamingRenderAsset.BudgetLODBias;
//                    }
//                }
//
//                UE_LOG(LogContentStreaming, Log, TEXT("  LOD bias:			  %d %s"), ResourceState.MaxNumLODs - StreamingRenderAsset.MaxAllowedLODs, !BiasDesc.IsEmpty() ? *BiasDesc : TEXT(""));
//
//                if (InWorld && !GIsEditor)
//                {
//                    UE_LOG(LogContentStreaming, Log, TEXT("  Time: World=%.3f LastUpdate=%.3f "), InWorld->GetTimeSeconds(), LastWorldUpdateTime);
//                }
//
//                for (int ViewIndex = 0; ViewIndex < StreamingData.GetViewInfos().size(); ViewIndex++)
//                {
//                    // Calculate distance of viewer to bounding sphere.
//                    const FStreamingViewInfo& ViewInfo = StreamingData.GetViewInfos()[ViewIndex];
//                    UE_LOG(LogContentStreaming,
//                           Log,
//                           TEXT("  View%d: Position=(%s) ScreenSize=%f MaxEffectiveScreenSize=%f Boost=%f"),
//                           ViewIndex,
//                           *ViewInfo.ViewOrigin.ToString(),
//                           ViewInfo.ScreenSize,
//                           Settings.MaxEffectiveScreenSize,
//                           ViewInfo.BoostFactor);
//                }
//
//                StreamingData.UpdatePerfectWantedLODs_Async(StreamingRenderAsset, Settings, true);
//            }
//        }
//    }
//    else
//    {
//        Ar.Logf(TEXT("Usage: InvestigateTexture <name>"));
//    }
//    return true;
//}
//#endif   // !CROSSENGINE_RELEASE

// ---------------------- IStreamingManager region
bool StreamingManager::bPendingRemoveViews = false;

void StreamingManager::SetupViewInfos(float DeltaTime)
{
    // Reset CurrentViewInfos
    // TODO: Unused by now and need to deal with multi worlds
    //auto* transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
    //auto* cameraSys = mGameWorld->GetGameSystem<CameraSystemG>();
    //auto result = mGameWorld->Query<WorldTransformComponentG, CameraComponentG>();
    //CurrentViewInfos.clear();
    //CurrentViewInfos.reserve(result.GetEntityNum());
    //for (UInt32 index = 0; index < result.GetEntityNum(); index++)
    //{
    //    auto&& [worldTransformComp, cameraComp] = result[index];
    //    Double3 translation = transformSys->GetWorldTranslationT(worldTransformComp.Read());
    //    float screenWidth = static_cast<float>(cameraSys->GetTargetWidth(cameraComp.Read()));
    //    float fov = cameraSys->GetFOV(cameraComp.Read());

    //    float screenSize = screenWidth;
    //    float fovScreenSize = screenSize / std::tan(fov * 0.5f);
    //    CurrentViewInfos.emplace_back(StreamingViewInfo(translation, screenSize, fovScreenSize, 1.0f, 0.0f));
    //}
}

// ---------------------- CE region
void StreamingManager::Tick(FrameParam* fp)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingManager::Tick");

    if (!Settings.EnableStreaming)
    {
        return;
    }

    if (mIsFirstTick)
    {
        // Register terminal vars
        InGameTerminalManager::DeclareTerminalVar("streaming.stats", [=](TerminalVar var) {
            mDisplayStats = std::get<bool>(var);
            LOG_INFO("streaming.stats {}", mDisplayStats);
        });
        mIsFirstTick = false;
    }

    // TODO: Maybe no need, get view from cameras
    //SetupViewInfos(fp->GetDeltaTime());
    UpdateResourceStreaming(fp->GetDeltaTime());

    // Trigger a call to RemoveStreamingViews( RemoveStreamingViews_Normal ) next time a view is added.
    bPendingRemoveViews = true;

    if (Settings.EnableStats && mDisplayStats)
    {
        for (const auto world : mGameWorlds)
        {
            PrintStreamingStats(world);
        }
    }
}

bool StreamingManager::IsWorldSuitableForStreaming(GameWorld* gameWorld) const
{
    return gameWorld->GetWorldType() == WorldTypeTag::DefaultWorld
        || gameWorld->GetWorldType() == WorldTypeTag::PIEWorld
        || gameWorld->GetWorldType() == WorldTypeTag::PreviewWorld
        || gameWorld->GetWorldType() == WorldTypeTag::PrefabWorld;
}

void StreamingManager::OnWorldCreated(GameWorld* gameWorld)
{
    if (!IsWorldSuitableForStreaming(gameWorld))
    {
        return;
    }

    mGameWorlds.emplace(gameWorld);

    gameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);

    auto* modelSys = gameWorld->GetGameSystem<ModelSystemG>();
    modelSys->SubscribeEvent<ModelChangeEvent>(this);
}

void StreamingManager::OnWorldDestroyed(GameWorld* gameWorld)
{
    if (!IsWorldSuitableForStreaming(gameWorld))
    {
        return;
    }

    auto* modelSys = gameWorld->GetGameSystem<ModelSystemG>();
    modelSys->Unsubscribe<ModelChangeEvent>(this);

    gameWorld->Unsubscribe<EntityDestroyEvent>(this);

    mGameWorlds.erase(gameWorld);
}

void StreamingManager::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    // Process immediate event
    if (event.mEventType == cross::WorldLifeEvent::sEventType)
    {
        const auto* worldEvent = TYPE_CAST(const cross::WorldLifeEvent*, &event);
        const auto& worldData = worldEvent->mData;

        auto* worldSys = EngineGlobal::GetEngine()->GetGlobalSystem<WorldSystemG>();
        auto* world = worldSys->GetWorldByID(worldData.mWorldId);

        if (worldData.mEventType == WorldLifeEventType::Create)
        {
            OnWorldCreated(world);
        }
        else if (worldData.mEventType == WorldLifeEventType::Destroy)
        {
            OnWorldDestroyed(world);
        }
    }
    else if (event.mEventType == ModelChangeEvent::sEventType)
    {
        const auto* modelChangeEvent = TYPE_CAST(const cross::ModelChangeEvent*, &event);
        const auto& modelChangeData = modelChangeEvent->mData;
        // A new mesh asset data resource is added to a model
        if (modelChangeData.mResourcePtr)
        {
            auto* streamableResource = dynamic_cast<StreamableResource*>(modelChangeData.mResourcePtr->GetStreamableProxy());
            if (streamableResource)
            {
                AddReferenceEntity(modelChangeData.mEntity, streamableResource);
            }
        }
    }
    // process remained event
    else if (event.mEventType == cross::RemainedEventUpdatedEvent::sEventType)
    {
        const auto* remainedEvent = TYPE_CAST(const cross::RemainedEventUpdatedEvent*, &event);
        if (remainedEvent->mData.mRemainedEventType == cross::EntityDestroyEvent::sEventType)
        {
            for (UInt32 index = remainedEvent->mData.mFirstIndex; index <= remainedEvent->mData.mLastIndex; index++)
            {
                // TODO: Which GameWorld to access remained events?
                //const auto& entityDestroyEvent = mGameWorld->GetRemainedEvent<cross::EntityDestroyEvent>(index);
                //if ((entityDestroyEvent.mData.mEventFlag & cross::EntityLifeCycleEventFlag::DestroyComponent) != cross::EntityLifeCycleEventFlag::None)
                //{
                //    if (!(entityDestroyEvent.mData.mChangedComponentMask & cross::ModelComponentG::GetDesc()->GetMask()).IsZero())
                //    {
                //    }
                //}
            }
        }
    }
}

void StreamingManager::PrintStreamingStats(GameWorld* gameWorld) const
{
    if (!gameWorld)
    {
        return;
    }

    auto* primitiveRenderSys = gameWorld->GetGameSystem<PrimitiveRenderSystemG>();
    auto Print = [=](const std::string& text) {
            primitiveRenderSys->LogScreen(text, {1.0f, 0.0f, 0.0f, 1.0f});
    };

    auto BytesToMegaBytes = [=](SInt64 bytes) {
        return bytes / 1024.0 / 1024.0;
    };

    // TODO:
    // Num / Size / Percentage / Pool / Capacity
    Print(fmt::format("Streaming Pool:    {:.2f}MB", BytesToMegaBytes(DisplayedStats.StreamingPool)));
    //Print(fmt::format("Safety Pool:       {:.2f}MB", BytesToMegaBytes(DisplayedStats.SafetyPool)));
    Print(fmt::format("Required Pool:     {:.2f}MB", BytesToMegaBytes(DisplayedStats.RequiredPool)));
    //Print(fmt::format("Temporary Pool:    {:.2f}MB", BytesToMegaBytes(DisplayedStats.TemporaryPool)));
    Print(fmt::format("Visible LOD Size:  {:.2f}MB", BytesToMegaBytes(DisplayedStats.VisibleLODs)));
    Print(fmt::format("Hidden LOD Size:   {:.2f}MB", BytesToMegaBytes(DisplayedStats.HiddenLODs)));
    Print(fmt::format("Wanted LOD Size:   {:.2f}MB", BytesToMegaBytes(DisplayedStats.WantedLODs)));
    Print(fmt::format("Inflight LOD Size: {:.2f}MB", BytesToMegaBytes(DisplayedStats.PendingRequests)));
}

} // namespace cross
