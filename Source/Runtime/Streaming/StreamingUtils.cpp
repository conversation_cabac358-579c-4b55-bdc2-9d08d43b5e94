#include "Streaming/StreamingUtils.h"
#include "Streaming/StreamableResource.h"
#include "Streaming/StreamingResource.h"
#include "Runtime/Interface/CrossEngineImp.h"

#define ENABLE_RENDER_ASSET_TRACKING !(CROSSENGINE_RELEASE/* || UE_BUILD_TEST*/)
#define ENABLE_RENDER_ASSET_LOGGING  1
#define NUM_TRACKEDRENDERASSETEVENTS 512

namespace cross
{

#if ENABLE_RENDER_ASSET_TRACKING
struct FTrackedRenderAssetEvent
{
    FTrackedRenderAssetEvent() = default;
    FTrackedRenderAssetEvent(const std::string& InAssetNameString)
        : RenderAssetName(InAssetNameString)
        , NumResidentLODs(0)
        , NumRequestedLODs(0)
        , WantedLODs(0)
        , Timestamp(0.0f)
        , BoostFactor(1.0f)
    {}
    /** Partial name of the texture/mesh (not case-sensitive). */
    std::string RenderAssetName;
    /** Number of LOD-levels currently in memory. */
    int NumResidentLODs;
    /** Number of LOD-levels requested. */
    int NumRequestedLODs;
    /** Number of wanted LODs. */
    int WantedLODs;
    /** Timestamp, in seconds from startup. */
    float Timestamp;
    /** Currently used boost factor for the streaming distance. */
    float BoostFactor;
};

/** List of textures/meshes to track (using stristr for name comparison). */
std::vector<std::string> GTrackedRenderAssetNames;
bool GTrackedRenderAssetsInitialized = false;
FTrackedRenderAssetEvent GTrackedRenderAssetEvents[NUM_TRACKEDRENDERASSETEVENTS];
int GTrackedRenderAssetEventIndex = -1;
std::vector<FTrackedRenderAssetEvent> GTrackedRenderAssets;

/**
 * Initializes the texture/mesh tracking. Called when GTrackedRenderAssetsInitialized is false.
 */
void TrackRenderAssetInit()
{
    //if (GConfig && GConfig->IsReadyForUse())
    //{
    //    GTrackedRenderAssetNames.clear();
    //    GTrackedRenderAssetsInitialized = true;
    //    GConfig->GetArray(TEXT("RenderAssetTracking"), TEXT("RenderAssetName"), GTrackedRenderAssetNames, GEngineIni);
    //    std::vector<std::string> Tmp;
    //    GConfig->GetArray(TEXT("TextureTracking"), TEXT("TextureName"), Tmp, GEngineIni);
    //    if (Tmp.Num() > 0)
    //    {
    //        GTrackedRenderAssetNames.Append(MoveTemp(Tmp));
    //        check(!Tmp.Num());
    //        GConfig->SetArray(TEXT("TextureTracking"), TEXT("TextureName"), Tmp, GEngineIni);
    //        GConfig->SetArray(TEXT("RenderAssetTracking"), TEXT("RenderAssetName"), GTrackedRenderAssetNames, GEngineIni);
    //    }
    //}
}

/**
 * Checks a texture/mesh and tracks it if its name contains any of the tracked render asset names (GTrackedRenderAssetNames).
 *
 * @param RenderAsset					Texture/mesh to check
 * @param bForceLODLEvelsToBeResident	Whether all LOD-levels in the texture/mesh are forced to be resident
 * @param Manager                       can be null
 */
bool TrackRenderAssetEvent(StreamingResource* StreamingRenderAsset, StreamableResource* RenderAsset, bool bForceLODLevelsToBeResident, const StreamingManager* Manager)
{
    // Whether the texture/mesh is currently being destroyed
    const bool bIsDestroying = !StreamingRenderAsset;
    const bool bEnableLogging = ENABLE_RENDER_ASSET_LOGGING;

    // Initialize the tracking system, if necessary.
    if (!GTrackedRenderAssetsInitialized)
    {
        TrackRenderAssetInit();
    }

    int NumTrackedAssets = static_cast<int>(GTrackedRenderAssetNames.size());
    if (NumTrackedAssets && RenderAsset)
    {
        const StreamableResourceState ResourceState = RenderAsset->GetStreamableResourceState();

        // See if it matches any of the texture/mesh names that we're tracking.
        std::string AssetNameString = RenderAsset->GetFullName();
        const char* AssetName = AssetNameString.c_str();
        for (int TrackedAssetIndex = 0; TrackedAssetIndex < NumTrackedAssets; ++TrackedAssetIndex)
        {
            const std::string& TrackedAssetName = GTrackedRenderAssetNames[TrackedAssetIndex];
            if (strstr(AssetName, TrackedAssetName.c_str()) != NULL)
            {
                if (bEnableLogging)
                {
                    // Find the last event for this particular texture/mesh.
                    FTrackedRenderAssetEvent* LastEvent = NULL;
                    for (int LastEventIndex = 0; LastEventIndex < GTrackedRenderAssets.size(); ++LastEventIndex)
                    {
                        FTrackedRenderAssetEvent* Event = &GTrackedRenderAssets[LastEventIndex];
                        if (strcmp(AssetName, Event->RenderAssetName.c_str()) == 0)
                        {
                            LastEvent = Event;
                            break;
                        }
                    }
                    // Didn't find any recorded event?
                    if (LastEvent == NULL)
                    {
                        GTrackedRenderAssets.emplace_back(FTrackedRenderAssetEvent(AssetNameString));
                        int NewIndex = static_cast<int>(GTrackedRenderAssets.size()) - 1;
                        LastEvent = &GTrackedRenderAssets[NewIndex];
                    }

                    int WantedLODs = ResourceState.RequestedLODCount;
                    float BoostFactor = 1.0f;
                    StreamableResourceType AssetType = StreamableResourceType::Unknown;
                    if (StreamingRenderAsset)
                    {
                        WantedLODs = StreamingRenderAsset->WantedLODs;
                        BoostFactor = StreamingRenderAsset->BoostFactor;
                        AssetType = StreamingRenderAsset->RenderAssetType;
                    }

                    if (LastEvent->NumResidentLODs != ResourceState.ResidentLODCount || LastEvent->NumRequestedLODs != ResourceState.RequestedLODCount || LastEvent->WantedLODs != WantedLODs || LastEvent->BoostFactor != BoostFactor ||
                        bIsDestroying)
                    {
                        GTrackedRenderAssetEventIndex = (GTrackedRenderAssetEventIndex + 1) % NUM_TRACKEDRENDERASSETEVENTS;
                        FTrackedRenderAssetEvent& NewEvent = GTrackedRenderAssetEvents[GTrackedRenderAssetEventIndex];
                        NewEvent.RenderAssetName = LastEvent->RenderAssetName;
                        NewEvent.NumResidentLODs = LastEvent->NumResidentLODs = ResourceState.ResidentLODCount;
                        NewEvent.NumRequestedLODs = LastEvent->NumRequestedLODs = ResourceState.ResidentLODCount;
                        NewEvent.WantedLODs = LastEvent->WantedLODs = WantedLODs;
                        NewEvent.Timestamp = LastEvent->Timestamp = float(EngineGlobal::GetEngine()->GetRealTimeSeconds()/*FPlatformTime::Seconds() - GStartTime*/); // Elapsed time
                        NewEvent.BoostFactor = LastEvent->BoostFactor = BoostFactor;
                        LOG_INFO("{}: \"{}\", ResidentLODs: {}/{}, RequestedLODs: {}, WantedLODs: {}, Boost: {} ({})",
                               StreamingResource::GetStreamingAssetTypeStr(AssetType),
                               AssetName,
                               LastEvent->NumResidentLODs,
                               ResourceState.LODCount,
                               bIsDestroying ? 0 : LastEvent->NumRequestedLODs,
                               LastEvent->WantedLODs,
                               BoostFactor,
                               bIsDestroying ? "DESTROYED" : "updated");
                    }
                }
                return true;
            }
        }
    }
    return false;
}

#endif

StreamableResource* StreamingUtils::AsStreamableResource(IStreamableResource* resource)
{
    return dynamic_cast<StreamableResource*>(resource);
}

StreamableResource* StreamingUtils::AsStreamableResource(const MeshAssetDataResourcePtr& resource)
{
    return StreamingUtils::AsStreamableResource(resource->GetStreamableProxy());
}

// TODO: This may be called in early engine stage when settiing manager is not initialized
bool StreamingUtils::IsEditor()
{
    auto* settingManager = EngineGlobal::GetSettingMgr();
    if (settingManager)
    {
        return settingManager->GetAppStartUpType() == AppStartUpType::AppStartUpTypeCrossEditor;
    }
    else
    {
        return false;
    }
}

bool StreamingUtils::IsHeadless()
{
    auto* settingManager = EngineGlobal::GetSettingMgr();
    if (settingManager)
    {
        return settingManager->GetAppStartUpType() == AppStartUpType::AppStartUpTypeHeadless;
    }
    else
    {
        return false;
    }
}

} // namespace cross
