#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CrossBase/Math/CrossMath.h"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/Resource.h"
#include "Runtime/GameWorld/HierachicalInstancedStaticModelSystemG.h"

namespace cross {
ecs::ComponentDesc* cross::HierachicalInstancedStaticModelComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::HierachicalInstancedStaticModelComponentG>({false, true, true, false},
                                                                                                                          &HierachicalInstancedStaticModelSystemG::SerializeHierachicalInstancedStaticModelComponent,
                                                                                                                          &HierachicalInstancedStaticModelSystemG::DeserializeHierachicalInstancedStaticModelComponent,
                                                                                                                          &HierachicalInstancedStaticModelSystemG::PostDeserializeHierachicalInstancedStaticModelComponent,
                                                                                                                          &HierachicalInstancedStaticModelSystemG::UpdateDeserializeHierachicalInstancedStaticModelComponent);
}

SerializeNode HierachicalInstancedStaticModelSystemG::SerializeHierachicalInstancedStaticModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "Serialize HierachicalInstancedStaticModelComponent");

    auto comp = static_cast<HierachicalInstancedStaticModelComponentG*>(componentPtr);

    SerializeNode rootNode;

    rootNode["Test"] = comp->m_Test;
    return rootNode;
}

void HierachicalInstancedStaticModelSystemG::DeserializeHierachicalInstancedStaticModelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& rootNode, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "Deserialize HierachicalInstancedStaticModelComponent");

    using namespace resource;

    if (rootNode.IsNull())
    {
        return;
    }

    auto comp = static_cast<HierachicalInstancedStaticModelComponentG*>(componentPtr);
    comp->m_Test = rootNode["Test"].AsBoolean();
}

void HierachicalInstancedStaticModelSystemG::PostDeserializeHierachicalInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

void HierachicalInstancedStaticModelSystemG::UpdateDeserializeHierachicalInstancedStaticModelComponent(const DeserializeNode& rootNode, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    Assert(false);
    LOG_ERROR("Not Implemented");
    if (rootNode.IsNull())
    {
        return;
    }
}

void HierachicalInstancedStaticModelSystemG::GetResourceHierachicalInstancedStaticModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource) {}

HierachicalInstancedStaticModelSystemG* HierachicalInstancedStaticModelSystemG::CreateInstance()
{
    return new HierachicalInstancedStaticModelSystemG();
}

HierachicalInstancedStaticModelSystemG::HierachicalInstancedStaticModelSystemG()
{
    mRenderSystem = HierachicalInstancedStaticModelSystemR::CreateInstance();
}

HierachicalInstancedStaticModelSystemG::~HierachicalInstancedStaticModelSystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderSystem->Release();
    }
    mRenderSystem = nullptr;
}

void HierachicalInstancedStaticModelSystemG::Release()
{
    delete this;
}

RenderSystemBase* HierachicalInstancedStaticModelSystemG::GetRenderSystem()
{
    return mRenderSystem;
}

}   // namespace cross
