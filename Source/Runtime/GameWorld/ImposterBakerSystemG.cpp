
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/OctahedralCommon.h"
#include "FileSystem/PathHelper.h"
#include "Resource/Prefab/PrefabResource.h"
#include "Prefab/PrefabSystemG.h"
#include "Runtime/GameWorld/ImposterBakerSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/RendererSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "RenderEngine/ImposterBakerSystemR.h"
#include "Threading/RenderingThread.h"
#include "Texture/Texture.h"
#include "imageio.h"
#include "CrossImage/image.h"
#if CROSSENGINE_WIN
#include "cmft/image.h"
#endif

namespace cross {
ecs::ComponentDesc* ImposterBakerComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<ImposterBakerComponentG>(
        {false, true, true, false}, ImposterBakerSystemG::SerializeImposterBakerComponent, ImposterBakerSystemG::DeserializeImposterBakerComponent, ImposterBakerSystemG::PostDeserializeImposterBakerComponent);
}

ImposterBakerSystemG* ImposterBakerSystemG::CreateInstance()
{
    ImposterBakerSystemG* system = new ImposterBakerSystemG();
    return system;
}

ImposterBakerSystemG::ImposterBakerSystemG()
{
    mRenderObject = ImposterBakerSystemR::CreateInstance();
}

ImposterBakerSystemG::~ImposterBakerSystemG()
{
    if (mIsRenderObjectOwner && mRenderObject)
    {
        mRenderObject->Release();
        mRenderObject = nullptr;
    }
}

void ImposterBakerSystemG::Release()
{
    delete this;
}

void ImposterBakerSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

RenderSystemBase* ImposterBakerSystemG::GetRenderSystem()
{
    return mRenderObject;
}

void ImposterBakerSystemG::OnFirstUpdate(FrameParam* frameParam)
{
}

static UInt32 sIndex = 0;
static const UInt32 sImposterLayer = 7;
static const float sCameraDistance = 2000.0f;
void ImposterBakerSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
#if CROSSENGINE_WIN
    if (sIndex < mViewCaptureVec.size())
    {
        //if (sIndex != 0)
        //{
        //    sIndex++;
        //    return;
        //}

        auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
        auto cameraSys = (cross::CameraSystemG*)mGameWorld->GetGameSystem<cross::CameraSystemG>();

        auto transformComp = mGameWorld->GetComponent<WorldTransformComponentG>(mCaptureEntity);
        Float4x4 worldMat = transformSystem->GetWorldMatrix(transformComp.Read());
        BoundingSphere worldSphere;
        mBoundingSphere.Transform(worldSphere, worldMat);

        Float3 center = worldSphere.GetCenter();
        float radius = worldSphere.GetRadius();
        float cameraDistance = sCameraDistance;
        bool orthographic = false;

        auto [cameraComponent, transformComponent] = mGameWorld->GetComponent<CameraComponentG, WorldTransformComponentG>(mCamera);

        // Camera fov
        float fov = std::atan(radius / cameraDistance) * 2;
        cameraSys->SetPerspectiveFov(cameraComponent.Write(), fov);
        cameraSys->SetPerspectiveAspect(cameraComponent.Write(), 1.0f);
        cameraSys->SetCameraMask(cameraComponent.Write(), (1 << sImposterLayer));
        cameraSys->SetPerspectiveFar(cameraComponent.Write(), 1.5f * cameraDistance);
        auto& curVector = mViewCaptureVec[sIndex++];
        Float3 vec = Float3(curVector.x, curVector.z, -curVector.y);
        // Set camera transform
        Float3 position;
        if (orthographic)
        {}
        else
        {
            position = center + cameraDistance * vec;
        }
        Quaternion baseRot = Quaternion::EulerToQuaternion(Float3(0, 90, 0).ToRadian());

        vec = -1 * curVector;
        float yaw, pitch;
        yaw = std::atan2(vec.y, vec.x);
        pitch = std::atan2(-vec.z, std::sqrt(vec.x * vec.x + vec.y * vec.y));
        Quaternion rotation = Quaternion::CreateFromYawPitchRoll(yaw, pitch, 0) * baseRot;
        //rotation = Quaternion(-rotation.z, -rotation.x, rotation.y, rotation.w);
        Float3 angle = Quaternion::QuaternionToEuler(rotation).ToDegree();
        rotation = Quaternion::EulerToQuaternion(Float3(pitch, yaw, 0)) * baseRot;
        angle = Quaternion::QuaternionToEuler(rotation).ToDegree();

        transformSystem->SetWorldTranslation(transformComponent.Write(), position);
        transformSystem->SetWorldRotation(transformComponent.Write(), rotation);
        transformSystem->UpdateWorldTransform(transformComponent.Write());

        //CaptureScene();

        DispatchRenderingCommandWithToken([&, _center = center, _radius = radius] {
            mRenderObject->SetBakeTextureEnable(true);
            mRenderObject->SetCurrentBakeFrame(sIndex);
            mRenderObject->SetCenter(_center);
            mRenderObject->SetRadius(_radius);
        });
    }
    else
    {
        if (mViewCaptureVec.size() > 0 && sIndex >= mViewCaptureVec.size())
        {
            auto propertySystem = mGameWorld->GetGameSystem<RenderPropertySystemG>();
            auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
            auto [renderPropH, metaHandle] = mGameWorld->GetComponent<RenderPropertyComponentG, ecs::EntityMetaComponentG>(mCaptureEntity);
            auto entityName = metaSys->GetName(metaHandle.Read());
            auto imposterBakerH = mGameWorld->GetComponent<ImposterBakerComponentG>(mImposterEntity);
            const ImposterBakerComponentWriter& writer = imposterBakerH.Write();
            std::string diretory = writer->mOutputFolder;
            std::string basecolorTexName = PathHelper::Combine(diretory.c_str(), (entityName + "_imposter_basecolor.nda").c_str());
            std::string normalTexName = PathHelper::Combine(diretory.c_str(), (entityName + "_imposter_normal.nda").c_str());
            if (!mFinishCapture)
            {
                // Finish capture
                mFinishCapture = true;

                propertySystem->SetLayerIndex(renderPropH.Write(), 0);
                propertySystem->SetLodSelected(renderPropH.Write(), -1);
                DispatchRenderingCommandWithToken([&] { mRenderObject->SetBakeTextureEnable(false); });

                // save texture resource
                auto saveRenderTexture = [=](RenderTextureResourcePtr renderTextureResourcePtr, std::string fileName, bool createMaterial) {
                    auto rendererSystem = EngineGlobal::Inst().GetEngine()->GetGlobalSystem<RendererSystemG>();

                    auto width = renderTextureResourcePtr->GetWidth();
                    auto height = renderTextureResourcePtr->GetHeight();
                    const UInt32 dataSize = width * height * sizeof(UInt32);
                    void* data = cross::Memory::Malloc(dataSize);

                    rendererSystem->ReadbackRenderTexture(renderTextureResourcePtr, 0, {0, 0, 0}, {width, height, 1}, data, dataSize, [=] {
                        CrossSchema::TextureAssetT texture;
                        cmft::Image src;
                        cmft::imageCreate(src, width, height, 0x303030ff, 1, 1, cmft::TextureFormat::RGBA8);
                        cmft::imageConvert(src, cmft::TextureFormat::RGBA8);
                        memcpy(src.m_data, data, dataSize);
                        // bool result = cmft::imageSave(src, fileName, cmft::ImageFileType::DDS, cmft::TextureFormat::RGBA8);
                        imageio::GPUImage gpu_image;
                        bool result = imageio::compress2dxbcnvtt(src, gpu_image, false, true, true, nvtt::Format::Format_BC7, false);
                        if (result)
                        {
                            std::vector<CrossSchema::TextureAssetImage> texImages;
                            texture.data.resize(gpu_image.m_total_size);
                            int mip_count = gpu_image.m_mip_count;
                            for (int i = 0; i < gpu_image.m_mipmaps.size(); ++i)
                            {
                                int mip = i % mip_count;
                                auto& mipmap = gpu_image.m_mipmaps[i];
                                auto& image = texImages.emplace_back();
                                image.mutate_width(mipmap.m_width);
                                image.mutate_height(mipmap.m_height);
                                image.mutate_databytesize(mipmap.m_data_size);
                                image.mutate_depth(1);
                                image.mutate_rowpitch(mipmap.m_pitch);
                                uint32_t offset = gpu_image.m_mip_offsets[0][mip];
                                image.mutate_dataoffset(offset);
                                memcpy(texture.data.data() + offset, mipmap.m_data, mipmap.m_data_size);
                            }

                            texture.images = texImages;
                            texture.mipcount = static_cast<UInt32>(mip_count);
                            texture.format = CrossSchema::TextureFormat::BC7;
                            texture.dimension = CrossSchema::TextureDimension::Tex2D;
                            texture.flags = 0;
                            texture.colorspace = CrossSchema::ColorSpace::Linear;
                            texture.vtstreaming = false;
                            texture.texturegroup = CrossSchema::TextureGroup::UI;

                            Texture2DPtr tex2d = gResourceMgr.CreateResourceAs<resource::Texture2D>();
                            tex2d->CreateAsset(fileName);
                            // tex2d->SetImportSet(setting.SerializeToString());
                            tex2d->Serialize(texture);
                        }

                        cross::Memory::Free(data);

                        if (createMaterial)
                        {
                            mCreateResource = true;
                        }
                    });
                };
                saveRenderTexture(mImposterTextures[0], basecolorTexName.c_str(), false);
                saveRenderTexture(mImposterTextures[1], normalTexName.c_str(), true);
            }

            if (mCreateResource)
            {
                sIndex = 0;
                mViewCaptureVec.clear();
                mCreateResource = false;
                mFinishCapture = false;

                // Create material and set parameter
                auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
                auto transformComp = mGameWorld->GetComponent<WorldTransformComponentG>(mCaptureEntity);
                Float4x4 worldMat = transformSystem->GetWorldMatrix(transformComp.Read());
                Float3 location = transformSystem->GetWorldTranslation(transformComp.Read());
                BoundingSphere worldSphere;
                mBoundingSphere.Transform(worldSphere, worldMat);
                Float3 center = worldSphere.GetCenter();
                float radius = worldSphere.GetRadius();
                Float4 pivotOffset = Float4(center - location, 0);

                std::string materialPath = PathHelper::Combine(diretory.c_str(), (entityName + "_imposter_material.nda").c_str());
                cross::MaterialPtr material = TypeCast<cross::resource::Material>(cross::resource::Material::CreateMaterialInstance("Shader/Features/Imposter/Imposter.fx.nda"));
                material->SetTexture("_BaseMap", cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(basecolorTexName)));
                material->SetTexture("_NormalMap", cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(normalTexName)));
                material->SetFloat("_DefaultMeshSize", 2 * radius);
                material->SetFloat4("_PivotOffset", pivotOffset.data());
                cross::SerializeNode emptyNode = {};
                material->Serialize(std::move(emptyNode), materialPath);

                // create prefab resource
                auto rootEntity = transformSystem->GetRootEntity();
                auto prefabSys = mGameWorld->GetGameSystem<PrefabSystemG>();
                auto modelSys = mGameWorld->GetGameSystem<cross::ModelSystemG>();
                std::string savePrefab = PathHelper::Combine(diretory.c_str(), (entityName + "_imposter.prefab").c_str());
                writer->mPrefabResource = savePrefab;
                savePrefab = PathHelper::GetCurrentDirectoryPath() + "/" + savePrefab;
                auto prefabEntity = writer->mPrefabEntity;
                auto [prefabMetaHandle, modelHandel] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, ModelComponentG>(prefabEntity);
                metaSys->SetName(prefabMetaHandle.Write(), entityName + "_imposter");
                modelSys->SetModelLodMaterialPath(modelHandel.Write(), materialPath, 0, 0,0);
                if (!PathHelper::IsFileExist(savePrefab))
                {
                    auto prefabMgr = EngineGlobal::GetPrefabMgr();
                    prefabMgr->CreatePrefab(mGameWorld, prefabEntity, diretory, false);
                }
                mGameWorld->CallEditorUpdateHierarchyCallback();
            }
        }
        
    }
#endif
}

SerializeNode ImposterBakerSystemG::SerializeImposterBakerComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode componentJson;

    const auto imposterBakerComponent = static_cast<ImposterBakerComponentG*>(componentPtr);
    componentJson["mImposterType"] = imposterBakerComponent->mImposterType;
    componentJson["mFramesInterval"] = imposterBakerComponent->mFramesInterval;
    componentJson["mOutputFolder"] = imposterBakerComponent->mOutputFolder;

    return componentJson;
}

void ImposterBakerSystemG::DeserializeImposterBakerComponent(ISerializeWorld* serializeWorld, const DeserializeNode& componentJson, ecs::IComponent* componentPtr)
{
    if (!componentJson.IsNull())
    {
        auto imposterBakerComponent = static_cast<ImposterBakerComponentG*>(componentPtr);

        if (componentJson.HasMember("mImposterType"))
        {
            imposterBakerComponent->mImposterType = (ImposterType)componentJson["mImposterType"].AsUInt32();
        }

        if (componentJson.HasMember("mFramesInterval"))
        {
            imposterBakerComponent->mFramesInterval = componentJson["mFramesInterval"].AsUInt32();
        }

        if (componentJson.HasMember("mOutputFolder"))
        {
            imposterBakerComponent->mOutputFolder = componentJson["mOutputFolder"].AsString();
        }
    }
}

void ImposterBakerSystemG::PostDeserializeImposterBakerComponent(const DeserializeNode& componentJson, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityID)
{
}

void ImposterBakerSystemG::DrawDebugCamera(const ImposterBakerComponentWriter& writer, bool enable) 
{
    auto captureEntity = writer->mCaptureEntity;
    auto imposterEntity = writer.GetEntityID();

    auto viewCaptureVector = GenerateViewCaptureVector(writer->mFramesInterval, writer->mImposterType);

    auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    std::string rootName = "DebugCameraRoot";
    if (enable)
    {
        // Root node
        auto rootNode = mGameWorld->CreateEntity("Common");
        auto [rootMetaHandle, rootTransformH] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, WorldTransformComponentG>(rootNode);
        metaSys->SetEUID(rootMetaHandle.Write(), CrossUUID::GenerateCrossUUID());
        metaSys->SetName(rootMetaHandle.Write(), rootName);
        transformSystem->Joint(rootNode, imposterEntity, false);

        // Get entity sphere radius
        auto propertySystem = mGameWorld->GetGameSystem<RenderPropertySystemG>();
        auto modelSys = mGameWorld->GetGameSystem<cross::ModelSystemG>();
        auto aabbSys = mGameWorld->GetGameSystem<AABBSystemG>();
        auto [modelH, renderPropH, transformComp] = mGameWorld->GetComponent<ModelComponentG, RenderPropertyComponentG, WorldTransformComponentG>(captureEntity);
        auto& modelAsset = modelSys->GetModelAsset(modelH.Read(), 0);
        auto boundingSphere = modelAsset->GetAssetData()->CalculateBoundingSphere(0);
        Float4x4 worldMat = transformSystem->GetWorldMatrix(transformComp.Read());
        BoundingSphere worldSphere;
        boundingSphere.Transform(worldSphere, worldMat);

        Float3 center = worldSphere.GetCenter();
        float radius = worldSphere.GetRadius();

        // Debug code
        for (UInt32 i = 0; i < viewCaptureVector.size(); ++i)
        {
            float cameraDistance = sCameraDistance;
            bool orthographic = false;

            auto& curVector = viewCaptureVector[i];
            Float3 vec = Float3(curVector.x, curVector.z, -curVector.y);
            //  Set camera transform
            Float3 position;
            if (orthographic)
            {}
            else
            {
                position = center + cameraDistance * Float3(vec.x, vec.y, vec.z);
            }
            Quaternion baseRot = Quaternion::EulerToQuaternion(Float3(0, 0, 0).ToRadian());

            vec = -1 * curVector;
            float yaw, pitch;
            yaw = std::atan2(vec.y, vec.x);
            pitch = std::atan2(vec.z, std::sqrt(vec.x * vec.x + vec.y * vec.y));
            Quaternion rotation = Quaternion::CreateFromYawPitchRoll(pitch, yaw, 0) * baseRot;
            // rotation = Quaternion(-rotation.z, -rotation.x, rotation.y, rotation.w);
            Float3 angle = Quaternion::QuaternionToEuler(rotation).ToDegree();
            rotation = Quaternion::EulerToQuaternion(Float3(0, yaw, pitch)) * baseRot;
            angle = Quaternion::QuaternionToEuler(rotation).ToDegree();

            auto cameraPoint = mGameWorld->CreateEntity("StaticModel");
            auto [metaHandle, pointTransformH, pointModelH] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, WorldTransformComponentG, ModelComponentG>(cameraPoint);
            metaSys->SetEUID(metaHandle.Write(), CrossUUID::GenerateCrossUUID());
            metaSys->SetName(metaHandle.Write(), "CameraPoint" + std::to_string(i));
            transformSystem->Joint(cameraPoint, rootNode, false);
            transformSystem->UpdateWorldTransform(cameraPoint);
            transformSystem->SetWorldTranslation(pointTransformH.Write(), position);
            transformSystem->SetWorldRotation(pointTransformH.Write(), rotation);
            modelSys->SetModelAssetPath(pointModelH.Write(), "EngineResource/Model/MatineeCamModel.nda", 0);
            modelSys->SetModelLodMaterialPath(pointModelH.Write(), "Material/DefaultFoliage.nda", 0, 0, 0);
        }
    }
    else
    {
        auto childCount = transformSystem->GetChildEntityNumber(imposterEntity);
        for (auto childIndex = 0; childIndex < childCount; ++childIndex)
        {
            auto childEntity = transformSystem->GetChildEntity(imposterEntity, childIndex);
            auto [metaHandle, worldTransformH] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, WorldTransformComponentG>(childEntity);
            auto childEntityName = metaSys->GetName(metaHandle.Read());
            if (childEntityName == rootName)
            {
                // destroy child
                auto childCamCount = transformSystem->GetChildEntityNumber(childEntity);
                for (int childCamIndex = static_cast<int>(childCamCount) - 1; childCamIndex >= 0; --childCamIndex)
                {
                    auto childCamEntity = transformSystem->GetChildEntity(childEntity, childCamIndex);
                    auto [camMetaHandle, camWorldTransformH] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, WorldTransformComponentG>(childCamEntity);
                    transformSystem->DisjointParent(camWorldTransformH.Read());
                    mGameWorld->DestroyEntity(childCamEntity);
                }

                transformSystem->DisjointParent(worldTransformH.Read());
                mGameWorld->DestroyEntity(childEntity);
                break;
            }
        }
    }
    mGameWorld->CallEditorUpdateHierarchyCallback();
}

std::vector<Float3> ImposterBakerSystemG::GenerateViewCaptureVector(UInt32 framesInterval, ImposterType imposterType)
{
    std::vector<Float3> viewCaptureVec;
    float framesIntervalF = static_cast<float>(framesInterval);
    for (UInt32 y = 0; y < framesInterval; ++y)
    {
        for (UInt32 x = 0; x < framesInterval; ++x)
        {
            Float2 oct = Float2(static_cast<float>(x) / (framesIntervalF - 1.0f), static_cast<float>(y) / (framesIntervalF - 1.0f));
            oct = oct * 2 - 1;
            Float3 vec = Float3::Zero();
            switch (imposterType)
            {
            case cross::ImposterType::IMPOSTER_TYPE_FULL_SPHERE:
                vec = OctahedronToUnitVector(oct);
                break;
            case cross::ImposterType::IMPOSTER_TYPE_UPPER_HEMISHPERE:
                vec = HemiOctahedronToUnitVector(oct);
                break;
            default:
                break;
            }
            viewCaptureVec.push_back(std::move(vec));
        }
    }
    return viewCaptureVec;
}

void ImposterBakerSystemG::SetupOctahedronLayout(const ImposterBakerComponentReader& reader)
{
    sIndex = 0;
    mViewCaptureVec = GenerateViewCaptureVector(reader->mFramesInterval, reader->mImposterType);
    mCaptureEntity = reader->mCaptureEntity;

    // Get entity sphere radius
    auto propertySystem = mGameWorld->GetGameSystem<RenderPropertySystemG>();
    auto modelSys = mGameWorld->GetGameSystem<cross::ModelSystemG>();
    auto aabbSys = mGameWorld->GetGameSystem<AABBSystemG>();
    auto [modelH, renderPropH] = mGameWorld->GetComponent<ModelComponentG, RenderPropertyComponentG>(mCaptureEntity);

    propertySystem->SetLayerIndex(renderPropH.Write(), sImposterLayer);
    propertySystem->SetLodSelected(renderPropH.Write(), 0);

    auto& modelAsset = modelSys->GetModelAsset(modelH.Read(), 0);
    mBoundingSphere = modelAsset->GetAssetData()->CalculateBoundingSphere(0);

    DispatchRenderingCommandWithToken([&, viewCaptureVec = mViewCaptureVec] {
        mRenderObject->SetViewCaptureVec(viewCaptureVec);
        auto renderWorld = mGameWorld->GetRenderWorld();
    });
}

void ImposterBakerSystemG::SetPrefabResource(const ImposterBakerComponentWriter& writer, const std::string& prefabResource) 
{
    writer->mPrefabResource = prefabResource;
}

void ImposterBakerSystemG::SetCaptureEntity(const ImposterBakerComponentWriter& writer, ecs::EntityID entity) 
{
    writer->mCaptureEntity = entity;

    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    if (!mGameWorld->IsEntityAlive(writer->mPrefabEntity))
    {
        std::string prefabPath = "EngineResource/Model/ImposterMesh.prefab";
        auto prefabSys = mGameWorld->GetGameSystem<PrefabSystemG>();
        auto modelSys = mGameWorld->GetGameSystem<cross::ModelSystemG>();
        writer->mPrefabEntity = prefabSys->CreatePrefabInstance(prefabPath, writer.GetEntityID());
        mGameWorld->CallEditorUpdateHierarchyCallback();
    }

    auto transformComp = mGameWorld->GetComponent<WorldTransformComponentG>(writer->mCaptureEntity);
    auto transformH = mGameWorld->GetComponent<WorldTransformComponentG>(writer->mPrefabEntity);
    Float4x4 worldMat = transformSystem->GetWorldMatrix(transformComp.Read());
    auto location = transformSystem->GetWorldTranslation(transformComp.Read());
    transformSystem->SetWorldTranslation(transformH.Write(), location + Float3(0, 0, 500));
}

void ImposterBakerSystemG::SetOutputFolder(const ImposterBakerComponentWriter& writer, std::string outputFolder) 
{
    writer->mOutputFolder = outputFolder;
}

void ImposterBakerSystemG::SetRenderCamera(ecs::EntityID camera) 
{
    mCamera = camera;

    resource::RenderTextureInfo info{"ImposterTexture", TextureDimension::Tex2D, RenderTextureFormat::R8G8B8A8_UNorm, 4096, 4096};
    mImposterTextures[0] = gResourceMgr.CreateResourceAs<cross::resource::RenderTextureResource>(info);
    mImposterTextures[1] = gResourceMgr.CreateResourceAs<cross::resource::RenderTextureResource>(info);

    DispatchRenderingCommandWithToken([&] {
        ImposterTextureArray imposterTextures;
        imposterTextures[0] = mImposterTextures[0]->GetNGITexture();
        imposterTextures[1] = mImposterTextures[1]->GetNGITexture();
        mRenderObject->SetImposterRenderTexture(imposterTextures);
        mRenderObject->SetRenderCamera(mCamera);
    });
}

void ImposterBakerSystemG::CaptureImposterGrid(ecs::EntityID imposter)
{
    mImposterEntity = imposter;
    auto imposterComp = mGameWorld->GetComponent<ImposterBakerComponentG>(imposter);
    SetupOctahedronLayout(imposterComp.Read());
}

void ImposterBakerSystemG::CaptureScene() 
{
    auto rendererSystem = EngineGlobal::Inst().GetEngine()->GetGlobalSystem<RendererSystemG>();
    auto cameraSys = (cross::CameraSystemG*)mGameWorld->GetGameSystem<cross::CameraSystemG>();
    auto cameraComponent = mGameWorld->GetComponent<CameraComponentG>(mCamera);
    auto renderTextureResourcePtr = cameraSys->GetCameraRenderTextureResourcePtr(cameraComponent.Read());

    auto width = cameraSys->GetTargetWidth(cameraComponent.Read());
    auto height = cameraSys->GetTargetHeight(cameraComponent.Read());
    const UInt32 dataSize = width * height * sizeof(UInt32);
    void* data = cross::Memory::Malloc(dataSize);

    UInt32 index = sIndex;
    rendererSystem->ReadbackRenderTexture(renderTextureResourcePtr, 0, {0, 0, 0}, {width, height, 1}, data, dataSize, [=] {
        {
            {
                imageio::color_rgba* colors1 = (imageio::color_rgba*)data;
                imageio::image img(width, height);
                for (UInt32 y = 0; y < height; ++y)
                {
                    for (UInt32 x = 0; x < width; ++x)
                    {
                        auto idx = y * width + x;
                        auto& sample = colors1[idx];
                        auto& color = img(x, y);
                        color.r = sample.r;
                        color.g = sample.g;
                        color.b = sample.b;
                        color.a = 255;
                    }
                }
                std::string fileName = "D:/testimg/test" + std::to_string(index) + ".png";
                imageio::save_png(fileName, img);
            }
        }
        cross::Memory::Free(data);
    });
}

}
