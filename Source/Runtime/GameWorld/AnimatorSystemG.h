#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CEAnimation/Animator/AnimatorParameter.h"
#include "CEAnimation/Transform/AnimPose.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Animation/AnimFactory.h"
#include "Runtime/GameWorld/ScriptSystemG.h"
#include "Runtime/GameWorld/SkBuildSystemG.h"
#include "Resource/Animation/Animator/AnimatorResource.h"
#include "RendererSystemG.h"
using namespace cross::anim;
namespace cross {

struct ComponentDesc;

struct AnimatorComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = AnimatorSystemG);

    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

    bool mEnable{false};
    anim::AnimatorResPtr AnimatorResPtr{nullptr};
    std::shared_ptr<anim::Animator> Animator{nullptr};

    friend class AnimatorSystemG;
};

class ENGINE_API AnimatorSystemG final : public GameSystemBase, public SystemEventManager<AnimAttachEvent>
{
    CESystemInternal(ComponentType = AnimatorComponentG)
public:
    using AnimatorCompHandle = ecs::ComponentHandle<AnimatorComponentG>;
    DEFINE_COMPONENT_READER_WRITER(AnimatorComponentG, AnimatorCompReader, AnimatorCompWriter);

    CEFunction(Reflect)
    static AnimatorSystemG* CreateInstance();

    virtual void Release() override {}

    virtual void NotifyAddRenderSystemToRenderWorld() override {}

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    CEFunction(Reflect)
    virtual void OnBuildPostUpdateTasks(FrameParam* frameParam) override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

public:
    void SetAnimatorEnable(const AnimatorCompWriter& handle, bool enable)
    {
        handle->mEnable = enable;
    }
    bool GetAnimatorEnable(const AnimatorCompReader& handle)
    {
        return handle->mEnable;
    }
    // Script interface
    CEFunction(Editor, Script) bool SetAnimator(const AnimatorCompWriter& handle, const std::string& animatorAssetPath);

    // Stb Debug Interface
    CEFunction(Editor, Script) void SetIsBeingDebugged(const AnimatorCompWriter& handle, bool bDebug);
    CEFunction(Editor, Script) bool GetIsBeingDebugged(const AnimatorCompReader& handle) const;
    void GetStbDebugData(const AnimatorCompReader& handle, StbDebugData& outdata);
    // ~Stb Debug Interface

    CEFunction(Editor, Script) bool SetAnimatorPlayable(const AnimatorCompWriter& handle, bool bPlayable);
    CEFunction(Editor, Script) std::string GetAnimatorAssetPath(const AnimatorCompReader& handle);
    CEFunction(Editor, Script) std::string GetAnimatorName(const AnimatorCompReader& handle);

    bool PlayAnimSequence(const AnimatorCompWriter& handle, const std::string& animAssetPath, bool loop, float playRate, const CEName& slotName, const CEName& syncGroupName);
    bool PlayAnimComposite(const AnimatorCompWriter& handle, const std::string& animAssetPath, bool loop, float playRate);
    bool PlayAnimatrix(const AnimatorCompWriter& handle, const std::string& animAssetPath);

    AnimCmpInstanceBasePtr GetAnimInstanceForID(const AnimatorCompReader& handle, SInt32 animInstanceID); 
    AnimCmpInstanceBasePtr CreateAnimInstanceFromAnimSequence(const AnimatorCompWriter& handle, const std::string& animAssetPath, bool loop, float playRate, const CEName& slotName);

    void PlayAnimInstance(const AnimatorCompWriter& handle, const AnimCmpInstanceBasePtr& animInstance, bool stopOthers);

    template<class ParamT, class ValueT>
    void SetParam(ecs::EntityID entity, const AnimatorCompReader& handle, const std::string& name, const ValueT& value)
    {
        if (!handle->Animator)
        {
            return;
        }
        ParameterPtr param = handle->Animator->GetParameterByName(name.c_str());
        auto paramCasted = std::dynamic_pointer_cast<ParamT>(param);
        if (paramCasted)
        {
            paramCasted->GetValue() = value;

            handle->Animator->GetParamSymbolTable().UpdateTableByParam(param.get());
        }
    }

    void SetParam(const AnimatorCompReader& handle, const std::string& name, const CppParam& value)
    {
        assert(handle->Animator);
        std::visit(
            [&](auto&& arg) {
                using ValueT = std::decay_t<decltype(arg)>;
                using ParamT = BaseParameter<ValueT>;
                SetParam<ParamT, ValueT>(handle.GetEntityID(), handle, name, arg);
            },
            value);
    }

    template<typename TEnum>
        requires std::is_enum_v<TEnum>
    void SetParam(const AnimatorCompReader& handle, const std::string& name, const TEnum value)
    {
        assert(handle->Animator);
        SetParam<IntParameter, SInt32>(handle.GetEntityID(), handle, name, static_cast<SInt32>(value));
    }

    bool HasParam(ecs::EntityID entity, const AnimatorCompReader& handle, const std::string& name);

    anim::ParamMode::Type GetParamType(ecs::EntityID entity, const AnimatorCompReader& handle, const std::string& name);

    template<class T>
    T GetParamValue(ecs::EntityID entity, const AnimatorCompReader& handle, const std::string& name)
    {
        if (!handle->Animator)
            return T(0);

        ParameterPtr param = handle->Animator->GetParameterByName({name.c_str()});

        T tempT;
        Parameter::Extract(param.get(), &tempT);
        return std::move(tempT);
    }

    anim::Animator& GetAnimator(const AnimatorCompReader& handle)
    {
        return *handle->Animator;
    }

    // For script
    bool PlayAnimation(const AnimatorCompWriter& handle, const char* aniPath, bool loop, float playRate, const char* slotName, const char* groupName);
#if SUPPORT_LUA
    CEFunction(Script) script::Local<script::Value> PlayAnimation(const AnimatorCompWriter& handle, script::Arguments const& args);
    CEFunction(Script) void SetParameter(const AnimatorCompReader& handle, script::Arguments const& args);
    CEFunction(Script) script::Local<script::Value> GetParameter(const AnimatorCompReader& handle, script::Arguments const& args);
#endif
    CEFunction(Editor) void EnableUpdateInEditor(bool b);

public:
    static SerializeNode SerializeAnimatorComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeAnimatorComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeAnimatorComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    static void UpdateDeserializeAnimatorComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    static void GetResourceAnimatorComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource);

protected:
    AnimatorSystemG();
    virtual ~AnimatorSystemG() = default;
    virtual void OnFirstUpdate(FrameParam* frameParam) override;
    virtual RenderSystemBase* GetRenderSystem() override;
    void OnAnimSeqCreated(const Animator* inAnimator, GameWorld* inGameWorld, anim::AnimSeqPtr inSeqPtr);
    void OnAnimExecCreated(const Animator* inAnimator, GameWorld* inGameWorld, AnimExecPtr inSeqPtr);
    float ProcessDeltaTime(float elapsedTime);

public:
    bool mEnableUpdateInEditor = false;

private:
    AnimFactory mAnimFactory;
};

}   // namespace cross
