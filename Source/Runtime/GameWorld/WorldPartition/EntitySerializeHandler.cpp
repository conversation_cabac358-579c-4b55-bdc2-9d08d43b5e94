// Include
#include "CECommon/Common/WorldConst.h"
#include "Runtime/GameWorld/WorldPartition/EntitySerializeHandler.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/Prefab/PrefabManager.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"            // TEMP
#include "Runtime/GameWorld/RenderPropertySystemG.h"   // TEMP
#include "Runtime/GameWorld/LightMapSystemG.h"         // TEMP
#include "Runtime/GameWorld/FoliageComponentG.h"       // TEMP
#include "Runtime/GameWorld/LightProbeSystemG.h"       // TEMP
#include "Runtime/Reflection/TypeGet.h"
namespace cross {

SerializeNode EntitySerializeHandler::Serialize(GameWorld* world, const ecs::EntityID& entity, bool checkSelfPrefab)
{
    auto metaSys = world->GetGameSystem<EntityMetaSystem>();
    auto metaHandle = world->GetComponent<ecs::EntityMetaComponentG>(entity);
    if (!metaSys->IsSerializable(metaHandle.Read()))
        return SerializeNode::EmptyObject();

    SerializeNode entityJson;
    // entity meta info
    auto prefabData = metaSys->GetPrefabEntityData(metaHandle.Read());
    entityJson[WorldSerializeConst::PrefabID] = prefabData ? (checkSelfPrefab ? prefabData->mInheritPrefabId : prefabData->mPrefabId) : "";
    entityJson[WorldSerializeConst::PrefabEUID] = prefabData ? (checkSelfPrefab ? (prefabData->IsInherit() ? prefabData->mInheritPrefabEUID.ToString() : "") : prefabData->mPrefabEUID.ToString()) : "";
    entityJson[WorldSerializeConst::Euid] = metaSys->GetEUID(metaHandle.Read()).ToString();
    entityJson[WorldSerializeConst::Name] = metaSys->GetName(metaHandle.Read());
    entityJson[WorldSerializeConst::PrototypeHash] = world->GetPrototype(entity)->Hash;
    entityJson[WorldSerializeConst::BFolder] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsFolder);
    entityJson[WorldSerializeConst::BExpand] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsExpand);
    entityJson[WorldSerializeConst::BHide] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsHide);
    entityJson[WorldSerializeConst::BSelectable] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsSelectable);
    // serialize components in this Entity
    Assert(world->GetPrototype(entity));
    SerializeNode comsJson;
    auto componentArray = world->GetAllComponentsArray(entity);
    for (auto [componentPtr, compDesc] : componentArray)
    {
        if (!compDesc->IsSerializeable())
            continue;
        SerializeNode componentJson;
        // Serialize
        if (compDesc->Serialize) {
            componentJson = compDesc->Serialize(world, componentPtr, entity);
            if (compDesc->IsEditorOnly())
                componentJson[WorldSerializeConst::EditorOnly] = true;
        }
        auto& componentName = compDesc->Name;
        componentJson[WorldSerializeConst::ComponentHash] = componentName.GetHash32();
        comsJson[componentName.GetString()] = std::move(componentJson);
    }
    entityJson[WorldSerializeConst::ComponentList] = std::move(comsJson);
    return entityJson;
}

SerializeNode EntitySerializeHandler::Serialize(GameWorld* world, const ecs::EntityID& entity, ResourcePtr resource, bool checkSelfPrefab)
{
    auto metaSys = world->GetGameSystem<EntityMetaSystem>();
    auto metaHandle = world->GetComponent<ecs::EntityMetaComponentG>(entity);
    if (!metaSys->IsSerializable(metaHandle.Read()))
        return SerializeNode::EmptyObject();

    SerializeNode entityJson;
    // entity meta info
    auto prefabData = metaSys->GetPrefabEntityData(metaHandle.Read());
    entityJson[WorldSerializeConst::PrefabID] = prefabData ? (checkSelfPrefab ? prefabData->mInheritPrefabId : prefabData->mPrefabId) : "";
    entityJson[WorldSerializeConst::PrefabEUID] = prefabData ? (checkSelfPrefab ? (prefabData->IsInherit() ? prefabData->mInheritPrefabEUID.ToString() : "") : prefabData->mPrefabEUID.ToString()) : "";
    entityJson[WorldSerializeConst::Euid] = metaSys->GetEUID(metaHandle.Read()).ToString();
    entityJson[WorldSerializeConst::Name] = metaSys->GetName(metaHandle.Read());
    entityJson[WorldSerializeConst::PrototypeHash] = world->GetPrototype(entity)->Hash;
    entityJson[WorldSerializeConst::BFolder] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsFolder);
    entityJson[WorldSerializeConst::BExpand] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsExpand);
    entityJson[WorldSerializeConst::BHide] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsHide);
    entityJson[WorldSerializeConst::BSelectable] = metaSys->GetFlags(metaHandle.Read()).GetEntityProp(ecs::EntityDescFlags::EntityProps::IsSelectable);
    // serialize components in this Entity
    Assert(world->GetPrototype(entity));
    SerializeNode comsJson;
    auto componentArray = world->GetAllComponentsArray(entity);
    for (auto [componentPtr, compDesc] : componentArray)
    {
        if (!compDesc->IsSerializeable())
            continue;
        SerializeNode componentJson;
        // Serialize
        if (compDesc->Serialize)
        {
            componentJson = compDesc->Serialize(world, componentPtr, entity);
            if (compDesc->IsEditorOnly())
                componentJson[WorldSerializeConst::EditorOnly] = true;
            if (compDesc->GetResource)
                compDesc->GetResource(world, componentPtr, resource);
        }
        auto& componentName = compDesc->Name;
        componentJson[WorldSerializeConst::ComponentHash] = componentName.GetHash32();
        comsJson[componentName.GetString()] = std::move(componentJson);
    }
    entityJson[WorldSerializeConst::ComponentList] = std::move(comsJson);
    return entityJson;
}

SerializeNode EntitySerializeHandler::SerializeChildren(GameWorld* world, const ecs::EntityID& entity)
{
    auto metaSys = world->GetGameSystem<EntityMetaSystem>();
    auto transSys = world->GetGameSystem<TransformSystemG>();
    // children
    SerializeNode childrenNode = SerializeNode::EmptyArray();
    auto childEntity = transSys->GetEntityFirstChild(entity);
    while (childEntity != ecs::EntityID::InvalidHandle())
    {
        auto childMetaHandle = world->GetComponent<ecs::EntityMetaComponentG>(childEntity).Read();
        if (metaSys->IsSerializable(childMetaHandle))
        {
            auto childEuid = metaSys->GetEUID(childMetaHandle);
            childrenNode.PushBack(childEuid.ToString());
        }
        childEntity = transSys->GetEntityNextSibling(childEntity);
        if (childEntity == transSys->GetEntityFirstChild(entity))
            break;
    }
    return childrenNode;
}

SerializeNode EntitySerializeHandler::SerializeChildrenPrefab(GameWorld* world, const ecs::EntityID& entity)
{
    auto metaSys = world->GetGameSystem<EntityMetaSystem>();
    auto prefabData = metaSys->GetPrefabEntityData(world->GetComponent<ecs::EntityMetaComponentG>(entity).Read());
    if (!prefabData)
        return SerializeNode::EmptyArray();
    // children
    auto transSys = world->GetGameSystem<TransformSystemG>();
    SerializeNode childrenPrefabNode = SerializeNode::EmptyArray();
    auto childEntity = transSys->GetEntityFirstChild(entity);
    while (childEntity != ecs::EntityID::InvalidHandle())
    {
        auto childMetaHandle = world->GetComponent<ecs::EntityMetaComponentG>(childEntity).Read();
        if (metaSys->IsSerializable(childMetaHandle))
        {
            auto childPrefabData = metaSys->GetPrefabEntityData(world->GetComponent<ecs::EntityMetaComponentG>(childEntity).Read());
            auto childPrefabEuid = (childPrefabData && childPrefabData->mPrefabId == prefabData->mPrefabId) ? childPrefabData->mPrefabEUID.ToString() : "";
            childrenPrefabNode.PushBack(childPrefabEuid);
        }
        childEntity = transSys->GetEntityNextSibling(childEntity);
        if (childEntity == transSys->GetEntityFirstChild(entity))
            break;
    }
    return childrenPrefabNode;
}

void EntitySerializeHandler::GetResource(GameWorld* world, const ecs::EntityID& entity, ResourcePtr resource)
{
    Assert(world->GetPrototype(entity));
    auto componentArray = world->GetAllComponentsArray(entity);
    for (auto [componentPtr, compDesc] : componentArray)
    {
        if (compDesc->GetResource)
        {
            compDesc->GetResource(world, componentPtr, resource);
        }
    }
}

ecs::ComponentBitMask EntitySerializeHandler::GetTypeMask(const DeserializeNode& componentsJson)
{
    // add component list
    bool isApp = EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeCrossEditor;
    ecs::ComponentBitMask typeMask;
    for (auto [componentName, componentJson] : componentsJson)
    {
        if (!componentJson.HasMember(WorldSerializeConst::ComponentHash))
            continue;
        if (isApp)
        {
            auto editorOnly = componentJson.HasMember(WorldSerializeConst::EditorOnly);
            if (editorOnly && editorOnly->AsBoolean())
                continue;
        }
        auto* compDesc = ecs::GetComponentDescByHash(componentJson[WorldSerializeConst::ComponentHash].AsUInt32());
        typeMask.Set(compDesc->GetMaskBitIndex(), true);
    }
    {
        // TODO(yuanwan) Remove this after all hand-made worlds have been updated // Script-created entities may have no RenderPropertyComponent now
        // hack: inject component for world files exported by other engines
        auto* modelDesc = ModelComponentG::GetDesc();
        auto* renderProperDesc = RenderPropertyComponentG::GetDesc();
        if (typeMask[modelDesc->GetMaskBitIndex()])
        {
            typeMask.Set(renderProperDesc->GetMaskBitIndex(), true);
        }
        //// TODO(WWT): why hardcoded here???
        //auto& desc_func = reflection::GetMemberFunction("cross::LightMapEditorComponentG", "GetDesc");
        //auto* lightMapEditorCompDesc = &reflection::runtime::callStatic(desc_func).ref<ecs::ComponentDesc>();
        //auto* lightProbeCompDesc = LightProbeComponentG::GetDesc();
        //auto* foliageCompDesc = FoliageComponentG::GetDesc();
        //if (typeMask[foliageCompDesc->GetMaskBitIndex()])
        //{
        //    typeMask.Set(lightMapEditorCompDesc->GetMaskBitIndex(), true);
        //    typeMask.Set(lightProbeCompDesc->GetMaskBitIndex(), true);
        //}
    }
    return typeMask;
}

void EntitySerializeHandler::DeserializeEntity(GameWorld* world, const ecs::EntityID& entity, const ecs::ComponentBitMask& componentMask, const DeserializeNode& entityJson)
{
    const DeserializeNode& componentsJson = entityJson[WorldSerializeConst::ComponentList];
    // set component data
    auto componentArray = world->GetAllComponentsArray(entity);
    for (auto [componentPtr, compDesc] : componentArray)
    {
        if (!compDesc->IsSerializeable())
            continue;

        if (compDesc->Deserialize)
        {
            if (auto&& value = componentsJson.HasMember(compDesc->Name.GetCString()); value)
            {
                compDesc->Deserialize(world, value.value(), componentPtr);
            }
        }
    }

    // set EntityMetaComponent
    auto metaSys = world->GetGameSystem<EntityMetaSystem>();
    auto metaHandle = world->GetComponent<ecs::EntityMetaComponentG>(entity);
    metaSys->SetEUID(metaHandle.Write(), CrossUUID(entityJson[WorldSerializeConst::Euid].AsString()));
    metaSys->SetName(metaHandle.Write(), entityJson[WorldSerializeConst::Name].AsString());
    metaSys->GetFlagsToSet(metaHandle.Write()).SetEntityProp(ecs::EntityDescFlags::EntityProps::Serializeable, true);
    if (entityJson.HasMember(WorldSerializeConst::BFolder))
    {
        metaSys->GetFlagsToSet(metaHandle.Write()).SetEntityProp(ecs::EntityDescFlags::EntityProps::IsFolder, entityJson[WorldSerializeConst::BFolder].AsBoolean());
    }
    if (entityJson.HasMember(WorldSerializeConst::BExpand))
    {
        metaSys->GetFlagsToSet(metaHandle.Write()).SetEntityProp(ecs::EntityDescFlags::EntityProps::IsExpand, entityJson[WorldSerializeConst::BExpand].AsBoolean());
    }
    if (entityJson.HasMember(WorldSerializeConst::BHide))
    {
        metaSys->GetFlagsToSet(metaHandle.Write()).SetEntityProp(ecs::EntityDescFlags::EntityProps::IsHide, entityJson[WorldSerializeConst::BHide].AsBoolean());
    }
    if (entityJson.HasMember(WorldSerializeConst::BSelectable))
    {
        metaSys->GetFlagsToSet(metaHandle.Write()).SetEntityProp(ecs::EntityDescFlags::EntityProps::IsSelectable, entityJson[WorldSerializeConst::BSelectable].AsBoolean());
    }
}

std::pair<ecs::EntityID, std::function<void()>> EntitySerializeHandler::DeserializeDeffer(GameWorld* world, const DeserializeNode& entityJson, UInt64 targetBlockId)
{
    ecs::EntityID entity = world->CreateEntityID();
    // component

    const DeserializeNode& componentsJson = entityJson[WorldSerializeConst::ComponentList];

    auto typeMask = GetTypeMask(componentsJson);

    world->AddComponentByPrototype(entity, ecs::GetOrCreatePrototypeG(typeMask), targetBlockId, true);

    auto ret = [=, &json = entityJson]() {
        DeserializeEntity(world, entity, typeMask, json);
    };
    return { entity, ret };
}

ecs::EntityID EntitySerializeHandler::Deserialize(GameWorld* world, const DeserializeNode& entityJson, UInt64 targetBlockId)
{
    auto ret = DeserializeDeffer(world, entityJson, targetBlockId);
    ret.second();
    return ret.first;
}

void EntitySerializeHandler::PostDeserialize(GameWorld* world, ecs::EntityID entity, const DeserializeNode& entityJson)
{
    //LOG_INFO("PostDeserialize {}", entityJson[WorldSerializeConst::Name].AsString());
    // component
    const DeserializeNode& componentsJson = entityJson[WorldSerializeConst::ComponentList];
    // Post Deserialize
    auto componentArray = world->GetAllComponentsArray(entity);
    for (auto [componentPtr, compDesc] : componentArray)
    {
        if (!compDesc->IsSerializeable())
            continue;

        
        if (compDesc->PostDeserialize)
        {
            if (auto&& value = componentsJson.HasMember(compDesc->Name.GetCString()); value)
            {
                //LOG_INFO("Post Comp {}", compDesc->Name.GetCString());
                compDesc->PostDeserialize(value.value(), componentPtr, world, entity);
            }
        }
    }
}
}   // namespace cross
