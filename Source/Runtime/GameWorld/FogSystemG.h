#pragma once
#include "RenderEngine/FogSystemR.h"
#include "RenderPipeline/Effects/VolumetricFog.h"

namespace cross {

    struct ENGINE_API FogComponentG : ecs::IComponent
    {
        CEComponentInternal(SystemType = FogComponentG)

        CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of Fog effect"))
        FogSetting Fog;

        CEFunction(Reflect)
        static ecs::ComponentDesc* GetDesc();

        CE_Serialize_Deserialize;
    };

    class ENGINE_API CEMeta(Editor) FogSystemG : public GameSystemBase
    {
        CESystemInternal(ComponentType = FogComponentG)
    public:
        using FogCompHandle = ecs::ComponentHandle<FogComponentG>;
        DEFINE_COMPONENT_READER_WRITER(FogComponentG, <PERSON>og<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FogCompWriter)

        // GameSystemBase
        CEFunction(Reflect)
        static FogSystemG* CreateInstance();
        void Release() override;

        virtual void NotifyAddRenderSystemToRenderWorld() override {}
        RenderSystemBase* GetRenderSystem() override { return mFogSystemR; }

        // FogComponentG
        static SerializeNode SerializeFogComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
        static void DeserializeFogComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
        static void PostDeserializeFogComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
        static void GetResourceComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource);
        static void UpdateDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

        // FogSystemG
        CEFunction(Editor)
        void SetFogSetting(const FogCompWriter& comp, const FogSetting& val) const;

        CEFunction(Editor)
        const FogSetting& GetFogSetting(const FogCompReader& comp) const { return comp->Fog; }

        FogSetting& GetFogSetting(const FogCompWriter& comp) { return comp->Fog; }
    protected:
        FogSystemG();
        virtual ~FogSystemG() = default;

        FogSystemR* mFogSystemR{ nullptr };

    };
}