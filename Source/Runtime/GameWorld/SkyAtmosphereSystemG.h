#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/SkyAtmosphere/Model.h"
#include "RenderEngine/SkyAtmosphereSetting.h"

namespace cross {

class SkyAtmosphereSystemR;
struct ComponentDesc;

enum class CEMeta(Editor) AtmosphereUnit
{
    M,
    CM
};

struct ENGINE_API CEMeta(Cli, Puerts, WorkflowType) SkyAtmosphereConfig
{
    SkyAtmosphereConfig();
    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "MiePhase", bKeyFrame = true))
    float MiePhase = 0.0004f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Mie scattering coeff", bKeyFrame = true))
    Float3 MieScattCoeff = {147.0f / 255.0f, 147.0f / 255.0f, 147.0f / 255.0f};

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "MieScattScale", bKeyFrame = true))
    float MieScattScale = 0.00396f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Mie absorption coeff", bKeyFrame = true))
    Float3 MieAbsorCoeff = {147.0f / 255.0f, 147.0f / 255.0f, 147.0f / 255.0f};

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "MieAbsorScale", bKeyFrame = true))
    float MieAbsorScale = 0.005f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Raylei scattering coeff", bKeyFrame = true))
    Float3 RayScattCoeff = {41.0f / 255.0f, 95.0f / 255.0f, 233.0f / 255.0f};

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "RayScattScale", bKeyFrame = true))
    float RayScattScale = 0.0331f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Absorption coeff", bKeyFrame = true))
    Float3 AbsorptiCoeff = {83.0f / 255.0f, 241.0f / 255.0f, 11.0f / 255.0f};

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "AbsorptiScale", bKeyFrame = true))
    float AbsorptiScale = 0.0018f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "PlanetRadius", bKeyFrame = true))
    float PlanetRadius = 6375.0f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "AtmosHeight", bKeyFrame = true))
    float AtmosHeight = 60.0f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "MieScaleHeight", bKeyFrame = true))
    float MieScaleHeight = 1.200f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "RayScaleHeight", bKeyFrame = true))
    float RayScaleHeight = 8.000f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Ground albedo", bKeyFrame = true))
    Float3 GroundAlbedo3 = {0.001f, 0.001f, 0.001f};

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "SFogMieScattScale", bKeyFrame = true))
    float SFogMieScattScale = 0.05f;

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "SkyLuminanceFactor", bKeyFrame = true))
    Float3 SkyLuminanceFactor = Float3::One();

    CEProperty(Editor, Script, Reflect, Serialize, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "MultiScatteringFactor", bKeyFrame = true))
    float MultiScatteringFactor = 1.f;

    CE_Serialize_Deserialize;
    friend struct SkyAtmosphereSystemGInterface;
};

struct SkyAtmosphereComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = SkyAtmosphereSystemG) 
    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

protected:
    /*
    *   sky, atmosphere and planet inner configuration for editor.
    *   Need to recompute texture when changed.
    */
    CEProperty(Editor, Script) SkyAtmosphereConfig config;

    /*
     *   outer param.
     */
    CEProperty(Editor, Script) SkyAtmosphereOuterParam outerParam;

    /*
    *   Physical model for sky and atmosphere used for pre-computation and rendering.
    */
    bool configDirty = true;
    bool paramDirty = true;
    std::shared_ptr<atmosphere::Model> model = nullptr;

    CE_Serialize_Deserialize;

    friend class SkyAtmosphereSystemG;
    friend struct SkyAtmosphereSystemGInterface;
};

class ENGINE_API SkyAtmosphereSystemG final : public GameSystemBase
{
    CESystemInternal(ComponentType = SkyAtmosphereSystemG)
public :
    using SkyAtmosphereCompHandle = ecs::ComponentHandle<SkyAtmosphereComponentG>;
    DEFINE_COMPONENT_READER_WRITER(SkyAtmosphereComponentG, SkyAtmosphereCompReader, SkyAtmosphereCompWriter)

    CEFunction(Reflect) static SkyAtmosphereSystemG* CreateInstance();

    virtual void Release() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

public:
    CEFunction(Editor, Script) SkyAtmosphereConfig GetSkyAtmosphereConfig(const SkyAtmosphereCompReader& comp) const
    {
        return comp->config;
    }

    CEFunction(Editor, Script) void SetSkyAtmosphereConfig(const SkyAtmosphereCompWriter& comp, const SkyAtmosphereConfig& inConfig)
    {
        comp->config = inConfig;
        comp->configDirty = true;
    }

    CEFunction(Editor, Script) SkyAtmosphereOuterParam GetSkyAtmosphereOuterParam(const SkyAtmosphereCompReader& comp) const
    {
        return comp->outerParam;
    }

    CEFunction(Editor, Script) void SetSkyAtmosphereOuterParam(const SkyAtmosphereCompWriter& comp, const SkyAtmosphereOuterParam& inParam)
    {
        comp->outerParam = inParam;
        comp->paramDirty = true;
    }

    void SetRenderSunDisk(const SkyAtmosphereCompWriter& comp, bool enabled)
    {
        comp->paramDirty |= (comp->outerParam.RenderSunDisk != enabled);
        comp->outerParam.RenderSunDisk = enabled;
    }

    void SetRenderMoonDisk(const SkyAtmosphereCompWriter& comp, bool enabled)
    {
        comp->paramDirty |= (comp->outerParam.RenderMoonDisk != enabled);
        comp->outerParam.RenderMoonDisk = enabled;
    }

public:
    /*
    *   Compute Planet radius at given [Any, latitude, altitude] and refresh all sky atmosphere component
    */
    void RefreshPlanetRadiusByLocation(double latitude, double altitude = 0.0);

    /*
     *   Compute Planet radius at given [Any, latitude, altitude] and refresh given sky atmosphere component
     */
    CEFunction(Editor, Script)
    void RefreshPlanetRadiusByLocation(const SkyAtmosphereCompWriter& comp, double latitudeInDegree, double altitude);

    /*
    *   Set if use override fog mie value
    */
    void SetFogMieState(bool value);

public:
    static SerializeNode SerializeSkyAtmosphereComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeSkyAtmosphereComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeSkyAtmosphereComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    static void UpdateDeserializeSkyAtmosphereComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    SkyAtmosphereSystemG();
    virtual ~SkyAtmosphereSystemG();
    virtual void OnFirstUpdate(FrameParam* frameParam) override;

private:
    void UpdateDebugAPScale();

private:
    SkyAtmosphereSystemR* mRenderObject{nullptr};
    bool mIsRenderObjectOwner{true};

    ComputeShaderPtr mPrecomputeShader;
    ComputeShaderPtr mPrecomputeAPShader;

    bool mStoreCVars = false;
    bool EnableSFogMie = false;

    float mDebugAPScale = 0.0f;
};

}   // namespace cross
