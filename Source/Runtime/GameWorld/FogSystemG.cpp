#include "FogSystemG.h"
#include "ECS/Develop/Framework.h"
#include "Runtime/GameWorld/GameWorld.h"
namespace cross {
    ecs::ComponentDesc* FogComponentG::GetDesc()
    {
        return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::FogComponentG>(
            { false, true, true },
            &FogSystemG::SerializeFogComponent,
            &FogSystemG::DeserializeFogComponent,
            &FogSystemG::PostDeserializeFogComponent,
            &FogSystemG::UpdateDeserializeComponent,
            &FogSystemG::GetResourceComponent);
    }

    SerializeNode FogSystemG::SerializeFogComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
    {
        auto fogCompPtr = static_cast<FogComponentG*>(componentPtr);

        SerializeContext context;
        SerializeNode outNode = fogCompPtr->Serialize(context);
        return outNode;
    }

    void FogSystemG::DeserializeFogComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
    {
        if (json.IsNull())
            return;

        auto fogCompPtr = static_cast<FogComponentG*>(componentPtr);
        if (json.IsObject())
        {
            SerializeContext context;
            fogCompPtr->Deserialize(json, context);
        }
    }

    void FogSystemG::PostDeserializeFogComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
    {
        auto* gameSystem = gameWorld->GetGameSystem<cross::FogSystemG>();
        auto handle = gameWorld->GetComponent<cross::FogComponentG>(entityId);
        auto compW = handle.Write();
        auto renderSystem = gameSystem->mFogSystemR;
        DispatchRenderingCommandWithToken([renderSystem = renderSystem, eID = entityId, val = compW->Fog]() { renderSystem->SetFogSettings(eID, val); });
    }

    void FogSystemG::GetResourceComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource) {}

    void FogSystemG::UpdateDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

    FogSystemG::FogSystemG()
    {
        mFogSystemR = FogSystemR::CreateInstance();
    }

    FogSystemG* FogSystemG::CreateInstance()
    {
        return new FogSystemG();
    }

    void FogSystemG::Release()
    {
        delete this;
    }

    void FogSystemG::SetFogSetting(const FogCompWriter& comp, const FogSetting& val) const
    {
        comp->Fog = val;
        DispatchRenderingCommandWithToken([renderSystem = mFogSystemR, eID = comp.GetEntityID(), val]() { renderSystem->SetFogSettings(eID, val); });
    }
}