#include "EnginePrefix.h"
#include "Runtime/GameWorld/RenderPipelineG.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Material.h"

cross::RenderPipelineG::RenderPipelineG() {}

void cross::RenderPipelineG::SetCamera(ecs::EntityID camera)
{
    if (mCamera != camera)
    {
        mCamera = camera;
    }
}

void cross::RenderPipelineG::SetEnable(bool enable)
{
    if (mEnable != enable)
    {
        mEnable = enable;
    }
}