#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Utilities/ImguiwsConsole.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/WorldConst.h"
#include "Scripts/BaseLayer/ScriptMath.h"
#if SUPPORT_LUA
#include "ScriptEngine/src/utils/MessageQueue.h"
#endif
#include "Resource/ResourceManager.h"
#include "Runtime/Scripts/EngineLayer/ScriptWorld.h"
#include "Runtime/GameWorld/ScriptSystemG.h"

#include "Prefab/PrefabStreamingSystemG.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/Animation/Notify/AnimNotify_Script.h"
#include "Runtime/GameWorld/ScreenTerminalSystemG.h"
#include "Runtime/GameWorld/VolumeTriggerSystemG.h"

namespace cross {

ScriptSystemG* ScriptSystemG::CreateInstance()
{
    return new ScriptSystemG();
}

ScriptSystemG::ScriptSystemG()
{
    //
}
ScriptSystemG::~ScriptSystemG() {}

void ScriptSystemG::Release()
{
    delete this;
}
cross::SerializeNode ScriptSystemG::SerializeScriptComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode json;
    auto componetPtr = static_cast<cross::ScriptComponentG*>(componentPtr);

    json["ScriptPath"] = componetPtr->mScriptPath;
    json["EnableUpdate"] = componetPtr->mEnable;
    auto node = json["ScriptProperties"];
    for (size_t i = 0; i < componetPtr->mProperties.size(); i++)
    {
        auto propname = componetPtr->mProperties[i].mPropName;
        node[propname] = componetPtr->mProperties[i].Serialize(serializeWorld);
    }
    return json;
}

void ScriptSystemG::DeserializeScriptComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr) {}
void ScriptSystemG::GetResourceComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource)
{
    auto componetPtr = static_cast<cross::ScriptComponentG*>(componentPtr);
    resource->AddReferenceResource(componetPtr->mScriptPath);
}
void ScriptSystemG::PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
#if SUPPORT_LUA
    // deserialize script properties
    if (!json.IsNull())
    {
        auto script = static_cast<cross::ScriptComponentG*>(componentPtr);
        script->mScriptPath = json["ScriptPath"].AsString();
        if (json.HasMember("EnableUpdate"))
        {
            script->mEnable = json["EnableUpdate"].AsBoolean();
        }
        else
        {
            script->mEnable = true;
        }
        auto propsarray = json["ScriptProperties"];
        if (!propsarray.IsNull())
        {
            script->mProperties.clear();
            for (auto it = propsarray.begin(); it != propsarray.end(); ++it)
            {
                std::string propName = it.Key().data();
                auto propData = it.Value();
                cross::ScriptProperty scriptprop;
                scriptprop.Deserialize(propData, propName, gameWorld);
                script->mProperties.emplace_back(scriptprop);
                script->mPropertyMap[propName] = script->mProperties.size() - 1;
            }
        }
    }

    auto scriptsystem = gameWorld->GetGameSystem<cross::ScriptSystemG>();
    auto scriptcomp = static_cast<cross::ScriptComponentG*>(componentPtr);
    if (!scriptcomp)
        return;
    auto scope = ScriptModule::Instance().Enter();
    try
    {
        auto [scriptRes, scriptInstance] = scriptsystem->LoadScriptResource(scriptcomp->mScriptPath);

        // create instance of this.Entity
        auto currentEngine = script::EngineScope::currentEngine();
        auto entityObject = currentEngine->newNativeClass<scripts::Entity>(scriptsystem->mGameworldInstance.get());
        Assert(currentEngine->isInstanceOf<scripts::Entity>(entityObject));
        auto nativeEntityObject = currentEngine->getNativeInstance<scripts::Entity>(entityObject);
        nativeEntityObject->EntityID = entityId;

        scriptsystem->mScriptEventQueue.EmplaceBack(entityId, ScriptEventType::Create);

        // assign the this.Entity property for the Entity Script Object
        if (scriptInstance.has("Entity"))
        {
            LOG_WARN("Entity member is reserved for ECS.Entity wrapper.");
        }
        if (scriptInstance.has("Level"))
        {
            LOG_WARN("Level member is reserved for ECS.World wrapper.");
        }
#ifndef SCRIPT_ENGINE_BACKEND_LUA
        auto entityObjectProxy = ScriptModule::Instance().CreateEntityProxy(entityObject).asObject();
        scriptInstance.set("Entity", entityObjectProxy);
#else
        scriptInstance.set("Entity", entityObject);
#endif
        scriptcomp->mScriptResource = scriptRes;
        scriptcomp->mScriptInstance = std::make_shared<script::Global<script::Object>>(scriptInstance);

        auto handle = gameWorld->GetComponent<ScriptComponentG>(entityId);
        scriptsystem->ReflectScriptMember(handle.Write());

        // Call world script OnEntityScriptLoaded function:
        if (!scriptsystem->mWorldScriptInstance.isEmpty())
        {
            script::Local<script::Object> worldScriptInstance = scriptsystem->mWorldScriptInstance.get();
            script::Local<script::Value> onEntityScriptLoadedFunc = worldScriptInstance.get("OnEntityScriptLoaded");
            if (onEntityScriptLoadedFunc.isFunction())
            {
                onEntityScriptLoadedFunc.asFunction().call(worldScriptInstance, scriptInstance);
            }
        }
    }
    catch (script::Exception const& e)
    {
        LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
    }
    catch (std::exception const& e)
    {
        LOG_ERROR("{}", e.what());
    }
    catch (...)
    {
        LOG_ERROR("Lua other exception:{}", json["ScriptPath"].AsString());
    }
#endif
}

void ScriptSystemG::UpdateDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    PostDeserializeComponent(json, componentPtr, gameWorld, entityId);
}
bool ScriptSystemG::ReflectScriptMember(const ScriptComponentWriter& script)
{
#if SUPPORT_LUA
    SizeType propertyCount = 0;

    auto& scriptResource = script->mScriptResource;
    if (!scriptResource)
        return false;

    propertyCount = scriptResource->GetPropeties().size();
    std::vector<ScriptProperty> oldProperties = script->mProperties;
    CEHashMap<std::string, UInt32> oldPropertyMap = script->mPropertyMap;
    script->mProperties = scriptResource->GetPropeties();

    script->mPropertyMap.clear();
    int index = 0;
    for (const auto& prop : script->mProperties)
    {
        script->mPropertyMap[prop.mPropName] = index++;
    }

    // the old may contains user-adjusted data
    for (const auto& old : oldPropertyMap)
    {
        auto iter = script->mPropertyMap.find(old.first);
        if (iter != script->mPropertyMap.end())
        {
            auto& oldProp = oldProperties[old.second];
            auto& curProp = script->mProperties[iter->second];
            if (oldProp.GetDataType() == curProp.GetDataType())
            {
                curProp.mPropData = oldProp.mPropData;
            }
        }
    }
#endif
    return true;
}
const std::string& ScriptSystemG::GetScriptPath(const ScriptComponentReader& script)
{
    return script->mScriptPath;
}
const std::string& ScriptSystemG::GetMemberName(const ScriptComponentReader& script, UInt32 index)
{
    return script->mProperties[index].mPropName;
}

const bool ScriptSystemG::GetMemberIndex(const ScriptComponentReader& script, std::string name, UInt32& index)
{
#if SUPPORT_LUA
    auto itr = script->mPropertyMap.find(name);
    if (itr != script->mPropertyMap.end())
    {
        index = itr->second;
        return true;
    }
#endif
    return false;
}

const UInt32 ScriptSystemG::GetMemberCount(const ScriptComponentReader& script)
{
#if SUPPORT_LUA
    return script->mProperties.size();
#endif
    return 0;
}

const int ScriptSystemG::GetMemberType(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;

    if (GetMemberIndex(script, name, index))
    {
        auto data = script->mProperties[index];
        return static_cast<int>(data.GetDataType());
    }
#endif
    return static_cast<int>(0);
}

bool ScriptSystemG::SetScriptPath(const ScriptComponentWriter& handle, const std::string& scriptPath)
{
#if SUPPORT_LUA
    {
        auto scope = ScriptModule::Instance().Enter();
        try
        {
            auto [scriptRes, scriptInstance] = LoadScriptResource(scriptPath);

            // create instance of this.Entity
            auto currentEngine = script::EngineScope::currentEngine();
            auto entityObject = currentEngine->newNativeClass<scripts::Entity>(mGameworldInstance.get());
            Assert(currentEngine->isInstanceOf<scripts::Entity>(entityObject));
            auto nativeEntityObject = currentEngine->getNativeInstance<scripts::Entity>(entityObject);
            nativeEntityObject->EntityID = handle.GetEntityID();

            // assign the this.Entity property for the Entity Script Object
            if (scriptInstance.has("Entity"))
            {
                LOG_WARN("Entity member is reserved for ECS.Entity wrapper.");
            }
            if (scriptInstance.has("Level"))
            {
                LOG_WARN("Level member is reserved for ECS.World wrapper.");
            }

#ifndef SCRIPT_ENGINE_BACKEND_LUA
            auto entityObjectProxy = ScriptModule::Instance().CreateEntityProxy(entityObject).asObject();
            scriptInstance.set("Entity", entityObjectProxy);
#else
            scriptInstance.set("Entity", entityObject);
#endif

            mScriptEventQueue.EmplaceBack(handle.GetEntityID(), ScriptEventType::Create);

            handle->mScriptPath = scriptPath;
            handle->mScriptResource = scriptRes;
            handle->mScriptInstance = std::make_shared<script::Global<script::Object>>(scriptInstance);

            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
                ReflectScriptMember(handle);

            // Call world script OnEntityScriptLoaded function:
            if (!mWorldScriptInstance.isEmpty())
            {
                script::Local<script::Object> worldScriptInstance = mWorldScriptInstance.get();
                script::Local<script::Value> onEntityScriptLoadedFunc = worldScriptInstance.get("OnEntityScriptLoaded");
                if (onEntityScriptLoadedFunc.isFunction())
                {
                    onEntityScriptLoadedFunc.asFunction().call(worldScriptInstance, scriptInstance);
                }
            }
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", scriptPath);
        }
    }
#endif
    return true;
}

void ScriptSystemG::SetScriptEnable(const ScriptComponentWriter& handle, bool bEnable)
{
    handle->mEnable = bEnable;
}
const bool ScriptSystemG::GetBoolMember(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(script, name, index))
    {
        auto scope = ScriptModule::Instance().Enter();
        bool result;
        GetPropertyByType<bool>(script, index, result);
        return result;
    }
#endif
    return false;
}

const double ScriptSystemG::GetNumberMember(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(script, name, index))
    {
        auto scope = ScriptModule::Instance().Enter();
        double result;
        GetPropertyByType<double>(script, index, result);
        return result;
    }
#endif
    return 0.0;
}

const Float3 ScriptSystemG::GetVector3Member(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(script, name, index))
    {
        auto scope = ScriptModule::Instance().Enter();
        Float3 result;
        GetPropertyByType<Float3>(script, index, result);
        return result;
    }
#endif
    return Float3{0.f, 0.f, 0.f};
}

const Float2 ScriptSystemG::GetVector2Member(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(script, name, index))
    {
        auto scope = ScriptModule::Instance().Enter();
        Float2 result;
        GetPropertyByType<Float2>(script, index, result);
        return result;
    }
#endif
    return Float2{0.f, 0.f};
}

const std::string ScriptSystemG::GetStringMember(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(script, name, index))
    {
        auto scope = ScriptModule::Instance().Enter();
        std::string result;
        GetPropertyByType<std::string>(script, index, result);
        return result;
    }
#endif
    return "";
}

const ecs::EntityID ScriptSystemG::GetEntityMember(const ScriptComponentReader& script, std::string name)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(script, name, index))
    {
        auto scope = ScriptModule::Instance().Enter();
        ecs::EntityID result;
        GetPropertyByType<ecs::EntityID>(script, index, result);
        return result;
    }
#endif
    return ecs::EntityID::InvalidHandle();
}

void ScriptSystemG::SetBoolMember(const ScriptComponentWriter& script, std::string name, bool value)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(ecs::GrantReadAccess(script), name, index))
    {
        try
        {
            auto scope = ScriptModule::Instance().Enter();
            script->mProperties[index].mPropData = value;
            script->mScriptInstance.get()->getValue().asObject().set(name, value);
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", GetScriptPath(ecs::GrantReadAccess(script)));
        }
    }
#endif
}

void ScriptSystemG::SetNumberMember(const ScriptComponentWriter& script, std::string name, double value)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(ecs::GrantReadAccess(script), name, index))
    {
        try
        {
            auto scope = ScriptModule::Instance().Enter();
            script->mProperties[index].mPropData = value;
            script->mScriptInstance.get()->getValue().asObject().set(name, value);
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", GetScriptPath(ecs::GrantReadAccess(script)));
        }
    }
#endif
}

void ScriptSystemG::SetVector3Member(const ScriptComponentWriter& script, std::string name, Float3 value)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(ecs::GrantReadAccess(script), name, index))
    {
        try
        {
            auto scope = ScriptModule::Instance().Enter();
            script->mProperties[index].mPropData = value;

            script::Local<script::Value> x = script::Number::newNumber(value.x);
            script::Local<script::Value> y = script::Number::newNumber(value.y);
            script::Local<script::Value> z = script::Number::newNumber(value.z);

            auto engine = script::EngineScope::currentEngine();
            auto vector = engine->newNativeClass<scripts::Vector3>(x, y, z).asValue();
            script->mScriptInstance.get()->getValue().asObject().set(name, vector);
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", GetScriptPath(ecs::GrantReadAccess(script)));
        }
    }
#endif
}

void ScriptSystemG::SetVector2Member(const ScriptComponentWriter& script, std::string name, Float2 value)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(ecs::GrantReadAccess(script), name, index))
    {
        try
        {
            auto scope = ScriptModule::Instance().Enter();
            script::Local<script::Value> x = script::Number::newNumber(value.x);
            script::Local<script::Value> y = script::Number::newNumber(value.y);

            auto engine = script::EngineScope::currentEngine();
            auto vector = engine->newNativeClass<scripts::Vector2>(x, y).asValue();
            script->mScriptInstance.get()->getValue().asObject().set(name, vector);
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", GetScriptPath(ecs::GrantReadAccess(script)));
        }
    }
#endif
}

void ScriptSystemG::SetStringMember(const ScriptComponentWriter& script, std::string name, std::string value)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(ecs::GrantReadAccess(script), name, index))
    {
        try
        {
            auto scope = ScriptModule::Instance().Enter();
            script->mProperties[index].mPropData = value;
            script->mScriptInstance.get()->getValue().asObject().set(name, value);
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", GetScriptPath(ecs::GrantReadAccess(script)));
        }
    }
#endif
}

void ScriptSystemG::SetEntityMember(const ScriptComponentWriter& script, std::string name, ecs::EntityID value)
{
#if SUPPORT_LUA
    UInt32 index = 0;
    if (GetMemberIndex(ecs::GrantReadAccess(script), name, index))
    {
        try
        {
            auto scope = ScriptModule::Instance().Enter();
            auto currentEngine = script::EngineScope::currentEngine();
            auto entityObject = currentEngine->newNativeClass<scripts::Entity>(mGameworldInstance.get());
            auto nativeEntityObject = currentEngine->getNativeInstance<scripts::Entity>(entityObject);
            nativeEntityObject->EntityID = value;
#ifndef SCRIPT_ENGINE_BACKEND_LUA
            auto entityObjectProxy = ScriptModule::Instance().CreateEntityProxy(entityObject).asObject();
            script->mScriptInstance.get()->getValue().asObject().set(name, entityObjectProxy);
#else
            script->mScriptInstance.get()->getValue().asObject().set(name, entityObject);
#endif
            script->mProperties[index].mPropData = value;
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("Lua other exception:{}", GetScriptPath(ecs::GrantReadAccess(script)));
        }
    }
#endif
}
#if SUPPORT_LUA
void ScriptSystemG::OnEndFrame(FrameParam* frameParam)
{
    ClearTick();
}

void ScriptSystemG::OnBuildPreUpdateTasks(FrameParam* frameParam)
{
    auto elapsedTime = frameParam->GetDeltaTime();
    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::PreUpdate, {}, [this, elapsedTime] {
        // Console Lua
        auto console = EngineGlobal::Inst().GetEngine()->GetGlobalSystem<ImguiwsConsole>();
        if (console->GetCurrentScript().size())
        {
            try
            {
                ScriptModule::Instance().Eval(console->GetCurrentScript(), "");
            }
            catch (script::Exception const& e)
            {
                LOG_ERROR("ScriptEngine Lua Exception:{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("std expection:{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("Script Update Expection;");
            }
            console->GetCurrentScript().clear();
        }
        SCOPED_CPU_TIMING(GroupScript, "ScriptPreUpdate");

#if CROSSENGINE_EDITOR
        // Update Lua Resource if out-of-date
        // 脚本异常的时候，会导致资源丢失
        auto scriptEntities = mGameWorld->Query<ScriptComponentG>();
        for (const auto& scriptHandle : scriptEntities)
        {
            if (scriptHandle.Read()->mScriptResource && scriptHandle.Read()->mScriptResource->ReleaseDirty())
            {
                SetScriptPath(scriptHandle.Write(), scriptHandle.Read()->mScriptPath);
            }
        }
#endif   //  CROSSENGINE_EDITOR

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            auto scriptEntities = mGameWorld->Query<ScriptComponentG>();
            for (const auto& scriptHandle : scriptEntities)
            {
                if (scriptHandle.Read()->mOnCreateFinished)
                    OnEntityEvent(scriptHandle, ScriptEventType::PreUpdate, elapsedTime); 
            }
        }
    });
}

void ScriptSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    auto elapsedTime = frameParam->GetDeltaTime();

    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::Update, {}, [this, elapsedTime] {          
        SCOPED_CPU_TIMING(GroupScript, "ScriptUpdate");
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            auto scriptEntities = mGameWorld->Query<ScriptComponentG>();
            bool hasScriptEntity = scriptEntities.GetEntityNum() > 0;

            if (hasScriptEntity)
            {
                auto scope = ScriptModule::Instance().Enter();
                try
                {
                    if (!mWorldScriptInstance.isEmpty())
                    {
                        SCOPED_CPU_TIMING(GroupScript, "WorldScriptUpdate");
                        script::Local<script::Object> worldScriptInstance = mWorldScriptInstance.get();
                        script::Local<script::Value> onUpdateFunc = worldScriptInstance.get("OnUpdate");
                        if (onUpdateFunc.isFunction())
                        {
                            onUpdateFunc.asFunction().call(worldScriptInstance, elapsedTime);
                        }
                    }
                }
                catch (script::Exception const& e)
                {
                    LOG_ERROR("ScriptEngine Lua Exception:{}\n{}", e.message(), e.stacktrace());
                }
                catch (std::exception const& e)
                {
                    LOG_ERROR("std expection:{}", e.what());
                }
                catch (...)
                {
                    LOG_ERROR("Script Update Expection;");
                }
            }

            if (hasScriptEntity)
            {
                SCOPED_CPU_TIMING(GroupScript, "EntityScriptUpdate");
                for (const auto& scriptHandle : scriptEntities)
                {
                    if (scriptHandle.Read()->mEnable)
                    {
                        SCOPED_CPU_TIMING(GroupScript, "ScriptUpdate");
                        if (scriptHandle.Read()->mOnCreateFinished)
                        {
                            OnEntityEvent(scriptHandle, ScriptEventType::Update, elapsedTime);
                        }
                        // update canvas message event
                        auto canvashandle = mGameWorld->GetComponent<CanvasComponentG>(scriptHandle.GetEntityID());
                        auto writer = scriptHandle.Write();
                        if (canvashandle.IsValid())
                        {
                            auto canvaswriter = canvashandle.Write();

                            if (canvaswriter->mCanvas.mMessages.size() > 0)
                            {
                                SCOPED_CPU_TIMING(GroupScript, "CanvasMessageUpdate");
                                for (const auto& message : canvaswriter->mCanvas.mMessages)
                                {
                                    OnEntityEvent(scriptHandle, ScriptEventType::Message, message);
                                }
                                canvaswriter->mCanvas.mMessages.clear();
                            }
                        }
                        if (writer->mNetworkMessages.size() > 0)
                        {
                            SCOPED_CPU_TIMING(GroupScript, "NetWorkMessageUpdate");
                            for (const auto& message : writer->mNetworkMessages)
                            {
                                OnEntityEvent(scriptHandle, ScriptEventType::NetworkMessage, message);
                            }
                            writer->mNetworkMessages.clear();
                        }
                    }
                }
            }

#ifdef SCRIPT_ENGINE_BACKEND_LUA
            if (hasScriptEntity)
            {
                SCOPED_CPU_TIMING(GroupScript, "ScriptUpdate_GC");
                ScriptModule::Instance().GC();
            }
#endif
        }
    });
}

void ScriptSystemG::OnBuildPostUpdateTasks(FrameParam* frameParam)
{
    auto elapsedTime = frameParam->GetDeltaTime();
    auto taskNotifyCallback = CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::PostUpdate, {}, [this, elapsedTime] {
        SCOPED_CPU_TIMING(GroupScript, "ScriptNotifyCallback");

        auto animatorSystemG = mGameWorld->GetGameSystem<AnimatorSystemG>();

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            auto scriptenties = mGameWorld->Query<ScriptComponentG, AnimatorComponentG>();
            for (const auto& entity : scriptenties)
            {
                const auto& scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(std::get<0>(entity).GetEntityID());
                if (scriptHandle.Read()->mOnCreateFinished)
                {
                    const auto& animatorHandle = mGameWorld->GetComponent<AnimatorComponentG>(std::get<0>(entity).GetEntityID());
                    auto& animator = animatorSystemG->GetAnimator(animatorHandle.Read());
                    auto& animatorNotifyQueue = animator.GetNotifyQueue();

                    std::vector<AnimNotifyEventReference> notifiesForScript;
                    animatorNotifyQueue.FilteringNotifiesFromType<AnimNotifyScript>(notifiesForScript, [this, &animator, &scriptHandle](AnimNotifyScript const* castedNotify, AnimNotifyEventReference& ref) { ref.SetOwner(&animator); });

                    for (auto& notifyRef : notifiesForScript)
                        notifyRef.Broadcast();
                }
            }
        }
    });

    auto postUpdateProcessor = CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::PostUpdate, {taskNotifyCallback}, [this, elapsedTime] {
        SCOPED_CPU_TIMING(GroupScript, "ScriptPostUpdate");

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            auto scriptEntities = mGameWorld->Query<ScriptComponentG>();
            for (const auto scriptHandle : scriptEntities)
            {
                if (scriptHandle.Read()->mOnCreateFinished)
                    OnEntityEvent(scriptHandle, ScriptEventType::PostUpdate, elapsedTime);
            }
        }
    });

    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::PostUpdate, {postUpdateProcessor}, [this, elapsedTime] {
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            DispatchScriptEvent();
            mScriptEventQueue.Clear();
        }
    });
}

void ScriptSystemG::ClearTick()
{
    auto engine = ScriptModule::Instance().GetScriptEnginePtr();
    // use for deconstruction the script binding object
    engine->messageQueue()->loopQueue(script::utils::MessageQueue::LoopType::kLoopOnce);
}

auto ScriptSystemG::LoadScriptResource(std::string const& scriptPath) -> std::pair<ScriptPtr, script::Local<script::Object>>
{
    // in case of null script when script comp was created in editor.
    if (scriptPath.empty())
    {
        return {(ScriptPtr) nullptr, script::Object::newObject()};
    }

    auto resource = gAssetStreamingManager->GetResource(scriptPath.c_str());
    if (!resource)
    {
        // LOG_WARN("Invalid Script Resoruce: {}.", scriptPath);
        return {(ScriptPtr) nullptr, script::Object::newObject()};
    }

    Assert(resource->GetClassID() == ClassID(ScriptResource));
    auto scriptResrouce = TypeCast<resource::ScriptResource>(resource);
    Assert(scriptResrouce);

    auto const classType = scriptResrouce->GetClass();
    if (!classType.isObject())
    {
        LOG_WARN("Internal Script Error:{} - Invalid Script Class.", scriptPath);
        return {(ScriptPtr) nullptr, script::Object::newObject()};
    }
#ifndef SCRIPT_ENGINE_BACKEND_LUA
    auto instance = script::Object::newObject(classType);
    if (!instance.instanceOf(classType))
    {
        LOG_WARN("Internal Script Error:{} - New a wrong type of instance.", scriptPath);
        return {(ScriptPtr) nullptr, script::Object::newObject()};
    }

    return {scriptResrouce, instance};
#else
    script::Local<script::Value> newfunc = scriptResrouce->GetClass().asObject().get("new");
    auto scriptinstance = newfunc.asFunction().call(scriptResrouce->GetClass().asObject());
    return {scriptResrouce, scriptinstance.asObject()};
#endif
}

bool ScriptSystemG::LoadWorldScript(const std::string& path)
{
    if (path.empty())
    {
        return false;
    }

    auto scope = ScriptModule::Instance().Enter();
    try
    {
        auto [worldScriptRes, worldScriptInstance] = LoadScriptResource(path);

        script::Local<script::Value> onCreateFunc = worldScriptInstance.get("OnCreate");
        if (onCreateFunc.isFunction())
        {
            onCreateFunc.asFunction().call(worldScriptInstance);
        }

        mWorldScriptPath = path;
        mWorldScriptRes = worldScriptRes;
        mWorldScriptInstance = worldScriptInstance;
    }
    catch (script::Exception const& e)
    {
        LOG_ERROR("ScriptEngine Lua Exception: {}\n{}", e.message(), e.stacktrace());
    }
    catch (std::exception const& e)
    {
        LOG_ERROR("std expection: {}", e.what());
    }
    catch (...)
    {
        LOG_ERROR("Lua other exception:{}", path);
    }

    return true;
}

bool ScriptSystemG::UnloadScript(const ScriptComponentWriter& handle)
{
    auto entity = handle.GetEntityID();
    auto script = mGameWorld->GetComponent<ScriptComponentG>(entity);
    {
        auto scope = ScriptModule::Instance().Enter();
        try
        {
            script.Write()->mScriptPath = "";
            script.Write()->mScriptResource = {};
            script.Write()->mScriptInstance = std::make_shared<script::Global<script::Object>>(script::Object::newObject());
            script.Write()->mProperties.clear();
            mScriptEventQueue.EmplaceBack(entity, ScriptEventType::Create);
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
    }
    return true;
}

script::Local<script::Object> ScriptSystemG::GetScriptInstance(const ScriptComponentReader& script)
{
    return script->mScriptInstance->get();
}

void ScriptSystemG::CreateGameworldScriptInstance()
{
    auto scope = ScriptModule::Instance().Enter();
    try
    {
        // create instance of this.Level
        auto currentEngine = script::EngineScope::currentEngine();
        auto worldObject = currentEngine->newNativeClass<scripts::World>({});
        Assert(currentEngine->isInstanceOf<scripts::World>(worldObject));

        // assign current world
        auto nativeWorldObject = currentEngine->getNativeInstance<scripts::World>(worldObject);
        nativeWorldObject->GameWorld = mGameWorld;

        nativeWorldObject->NearestZombieEntity = script::EngineScope::currentEngine()->newNativeClass<scripts::Entity>(worldObject).asValue();
        nativeWorldObject->CannonAimingPosition = script::EngineScope::currentEngine()->newNativeClass<scripts::Vector3>().asValue();

        // assign current engine
        auto engineObject = currentEngine->newNativeClass<scripts::Engine>({});
        auto nativeEngine = currentEngine->getNativeInstance<scripts::Engine>(engineObject);
        worldObject.set("Engine", engineObject);

        // assign input manager
        auto inputManager = currentEngine->newNativeClass<scripts::InputManager>(worldObject);
        auto nativeInputManager = currentEngine->getNativeInstance<scripts::InputManager>(inputManager);
        nativeInputManager->Engine = EngineGlobal::GetEngine();
        worldObject.set("Input", inputManager);

        // auto inputProxy = ScriptModule::Instance().CreateInputProxy(inputManager.asValue());

        auto rootID = mGameWorld->GetGameSystem<cross::TransformSystemG>()->GetRootEntity();

        auto root = currentEngine->newNativeClass<scripts::Entity>(worldObject);
        auto nativeroot = currentEngine->getNativeInstance<scripts::Entity>(root);
        nativeroot->EntityID = rootID;
        worldObject.set("Root", root);

        // assign instance object
        mGameworldInstance = worldObject;
        mInputInstance = inputManager;

        auto scriptEngine = ScriptModule::Instance().GetEngine();

        auto ce = scriptEngine->get("ce");
        Assert(ce.isObject());
        ce.asObject().set("Level", worldObject);
        ce.asObject().set("Input", inputManager);
        ce.asObject().set("MathUtils", currentEngine->newNativeClass<scripts::MathUtils>({}));
    }
    catch (script::Exception const& e)
    {
        LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
    }
    catch (std::exception const& e)
    {
        LOG_ERROR("{}", e.what());
    }
}





void ScriptSystemG::DispatchScriptEvent()
{
    UInt32 flag = 0;
    for (auto i = mScriptEventQueue.Begin(); i < mScriptEventQueue.End(); i++)
    {
        const auto& eventData = mScriptEventQueue.GetData(i);
        NotifyEvent(eventData, flag);
    }
}

void ScriptSystemG::NotifyScriptEvent(const ecs::EntityID entity, ScriptEventType type)
{
    mScriptEventQueue.EmplaceBack(entity, type);
}

void ScriptSystemG::SetupProp(script::Global<script::Object>& gameworldInstance, const ScriptComponentReader& scriptCompReader)
{
    try
    {
        auto scope = ScriptModule::Instance().Enter();
        auto scriptObj = scriptCompReader->mScriptInstance.get()->getValue().asObject();
        auto currentEngine = script::EngineScope::currentEngine();

        for (const auto& prop : scriptCompReader->mProperties)
        {
            const auto& propName = prop.mPropName;
            const auto& propData = prop.mPropData;
            switch (propData.index())
            {
            case 0:
                scriptObj.set(propName, *std::get_if<0>(&propData));
                break;
            case 1:
                scriptObj.set(propName, *std::get_if<1>(&propData));
                break;
            case 2:
                scriptObj.set(propName, *std::get_if<2>(&propData));
                break;
            case 3:
            {
                auto vec2 = currentEngine->newNativeClass<scripts::Vector2>(std::get_if<3>(&propData)->x, std::get_if<3>(&propData)->y).asValue();
                scriptObj.set(propName, vec2);
            }
            break;
            case 4:
            {
                auto vec3 = currentEngine->newNativeClass<scripts::Vector3>(std::get_if<4>(&propData)->x, std::get_if<4>(&propData)->y, std::get_if<4>(&propData)->z).asValue();
                scriptObj.set(propName, vec3);
            }
            break;
            case 5:
            {
                auto entityObject = currentEngine->newNativeClass<scripts::Entity>(gameworldInstance.get());
                auto nativeEntityObject = currentEngine->getNativeInstance<scripts::Entity>(entityObject);
                nativeEntityObject->EntityID = *std::get_if<5>(&propData);
#ifndef SCRIPT_ENGINE_BACKEND_LUA
                auto entityObjectProxy = ScriptModule::Instance().CreateEntityProxy(entityObject).asObject();
                scriptObj.set(propName, entityObjectProxy);
#else
                scriptObj.set(propName, entityObject);
#endif
            }
            break;
            default:
                break;
            }
        }
    }
    catch (script::Exception const& e)
    {
        LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
    }
    catch (std::exception const& e)
    {
        LOG_ERROR("{}", e.what());
    }
    catch (...)
    {
        LOG_ERROR("Lua other exception:{}", scriptCompReader->mScriptPath);
    }
}

void ScriptSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);
    // Script Event
    if (event.mEventType == ScriptEvent::sEventType)
    {
        const ScriptEvent* e = TYPE_CAST(const ScriptEvent*, &event);

        try
        {
            const auto& scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(e->mData.mScriptEntity);
            if (e->mData.mEventType == ScriptEventType::Create)
            {
                if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
                {
                    SetupProp(mGameworldInstance, scriptHandle.Read());
                }

                OnEntityEvent(scriptHandle, ScriptEventType::Create);
                const auto& scriptHandleAfterModfity = mGameWorld->GetComponent<ScriptComponentG>(e->mData.mScriptEntity);
                if (scriptHandleAfterModfity.IsValid())
                    scriptHandleAfterModfity.Write()->mOnCreateFinished = true;
            }
            else if (e->mData.mEventType == ScriptEventType::Destroy)
            {
                OnEntityEvent(scriptHandle, ScriptEventType::Destroy);
                mGameWorld->DestroyEntity(scriptHandle.GetEntityID());
            }
            else
            {
                OnEntityEvent(scriptHandle, e->mData.mEventType);
            }
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("{}", e.what());
        }
        catch (...)
        {
            auto scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(e->mData.mScriptEntity);
            LOG_ERROR("Lua other exception:{}", GetScriptPath(scriptHandle.Read()));
        }
    }

    if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType) 
    {
        auto triggerVolumeSys = mGameWorld->GetGameSystem<VolumeTriggerSystemG>();
        if (triggerVolumeSys) 
        {
            triggerVolumeSys->SubscribeEvent<OverlapChangeEvent>(this, 0);
        }
        auto worldLoaidngSys = mGameWorld->GetGameSystem<WorldLoadingSystemG>();
        if (worldLoaidngSys)
        {
            worldLoaidngSys->SubscribeEvent<WorldChangedEvent>(this, 0);
        }
        auto prefabStreaming = mGameWorld->GetGameSystem<PrefabStreamingSystemG>();
        if (prefabStreaming)
        {
            prefabStreaming->SubscribeEvent<WorldChangedEvent>(this, 0);
        }

        mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
    else if (event.mEventType == OverlapChangeEvent::sEventType)
    {
        const OverlapChangeEvent* overlapEvent = TYPE_CAST(const OverlapChangeEvent*, &event);
        auto scriptComp = mGameWorld->GetComponent<ScriptComponentG>(overlapEvent->mData.mEntity);
        if (scriptComp) 
        {
            if (overlapEvent->mData.mType == OverlapType::Begin) 
            {
                OnEntityOverlapEvent(scriptComp, ScriptEventType::BeginOverlap, overlapEvent->mData.mTriggerVolume);
            }
            else
            {
                OnEntityOverlapEvent(scriptComp, ScriptEventType::EndOverlap, overlapEvent->mData.mTriggerVolume);
            }
        }
    }
    else if (event.mEventType == WorldChangedEvent::sEventType)
    {
        const WorldChangedEvent* loadingevent = TYPE_CAST(const WorldChangedEvent*, &event);
        if (loadingevent->mData.mWorldLoadingStatus == WorldLoadingStatus::Loading)
        {
            mSystemState = ScriptSystemState::Loading;
        }
        else if (loadingevent->mData.mWorldLoadingStatus == WorldLoadingStatus::Loaded)
        {
            mSystemState = ScriptSystemState::Play;
        }
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent* e = dynamic_cast<RemainedEventUpdatedEvent const*>(&event);
        if (e->mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            for (UInt32 eventIndex = e->mData.mFirstIndex; eventIndex <= e->mData.mLastIndex; ++eventIndex)
            {
                const EntityDestroyEvent& entityLifeCycleEvent = mGameWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);

                if ((entityLifeCycleEvent.mData.mEventFlag & EntityLifeCycleEventFlag::DestroyEntity) != EntityLifeCycleEventFlag::None)
                {
                    auto scriptComp = mGameWorld->GetComponent<ScriptComponentG>(entityLifeCycleEvent.mData.mEntityID);
                    if (scriptComp.IsValid())
                    {
                        OnEntityEvent(scriptComp, ScriptEventType::Destroy);
                    }
                }
            }
        }
    }
}



std::string ScriptSystemG::GetMessage(const ScriptComponentReader& script)
{
    try
    {
        auto scope = ScriptModule::Instance().Enter();
        if (script->mScriptInstance)
        {
            auto object = script->mScriptInstance->get();

            script::Local<script::Value> func = object.get("GetMessage");
            if (func.isFunction())
            {
                auto ret = func.asFunction().call(object);
                if (ret.isString())
                {
                    return ret.asString().toString();
                }
                else
                {
                    LOG_WARN("Script [{}]: Script Function \"GetMessage\" except return a string.", GetScriptPath(script));
                }
            }
            else
            {
                LOG_WARN("Script [{}]: Script Event Function \"GetMessage\" not implemented or mismatches, it's maybe a spelling error.", GetScriptPath(script));
            }
        }
        else
        {
            LOG_ERROR("Script component of entity {} doesn't be set a script.", script.GetEntityID());
        }
    }
    catch (script::Exception const& e)
    {
        LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
    }
    catch (std::exception const& e)
    {
        LOG_ERROR("std exception:{}", e.what());
    }
    catch (...)
    {
        LOG_ERROR("lua other exception:{}", GetScriptPath(script));
    }
    return "";
}

void ScriptSystemG::CallCunstomFunction(const ScriptComponentReader& script, const std::string& functionName)
{
    auto scope = ScriptModule::Instance().Enter();
    try
    {
        if (script->mScriptInstance)
        {
            auto object = script->mScriptInstance->get();
            script::Local<script::Value> func = object.get(functionName);
            if (func.isFunction())
            {
                func.asFunction().call(object);
            }
            // else
            // {
            //     LOG_WARN("Script [{}]: Can not find function \"{}\" in {}.", functionName, GetScriptPath(script));
            // }
        }
    }
    catch (script::Exception const& e)
    {
        LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
    }
    catch (std::exception const& e)
    {
        LOG_ERROR("std exception:{}", e.what());
    }
    catch (...)
    {
        LOG_ERROR("lua other exception:{}", GetScriptPath(script));
    }
}
#endif
}   // namespace cross
