#pragma once
#include "Runtime/GameWorld/MeshBuilderBaseG.h"
#include "RenderEngine/RenderWorldConst.h"

namespace cross
{

class SkinnedMeshBuilderR;

class SkinnedMeshBuilderG : public MeshBuilderBaseG
{
public:
	SkinnedMeshBuilderG();

	~SkinnedMeshBuilderG();

	virtual MeshBatchFlag GetBatchFlag() const override { return (MeshBatchFlag)(MESH_BATCH_SKIN | MESH_BATCH_DO_NOT_MERGE | MESH_BATCH_INSTANCING); }

protected:
	virtual MeshBuilderBaseR* GetRenderObject() override;

	virtual void NotifyAddRenderObjectToRenderWorld() override { mOwnRenderObject = false; }

	SkinnedMeshBuilderR* mRenderObject{ nullptr };

	bool mOwnRenderObject{ true };
};

}
