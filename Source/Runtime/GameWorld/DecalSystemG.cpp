#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/DecalSystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"

#include "Runtime/GameWorld/TransformSystemG.h"
#include "Threading/RenderingThread.h"
#include "RenderEngine/DecalSystemR.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Shader.h"
#include "Resource/Material.h"

using namespace cross::skeleton;

namespace cross {

ecs::ComponentDesc* DecalComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::DecalComponentG>(
        { false, true, true }, &DecalSystemG::SerializeDecalComponent, &DecalSystemG::DeserializeDecalComponent, &DecalSystemG::PostDeserializeDecalComponent);
}

DecalSystemG* DecalSystemG::CreateInstance()
{
    return new DecalSystemG();
}

RenderSystemBase* DecalSystemG::GetRenderSystem()
{
    return mRenderObject;
}

DecalSystemG::DecalSystemG()
{
    mRenderObject = DecalSystemR::CreateInstance();
}

DecalSystemG::~DecalSystemG()
{
    if (mIsRenderObjectOwner && mRenderObject)
    {
        mRenderObject->Release();
        mRenderObject = nullptr;
    }
}

static void ComputeDecalToWorld(const TRS_A& inDecalToWorldWithoutScale, const Float3& inDecalSize, Float4x4& outDecalToWorld, Float3& outTile)
{
    TRS_A DecalToWorldWithDecalSize = inDecalToWorldWithoutScale;
    DecalToWorldWithDecalSize.mScale *= static_cast<TRSVector3Type>(inDecalSize);

    // Avoid negative and very small scale
    DecalToWorldWithDecalSize.mScale = DecalToWorldWithDecalSize.mScale.Abs();
    DecalToWorldWithDecalSize.mScale.x = MathUtils::Max(DecalToWorldWithDecalSize.mScale.x, static_cast<TRSScalarType>(MathUtils::MathSmallNumber));
    DecalToWorldWithDecalSize.mScale.y = MathUtils::Max(DecalToWorldWithDecalSize.mScale.y, static_cast<TRSScalarType>(MathUtils::MathSmallNumber));
    DecalToWorldWithDecalSize.mScale.z = MathUtils::Max(DecalToWorldWithDecalSize.mScale.z, static_cast<TRSScalarType>(MathUtils::MathSmallNumber));

    Float3 Translation = static_cast<Float3>(DecalToWorldWithDecalSize.mTranslation);
    Float3 Scale = static_cast<Float3>(DecalToWorldWithDecalSize.mScale);
    Quaternion Rotation = static_cast<Quaternion>(DecalToWorldWithDecalSize.mRotation);

    outTile = {0, 0, 0};
#ifdef CE_USE_DOUBLE_TRANSFORM
    GetTileAndOffsetForAbsolutePosition(DecalToWorldWithDecalSize.mTranslation, outTile, Translation);
#endif

    outDecalToWorld = Float4x4::Compose(Scale, Rotation, Translation);
}

void DecalSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);
    if (event.mEventType == TRSChangedEvent::sEventType)
    {
        const TRSChangedEvent& e = TYPE_CAST(const TRSChangedEvent&, event);
        auto& eventData = e.mData;

        ecs::EntityID entityId = eventData.mEntity;

        if (eventData.mEventFlag & TRSEventFlagTRSChanged)
        {
            if (!mGameWorld->HasComponent<DecalComponentG>(entityId))
            {
                return;
            }
            auto decalHandle = mGameWorld->GetComponent<DecalComponentG>(entityId);
            if (decalHandle == DecalCompHandle::InvalidHandle())
            {
                return;
            }

            auto transformH = mGameWorld->GetComponent<WorldTransformComponentG>(decalHandle.GetEntityID());
            Assert(transformH.IsValid());
            auto transformReader = transformH.Read();
            const TRS_A& worldTransform = mGameWorld->GetGameSystem<TransformSystemG>()->GetWorldTransformT(transformReader);
            {
                auto decalWriter = decalHandle.Write();
                SetDecalTRS(decalWriter, worldTransform);
            }
        }
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
        const RemainedEventUpdatedEvent& ee = TYPE_CAST(const RemainedEventUpdatedEvent&, event);
        if (ee.mData.mRemainedEventType == TransformJointEvent::sEventType)
        {
            for (auto index = ee.mData.mFirstIndex; index <= ee.mData.mLastIndex; index++)
            {
                const TransformJointEvent& e = transformSystem->GetRemainedEvent<TransformJointEvent>(index);

                auto& eventData = e.mData;

                ecs::EntityID entityId = eventData.mChildEntity;

                if (!mGameWorld->HasComponent<DecalComponentG>(entityId))
                {
                    return;
                }
                auto decalHandle = mGameWorld->GetComponent<DecalComponentG>(entityId);
                if (decalHandle == DecalCompHandle::InvalidHandle())
                {
                    return;
                }

                auto decalWriter = decalHandle.Write();
                UpdateDecalParent(decalWriter, eventData.mNewParentEntity);
            }
        }


        // const TransformJointEvent& e = TYPE_CAST(const TransformJointEvent&, event);
        // auto& eventData = e.mData;
        //
        // ecs::EntityID entityId = eventData.mChildEntity;
        //
        // if (!mGameWorld->HasComponent<DecalComponentG>(entityId))
        //{
        //     return;
        // }
        // auto decalHandle = mGameWorld->GetComponent<DecalComponentG>(entityId);
        // if (decalHandle == DecalCompHandle::InvalidHandle())
        //{
        //     return;
        // }
        //
        // auto decalWriter = decalHandle.Write();
        // UpdateDecalParent(decalWriter, eventData.mNewParentEntity);
    }
    else if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
    {
        //Subscribe events when add to world
        mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
        mGameWorld->SubscribeRemainedEvent<EntityCreateEvent>(this, true);

        auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
        if (transformSys)
        {
            transformSys->SubscribeEvent<TRSChangedEvent>(this, 10);
            transformSys->SubscribeRemainedEvent<TransformJointEvent>(this, true);
        }
    }
}

void DecalSystemG::Release()
{
    delete this;
}

void DecalSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

void DecalSystemG::OnBeginFrame(FrameParam* frameParam)
{
    mChangeList.BeginFrame(frameParam, FRAME_STAGE_GAME);
}

void DecalSystemG::OnEndFrame(FrameParam* frameParam)
{
}

BoundingBox DecalSystemG::GetLocalBound(const DecalCompReader& reader) const
{
    Float3 decalSize = reader->config.DecalSize;
    BoundingBox boundingBox{Float3{0, 0, 0}, decalSize};
    return boundingBox;
}

static MaterialInterfacePtr LoadMaterialAssetDecal(const std::string& assetpath)
{
    auto materialResource = cross::TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(assetpath.c_str()))->CreateInstance();
    return materialResource;
}

static bool GetEntityVisibility(GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto entityMetaSystem = gameWorld->GetGameSystem<cross::EntityMetaSystem>();
    if (gameWorld->HasComponent<cross::ecs::EntityMetaComponentG>(entityId))
    {
        auto entityMetaComp = gameWorld->GetComponent<cross::ecs::EntityMetaComponentG>(entityId);
        bool isHide = entityMetaSystem->GetFlags(entityMetaComp.Read()).GetEntityProp(cross::ecs::EntityDescFlags::EntityProps::IsHide);
        return !isHide;
    }
    return false;
}

void DecalSystemG::SetDecalEnable(const DecalCompWriter& comp, bool enable)
{
    comp->enable = enable;
    bool visible = GetEntityVisibility(mGameWorld, comp.GetEntityID());
    mChangeList.EmplaceChangeData(comp.GetEntityID());
    DispatchRenderingCommandWithToken([this, entityId = comp.GetEntityID(), enable = enable && visible] { mRenderObject->SetDecalEnable(entityId, enable); });
}

void DecalSystemG::SetDecalVisible(const DecalCompWriter& comp, bool visible)
{
    bool enable = comp->enable;
    mChangeList.EmplaceChangeData(comp.GetEntityID());
    DispatchRenderingCommandWithToken([this, entityId = comp.GetEntityID(), enable = enable && visible] { mRenderObject->SetDecalEnable(entityId, enable); });
}


void DecalSystemG::SetDecalWireFrameShow(const DecalCompWriter& writer, bool isShown)
{
    writer->showWireFrame = isShown;
    DispatchRenderingCommandWithToken([renderSystem = mRenderObject, entity = writer.GetEntityID(), isShown]() { renderSystem->SetDecalWireFrameShow(entity, isShown); });
}

void DecalSystemG::OnComponentAddToEntity(const DecalCompWriter& writer) 
{
    auto transformH = mGameWorld->GetComponent<WorldTransformComponentG>(writer.GetEntityID());
    Assert(transformH.IsValid());
    auto transformReader = transformH.Read();
    const TRS_A& worldTransform = mGameWorld->GetGameSystem<TransformSystemG>()->GetWorldTransformT(transformReader);
    {
        SetDecalTRS(writer, worldTransform);
        SetDecalWireFrameShow(writer, true);
        UpdateDecalParent(writer);
    }
}

DecalComponentG DecalSystemG::OnCreateDecalComponent()
{
    DecalComponentG temp;
    return std::move(temp);
}

void DecalSystemG::SetDecalConfig(const DecalCompWriter& comp, const DecalConfig& inConfig)
{
    comp->config = inConfig;
    comp->configDirty = true;
    comp->decalMaterial = LoadMaterialAssetDecal(inConfig.DecalMaterial);

    Float3 TilePosition = {0, 0, 0};
    Float4x4 FinalDecalToWorld;
    ComputeDecalToWorld(comp->decalToWorldWithoutDecalSize, inConfig.DecalSize, FinalDecalToWorld, TilePosition);

    mChangeList.EmplaceChangeData(comp.GetEntityID());
    DispatchRenderingCommandWithToken([this, entityId = comp.GetEntityID(), MaterialRPtr = TYPE_CAST(MaterialR*, comp->decalMaterial->GetRenderMaterial()), config = inConfig, decalToWorld = FinalDecalToWorld, tilePosition = TilePosition] { 
        mRenderObject->SetDecalMaterial(entityId, MaterialRPtr);
        mRenderObject->SetDecalFadingParameters(entityId, config.FadeScreenSize, config.FadeStartDelay, config.FadeDuration, config.FadeInDuration, config.FadeInStartDelay);
        mRenderObject->SetDecalSortOrder(entityId, config.SortOrder);
        mRenderObject->SetDecalTransform(entityId, decalToWorld, tilePosition);
    });
    SetDecalEnable(comp, GetEntityVisibility(mGameWorld, comp.GetEntityID()));

    UpdateDecalAABB(comp.GetEntityID(), inConfig.DecalSize);
}

void DecalSystemG::OnEntityPropChange(ecs::EntityID entity, ecs::EntityDescFlags::EntityProps prop, bool bValue) 
{
    if (prop == ecs::EntityDescFlags::Visibility)
    {
        auto decalCompH = mGameWorld->GetComponent<cross::DecalComponentG>(entity);
        if (decalCompH.IsValid())
            SetDecalVisible(decalCompH.Write(), bValue);
    }
}

void DecalSystemG::UpdateDecalAABB(ecs::EntityID entity, const Float3& decalSize) 
{
    //auto aabbSystem = mGameWorld->GetGameSystem<AABBSystemG>();
    //if (mGameWorld->HasComponent<AABBComponentG>(entity))
    //{
    //    auto aabbComp = mGameWorld->GetComponent<AABBComponentG>(entity);
    //    aabbSystem->SetLocalAABB(aabbComp.Write(), {0, 0, 0}, {decalSize * 200.0f});
    //}
    //else
    //{
    //    Assert(false && "No AABB component for decal, please re-create decal component.");
    //}
}

void DecalSystemG::SetDecalTRS(const DecalCompWriter& comp, const TRS_A& inTRS)
{
    comp->configDirty = true;
    comp->decalToWorldWithoutDecalSize = inTRS;

    Float3 TilePosition = {0, 0, 0};
    Float4x4 FinalDecalToWorld;
    ComputeDecalToWorld(inTRS, comp->config.DecalSize, FinalDecalToWorld, TilePosition);

    mChangeList.EmplaceChangeData(comp.GetEntityID());
    DispatchRenderingCommandWithToken([this, entityId = comp.GetEntityID(), decalToWorld = FinalDecalToWorld, tilePosition = TilePosition] {
        mRenderObject->SetDecalTransform(entityId, decalToWorld, tilePosition);
    });
    SetDecalEnable(comp, GetEntityVisibility(mGameWorld, comp.GetEntityID()));
}

void DecalSystemG::UpdateDecalParent(const DecalCompWriter& comp, ecs::EntityID parent)
{
    DispatchRenderingCommandWithToken([this, entityId = comp.GetEntityID(), parent = parent] { mRenderObject->SetDecalParentID(entityId, parent); });
    SetDecalEnable(comp, GetEntityVisibility(mGameWorld, comp.GetEntityID()));
}

void DecalSystemG::UpdateDecalParent(const DecalCompWriter& comp)
{
    auto transSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    auto parent = transSystem->GetEntityParent(comp.GetEntityID());
    UpdateDecalParent(comp, parent);
}

SerializeNode DecalSystemG::SerializeDecalComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto comp = static_cast<DecalComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode json;
    json = comp->Serialize(context);
    return json;
}

void DecalSystemG::DeserializeDecalComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        return;
    }
    auto cmpntPtr = static_cast<DecalComponentG*>(componentPtr);
    SerializeContext context;
    cmpntPtr->Deserialize(json, context);
}

void DecalSystemG::PostDeserializeDecalComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) 
{
    auto* cmpntPtr = static_cast<DecalComponentG*>(componentPtr);

    cmpntPtr->decalMaterial = LoadMaterialAssetDecal(cmpntPtr->config.DecalMaterial);
    Float3 TilePosition = {0, 0, 0};
    Float4x4 FinalDecalToWorld;
    ComputeDecalToWorld(cmpntPtr->decalToWorldWithoutDecalSize, cmpntPtr->config.DecalSize, FinalDecalToWorld, TilePosition);

    bool enable = GetEntityVisibility(gameWorld, entityId);
    DispatchRenderingCommandWithToken([renderSystem = static_cast<DecalSystemR*>(gameWorld->GetGameSystem<DecalSystemG>()->GetRenderSystem()),
                                         entityId = entityId,
                                         MaterialRPtr = TYPE_CAST(MaterialR*, cmpntPtr->decalMaterial->GetRenderMaterial()),
                                         config = cmpntPtr->config,
                                         decalToWorld = FinalDecalToWorld,
                                         tilePosition = TilePosition,
                                         enable] {
        renderSystem->SetDecalMaterial(entityId, MaterialRPtr);
        renderSystem->SetDecalFadingParameters(entityId, config.FadeScreenSize, config.FadeStartDelay, config.FadeDuration, config.FadeInDuration, config.FadeInStartDelay);
        renderSystem->SetDecalSortOrder(entityId, config.SortOrder);
        renderSystem->SetDecalTransform(entityId, decalToWorld, tilePosition);
        renderSystem->SetDecalEnable(entityId, enable);
    });

    gameWorld->GetGameSystem<DecalSystemG>()->UpdateDecalAABB(entityId, cmpntPtr->config.DecalSize);
}

void DecalSystemG::OnFirstUpdate(FrameParam* frameParam)
{}

}   // namespace cross
