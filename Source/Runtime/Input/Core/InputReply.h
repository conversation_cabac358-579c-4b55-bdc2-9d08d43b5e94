#pragma once
#include "Runtime/Input/Core/InputKeys.h"
#include "CrossBase/Math/CrossMath.h"

namespace cross 
{ 
    class ISequenceableWidget;
    class SequenceableWidget;

    namespace FocusCause 
    {
        enum Type
        {
            /** Focus was changed because of a Mouse/Touch action. */
            Point,

            /** Focus was changed in response to a navigation, such as the arrow keys, TAB key, controller DPad, ... */
            Navigation,

            /** Focus was changed because someone asked the application to change it. */
            SetDirectly,

            /** Focus was explicitly cleared via the escape key or other similar action. */
            Cleared,

            /** Focus was changed because another widget lost focus, and focus moved to a new widget. */
            OtherWidgetLostFocus,

            /** Focus was set in response to the owning window being activated. */
            WindowActivate,
        };
    }; 
}   // namespace cross::FocusCause

namespace cross::input 
{
    class ENGINE_API CEReply final
    {
    public:
        /** An event should return a FReply::Handled().Capture Mouse/Touch( SomeWidget ) as a means of asking the system to forward all mouse events to SomeWidget */
        CEReply& CaptureMouse(std::shared_ptr<SequenceableWidget> inMouseCaptor);
        CEReply& CaptureTouch(std::shared_ptr<SequenceableWidget> inTouchCaptor);

        /**
         * An event should return a Reply::Handled().ReleasePointCapture() to ask the system to release Mouse/Touch capture
         */
        CEReply& ReleasePointCapture();

        /**
         * An event should return a FReply::Handled().ReleaseMouseLock() to ask the system to release mouse lock on a widget
         */
        CEReply& ReleaseMouseLock();

        /** Set the widget that handled the event; undefined if never handled. This method is to be used by WidgetRouter only! */
        CEReply& SetHandler(std::shared_ptr<SequenceableWidget> inHandler);

        /**
         * An event should return CEReply::Handled().Set Mouse Pos to ask Slate to move the mouse cursor to a different location
         */
        CEReply& SetMousePos(const Float2& inNewMousePos);

        /** An event should return CEReply::Handled().SetUserFocus( SomeWidget ) as a means of asking the system to set users focus to the provided widget*/
        CEReply& SetUserFocus(std::shared_ptr<SequenceableWidget> giveMeFocus, FocusCause::Type reasonFocusIsChanging = FocusCause::Type::SetDirectly);

        /** An event should return a FReply::Handled().ClearUserFocus() to ask the system to clear user focus*/
        CEReply& ClearUserFocus(FocusCause::Type ReasonFocusIsChanging = FocusCause::Type::SetDirectly);

        /**
         * An event should return FReply::Handled().LockMouseToWidget( SomeWidget ) as a means of asking the system to
         * Lock the mouse so it cannot move out of the bounds of the widget.
         */
        CEReply& LockMouseToWidget(std::shared_ptr<SequenceableWidget> giveMeLock);

        /**
         * An event should return CEReply::Handled().BeginDragDrop(  ) to initiate a drag and drop operation.
         *
         * @param InDragDropContent  The content that is being dragged. This could be a widget, or some arbitrary data
         *
         * @return reference back to the FReply so that this call can be chained.
         */
        CEReply& BeginDragDrop(std::shared_ptr<SequenceableWidget> dragFromFocus)
        {
            // this->DragDropContent = InDragDropContent;
            return Me();
        }

        /** An event should return FReply::Handled().EndDragDrop() to request that the current drag/drop operation be terminated. */
        CEReply& EndDragDrop()
        {
            return Me();
        }

    public:
        /**
         * An event should return a FReply::Handled() to let the system know that an event was handled.
         */
        static CEReply Handled() { return CEReply(true); }

        /**
         * An event should return a FReply::Unhandled() to let the system know that an event was unhandled.
         */
        static CEReply Unhandled() { return CEReply(false); }

        /** @return true if this this reply is a result of the event being handled; false otherwise. */
        FORCE_INLINE bool IsHandled() const { return mIsHandled; }

        /** The widget that ultimately handled the event */
        FORCE_INLINE const std::shared_ptr<SequenceableWidget> GetHandler() const { return mHandler; }

        /** True if this reply indicated that we should release mouse/touch capture as a result of the event being handled */
        FORCE_INLINE bool ShouldReleasePointer() const { return mReleasePointerCapture; }

        /** true if this reply indicated that we should release focus as a result of the event being handled */
        FORCE_INLINE bool ShouldReleaseUserFocus() const { return mReleaseUserFocus; }

        /** true if this reply indicated that we should set focus as a result of the event being handled */
        FORCE_INLINE bool ShouldReceiveUserFocus() const { return mReceiveUserFocus; }

        /** True if the reply indicated that we should release mouse lock */
        FORCE_INLINE bool ShouldReleaseMouseLock() const { return mReleaseMouseLock; }

        /** Get the reason that a focus change is being requested. */
        FORCE_INLINE FocusCause::Type GetFocusCause() const { return mFocusChangeReason; }

        /** If the event replied with a request to capture the mouse, this returns the desired mouse captor. Otherwise returns an invalid pointer. */
        FORCE_INLINE std::shared_ptr<cross::SequenceableWidget> GetPointerCaptor() const { return mPointerCaptor; }

        /** When not nullptr, user focus has been requested to be set on the FocusRecipient. */
        FORCE_INLINE std::shared_ptr<cross::SequenceableWidget> GetFocusRecepient() const { return mKeyboardCaptor; }

        /** return The coordinates of the requested mouse position */
        FORCE_INLINE const std::optional<Float2>& GetRequestedMousePos() const { return mRequestedMousePos; }

    private:
        CEReply(bool bIsHandled)
            : mIsHandled(bIsHandled)
        {}

        CEReply& Me()
        {
            return static_cast<CEReply&>(*this);
        }

    private:
        /** Has a widget handled an event. */
        bool mIsHandled = false;

        /** Widget that handled the event that generated this reply. */
        std::shared_ptr<cross::SequenceableWidget> mHandler = nullptr;

        /** Highest widget while current pointer overlay (mouse/touch) */
        std::shared_ptr<cross::SequenceableWidget> mPointerCaptor = nullptr;

        /** Focused highest widget may give up the input event, which passed & handled by parent widget recursively */
        std::shared_ptr<cross::SequenceableWidget> mKeyboardCaptor = nullptr;

        /**
         * An event should return FReply::Handled().LockMouseToWidget( SomeWidget ) as a means of asking the system to
         * Lock the mouse so it cannot move out of the bounds of the widget.
         */
        std::shared_ptr<cross::SequenceableWidget> mMouseLockWidget = nullptr;

        // assign a new mouse point or not for specify user, (touch pos can't be override)
        std::optional<Float2> mRequestedMousePos;

        UInt32 mReceiveUserFocus = 0;

        UInt32 mReleaseUserFocus = 0;

        UInt32 mReleasePointerCapture = 0;

        UInt32 mReleaseMouseLock = 0;

        UInt32 mReceiveMouseLock = 0;
    
        FocusCause::Type mFocusChangeReason;
    };
}
