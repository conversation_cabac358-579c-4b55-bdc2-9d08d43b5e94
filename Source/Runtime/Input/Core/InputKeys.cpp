#include "EnginePrefix.h"
#include "Runtime/Input/Core/InputKeys.h"
#include "Runtime/Input/Core/InputCrossPlatformHeader.h"
#include "CECommon/Common/SettingsManager.h"

namespace cross::input 
{
    CEHashMap<CEKey, std::shared_ptr<CEKeyConext>> CEKeys::InputKeys; 
    ///////////////////////////////////////////////////////////
    // IOS engine keys statement
    ///////////////////////////////////////////////////////////

    CEKeyConext const* CEKey::Context() const 
    {
        // Find previous registered key context or create a new key context
        if (mContextPtr == nullptr)
        {
            const auto& keys = CEKeys::GetInputKeys();
            auto it = keys.find(*this);
            if (it != keys.end())
            {
                mContextPtr = it->second;
            }
            else
            {
                mContextPtr = std::make_shared<CEKeyConext>(*this);
            }
        }
        return mContextPtr.get();
    }
     
    void CEKeyConext::CommonInit(const KeyFlagHandle InKeyFlags)
    {
        bIsGamepadKey                   = ((InKeyFlags & CEKeyFlags::GamepadKey) != 0);
        bIsTouch                        = ((InKeyFlags & CEKeyFlags::Touch) != 0);
        bIsMouseButton                  = ((InKeyFlags & CEKeyFlags::MouseButton) != 0);
        bShouldUpdateAxisWithoutSamples = ((InKeyFlags & CEKeyFlags::UpdateAxisWithoutSamples) != 0);
        bIsDeprecated                   = ((InKeyFlags & CEKeyFlags::Deprecated) != 0);
        bIsGesture                      = ((InKeyFlags & CEKeyFlags::Gesture) != 0);
        bIsKeyboard                     = ((InKeyFlags & CEKeyFlags::Keyboard) != 0);
    
        if ((InKeyFlags & CEKeyFlags::ButtonAxis) != 0)
        {
            Assert((InKeyFlags & (CEKeyFlags::Axis1D | CEKeyFlags::Axis2D | CEKeyFlags::Axis3D)) == 0);
            axisType = CEInputAxis::Button;
        }
        else if ((InKeyFlags & CEKeyFlags::Axis1D) != 0)
        {
            Assert((InKeyFlags & (CEKeyFlags::Axis2D | CEKeyFlags::Axis3D | CEKeyFlags::ButtonAxis)) == 0);
            axisType = CEInputAxis::Axis1D;
        }
        else if ((InKeyFlags & CEKeyFlags::Axis2D) != 0)
        {
            Assert((InKeyFlags & (CEKeyFlags::Axis1D | CEKeyFlags::Axis3D | CEKeyFlags::ButtonAxis)) == 0);
            axisType = CEInputAxis::Axis2D;
        }
        else if ((InKeyFlags & CEKeyFlags::Axis3D) != 0)
        {
            Assert((InKeyFlags & (CEKeyFlags::Axis1D | CEKeyFlags::Axis2D | CEKeyFlags::ButtonAxis)) == 0);
            axisType = CEInputAxis::Axis3D;
        }
        else
        {
            axisType = CEInputAxis::None;
        }
    
        // Set up default menu categories
        if (category == "")
        {
            if (IsGamepadKey())
            {
                category = CEKeysCategory::NAME_GamepadCategory;
            }
            else if (IsMouseButton())
            {
                category = CEKeysCategory::NAME_MouseCategory;
            }
            else
            {
                category = CEKeysCategory::NAME_KeyboardCategory;
            }
        }

        if (bIsTouch) 
        {
            auto prefixLength = (strlen)("Touch");
            touchIndex = atoi(reference.GetName().GetCString() + prefixLength) - 1;
        }
    }

    bool CEKeys::bInitialized = false;

    void CEKeys::Initialize(const InitInfo& initInfo)
    {
        if (bInitialized)
            return;
        bInitialized = true;

        AddKey(CEKeyConext(CEKeys::AnyKey,              { ContextKeyFlags::Keyboard | ContextKeyFlags::MouseButton | ContextKeyFlags::GamepadKey }));
        AddKey(CEKeyConext(CEKeys::Invalid,             { ContextKeyFlags::Keyboard }));

        //////////////////////////////////////////////////////////////////////
        // Mouse 

        AddKey(CEKeyConext(CEKeys::CursorX,             { ContextKeyFlags::MouseButton | ContextKeyFlags::Axis1D | ContextKeyFlags::UpdateAxisWithoutSamples}));
        AddKey(CEKeyConext(CEKeys::CursorY,             { ContextKeyFlags::MouseButton | ContextKeyFlags::Axis1D | ContextKeyFlags::UpdateAxisWithoutSamples}));
        AddPairedKey(CEKeyConext(CEKeys::Mouse2D,       { ContextKeyFlags::Axis2D | ContextKeyFlags::MouseButton | ContextKeyFlags::UpdateAxisWithoutSamples}), CEKeys::CursorX, CEKeys::CursorY);
        AddKey(CEKeyConext(CEKeys::MouseScrollUp,       { ContextKeyFlags::MouseButton | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::MouseScrollDown,     { ContextKeyFlags::MouseButton | ContextKeyFlags::ButtonAxis }));

        AddKey(CEKeyConext(CEKeys::LeftMouseButton,     { ContextKeyFlags::MouseButton }));
        AddKey(CEKeyConext(CEKeys::RightMouseButton,    { ContextKeyFlags::MouseButton }));
        AddKey(CEKeyConext(CEKeys::MiddleMouseButton,   { ContextKeyFlags::MouseButton }));
        AddKey(CEKeyConext(CEKeys::ThumbMouseButton,    { ContextKeyFlags::MouseButton }));
        AddKey(CEKeyConext(CEKeys::ThumbMouseButton2,   { ContextKeyFlags::MouseButton }));
        
        //////////////////////////////////////////////////////////////////////
        // Keyboard

        AddKey(CEKeyConext(CEKeys::Tab,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Enter,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Pause,               { ContextKeyFlags::Keyboard }));
                         
        AddKey(CEKeyConext(CEKeys::CapsLock,            { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Escape,              { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::SpaceBar,            { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::PageUp,              { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::PageDown,            { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::End,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Home,                { ContextKeyFlags::Keyboard }));

        AddKey(CEKeyConext(CEKeys::Left,                { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::Up,                  { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::Right,               { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::Down,                { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));

        AddKey(CEKeyConext(CEKeys::Insert,              { ContextKeyFlags::Keyboard }));

        AddKey(CEKeyConext(CEKeys::BackSpace,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Delete,              { ContextKeyFlags::Keyboard }));

        AddKey(CEKeyConext(CEKeys::Zero,                { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::One,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Two,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Three,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Four,                { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Five,                { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Six,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Seven,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Eight,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Nine,                { ContextKeyFlags::Keyboard }));

        AddKey(CEKeyConext(CEKeys::A,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::B,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::C,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::D,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::E,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::F,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::G,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::H,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::I,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::J,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::K,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::L,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::M,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::N,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::O,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::P,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::Q,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::R,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::S,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::T,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::U,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::V,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::W,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::X,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::Y,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));
        AddKey(CEKeyConext(CEKeys::Z,                   { ContextKeyFlags::Keyboard | ContextKeyFlags::ButtonAxis }));

        AddKey(CEKeyConext(CEKeys::NumPadZero,          { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadOne,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadTwo,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadThree,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadFour,          { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadFive,          { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadSix,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadSeven,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadEight,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::NumPadNine,          { ContextKeyFlags::Keyboard }));
                     
        AddKey(CEKeyConext(CEKeys::Multiply,            { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Add,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Subtract,            { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Decimal,             { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Divide,              { ContextKeyFlags::Keyboard }));
                                              
        AddKey(CEKeyConext(CEKeys::F1,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F2,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F3,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F4,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F5,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F6,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F7,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F8,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F9,                  { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F10,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F11,                 { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::F12,                 { ContextKeyFlags::Keyboard }));

        AddKey(CEKeyConext(CEKeys::NumLock,             { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::ScrollLock,          { ContextKeyFlags::Keyboard }));

        AddKey(CEKeyConext(CEKeys::LeftShift,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::RightShift,          { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::LeftControl,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::RightControl,        { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::LeftAlt,             { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::RightAlt,            { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::LeftCommand,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::RightCommand,        { ContextKeyFlags::Keyboard }));
                                                            
        AddKey(CEKeyConext(CEKeys::Semicolon,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Equals,              { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Comma,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Hyphen,              { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Underscore,          { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Period,              { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Slash,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Tilde,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::LeftBracket,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Backslash,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::RightBracket,        { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Apostrophe,          { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Quote,               { ContextKeyFlags::Keyboard }));
                           
        AddKey(CEKeyConext(CEKeys::LeftParantheses,     { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::RightParantheses,    { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Ampersand,           { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Asterix,             { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Caret,               { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Dollar,              { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Exclamation,         { ContextKeyFlags::Keyboard }));
        AddKey(CEKeyConext(CEKeys::Colon,               { ContextKeyFlags::Keyboard }));

        //////////////////////////////////////////////////////////////////////
        // Gamepad
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftX,                   {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftY,                   {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddPairedKey(CEKeyConext(CEKeys::Gamepad_Left2D,            {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis2D}, CEKeysCategory::NAME_GamepadCategory), CEKeys::Gamepad_LeftX, CEKeys::Gamepad_LeftY);
        AddKey(CEKeyConext(CEKeys::Gamepad_RightX,                  {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightY,                  {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddPairedKey(CEKeyConext(CEKeys::Gamepad_Right2D,           {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis2D}, CEKeysCategory::NAME_GamepadCategory), CEKeys::Gamepad_RightX, CEKeys::Gamepad_RightY);


        AddKey(CEKeyConext(CEKeys::Gamepad_LeftTriggerAxis,         {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightTriggerAxis,        {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
                                                                                                 
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftThumbstick,          {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightThumbstick,         {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_Special_Left,            {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_Special_Left_X,          {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_Special_Left_Y,          {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_Special_Right,           {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_FaceButton_Bottom,       {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_FaceButton_Right,        {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_FaceButton_Left,         {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_FaceButton_Top,          {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftShoulder,            {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightShoulder,           {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftTrigger,             {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightTrigger,            {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_DPad_Up,                 {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_DPad_Down,               {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_DPad_Right,              {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_DPad_Left,               {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));

        AddKey(CEKeyConext(CEKeys::Gamepad_LeftStick_Up,            {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftStick_Down,          {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftStick_Right,         {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_LeftStick_Left,          {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightStick_Up,           {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightStick_Down,         {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightStick_Right,        {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::Gamepad_RightStick_Left,         {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        
        //////////////////////////////////////////////////////////////////////
        // Fingers
        for (int touchIndex = 0; touchIndex < CEKeys::NUM_TOUCH_KEYS; touchIndex++)
        {
            std::ostringstream ss;
            ss << "Touch" << touchIndex + 1;
            UniqueString str = ss.str().c_str();

            const_cast<CEKey&>(CEKeys::TouchKeys[touchIndex]) = CEKey(str);
        }
        // FIXME(hendrikwang): Remove Axis2D binding
        for (SInt32 i = 0, size = NUM_TOUCH_KEYS - 1; i < size; ++i)
            AddKey(CEKeyConext(CEKeys::TouchKeys[i],              { ContextKeyFlags::Touch }));

        // Represent any first touch & mouse move
        // FIXME(hendrikwang): Make the Touch key for the cursor pointer invalid
        AddKey(CEKeyConext(CEKeys::TouchKeys[NUM_TOUCH_KEYS - 1], { ContextKeyFlags::Touch | ContextKeyFlags::MouseButton | ContextKeyFlags::Axis2D }));

        //////////////////////////////////////////////////////////////////////
        // Gestures

        AddKey(CEKeyConext(CEKeys::Gesture_Pinch, {ContextKeyFlags::Gesture}));
        AddKey(CEKeyConext(CEKeys::Gesture_Flick, {ContextKeyFlags::Gesture}));
        AddKey(CEKeyConext(CEKeys::Gesture_Rotate, {ContextKeyFlags::Gesture}));

        //////////////////////////////////////////////////////////////////////
        // Android

        AddKey(CEKeyConext(CEKeys::Android_Back,        { ContextKeyFlags::GamepadKey }, CEKeysCategory::NAME_AndroidCategory));
        AddKey(CEKeyConext(CEKeys::Android_Volume_Up,   { ContextKeyFlags::GamepadKey }, CEKeysCategory::NAME_AndroidCategory));
        AddKey(CEKeyConext(CEKeys::Android_Volume_Down, { ContextKeyFlags::GamepadKey }, CEKeysCategory::NAME_AndroidCategory));
        AddKey(CEKeyConext(CEKeys::Android_Menu,        { ContextKeyFlags::GamepadKey }, CEKeysCategory::NAME_AndroidCategory));

        //////////////////////////////////////////////////////////////////////
        // IOS

        //////////////////////////////////////////////////////////////////////
        // Oculus Touch Controller
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_X_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Y_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_X_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Y_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Menu_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Grip_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Grip_Axis, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Trigger_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Trigger_Axis, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Trigger_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_X, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Y, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Up, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Down, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Left, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Left_Thumbstick_Right, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_A_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_B_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_A_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_B_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_System_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Grip_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Grip_Axis, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Trigger_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Trigger_Axis, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Trigger_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_X, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Y, {ContextKeyFlags::GamepadKey | ContextKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Click, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Touch, {ContextKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Up, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Down, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Left, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));
        AddKey(CEKeyConext(CEKeys::OculusTouch_Right_Thumbstick_Right, {ContextKeyFlags::GamepadKey | ContextKeyFlags::ButtonAxis}, CEKeysCategory::NAME_GamepadCategory));


        //////////////////////////////////////////////////////////////////////
        // Initialize the input key manager.  This will cause any additional OEM keys to get added
        CEInputKeyManager::Get().InitKeyMappings(initInfo);
    }
    
    void CEKeys::AddKey(const CEKeyConext& inContext) 
    {
        const CEKey& key = inContext.Reference();
        Assert(InputKeys.find(key) == InputKeys.end());
        key.mContextPtr = std::make_shared<CEKeyConext>(inContext);
        InputKeys.emplace(key, key.mContextPtr);
    }

    void CEKeys::AddPairedKey(const CEKeyConext& KeyDetails, CEKey KeyX, CEKey KeyY)
    {
        AddKey(KeyDetails);

        InputKeys[KeyX]->PairedAxis = CEPairedAxis::X;
        InputKeys[KeyY]->PairedAxis = CEPairedAxis::Y;
        InputKeys[KeyX]->PairedAxisKey = InputKeys[KeyY]->PairedAxisKey = KeyDetails.Reference();
    }

    std::shared_ptr<CEKeyConext> CEKeys::GetKeyContext(const CEKey& Key)
    {
        if (InputKeys.find(Key) != InputKeys.end())
            return InputKeys[Key];
        return nullptr;
    }

    input::CEKey const& CEKeys::TranslateMouseButtonToKey(const input::CEMouseButton::Type Button)
    {
        switch (Button)
        {
            case input::CEMouseButton::Left:
                return CEKeys::LeftMouseButton;

            case input::CEMouseButton::Middle:
                return CEKeys::MiddleMouseButton;

            case input::CEMouseButton::Right:
                return CEKeys::RightMouseButton;

            case input::CEMouseButton::Thumb01:
                return CEKeys::ThumbMouseButton;

            case input::CEMouseButton::Thumb02:
                return CEKeys::ThumbMouseButton2;
            default:
            {
                Assert(0);
                return CEKeys::Invalid;
            }
        }
    }

    input::CEKey const& CEKeys::TranslatePointerIndexToKey(const SInt32 pointerIndex) 
    {
        Assert(pointerIndex >= 0 && pointerIndex <= input::CETouchIndex::CursorPointerIndex);
        /*if (pointerIndex == input::CETouchIndex::CursorPointerIndex)
            return CEKeys::Invalid;
        else
            return TouchKeys[pointerIndex];*/
        return TouchKeys[pointerIndex];
    }

    input::CEKey const& CEKeys::TranslateNameToKey(UniqueString Name) 
    {
        Assert(InputKeys.find(Name) != InputKeys.end());
        return InputKeys[Name]->Reference();    
    }

    CEInputKeyManager::CEInputKeyManager() 
    {}

    CEInputKeyManager& CEInputKeyManager::Get()
    {
        static CEInputKeyManager Instance;
        return Instance;
    }

    void CEInputKeyManager::InitKeyMappings(const InitInfo& initInfo)
    {
        static const UInt32 MAX_KEY_MAPPINGS(256);
        UInt32 KeyCodes[MAX_KEY_MAPPINGS], CharCodes[MAX_KEY_MAPPINGS];
        UniqueString KeyNames[MAX_KEY_MAPPINGS], CharKeyNames[MAX_KEY_MAPPINGS];

#if CROSSENGINE_EDITOR
        const bool bStartByEditor = (initInfo.StartupType == cross::AppStartUpType::AppStartUpTypeCrossEditor);

        UInt32 const CharKeyMapSize(bStartByEditor ? EditorInput::GetCharKeyMap(CharCodes, CharKeyNames, MAX_KEY_MAPPINGS) : PlatformInput::GetCharKeyMap(CharCodes, CharKeyNames, MAX_KEY_MAPPINGS));
        UInt32 const PlatformKeyMapSize(bStartByEditor ? EditorInput::GetPlatformKeyMap(KeyCodes, KeyNames, MAX_KEY_MAPPINGS) : PlatformInput::GetPlatformKeyMap(KeyCodes, KeyNames, MAX_KEY_MAPPINGS));
#else
        UInt32 const CharKeyMapSize(PlatformInput::GetCharKeyMap(CharCodes, CharKeyNames, MAX_KEY_MAPPINGS));
        UInt32 const PlatformKeyMapSize(PlatformInput::GetPlatformKeyMap(KeyCodes, KeyNames, MAX_KEY_MAPPINGS));
#endif

        // When the input language changes, a key that was virtual may no longer be virtual.
        // We must repopulate the maps to ensure GetKeyFromCodes returns the correct value per language.
        KeyMapPlatformToEnum.clear();
        KeyMapCharToEnum.clear();
        for (UInt32 idx = 0; idx < PlatformKeyMapSize; ++idx)
        {
            CEKey cursor(KeyNames[idx]);
            Assert(CEKeys::InputKeys.find(cursor) != CEKeys::InputKeys.end());

            KeyMapPlatformToEnum.emplace(KeyCodes[idx], CEKeys::InputKeys[cursor]);
        }

        for (UInt32 idx = 0; idx < CharKeyMapSize; ++idx)
        {
            // repeated linear search here isn't ideal, but it's just once at startup
            CEKey cursor(CharKeyNames[idx]);
            Assert(CEKeys::InputKeys.find(cursor) != CEKeys::InputKeys.end());

            KeyMapCharToEnum.emplace(CharCodes[idx], CEKeys::InputKeys[cursor]);
        }
    }

    CEKey const* CEInputKeyManager::AnyKey() const
    {
        return GetKeyFromCode(static_cast<UInt32>(PlatformInput::AnyKeyCode), 0);
    }

    CEKey::CEKey(const CEKey& other)
        : mName(other.mName)
        , mDisplayName(other.mDisplayName)
        , mContextPtr(other.mContextPtr)
    {}

    CEKey& CEKey::operator= (const CEKey& rhs)
    {
        mName = rhs.mName;
        mDisplayName = rhs.mDisplayName;
        mContextPtr = rhs.mContextPtr;
        return *this;
    }

    CEKey::~CEKey(){}

    bool CEKey::IsValid() const
    {
        if (!mName.GetStringView().empty())
        {
            if (mContextPtr == nullptr)
            {
                mContextPtr = CEKeys::GetKeyContext(*this);
            }
            return mContextPtr == nullptr ? false : true;
        }
        return false;
    }

    CEKey CEKey::GetPairedAxisKey() const
    {
        return IsValid() ? mContextPtr->PairedAxisKey : CEKey();
    }

    CEPairedAxis CEKey::GetPairedAxis() const
    {
        return IsValid() ? mContextPtr->PairedAxis : CEPairedAxis::Unpaired;
    }
    CEKey::CEKey()
    {}
    CEKey::CEKey(const UniqueString inName, UniqueString inDisplayName)
        : mName(inName)
        , mDisplayName(inDisplayName)
    {}

    CEKey CEKey::CreateCEKeyByStr(const std::string& keyName)
    {
        return CEKey(keyName.c_str());
    }

    CEKey::CEKey(const UniqueString inName) 
        : mName(inName)
        , mDisplayName(inName)
    {}
    }   // namespace cross::input
