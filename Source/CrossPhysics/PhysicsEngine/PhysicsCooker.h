#pragma once
#include "CrossPhysics.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsGeometry.h"

namespace cross {
class CrossPhysics_API PhysicsCooker
{
public:
    virtual ~PhysicsCooker() = default;

    virtual std::shared_ptr<PhysicsTriangleMesh> BuildTriangleMesh(const UInt8* vertexData, UInt32 vertexCount, UInt16 vertexStride, const UInt8* indexData, UInt32 indexCount, UInt16 indexStride) = 0;
    virtual std::shared_ptr<PhysicsConvexMesh> BuildConvexMesh(const UInt8* vertexData, UInt32 vertexCount, UInt16 vertexStride, const UInt8* indexData, UInt32 indexCount, UInt16 indexStride) = 0;

    virtual std::vector<UInt8> SerializeTriangleMesh(const PhysicsTriangleMesh* mesh) const = 0;
    virtual std::shared_ptr<PhysicsTriangleMesh> DeserializeTriangleMesh(const UInt8* data, size_t size) const = 0;

    virtual std::vector<UInt8> SerializeConvexMesh(const PhysicsConvexMesh* mesh) const = 0;
    virtual std::shared_ptr<PhysicsConvexMesh> DeserializeConvexMesh(const UInt8* data, size_t size) const = 0;

    virtual bool IsSerializedToBin(const UInt8* data, size_t size) const = 0;
};
}   // namespace cross
