#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "PxPhysicsAPI.h"

#include "PhysicsEngine/PhysicsCooker.h"

namespace cross {
class PhysXCooker : public PhysicsCooker
{
public:
    PhysXCooker(physx::PxPhysics* pxPhysics, physx::PxFoundation* pxFoundation, const physx::PxTolerancesScale& scale);
    ~PhysXCooker() override;

    std::shared_ptr<PhysicsTriangleMesh> BuildTriangleMesh(const UInt8* vertexData, UInt32 vertexCount, UInt16 vertexStride, const UInt8* indexData, UInt32 indexCount, UInt16 indexStride) override;
    std::shared_ptr<PhysicsConvexMesh> BuildConvexMesh(const UInt8* vertexData, UInt32 vertexCount, UInt16 vertexStride, const UInt8* indexData, UInt32 indexCount, UInt16 indexStride) override;

    bool IsSerializedToBin(const UInt8* data, size_t size) const override;

    std::vector<UInt8> SerializeTriangleMesh(const PhysicsTriangleMesh* mesh) const override;
    std::shared_ptr<PhysicsTriangleMesh> DeserializeTriangleMesh(const UInt8* data, size_t size) const override;
    std::vector<UInt8> SerializeConvexMesh(const PhysicsConvexMesh* mesh) const override;
    std::shared_ptr<PhysicsConvexMesh> DeserializeConvexMesh(const UInt8* data, size_t size) const override;

    std::vector<UInt8> SerializeTriangleMesh(const PhysicsTriangleMesh* mesh, bool serializeToBin) const;
    std::shared_ptr<PhysicsTriangleMesh> DeserializeTriangleMesh(const UInt8* data, size_t size, bool deserializeFromBin) const;
    std::vector<UInt8> SerializeConvexMesh(const PhysicsConvexMesh* mesh, bool serializeToBin) const;
    std::shared_ptr<PhysicsConvexMesh> DeserializeConvexMesh(const UInt8* data, size_t size, bool deserializeFromBin) const;

protected:
    physx::PxCooking* mCooking = nullptr;
    physx::PxSerializationRegistry* mSerializationRegistry = nullptr;

    // mPhysics and mFoundation are held in PhysXEngine, don't release it when destructing
    physx::PxPhysics* mPhysics = nullptr;
    physx::PxFoundation* mFoundation = nullptr;
    bool mSerializeToBin;
};
}   // namespace cross
