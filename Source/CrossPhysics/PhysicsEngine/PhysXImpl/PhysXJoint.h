#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "PxPhysicsAPI.h"

#include "PhysicsEngine/PhysicsJoint.h"
#include "PhysicsEngine/PhysXImpl/PhysXActor.h"
#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"

namespace cross {
class PhysXJoint : public PhysicsJoint
{
public:
    PhysXJoint(physx::PxD6Joint* pxJoint)
        : mPxJoint(pxJoint) {}

    ~PhysXJoint() override;
    void SetBreakForce(float force, float torque) override;

    std::pair<PhysicsActor*, PhysicsActor*> GetActors() override;
    void SetActors(PhysicsActor* actor0, PhysicsActor* actor1) override;

    void SetLocalPos(const Transform& pos0, const Transform& pos1) override;
    std::pair<Transform, Transform> GetLocalPos() override;

    void SetConfig(const PhysicsJointConfig& config) override;

    template<class Underlay>
    Underlay* GetUnderlay() const
    {
        Assert(mPxJoint->is<Underlay>());
        return mPxJoint->is<Underlay>();
    }

    static PhysXJoint* Create(PhysicsActor* actor0, PhysicsActor* actor1, const PhysicsJointConfig& config);
protected:
    physx::PxD6Joint* mPxJoint = nullptr;
};
}
