#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "PxPhysicsAPI.h"
#include "PhysicsEngine/PhysicsShape.h"
#include "PhysicsEngine/PhysicsGeometry.h"
#include "PhysicsEngine/PhysicsMaterial.h"

namespace cross
{
    enum class PhysXFilterBit
    {
        REPORT_COLLISION_POINT = 1 << 0, //report contact point
    };

    class PhysXShape : public PhysicsShape
    {
        PhysXShape(physx::PxShape* shape, const Float3& scale);
        ~PhysXShape() override;
    public:
        static PhysXShape* CreateTriangleMeshShape(const PhysicsGeometryMesh& meshGeo, const Float3& scale, PhysicsMaterial* material);
        static PhysXShape* CreateConvexMeshShape(const PhysicsGeometryConvex& convexGeo, const Float3& scale, PhysicsMaterial* material);
        static PhysXShape* CreateBoxShape(const PhysicsGeometryBox& boxGeo, const Float3& scale, PhysicsMaterial* material);
        static PhysXShape* CreateSphereShape(const PhysicsGeometrySphere& sphereGeo, const Float3& scale, PhysicsMaterial* material);
        static PhysXShape* CreateCapsuleShape(const PhysicsGeometryCapsule& capsuleGeo, const Float3& scale, PhysicsMaterial* material);
        static PhysXShape* CreatePlaneShape(const PhysicsGeometryPlane& planeGeo, const Float3& scale, PhysicsMaterial* material);
        static void DestroyShape(PhysXShape* shape);

        void SetLocalPos(const Float3& localPos, const Quaternion& localRotate) override;
        std::pair<Float3, Quaternion> GetLocalPos() override;

        void SetScale(const Float3& scale) override;
        Float3 GetScale() const override;

        PhysicsShapeType GetShapeType() const override;

        physx::PxShape* GetUnderlay() const { return mShape; }

        void SetCollisionInfo(CollisionTypeValue collisionType, CollisionMask mask, bool isTrigger) override;
        void SetEnableReportCollision(bool enable) override;
    protected:
        physx::PxShape* mShape;
        Float3 mScale;
    };
}
