#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "PxPhysicsAPI.h"
#include "Math/CrossMath.h"
#include "PhysicsEngine/PhysicsGeometry.h"


namespace cross
{
	struct PhysXConvexMesh : public PhysicsConvexMesh
	{
	public:
		PhysXConvexMesh(physx::PxConvexMesh* mesh, void* data = nullptr) : mPxConvexMesh(mesh), mData(data){}
		~PhysXConvexMesh() override;

		physx::PxConvexMesh* GetUnderlay() const { return mPxConvexMesh; }
	protected:
		physx::PxConvexMesh* mPxConvexMesh = nullptr;
		void* mData = nullptr;
	};

	struct PhysXTriangleMesh : public PhysicsTriangleMesh
	{
	public:
		PhysXTriangleMesh(physx::PxTriangleMesh* mesh, void* data = nullptr) :mPxTriangleMesh(mesh), mData(data){}
		~PhysXTriangleMesh() override;

		physx::PxTriangleMesh* GetUnderlay() const { return mPxTriangleMesh; }
	protected:
		physx::PxTriangleMesh* mPxTriangleMesh = nullptr;
		void* mData = nullptr;
	};
}
