#include "EnginePrefix.h"
#include "PhysXCooker.h"

#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"

namespace cross {
PhysXCooker::PhysXCooker(physx::PxPhysics* pxPhysics, physx::PxFoundation* pxFoundation, const physx::PxTolerancesScale& scale)
    : mPhysics(pxPhysics)
    , mFoundation(pxFoundation)
    , mSerializeToBin(false)
{
    Assert(mPhysics);
    Assert(mFoundation);
    mCooking = PxCreateCooking(PX_PHYSICS_VERSION, *mFoundation, physx::PxCookingParams(scale));
    Assert(mCooking);
    mSerializationRegistry = physx::PxSerialization::createSerializationRegistry(PxGetPhysics());
    Assert(mSerializationRegistry);
    EngineGlobal::GetSettingMgr()->GetValue("Physics.SerializeToBinary", mSerializeToBin);
}

PhysXCooker::~PhysXCooker()
{
    if (mCooking)
        mCooking->release();
    if (mSerializationRegistry)
        mSerializationRegistry->release();
}

std::shared_ptr<PhysicsTriangleMesh> PhysXCooker::BuildTriangleMesh(const UInt8* vertexData, UInt32 vertexCount, UInt16 vertexStride, const UInt8* indexData, UInt32 indexCount, UInt16 indexStride)
{
    QUICK_SCOPED_CPU_TIMING("Physics::BuildTriangleMesh");

    physx::PxTriangleMeshDesc meshDesc;
    meshDesc.points.count = static_cast<unsigned int>(vertexCount);
    meshDesc.points.data = vertexData;
    meshDesc.points.stride = vertexStride;
    meshDesc.triangles.count = static_cast<unsigned int>(indexCount / 3);
    meshDesc.triangles.stride = 3 * indexStride;
    meshDesc.triangles.data = indexData;
    if (indexStride == 2)
        meshDesc.flags.set(physx::PxMeshFlag::e16_BIT_INDICES);
    Assert(meshDesc.isValid());
    physx::PxDefaultMemoryOutputStream buf;
    physx::PxTriangleMeshCookingResult::Enum result;
    [[maybe_unused]] bool statue = mCooking->cookTriangleMesh(meshDesc, buf, &result);
    Assert(statue);
    physx::PxDefaultMemoryInputData input(buf.getData(), buf.getSize());
    physx::PxTriangleMesh* triangleMesh = mPhysics->createTriangleMesh(input);
    Assert(triangleMesh);
    return std::make_shared<PhysXTriangleMesh>(triangleMesh);
}

std::shared_ptr<PhysicsConvexMesh> PhysXCooker::BuildConvexMesh(const UInt8* vertexData, UInt32 vertexCount, UInt16 vertexStride, const UInt8* indexData, UInt32 indexCount, UInt16 indexStride)
{
    physx::PxConvexMeshDesc convexDesc;
    convexDesc.points.count = static_cast<unsigned int>(vertexCount);
    convexDesc.points.data = vertexData;
    convexDesc.points.stride = vertexStride;
    convexDesc.flags = physx::PxConvexFlag::eCOMPUTE_CONVEX;
    physx::PxDefaultMemoryOutputStream buf;
    physx::PxConvexMeshCookingResult::Enum result;
    [[maybe_unused]] bool statue = mCooking->cookConvexMesh(convexDesc, buf, &result);
    Assert(statue != false);
    physx::PxDefaultMemoryInputData input(buf.getData(), buf.getSize());
    physx::PxConvexMesh* convexMesh = mPhysics->createConvexMesh(input);
    Assert(convexMesh);
    return std::make_shared<PhysXConvexMesh>(convexMesh);
}

bool PhysXCooker::IsSerializedToBin(const UInt8* data, size_t size) const
{
    static constexpr std::string_view HeaderOfBin = "SEBD";
    static constexpr std::string_view HeaderOfXml = "<PhysXCollection";
    const std::string_view buffer = std::string_view(reinterpret_cast<const char*>(data), size);
    if (std::strncmp(HeaderOfBin.data(), reinterpret_cast<const char*>(data), HeaderOfBin.length()) == 0)
        return true;
    Assert(std::strncmp(HeaderOfXml.data(), reinterpret_cast<const char*>(data), HeaderOfXml.length()) == 0);
    return false;
}

std::vector<UInt8> PhysXCooker::SerializeTriangleMesh(const PhysicsTriangleMesh* mesh, bool serializeToBin) const
{
    physx::PxTriangleMesh* pxMesh = static_cast<const PhysXTriangleMesh*>(mesh)->GetUnderlay();
    physx::PxCollection* collection = PxCreateCollection();
    collection->add(*pxMesh);
    physx::PxSerialization::complete(*collection, *mSerializationRegistry);

    physx::PxDefaultMemoryOutputStream outStream;
    if (serializeToBin)
    {
        physx::PxSerialization::serializeCollectionToBinary(outStream, *collection, *mSerializationRegistry);
    }
    else
    {
        physx::PxSerialization::serializeCollectionToXml(outStream, *collection, *mSerializationRegistry);
    }

    collection->release();

    return std::vector<UInt8>(outStream.getData(), outStream.getData() + outStream.getSize());
}

std::vector<UInt8> PhysXCooker::SerializeTriangleMesh(const PhysicsTriangleMesh* mesh) const
{
    return SerializeTriangleMesh(mesh, mSerializeToBin);
}

std::shared_ptr<PhysicsTriangleMesh> PhysXCooker::DeserializeTriangleMesh(const UInt8* data, size_t size, bool deserializeFromBin) const
{
    std::shared_ptr<PhysicsTriangleMesh> ret = nullptr;

    if (deserializeFromBin)
    {
        void* memory128 = nullptr;
        ALIGNED_ALLOC(size, PX_SERIAL_FILE_ALIGN, memory128);
        memcpy(memory128, data, size);
        auto* collection = physx::PxSerialization::createCollectionFromBinary(memory128, *mSerializationRegistry);   // dango phy TODO: release collection
        Assert(collection->getNbObjects() == 1);
        physx::PxBase& obj = collection->getObject(0);
        physx::PxTriangleMesh* tria = obj.is<physx::PxTriangleMesh>();
        Assert(tria);
        ret = std::make_shared<PhysXTriangleMesh>(tria, memory128);
    }
    else
    {
        physx::PxDefaultMemoryInputData inputData(const_cast<UInt8*>(data), static_cast<physx::PxU32>(size));
        physx::PxCollection* collection = physx::PxSerialization::createCollectionFromXml(inputData, *mCooking, *mSerializationRegistry);   // dango phy TODO: release collection
        Assert(collection->getNbObjects() == 1);
        physx::PxBase& obj = collection->getObject(0);
        physx::PxTriangleMesh* tria = obj.is<physx::PxTriangleMesh>();
        Assert(tria);
        ret = std::make_shared<PhysXTriangleMesh>(tria, nullptr);
    }

    return ret;
}

std::shared_ptr<PhysicsTriangleMesh> PhysXCooker::DeserializeTriangleMesh(const UInt8* data, size_t size) const
{
    bool isBinary = IsSerializedToBin(data, size);
    return DeserializeTriangleMesh(data, size, isBinary);
}

std::vector<UInt8> PhysXCooker::SerializeConvexMesh(const PhysicsConvexMesh* mesh, bool serializeToBin) const
{
    physx::PxConvexMesh* pxMesh = static_cast<const PhysXConvexMesh*>(mesh)->GetUnderlay();
    physx::PxCollection* collection = PxCreateCollection();
    collection->add(*pxMesh);
    physx::PxSerialization::complete(*collection, *mSerializationRegistry);

    physx::PxDefaultMemoryOutputStream outStream;
    if (serializeToBin)
    {
        physx::PxSerialization::serializeCollectionToBinary(outStream, *collection, *mSerializationRegistry);
    }
    else
    {
        physx::PxSerialization::serializeCollectionToXml(outStream, *collection, *mSerializationRegistry);
    }
    collection->release();

    return std::vector<UInt8>(outStream.getData(), outStream.getData() + outStream.getSize());
}

std::vector<UInt8> PhysXCooker::SerializeConvexMesh(const PhysicsConvexMesh* mesh) const
{
    return SerializeConvexMesh(mesh, mSerializeToBin);
}

std::shared_ptr<PhysicsConvexMesh> PhysXCooker::DeserializeConvexMesh(const UInt8* data, size_t size, bool deserializeFromBin) const
{
    std::shared_ptr<PhysicsConvexMesh> ret = nullptr;

    if (deserializeFromBin)
    {
        void* memory128 = nullptr;
        ALIGNED_ALLOC(size, PX_SERIAL_FILE_ALIGN, memory128);
        memcpy(memory128, data, size);
        auto* collection = physx::PxSerialization::createCollectionFromBinary(memory128, *mSerializationRegistry);   // dango phy TODO: release collection
        Assert(collection->getNbObjects() == 1);
        physx::PxBase& obj = collection->getObject(0);
        physx::PxConvexMesh* tria = obj.is<physx::PxConvexMesh>();
        Assert(tria);
        ret = std::make_shared<PhysXConvexMesh>(tria, memory128);
    }
    else
    {
        physx::PxDefaultMemoryInputData inputData(const_cast<UInt8*>(data), static_cast<physx::PxU32>(size));
        physx::PxCollection* collection = physx::PxSerialization::createCollectionFromXml(inputData, *mCooking, *mSerializationRegistry);   // dango phy TODO: release collection
        Assert(collection->getNbObjects() == 1);
        physx::PxBase& obj = collection->getObject(0);
        physx::PxConvexMesh* conv = obj.is<physx::PxConvexMesh>();
        Assert(conv);
        ret = std::make_shared<PhysXConvexMesh>(conv, nullptr);
    }

    return ret;
}

std::shared_ptr<PhysicsConvexMesh> PhysXCooker::DeserializeConvexMesh(const UInt8* data, size_t size) const
{
    bool isBinary = IsSerializedToBin(data, size);
    return DeserializeConvexMesh(data, size, isBinary);
}
}   // namespace cross
