#include "EnginePrefix.h"
#include "PhysicsGeometry.h"

namespace cross {
bool PhysicsCollision::Empty() const
{
    return mBoxGeometry.empty() && mSphereGeometry.empty() && mCapsuleGeometry.empty() && mPlaneGeometry.empty() && mConvexGeometry.empty() && mMeshGeometry.empty();
}

void PhysicsCollision::Clear()
{
    mBoxGeometry.clear();
    mSphereGeometry.clear();
    mCapsuleGeometry.clear();
    mPlaneGeometry.clear();
    mConvexGeometry.clear();
    mMeshGeometry.clear();
}

void PhysicsCollision::Append(const PhysicsCollision& other)
{
    mBoxGeometry.insert(mBoxGeometry.end(), other.mBoxGeometry.begin(), other.mBoxGeometry.end());
    mSphereGeometry.insert(mSphereGeometry.end(), other.mSphereGeometry.begin(), other.mSphereGeometry.end());
    mCapsuleGeometry.insert(mCapsuleGeometry.end(), other.mCapsuleGeometry.begin(), other.mCapsuleGeometry.end());
    mPlaneGeometry.insert(mPlaneGeometry.end(), other.mPlaneGeometry.begin(), other.mPlaneGeometry.end());
    mConvexGeometry.insert(mConvexGeometry.end(), other.mConvexGeometry.begin(), other.mConvexGeometry.end());
    mMeshGeometry.insert(mMeshGeometry.end(), other.mMeshGeometry.begin(), other.mMeshGeometry.end());
}
}   // namespace cross
