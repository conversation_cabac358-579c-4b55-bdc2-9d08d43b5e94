#ifndef STRIPERINCLUDES_H
#define STRIPERINCLUDES_H

typedef signed char			sbyte;
typedef unsigned char		ubyte;
typedef signed short		sword;
typedef unsigned short		uword;
typedef signed int			sdword;
typedef unsigned int		udword;
typedef float				sfloat;


#define RELEASEARRAY(x)	{ if (x != nullptr) delete []x;	x = nullptr; }

#include <cstring>

inline void ZeroMemory_(void* addr, udword size)
{
	memset(addr, 0, size);
}

inline void CopyMemory_(void* dest, const void* src, udword size)
{
	memcpy(dest, src, size);
}

inline void FillMemory_(void* dest, udword size, ubyte val)
{
	memset(dest, val, size);
}

#endif
