#include <fstream>
#include "TextureAssemble.h"
#include "TextureAsset_generated.h"
#include "ResourceAsset_generated.h"
#include "CECommon/Resource/Serialize/FlatbufferSerialize.h"
#include <CrossBase/Serialization/ResourceMetaHeader.h>
#include <CrossBase/Serialization/SerializeNode.h>
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"

constexpr bool IsCubeType(CrossSchema::TextureDimension t)
{
    return t == CrossSchema::TextureDimension::TexCube || t == CrossSchema::TextureDimension::TexCubeArray;
}

constexpr bool IsArrayType(CrossSchema::TextureDimension t)
{
    return t == CrossSchema::TextureDimension::Tex2DArray || t == CrossSchema::TextureDimension::TexCubeArray;
}

enum class RenderTextureDimension : UInt32
{
    RenderTextureDimension_None = 0,

    Tex2D,
    TexCube
};

enum class DepthTextureDimension : UInt32
{
    DepthTextureDimension_None = 0,

    Tex2D,
    TexCube
};

constexpr bool IsIntFormat(CrossSchema::TextureFormat fmt)
{
    return fmt >= CrossSchema::TextureFormat::A8 && fmt <= CrossSchema::TextureFormat::R16;
}

constexpr bool IsFloatFormat(CrossSchema::TextureFormat fmt)
{
    return fmt >= CrossSchema::TextureFormat::RHalf && fmt <= CrossSchema::TextureFormat::R11G11B10Float;
}

constexpr bool IsCompressFormat(CrossSchema::TextureFormat fmt)
{
    return fmt >= CrossSchema::TextureFormat::PVRTC_RGB2 && fmt <= CrossSchema::TextureFormat::PVRTC_RGBA4;
}

constexpr bool IsETCFormat(CrossSchema::TextureFormat fmt)
{
    return fmt >= CrossSchema::TextureFormat::ETC_RGB4 && fmt <= CrossSchema::TextureFormat::ETC2_RGBA8;
}

constexpr bool IsBCFormat(CrossSchema::TextureFormat fmt)
{
    return fmt >= CrossSchema::TextureFormat::BC1 && fmt <= CrossSchema::TextureFormat::BC7;
}

constexpr bool IsASTCFormat(CrossSchema::TextureFormat fmt)
{
    return fmt >= CrossSchema::TextureFormat::ASTC_4x4 && fmt <= CrossSchema::TextureFormat::ASTC_HDR_12x12;
}

constexpr UInt32 GetPixelByteSize(CrossSchema::TextureFormat fmt)
{
    if (CrossSchema::IsASTCFormat(fmt))
        return 16;
    switch (fmt)
    {
    case CrossSchema::TextureFormat::A8:
    case CrossSchema::TextureFormat::R8:
        return 1;
    case CrossSchema::TextureFormat::RG16:
    case CrossSchema::TextureFormat::RGB565:
    case CrossSchema::TextureFormat::R16:
    case CrossSchema::TextureFormat::RHalf:
    case CrossSchema::TextureFormat::RGBA4444:
        return 2;
    case CrossSchema::TextureFormat::RGBX32:
    case CrossSchema::TextureFormat::RGBA32:
    case CrossSchema::TextureFormat::RFloat:
    case CrossSchema::TextureFormat::RGHalf:
    case CrossSchema::TextureFormat::RGB9e5Float:
    case CrossSchema::TextureFormat::R11G11B10Float:
        return 4;
    case CrossSchema::TextureFormat::RGFloat:
    case CrossSchema::TextureFormat::RGBAHalf:
        return 8;
    case CrossSchema::TextureFormat::RGBFloat:
        return 12;
    case CrossSchema::TextureFormat::RGBAFloat:
        return 16;
    default:
        // AssertMsg(false, "None Pixel Format");
        return 0;
    }
}

std::uint32_t ComputeRowPitch(CEAssetExchange::EPixelFormat format, std::uint32_t width)
{
    // Raw
    if (format == CEAssetExchange::EPixelFormat::PF_R8G8B8A8)
        return width * CrossSchema::GetPixelByteSize(CrossSchema::TextureFormat::RGBA32);
    else if (format == CEAssetExchange::EPixelFormat::PF_FloatRGBA)
        return width * CrossSchema::GetPixelByteSize(CrossSchema::TextureFormat::RGBAFloat);
    else if (format == CEAssetExchange::EPixelFormat::PF_G8)
        return width * CrossSchema::GetPixelByteSize(CrossSchema::TextureFormat::R8);
    else if (format == CEAssetExchange::EPixelFormat::PF_G16)
        return width * CrossSchema::GetPixelByteSize(CrossSchema::TextureFormat::R16);

    // BC
    std::uint32_t block_width = 4;
    std::uint32_t block_height = 4;
    std::uint32_t bytes_per_block = (format == CEAssetExchange::EPixelFormat::PF_DXT1 || format == CEAssetExchange::EPixelFormat::PF_BC4) ? 8 : 16;
    switch (format)
    {
    case CEAssetExchange::EPixelFormat::PF_BC6H:
        block_width = block_height = 4;
        bytes_per_block = 16;
    default:
        break;
    }

    return std::max(((width - 1) / block_width + 1) * bytes_per_block, bytes_per_block);
}

CrossSchema::TextureFormat ToCrossTextureFormat(CEAssetExchange::EPixelFormat format)
{
    switch (format)
    {
    case CEAssetExchange::PF_G8:
        return CrossSchema::TextureFormat::R8;
    case CEAssetExchange::PF_G16:
        return CrossSchema::TextureFormat::R16;
    case CEAssetExchange::PF_DXT1:
        return CrossSchema::TextureFormat::BC1;
    case CEAssetExchange::PF_DXT3:
        return CrossSchema::TextureFormat::BC2;
    case CEAssetExchange::PF_DXT5:
        return CrossSchema::TextureFormat::BC3;
    case CEAssetExchange::PF_FloatRGBA:
        return CrossSchema::TextureFormat::RGBAFloat;
    case CEAssetExchange::PF_BC4:
        return CrossSchema::TextureFormat::BC4;
    case CEAssetExchange::PF_BC5:
        return CrossSchema::TextureFormat::BC5;
    case CEAssetExchange::PF_R8G8B8A8:
        return CrossSchema::TextureFormat::RGBA32;
    case CEAssetExchange::PF_BC6H:
        return CrossSchema::TextureFormat::BC6H;
    case CEAssetExchange::PF_BC7:
        return CrossSchema::TextureFormat::BC7;
    default:
        return CrossSchema::TextureFormat::RGBA32;
    }
}

namespace CEAssetExchange {

void TextureAssemble::AsMipMapArray(MipMapArray* Mips, bool vtstreaming)
{
    assert(Mips);
    std::vector<CrossSchema::TextureAssetImage> texImages;
    std::uint32_t total_size = 0;
    for (std::uint32_t i = 0; i < Mips->MipMapNum; i++)
    {
        MipMapData* MipData = Mips->MipMap + i;
        assert(MipData);
        const auto size = MipData->mDataNum;
        texImages.emplace_back();
        CrossSchema::TextureAssetImage& image = texImages.back();
        image.mutate_width(MipData->SizeX);
        image.mutate_height(MipData->SizeY);
        image.mutate_dataoffset(total_size);
        image.mutate_databytesize(size);
        image.mutate_depth(1);
        image.mutate_rowpitch(ComputeRowPitch(Mips->Format, MipData->SizeX));
        mTexture.data.resize(total_size + size);
        memcpy(mTexture.data.data() + total_size, MipData->mData, size);
        total_size += size;
    }

    mTexture.flags = 0;
    mTexture.images = texImages;
    mTexture.vtstreaming = vtstreaming;
    mTexture.mipcount = static_cast<uint32_t>(texImages.size());
    mTexture.dimension = CrossSchema::TextureDimension::Tex2D;
    mTexture.format = ToCrossTextureFormat(Mips->Format);
    mTexture.colorspace = Mips->ColorSpace == ImportColorSpace::Linear ? CrossSchema::ColorSpace::Linear : CrossSchema::ColorSpace::SRGB;

    // clear UDIM
    mUDIM.clear();
}

void TextureAssemble::SetTextureImportSetting(TextureImportSettingWrapper setting)
{
        cross::editor::TextureImportSetting cesetting;

        cesetting.AutoImport = setting.AutoImport;
        cesetting.ColorSpace = static_cast<cross::editor::ImportColorSpace>(setting.ColorSpace);
        cesetting.Compression = static_cast<cross::editor::TextureCompression>(setting.ColorSpace);
        cesetting.FlipUV = setting.FlipUV;
        cesetting.GenerateMipmap = setting.GenerateMipmap;
        cesetting.GeneratePrefilterMipmap = setting.GeneratePrefilterMipmap;
        cesetting.GlossBias = setting.GlossBias;
        cesetting.ImportSize = static_cast<cross::editor::ImportTexureSize>(setting.ImportSize);

        cesetting.ImportTextureGroup = static_cast<cross::editor::ImportTextureGroup>(setting.ImportTextureGroup);
        cesetting.isAutoRecognition = setting.isAutoRecognition;
        cesetting.IsCook = setting.IsCook;
        cesetting.Type = static_cast<::TextureType>(setting.Type);
        cesetting.IsStreamFile = setting.IsStreamFile;
        cesetting.isUDIM = setting.isUDIM;
        cesetting.TileXY = cross::Float2(setting.TileX, setting.TileY);
        cesetting.VirtualTextureStreaming = setting.VirtualTextureStreaming;
        cesetting.OpenGLESCompatible = setting.OpenGLESCompatible;
        mImportSet = cesetting;
}

void TextureAssemble::SetAdjustmentSettings(AdjustmentSettings* m)
{
    Assert(m);
    mTexture.hue = m->hue;
    mTexture.minalpha = m->minalpha;
    mTexture.maxalpha = m->maxalpha;
    mTexture.vibrance = m->vibrance;
    mTexture.brightness = m->brightness;
    mTexture.saturation = m->saturation;
    mTexture.brightnesscurve = m->brightnesscurve;
}

void TextureAssemble::AsUDIM(UDIM* m)
{
    Assert(m);
    mUDIM.resize(m->BlocksNum);
    for (std::uint32_t i = 0; i < m->BlocksNum; i++)
    {
        mUDIM[i].Path = m->Blocks[i].Path;
        mUDIM[i].BlockX = m->Blocks[i].BlockX;
        mUDIM[i].BlockY = m->Blocks[i].BlockY;
    }
}

void TextureAssemble::SaveToFile()
{
    if (mSaved)
        return;

    if (mFile.empty())
        return;

    if (!mOverwrite && std::filesystem::exists(mFile))
    {
        mSaved = true;
        return;
    }

    if (mUDIM.size() > 0)
    {
        cross::SerializeNode contentN;
        cross::SerializeNode child_textures;
        for (auto beg = mUDIM.begin(); beg != mUDIM.end(); beg++)
        {
            cross::SerializeNode block;
            block["path"] = beg->Path;
            block["blockX"] = beg->BlockX;
            block["blockY"] = beg->BlockY;
            child_textures.PushBack(std::move(block));
        }
        contentN["child_textures"] = std::move(child_textures);
        const std::string contentNStr = contentN.FormatToJson();

        cross::SerializeNode metaHeader;
        {
            metaHeader["Version"] = 5;

            if (mGUID.empty())
            {
                metaHeader["GuidL"] = 0;
                metaHeader["GuidH"] = 0;
            }
            else
            {
                metaHeader["Guid"] = mGUID;
            }

            metaHeader["ClassID"] = ClassID(TextureUDIM);
            metaHeader["DataSize"] = static_cast<SInt32>(contentNStr.length());
            metaHeader["ContentType"] = static_cast<UInt32>(cross::CONTENT_TYPE::CONTENT_TYPE_JSON);

            // cross::SerializeNode dependenciesNode;
            // metaHeader["Dependency"] = std::move(dependenciesNode);
        }
        const std::string headerStr = metaHeader.FormatToJson();

        std::stringstream ss;
        UInt32 magicNum = ASSET_MAGIC_NUMBER_JMETA;
        ss.write(reinterpret_cast<char*>(&magicNum), sizeof(magicNum));
        ss << '\n';
        ss.write(headerStr.c_str(), headerStr.length());
        ss << '\n';
        ss.write(contentNStr.c_str(), contentNStr.length());

        std::filesystem::create_directories(mFile.parent_path());

        std::string ndaStr = ss.str();
        std::ofstream ostrm(mFile, std::ios::out | std::ios::binary);
        ostrm.write(ndaStr.c_str(), ndaStr.length());

        gAssetStreamingManager->LoadSynchronously(ndaStr);
    }
    else
    {
        cross::editor::TextureImporter::SerializeTexture2D(mTexture, mFile.string(), mImportSet);
    }

    mSaved = true;
}

}   // namespace CEAssetExchange
