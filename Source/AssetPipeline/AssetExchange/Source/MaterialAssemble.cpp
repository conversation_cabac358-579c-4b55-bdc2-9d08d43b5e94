#include <string>
#include <fstream>
#include <algorithm>
#include "PCH/CrossBasePCHPublic.h"
#include "TextureAssemble.h"
#include "MaterialAssemble.h"
#include <CrossBase/Serialization/SerializeNode.h>
#include <Resource/BaseClasses/ClassIDs.h>
#include <Resource/Fx.h>
#include <String/StringCodec.h>
#include <CrossBase/Serialization/ResourceMetaHeader.h>
#include "AssetPipeline/Interface/AssetPipeline.h"
#include <AssetPipeline/Import/AssetImporterManager.h>
#include <AssetPipeline/Import/ShaderImporter/ShaderImporter.h>
#include <CrossEngineSDK.h>

namespace CEAssetExchange {

std::string NormalizeUniformName(const char* Name)
{
    std::string Result = Name;
    auto _ = std::remove_if(Result.begin(), Result.end(), [](const unsigned char x) {
        if (x >= 'A' || x <= 'Z')
            return false;
        if (x >= 'a' || x <= 'z')
            return false;
        if (x >= '0' || x <= '9' || x == '_')
            return false;
        return x == ' ' || x == '-' || x == '\'' || x == '"' || x == ':' || x == '.' || x == '/' || x == '\\' || x == '+' || x == ',' || x == '(' || x == ')';
    });
    return Result;
}

std::string UniqueUniformName(std::set<std::string>& NameSet, std::string Name)
{
    if (NameSet.find(Name) == NameSet.end())
    {
        NameSet.insert(Name);
        return Name;
    }

    int Index = 0;
    auto Result = Name + std::to_string(Index);
    while (NameSet.find(Result) != NameSet.end())
    {
        Index++;
        Result = Name + std::to_string(Index);
    }

    NameSet.insert(Result);
    return Result;
}

FxAssemble::FxAssemble(std::filesystem::path& relative, std::filesystem::path& staging, std::string& guid)
    : Object(relative, staging, guid)
{
}

void FxAssemble::SaveToFile()
{
    if (mSaved)
        return;

    if (mFile.empty())
        return;

    if (!mOverwrite && std::filesystem::exists(mFile))
    {
        mSaved = true;
        return;
    }

    if (mType == Type::Unknown)
        return;

    auto filePath = mFile.string();

    if (mType == Type::Fx)
    {
        cesdk::resource::SDKFxResource fxResource{filePath};
        fxResource.Init(mJsonStr.c_str());
        fxResource.Serialize();

        cross::MaterialEditor materialEditor{};
        materialEditor.SetMaterialEditorCallback(&mCallback);
        materialEditor.Open(filePath.c_str());
        materialEditor.Apply(true);
    }
    else if (mType == Type::Material)
    {
        cesdk::resource::SDKMaterialResource materialResource{filePath};
        materialResource.Init(mJsonStr.c_str());
        materialResource.Serialize();

        cross::MaterialInstanceEditor materialInstance{materialResource.GetGuid()};
        materialInstance.OnPropertyChange();
    }
    else
    {
        cesdk::resource::SDKMaterialFunctionResource materialFunctionResource{filePath};
        materialFunctionResource.Init(mJsonStr.c_str());
        materialFunctionResource.Serialize();
    }

    mSaved = true;
}

const char* FxAssemble::GetCompileErrorMessage()
{
    return mCallback.mErrorMessage.c_str();
}

void FxAssemble::SetupFromJsonString(const char* jsonStr, Type type)
{
    mType = type;
    mJsonStr = jsonStr;
}

void FxAssemble::AddDependencies(const char* textureRP)
{
    mDependencies.emplace(textureRP);
}
}   // namespace CEAssetExchange
