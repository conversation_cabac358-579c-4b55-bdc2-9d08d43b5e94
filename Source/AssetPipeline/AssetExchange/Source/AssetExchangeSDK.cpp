
#include <map>
#include <string>
#include <fstream>
#include <sstream>

#include <chrono>

#include "Runtime/Interface/CrossEngineAddon.h"
static std::map<std::string, std::chrono::steady_clock::time_point> sTimepoint = {};
#define TICK(x)          sTimepoint["bench_" #x] = std::chrono::steady_clock::now();
#define TOCK(x)          std::cout << #x ": " << std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::steady_clock::now() - sTimepoint["bench_" #x]).count() << "s" << std::endl;
#define TICK_DURATION(x) (std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::steady_clock::now() - sTimepoint["bench_" #x]).count())

#include "AssetExchangeSDK.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "CrossBase/Threading/Task.h"
#include "CrossBase/Threading/TaskSystem.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "memory/Module.h"
#include "Resource/Material.h"
#include "Resource/MaterialFunction.h"
#include "Resource/MaterialParameterCollection.h"
#include "Resource/MaterialInterface.h"
#include "meta/reflection/builder/class_builder.inl"
IMPORT_MODULE
void MaterialBPRegister();
void CECommonRegister();
void CrossBaseRegister();
void ResourceRegister();
namespace CEAssetExchange {

static std::map<std::string, std::string> mGuidFiles = {};

const char* AssetExchangeSDK::GetStagingPath()
{
    return mStagingStr.c_str();
}
#define REGISTER_RUNTIME_RESOURCE_NEW(res, parent, ns) __append_register_cxx_type<ns::res>().custom_id(ClassID(res)).constructor<>();
AssetExchangeSDK::AssetExchangeSDK(const char* staging, const char* engineresourcepath)
    : mStaging{staging}
    , mEngineResourcePath(engineresourcepath) 
    , mStagingStr{staging}
    {
    }

    std::string TryGetGUID(const std::string& normPath, bool fallbackToRelativePath)
{
    auto& map = mGuidFiles;
    if (map.find(normPath) == map.end())
        return fallbackToRelativePath ? normPath : std::string{""};
    else
        return map[normPath];
}

std::string TryGetRefference(const std::string& normPath)
{
    return TryGetGUID(normPath, true);
}

void* AssetExchangeSDK::GetAssemble(const char* relativePath)
{
    std::string norm = relativePath;
    cross::PathHelper::Normalize(norm);
    return AssembleProxy::GetRetainAssemble(norm);
}

ITextureAssemble* AssetExchangeSDK::CreateTextureAssemble(const char* relativePath)
{
    DoAdaptiveWorkloads();
    EraseSavedAssemble();

    std::string norm = relativePath;
    cross::PathHelper::Normalize(norm);

    if (void* result = AssembleProxy::GetRetainAssemble(norm))
        return reinterpret_cast<ITextureAssemble*>(result);

    std::string guid = TryGetGUID(norm, false);

    if (guid.empty())
    {
        guid = cross::CrossUUID::GenerateCrossUUID().ToString();
        mGuidFiles.emplace(norm, guid);
    }

    std::filesystem::path normPath{norm};
    mTexture.emplace_back(std::make_unique<TextureAssemble>(normPath, mStaging, guid));
    mTextureProxy.emplace_back(std::make_unique<TextureAssembleProxy>(norm, mTexture.back().get()));
    mTexture.back()->mProxyHandle = mTextureProxy.back().get();
    mTexture.back()->mOverwrite = mSettings.mOverwriteExistingTexture;
    return mTextureProxy.back().get();
}

IFxAssemble* AssetExchangeSDK::CreateFxAssemble(const char* relativePath)
{
    DoAdaptiveWorkloads();
    EraseSavedAssemble();

    std::string norm = relativePath;
    cross::PathHelper::Normalize(norm);

    if (const void* result = AssembleProxy::GetRetainAssemble(norm))
        return reinterpret_cast<IFxAssemble*>(const_cast<void*>(result));

    std::string guid = TryGetGUID(norm, false);
    if (guid.empty())
    {
        guid = cross::CrossUUID::GenerateCrossUUID().ToString();
        mGuidFiles.emplace(norm, guid);
    }
    std::filesystem::path normPath{norm};
    mFx.emplace_back(std::make_unique<FxAssemble>(normPath, mStaging, guid));
    mFxProxy.emplace_back(std::make_unique<FxAssembleProxy>(norm, mFx.back().get()));
    mFx.back()->mProxyHandle = mFxProxy.back().get();
    mFx.back()->mOverwrite = mSettings.mOverwriteExistingMaterial;
    return mFxProxy.back().get();
}
IMeshAssemble* AssetExchangeSDK::CreateMeshAssemble(const char* relativePath)
{
    DoAdaptiveWorkloads();
    EraseSavedAssemble();

    std::string norm = relativePath;
    cross::PathHelper::Normalize(norm);

    if (const void* result = AssembleProxy::GetRetainAssemble(norm))
        return reinterpret_cast<IMeshAssemble*>(const_cast<void*>(result));

    std::string guid = TryGetGUID(norm, false);
    if (guid.empty())
    {
        guid = cross::CrossUUID::GenerateCrossUUID().ToString();
        mGuidFiles.emplace(norm, guid);
    }
    std::filesystem::path normPath{norm};
    mMesh.emplace_back(std::make_unique<MeshAssemble>(normPath, mStaging, guid));
    mMeshProxy.emplace_back(std::make_unique<MeshAssembleProxy>(norm, mMesh.back().get()));
    mMesh.back()->mProxyHandle = mMeshProxy.back().get();
    mMesh.back()->mOverwrite = mSettings.mOverwriteExistingMesh;
    return mMeshProxy.back().get();
}

IWorldAssemble* AssetExchangeSDK::CreateWorldAssemble(const char* relativePath)
{
    DoAdaptiveWorkloads();
    EraseSavedAssemble();

    std::string norm = relativePath;
    cross::PathHelper::Normalize(norm);

    if (const void* result = AssembleProxy::GetRetainAssemble(norm))
        return reinterpret_cast<IWorldAssemble*>(const_cast<void*>(result));

    std::string guid = TryGetGUID(norm, false);
    if (guid.empty())
    {
        guid = cross::CrossUUID::GenerateCrossUUID().ToString();
        mGuidFiles.emplace(norm, guid);
    }
    std::filesystem::path normPath{norm};
    mWorld.emplace_back(std::make_unique<WorldAssemble>(normPath, mStaging, guid));
    mWorldProxy.emplace_back(std::make_unique<WorldAssembleProxy>(norm, mWorld.back().get()));
    mWorld.back()->mProxyHandle = mWorldProxy.back().get();
    mWorld.back()->mOverwrite = mSettings.mOverwriteExistingWorld;
    return mWorldProxy.back().get();
}

IAssetExchangeSDK* CreateAssetExchangeSDK(const char* staging, const char* engineresourcePath)
{
    sTimepoint.clear();
    TICK(AssetExchangeSDK_Task);

    // try load guid list
    try
    {
        constexpr const char* CacheFileName = "ResourceList.json";
        std::string filePath = std::string{staging} + "/" + CacheFileName;
        if (cross::PathHelper::IsFileExist(filePath))
        {
            std::fstream file;
            file.open(filePath, std::ios_base::in | std::ios::binary);
            if (file.is_open())
            {
                auto ss = std::ostringstream{};
                ss << file.rdbuf();
                std::string fileContent = ss.str();
                cross::DeserializeNode rootNode = cross::DeserializeNode::ParseFromJson(fileContent);
                if (rootNode.IsObject())
                {
                    if (auto fileListNode = rootNode.HasMember("FileList"); fileListNode && fileListNode->IsObject())
                    {
                        for (auto it = fileListNode->begin(); it != fileListNode->end(); it++)
                        {
                            std::string rp = it.Value().AsString();
                            cross::PathHelper::Normalize(rp);
                            mGuidFiles.emplace(rp, it.Key().data());
                        }
                    }
                    else
                    {
                        for (auto it = rootNode.begin(); it != rootNode.end(); it++)
                        {
                            std::string rp = it.Value().AsString();
                            cross::PathHelper::Normalize(rp);
                            mGuidFiles.emplace(rp, it.Key().data());
                        }
                    }
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        std::cerr << e.what() << '\n';
    }

    return new AssetExchangeSDK(staging, engineresourcePath);
}

void AssetExchangeSDK::BeginAssemble(const SDKSettings* settings)
{
    if (settings)
        memcpy(&mSettings, settings, sizeof(SDKSettings));
}

void AssetExchangeSDK::AddResourceGUID(const char* relativePath, const char* GUID, bool overwrite)
{
    std::string rp = relativePath;
    cross::PathHelper::Normalize(rp);

    if (overwrite)
    {
        mGuidFiles.emplace(rp, std::string{GUID});
    }
    else
    {
        if (mGuidFiles.find(rp) == mGuidFiles.end())
            mGuidFiles.emplace(rp, std::string{GUID});
    }
}

void AssetExchangeSDK::EraseSavedAssemble()
{
    for (int i = static_cast<int>(mMesh.size()) - 1; i >= 0; i--)
    {
        if (mMesh[i]->mSaved)
        {
            reinterpret_cast<MeshAssembleProxy*>(mMesh[i]->mProxyHandle)->mAssemble = nullptr;
            mMesh.erase(mMesh.begin() + i);
        }
    }

    for (int i = static_cast<int>(mTexture.size()) - 1; i >= 0; i--)
    {
        if (mTexture[i]->mSaved)
        {
            reinterpret_cast<TextureAssembleProxy*>(mTexture[i]->mProxyHandle)->mAssemble = nullptr;
            mTexture.erase(mTexture.begin() + i);
        }
    }

    for (int i = static_cast<int>(mWorld.size()) - 1; i >= 0; i--)
    {
        if (mWorld[i]->mSaved)
        {
            reinterpret_cast<WorldAssembleProxy*>(mWorld[i]->mProxyHandle)->mAssemble = nullptr;
            mWorld.erase(mWorld.begin() + i);
        }
    }
}

void AssetExchangeSDK::FillWorkloadsArray(std::vector<Object*>& workloads)
{
    for (std::size_t i = 0, n = mWorld.size(); i < n; i++)
        if (mWorld[i]->mEndAssembled && !mWorld[i]->mSaved)
            workloads.emplace_back(mWorld[i].get());

    for (std::size_t i = 0, n = mMesh.size(); i < n; i++)
        if (mMesh[i]->mEndAssembled && !mMesh[i]->mSaved)
            workloads.emplace_back(mMesh[i].get());

    for (std::size_t i = 0, n = mTexture.size(); i < n; i++)
        if (mTexture[i]->mEndAssembled && !mTexture[i]->mSaved)
            workloads.emplace_back(mTexture[i].get());
}

void AssetExchangeSDK::DoAdaptiveWorkloads()
{
    if (!mSettings.mBalanceMode)
        return;

    std::vector<Object*> workloads;
    FillWorkloadsArray(workloads);

    const std::size_t jobCount = workloads.size();

    if (jobCount >= mSettings.mBalanceModeJobCount)
    {
        for (std::size_t i = 0, n = workloads.size(); i < n; i++)
            workloads[i]->Finish();
    }
}

void AssetExchangeSDK::Release(double* timeInSeconds)
{
    for (int i = 0; i < mMesh.size(); i++)
        mMesh[i]->SaveToFile();

    for (int i = 0; i < mTexture.size(); i++)
        mTexture[i]->SaveToFile();

    for (int i = 0; i < mWorld.size(); i++)
        mWorld[i]->SaveToFile();

    AssembleProxy::Release();

    delete this;

    TOCK(AssetExchangeSDK_Task);

    if (timeInSeconds)
        *timeInSeconds = TICK_DURATION(AssetExchangeSDK_Task);
}

}   // namespace CEAssetExchange
