#include "SDKComponent/SDKFogComponent.h"
#include "Components/FogComponent.h"

namespace cesdk {
    namespace fog {

        cross::Float3 Convert(::cross::Float3 v)
        {
            return { v.x, v.y, v.z };
        }

        cross::Float4 Convert(::cross::Float4 v)
        {
            return { v.x, v.y, v.z, v.w };
        }

        // ScreenFogDirectionalSetting conversion
        cross::ScreenFogDirectionalSetting Convert(const ::cross::ScreenFogDirectionalSetting& src) {
            cross::ScreenFogDirectionalSetting dst;
            dst.DirectionalInscatter = Convert(src.DirectionalInscatter);
            dst.AtomsphereContribution = src.AtomsphereContribution;
            dst.DirectionalIntensity = src.DirectionalIntensity;
            dst.InscatterExponent = src.InscatterExponent;
            dst.DirectionalStartDistance = src.DirectionalStartDistance;
            return dst;
        }

        // ScreenFogSetting conversion
        cross::ScreenFogSetting Convert(const ::cross::ScreenFogSetting& src) {
            cross::ScreenFogSetting dst;
            dst.MaxFogOpacity = src.MaxFogOpacity;
            dst.Inscatter = Convert(src.Inscatter);
            dst.InscatterSecond = Convert(src.InscatterSecond);
            dst.Directional = Convert(src.Directional);
            return dst;
        }

        // FogCommonSetting conversion
        cross::FogCommonSetting Convert(const ::cross::FogCommonSetting& src) {
            cross::FogCommonSetting dst;
            dst.Density = src.Density;
            dst.HeightFallOff = src.HeightFallOff;
            dst.HeightOffset = src.HeightOffset;
            dst.DensitySecond = src.DensitySecond;
            dst.HeightFallOffSecond = src.HeightFallOffSecond;
            dst.HeightOffsetSecond = src.HeightOffsetSecond;
            dst.StartDistance = src.StartDistance;
            dst.CutOffDistance = src.CutOffDistance;
            dst.UseWGS84 = src.UseWGS84;
            dst.CloudyAtomsphere = src.CloudyAtomsphere;
            return dst;
        }

        // VFogQualityTradeSetting conversion
        cross::VFogQualityTradeSetting Convert(const ::cross::VFogQualityTradeSetting& src) {
            cross::VFogQualityTradeSetting dst;
            dst.MaxLight = src.MaxLight;
            dst.MultiSampleNum = src.MultiSampleNum;
            dst.MultiSampleJitter = src.MultiSampleJitter;
            dst.UseBlur = src.UseBlur;
            dst.BlurSize = src.BlurSize;
            dst.BlurStrength = src.BlurStrength;
            dst.ResolutionRatio = src.ResolutionRatio;
            dst.ZSliceNum = src.ZSliceNum;
            dst.ZVoxelScale = src.ZVoxelScale;
            dst.Temporal = src.Temporal;
            dst.HistoryWeight = src.HistoryWeight;
            dst.LightInjection = src.LightInjection;
            dst.TurnOffDirectionalLight = src.TurnOffDirectionalLight;
            dst.CpuLightProjection = src.CpuLightProjection;
            return dst;
        }

        // VFogDustSetting conversion
        cross::VFogDustSetting Convert(const ::cross::VFogDustSetting& src) {
            cross::VFogDustSetting dst;
            dst.enable = src.enable;
            dst.DustAlbedo = Convert(src.DustAlbedo);
            dst.DustDensity = src.DustDensity;
            dst.DustScale = src.DustScale;
            dst.Height = src.Height;
            dst.Spiral = src.Spiral;
            dst.LightAbsorb = src.LightAbsorb;
            dst.Wind = Convert(src.Wind);
            dst.DustOverride = src.DustOverride;
            dst.SFogLightFactor = src.SFogLightFactor;
            return dst;
        }

        // VolumetricFogSetting conversion
        cross::VolumetricFogSetting Convert(const ::cross::VolumetricFogSetting& src) {
            cross::VolumetricFogSetting dst;
            dst.enable = src.enable;
            dst.StartDistance = src.StartDistance;
            dst.Albedo = cross::Float3(src.Albedo.x, src.Albedo.y, src.Albedo.z);
            dst.ExtinctionScale = src.ExtinctionScale;
            dst.AtomsphereFactor = src.AtomsphereFactor;
            dst.MiePhase1 = src.MiePhase1;
            dst.MiePhase2 = src.MiePhase2;
            dst.LightVolumetricFactor = src.LightVolumetricFactor;
            dst.BoundingFadeOut = src.BoundingFadeOut;
            dst.FadeOutDistance = src.FadeOutDistance;
            dst.CloudShadow = src.CloudShadow;
            dst.QualityTrade = Convert(src.QualityTrade);
            dst.Dust = Convert(src.Dust);
            return dst;
        }

        // FFSWeatherLightMaterialSetting conversion
        cross::FFSWeatherLightMaterialSetting Convert(const ::cross::FFSWeatherLightMaterialSetting& src) {
            cross::FFSWeatherLightMaterialSetting dst;
            dst.Scale_Size_Offset = src.Scale_Size_Offset;
            dst.Scale_Gain_Offset = src.Scale_Gain_Offset;
            dst.Intensity_Offset = src.Intensity_Offset;
            dst.Intensity_Gain_Offset = src.Intensity_Gain_Offset;
            return dst;
        }

        // FFSFogSetting conversion
        cross::FFSFogSetting Convert(const ::cross::FFSFogSetting& src) {
            cross::FFSFogSetting dst;
            dst.enable = src.enable;
            dst.RVR = src.RVR;
            dst.RVR_Min = src.RVR_Min;
            dst.RVR_Max = src.RVR_Max;
            dst.RVRFogTop = src.RVRFogTop;
            dst.FogBaseHeight = src.FogBaseHeight;
            dst.BlwCldVis = src.BlwCldVis;
            dst.BtmCldBase = src.BtmCldBase;
            dst.BtwCldVis = src.BtwCldVis;
            dst.MidCldBase = src.MidCldBase;
            dst.AbvCldVis = src.AbvCldVis;
            dst.FogTop = src.FogTop;
            dst.ClearFogProportion = src.ClearFogProportion;
            dst.VocanicAshModifier = src.VocanicAshModifier;
            dst.PatchyFogFactor = src.PatchyFogFactor;
            dst.StrobeDisExponent = src.StrobeDisExponent;
            dst.StrobeDisFallOff = src.StrobeDisFallOff;
            dst.RunWay_Light = Convert(src.RunWay_Light);
            dst.VASI_Light = Convert(src.VASI_Light);
            dst.ALS_Light = Convert(src.ALS_Light);
            dst.Strobe_Light = Convert(src.Strobe_Light);
            dst.Dir_Light = Convert(src.Dir_Light);
            dst.DirSurround_Light = Convert(src.DirSurround_Light);
            dst.OmniDir_Light = Convert(src.OmniDir_Light);
            dst.OmniDirSurround_Light = Convert(src.OmniDirSurround_Light);
            return dst;
        }

        // FogSetting conversion
        cross::FogSetting Convert(const ::cross::FogSetting& src) {
            cross::FogSetting dst;
            dst.enable = src.enable;
            dst.FogCommon = Convert(src.FogCommon);
            dst.SFog = Convert(src.SFog);
            dst.VFog = Convert(src.VFog);
            dst.ffsTempTest = Convert(src.ffsTempTest);
            return dst;
        }

        // Float3覆写
        void Overwrite(::cross::Float3& dst, const cross::Float3& src)
        {
            dst.x = src.x;
            dst.y = src.y;
            dst.z = src.z;
        }

        // Float4覆写
        void Overwrite(::cross::Float4& dst, const cross::Float4& src)
        {
            dst.x = src.x;
            dst.y = src.y;
            dst.z = src.z;
            dst.w = src.w;
        }

        // ScreenFogDirectionalSetting覆写
        void Overwrite(::cross::ScreenFogDirectionalSetting& dst, const cross::ScreenFogDirectionalSetting& src)
        {
            Overwrite(dst.DirectionalInscatter, src.DirectionalInscatter);
            dst.AtomsphereContribution = src.AtomsphereContribution;
            dst.DirectionalIntensity = src.DirectionalIntensity;
            dst.InscatterExponent = src.InscatterExponent;
            dst.DirectionalStartDistance = src.DirectionalStartDistance;
        }

        // ScreenFogSetting覆写
        void Overwrite(::cross::ScreenFogSetting& dst, const cross::ScreenFogSetting& src)
        {
            dst.MaxFogOpacity = src.MaxFogOpacity;
            Overwrite(dst.Inscatter, src.Inscatter);
            Overwrite(dst.InscatterSecond, src.InscatterSecond);
            Overwrite(dst.Directional, src.Directional);
        }

        // FogCommonSetting覆写
        void Overwrite(::cross::FogCommonSetting& dst, const cross::FogCommonSetting& src)
        {
            dst.Density = src.Density;
            dst.HeightFallOff = src.HeightFallOff;
            dst.HeightOffset = src.HeightOffset;
            dst.DensitySecond = src.DensitySecond;
            dst.HeightFallOffSecond = src.HeightFallOffSecond;
            dst.HeightOffsetSecond = src.HeightOffsetSecond;
            dst.StartDistance = src.StartDistance;
            dst.CutOffDistance = src.CutOffDistance;
            dst.UseWGS84 = src.UseWGS84;
            dst.CloudyAtomsphere = src.CloudyAtomsphere;
        }

        // VFogQualityTradeSetting覆写
        void Overwrite(::cross::VFogQualityTradeSetting& dst, const cross::VFogQualityTradeSetting& src)
        {
            dst.MaxLight = src.MaxLight;
            dst.MultiSampleNum = src.MultiSampleNum;
            dst.MultiSampleJitter = src.MultiSampleJitter;
            dst.UseBlur = src.UseBlur;
            dst.BlurSize = src.BlurSize;
            dst.BlurStrength = src.BlurStrength;
            dst.ResolutionRatio = src.ResolutionRatio;
            dst.ZSliceNum = src.ZSliceNum;
            dst.ZVoxelScale = src.ZVoxelScale;
            dst.Temporal = src.Temporal;
            dst.HistoryWeight = src.HistoryWeight;
            dst.LightInjection = src.LightInjection;
            dst.TurnOffDirectionalLight = src.TurnOffDirectionalLight;
            dst.CpuLightProjection = src.CpuLightProjection;
        }

        // VFogDustSetting覆写
        void Overwrite(::cross::VFogDustSetting& dst, const cross::VFogDustSetting& src)
        {
            dst.enable = src.enable;
            Overwrite(dst.DustAlbedo, src.DustAlbedo);
            dst.DustDensity = src.DustDensity;
            dst.DustScale = src.DustScale;
            dst.Height = src.Height;
            dst.Spiral = src.Spiral;
            dst.LightAbsorb = src.LightAbsorb;
            Overwrite(dst.Wind, src.Wind);
            dst.DustOverride = src.DustOverride;
            dst.SFogLightFactor = src.SFogLightFactor;
        }

        // VolumetricFogSetting覆写
        void Overwrite(::cross::VolumetricFogSetting& dst, const cross::VolumetricFogSetting& src)
        {
            dst.enable = src.enable;
            dst.StartDistance = src.StartDistance;
            dst.Albedo = ::cross::Float3(src.Albedo.x, src.Albedo.y, src.Albedo.z);
            dst.ExtinctionScale = src.ExtinctionScale;
            dst.AtomsphereFactor = src.AtomsphereFactor;
            dst.MiePhase1 = src.MiePhase1;
            dst.MiePhase2 = src.MiePhase2;
            dst.LightVolumetricFactor = src.LightVolumetricFactor;
            dst.BoundingFadeOut = src.BoundingFadeOut;
            dst.FadeOutDistance = src.FadeOutDistance;
            dst.CloudShadow = src.CloudShadow;
            Overwrite(dst.QualityTrade, src.QualityTrade);
            Overwrite(dst.Dust, src.Dust);
        }

        // FFSWeatherLightMaterialSetting覆写
        void Overwrite(::cross::FFSWeatherLightMaterialSetting& dst, const cross::FFSWeatherLightMaterialSetting& src)
        {
            dst.Scale_Size_Offset = src.Scale_Size_Offset;
            dst.Scale_Gain_Offset = src.Scale_Gain_Offset;
            dst.Intensity_Offset = src.Intensity_Offset;
            dst.Intensity_Gain_Offset = src.Intensity_Gain_Offset;
        }

        // FFSFogSetting覆写
        void Overwrite(::cross::FFSFogSetting& dst, const cross::FFSFogSetting& src)
        {
            dst.enable = src.enable;
            dst.RVR = src.RVR;
            dst.RVR_Min = src.RVR_Min;
            dst.RVR_Max = src.RVR_Max;
            dst.RVRFogTop = src.RVRFogTop;
            dst.FogBaseHeight = src.FogBaseHeight;
            dst.BlwCldVis = src.BlwCldVis;
            dst.BtmCldBase = src.BtmCldBase;
            dst.BtwCldVis = src.BtwCldVis;
            dst.MidCldBase = src.MidCldBase;
            dst.AbvCldVis = src.AbvCldVis;
            dst.FogTop = src.FogTop;
            dst.ClearFogProportion = src.ClearFogProportion;
            dst.VocanicAshModifier = src.VocanicAshModifier;
            dst.PatchyFogFactor = src.PatchyFogFactor;
            dst.StrobeDisExponent = src.StrobeDisExponent;
            dst.StrobeDisFallOff = src.StrobeDisFallOff;
            Overwrite(dst.RunWay_Light, src.RunWay_Light);
            Overwrite(dst.VASI_Light, src.VASI_Light);
            Overwrite(dst.ALS_Light, src.ALS_Light);
            Overwrite(dst.Strobe_Light, src.Strobe_Light);
            Overwrite(dst.Dir_Light, src.Dir_Light);
            Overwrite(dst.DirSurround_Light, src.DirSurround_Light);
            Overwrite(dst.OmniDir_Light, src.OmniDir_Light);
            Overwrite(dst.OmniDirSurround_Light, src.OmniDirSurround_Light);
        }

        // FogSetting覆写
        void Overwrite(::cross::FogSetting& dst, const cross::FogSetting& src)
        {
            dst.enable = src.enable;
            Overwrite(dst.FogCommon, src.FogCommon);
            Overwrite(dst.SFog, src.SFog);
            Overwrite(dst.VFog, src.VFog);
            Overwrite(dst.ffsTempTest, src.ffsTempTest);
        }
    }
    namespace cegf {
        static ::cegf::FogComponent* GetFogComponent(const SDKFogComponent* component)
        {
            if (!component || !component->componentInstance)
            {
                return nullptr;
            }

            return static_cast<::cegf::FogComponent*>(component->componentInstance);
        }

        cross::FogSetting SDKFogComponent::GetFogSettings() const
        {
            ::cegf::FogComponent* fogComponent = GetFogComponent(this);
            if (fogComponent)
            {
                ::cross::FogSetting FogSetting = fogComponent->GetFogSetting();
                return cesdk::fog::Convert(FogSetting);
            }
            return cross::FogSetting();
        }

        void SDKFogComponent::SetFogSettings(const cross::FogSetting& settings)
        {
            ::cegf::FogComponent* FogComponent = GetFogComponent(this);
            if (FogComponent)
            {
                ::cross::FogSetting FogSetting = FogComponent->GetFogSetting();
                cesdk::fog::Overwrite(FogSetting, settings);
                FogComponent->SetFogSetting(FogSetting);
            }
        }


    }
}