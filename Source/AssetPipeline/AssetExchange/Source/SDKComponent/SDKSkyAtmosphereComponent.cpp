#include "SDKComponent/SDKSkyAtmosphereComponent.h"
#include "GameFramework/Components/SkyAtmosphereComponent.h"
#include "Runtime/GameWorld/SkyAtmosphereSystemG.h"

namespace cesdk::cegf {

// Helper function to get the SkyAtmosphereComponent from a GameObjectComponent
static ::cegf::SkyAtmosphereComponent* GetSkyAtmosphereComponent(const SDKSkyAtmosphereComponent* component) 
{
    if (!component || !component->componentInstance)
    {
        return nullptr;
    }
    
    return static_cast<::cegf::SkyAtmosphereComponent*>(component->componentInstance);
}

// Convert engine SkyAtmosphereConfig to SDK SkyAtmosphereConfig
static cesdk::cross::SkyAtmosphereConfig ConvertToSDKConfig(const ::cross::SkyAtmosphereConfig& inConfig)
{
    static_assert(sizeof(::cross::SkyAtmosphereConfig) == sizeof(cesdk::cross::SkyAtmosphereConfig));
    cesdk::cross::SkyAtmosphereConfig sdkConfig;
    
    sdkConfig.MiePhase = inConfig.MiePhase;
    sdkConfig.MieScattCoeff.x = inConfig.MieScattCoeff.x;
    sdkConfig.MieScattCoeff.y = inConfig.MieScattCoeff.y;
    sdkConfig.MieScattCoeff.z = inConfig.MieScattCoeff.z;
    sdkConfig.MieScattScale = inConfig.MieScattScale;
    sdkConfig.MieAbsorCoeff.x = inConfig.MieAbsorCoeff.x;
    sdkConfig.MieAbsorCoeff.y = inConfig.MieAbsorCoeff.y;
    sdkConfig.MieAbsorCoeff.z = inConfig.MieAbsorCoeff.z;
    sdkConfig.MieAbsorScale = inConfig.MieAbsorScale;
    sdkConfig.RayScattCoeff.x = inConfig.RayScattCoeff.x;
    sdkConfig.RayScattCoeff.y = inConfig.RayScattCoeff.y;
    sdkConfig.RayScattCoeff.z = inConfig.RayScattCoeff.z;
    sdkConfig.RayScattScale = inConfig.RayScattScale;
    sdkConfig.AbsorptiCoeff.x = inConfig.AbsorptiCoeff.x;
    sdkConfig.AbsorptiCoeff.y = inConfig.AbsorptiCoeff.y;
    sdkConfig.AbsorptiCoeff.z = inConfig.AbsorptiCoeff.z;
    sdkConfig.AbsorptiScale = inConfig.AbsorptiScale;
    sdkConfig.PlanetRadius = inConfig.PlanetRadius;
    sdkConfig.AtmosHeight = inConfig.AtmosHeight;
    sdkConfig.MieScaleHeight = inConfig.MieScaleHeight;
    sdkConfig.RayScaleHeight = inConfig.RayScaleHeight;
    sdkConfig.GroundAlbedo3.x = inConfig.GroundAlbedo3.x;
    sdkConfig.GroundAlbedo3.y = inConfig.GroundAlbedo3.y;
    sdkConfig.GroundAlbedo3.z = inConfig.GroundAlbedo3.z;
    sdkConfig.SFogMieScattScale = inConfig.SFogMieScattScale;
    sdkConfig.SkyLuminanceFactor = {inConfig.SkyLuminanceFactor.x, inConfig.SkyLuminanceFactor.y, inConfig.SkyLuminanceFactor.z};
    sdkConfig.MultiScatteringFactor = inConfig.MultiScatteringFactor;
    
    return sdkConfig;
}

// Convert SDK SkyAtmosphereConfig to engine SkyAtmosphereConfig
static ::cross::SkyAtmosphereConfig ConvertToEngineConfig(const cesdk::cross::SkyAtmosphereConfig& sdkConfig)
{
    ::cross::SkyAtmosphereConfig inConfig;
    
    inConfig.MiePhase = sdkConfig.MiePhase;
    inConfig.MieScattCoeff.x = sdkConfig.MieScattCoeff.x;
    inConfig.MieScattCoeff.y = sdkConfig.MieScattCoeff.y;
    inConfig.MieScattCoeff.z = sdkConfig.MieScattCoeff.z;
    inConfig.MieScattScale = sdkConfig.MieScattScale;
    inConfig.MieAbsorCoeff.x = sdkConfig.MieAbsorCoeff.x;
    inConfig.MieAbsorCoeff.y = sdkConfig.MieAbsorCoeff.y;
    inConfig.MieAbsorCoeff.z = sdkConfig.MieAbsorCoeff.z;
    inConfig.MieAbsorScale = sdkConfig.MieAbsorScale;
    inConfig.RayScattCoeff.x = sdkConfig.RayScattCoeff.x;
    inConfig.RayScattCoeff.y = sdkConfig.RayScattCoeff.y;
    inConfig.RayScattCoeff.z = sdkConfig.RayScattCoeff.z;
    inConfig.RayScattScale = sdkConfig.RayScattScale;
    inConfig.AbsorptiCoeff.x = sdkConfig.AbsorptiCoeff.x;
    inConfig.AbsorptiCoeff.y = sdkConfig.AbsorptiCoeff.y;
    inConfig.AbsorptiCoeff.z = sdkConfig.AbsorptiCoeff.z;
    inConfig.AbsorptiScale = sdkConfig.AbsorptiScale;
    inConfig.PlanetRadius = sdkConfig.PlanetRadius;
    inConfig.AtmosHeight = sdkConfig.AtmosHeight;
    inConfig.MieScaleHeight = sdkConfig.MieScaleHeight;
    inConfig.RayScaleHeight = sdkConfig.RayScaleHeight;
    inConfig.GroundAlbedo3.x = sdkConfig.GroundAlbedo3.x;
    inConfig.GroundAlbedo3.y = sdkConfig.GroundAlbedo3.y;
    inConfig.GroundAlbedo3.z = sdkConfig.GroundAlbedo3.z;
    inConfig.SFogMieScattScale = sdkConfig.SFogMieScattScale;
    inConfig.SkyLuminanceFactor = {sdkConfig.SkyLuminanceFactor.x, sdkConfig.SkyLuminanceFactor.y, sdkConfig.SkyLuminanceFactor.z};
    inConfig.MultiScatteringFactor = sdkConfig.MultiScatteringFactor;
    
    return inConfig;
}

// Convert engine SkyAtmosphereOuterParam to SDK SkyAtmosphereOuterParam
static cesdk::cross::SkyAtmosphereOuterParam ConvertToSDKOuterParam(const ::cross::SkyAtmosphereOuterParam& inParam)
{
    static_assert(sizeof(::cross::SkyAtmosphereOuterParam) == sizeof(cesdk::cross::SkyAtmosphereOuterParam));
    cesdk::cross::SkyAtmosphereOuterParam sdkParam;
    
    sdkParam.PlanetTopAtWorldOrigin = inParam.PlanetTopAtWorldOrigin;
    sdkParam.RenderSunDisk = inParam.RenderSunDisk;
    sdkParam.RenderMoonDisk = inParam.RenderMoonDisk;
    sdkParam.MoonPhase = inParam.MoonPhase;
    sdkParam.AerialPerspStartDepthKM = inParam.AerialPerspStartDepthKM;
    sdkParam.Exposure = inParam.Exposure;
    sdkParam.APScale = inParam.APScale;
    sdkParam.HighQualityAP = inParam.HighQualityAP;
    sdkParam.ColorTransmittance = inParam.ColorTransmittance;
    
    return sdkParam;
}

// Convert SDK SkyAtmosphereOuterParam to engine SkyAtmosphereOuterParam
static ::cross::SkyAtmosphereOuterParam ConvertToEngineOuterParam(const cesdk::cross::SkyAtmosphereOuterParam& sdkParam)
{
    ::cross::SkyAtmosphereOuterParam inParam;
    
    inParam.PlanetTopAtWorldOrigin = sdkParam.PlanetTopAtWorldOrigin;
    inParam.RenderSunDisk = sdkParam.RenderSunDisk;
    inParam.RenderMoonDisk = sdkParam.RenderMoonDisk;
    inParam.MoonPhase = sdkParam.MoonPhase;
    inParam.AerialPerspStartDepthKM = sdkParam.AerialPerspStartDepthKM;
    inParam.Exposure = sdkParam.Exposure;
    inParam.APScale = sdkParam.APScale;
    inParam.HighQualityAP = sdkParam.HighQualityAP;
    inParam.ColorTransmittance = sdkParam.ColorTransmittance;
    
    return inParam;
}

cesdk::cross::SkyAtmosphereConfig SDKSkyAtmosphereComponent::GetSkyAtmosphereConfig() const
{
    ::cegf::SkyAtmosphereComponent* skyAtmosphereComponent = GetSkyAtmosphereComponent(this);
    if (skyAtmosphereComponent)
    {
        return ConvertToSDKConfig(skyAtmosphereComponent->GetSkyAtmosphereConfig());
    }
    return {};
}

void SDKSkyAtmosphereComponent::SetSkyAtmosphereConfig(const cesdk::cross::SkyAtmosphereConfig& inConfig)
{
    ::cegf::SkyAtmosphereComponent* skyAtmosphereComponent = GetSkyAtmosphereComponent(this);
    if (skyAtmosphereComponent)
    {
        skyAtmosphereComponent->SetSkyAtmosphereConfig(ConvertToEngineConfig(inConfig));
    }
}

cesdk::cross::SkyAtmosphereOuterParam SDKSkyAtmosphereComponent::GetSkyAtmosphereOuterParam() const
{
    ::cegf::SkyAtmosphereComponent* skyAtmosphereComponent = GetSkyAtmosphereComponent(this);
    if (skyAtmosphereComponent)
    {
        return ConvertToSDKOuterParam(skyAtmosphereComponent->GetSkyAtmosphereOuterParam());
    }
    return {};
}

void SDKSkyAtmosphereComponent::SetSkyAtmosphereOuterParam(const cesdk::cross::SkyAtmosphereOuterParam& inParam)
{
    ::cegf::SkyAtmosphereComponent* skyAtmosphereComponent = GetSkyAtmosphereComponent(this);
    if (skyAtmosphereComponent)
    {
        skyAtmosphereComponent->SetSkyAtmosphereOuterParam(ConvertToEngineOuterParam(inParam));
    }
}

} // namespace cesdk::cegf
