#include "SDKComponent/SDKPostProcessVolumeComponent.h"
#include "Components/PostProcessVolumeComponent.h"

namespace cesdk {
    namespace postprocess
    {
        cross::PostProcessVolumeType Convert(::cross::PostProcessVolumeType In)
        {
            switch (In)
            {

            case ::cross::PostProcessVolumeType::Global:
                return cross::PostProcessVolumeType::Global;
                break;
            case ::cross::PostProcessVolumeType::Local:
                return cross::PostProcessVolumeType::Local;
                break;
            case ::cross::PostProcessVolumeType::LocalLerpOnly:
                return cross::PostProcessVolumeType::LocalLerpOnly;
                break;
            }
            return cross::PostProcessVolumeType::Global;
        }

        // InfluenceType enum conversion
        cross::InfluenceType Convert(const ::cross::InfluenceType& src) {
            switch (src)
            {

            case ::cross::InfluenceType::RUN:
                return cross::InfluenceType::RUN;
                break;
            case ::cross::InfluenceType::WALK:
                return cross::InfluenceType::WALK;
                break;
            }
            return cross::InfluenceType::RUN;
        }

        cross::Float3 Convert(::cross::Float3 v)
        {
            return { v.x, v.y, v.z };
        }

        cross::Float4 Convert(::cross::Float4 v)
        {
            return { v.x, v.y, v.z, v.w };
        }

        // PostProcessBlendSetting conversion
        cross::PostProcessBlendSetting Convert(const ::cross::PostProcessBlendSetting& src) {
            cross::PostProcessBlendSetting dst;
            dst.Priority = src.Priority;
            dst.Weight = src.Weight;
            dst.Radius = src.Radius;
            return dst;
        }

        // PostProcessChromaticAberrationSetting conversion
        cross::PostProcessChromaticAberrationSetting Convert(const ::cross::PostProcessChromaticAberrationSetting& src) {
            cross::PostProcessChromaticAberrationSetting dst;
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.Range = src.Range;
            return dst;
        }

        // PostProcessVignetteSetting conversion
        cross::PostProcessVignetteSetting Convert(const ::cross::PostProcessVignetteSetting& src) {
            cross::PostProcessVignetteSetting dst;
            dst.enable = src.enable;
            dst.VignetteIntensity = src.VignetteIntensity;
            return dst;
        }

        // PostProcessWhiteBalanceSetting conversion
        cross::PostProcessWhiteBalanceSetting Convert(const ::cross::PostProcessWhiteBalanceSetting& src) {
            cross::PostProcessWhiteBalanceSetting dst;
            dst.EnableWhiteBalance = src.EnableWhiteBalance;
            dst.WhiteTemperature = src.WhiteTemperature;
            dst.WhiteTint = src.WhiteTint;
            return dst;
        }

        // PostProcessColorAdjustmentSetting conversion
        cross::PostProcessColorAdjustmentSetting Convert(const ::cross::PostProcessColorAdjustmentSetting& src) {
            cross::PostProcessColorAdjustmentSetting dst;
            dst.Saturation = Convert(src.Saturation);
            dst.Contrast = Convert(src.Contrast);
            dst.Gamma = Convert(src.Gamma);
            dst.Gain = Convert(src.Gain);
            dst.Offset = Convert(src.Offset);
            return dst;
        }

        // PostProcessLinearColorAdjustmentSetting conversion
        cross::PostProcessLinearColorAdjustmentSetting Convert(const ::cross::PostProcessLinearColorAdjustmentSetting& src) {
            cross::PostProcessLinearColorAdjustmentSetting dst;
            dst.EnableLinearColorAdjustment = src.EnableLinearColorAdjustment;
            dst.Global = Convert(src.Global);
            dst.Shadows = Convert(src.Shadows);
            dst.Midtones = Convert(src.Midtones);
            dst.Highlights = Convert(src.Highlights);
            dst.ColorAdjustShadowsMax = src.ColorAdjustShadowsMax;
            dst.ColorAdjustHighlightsMin = src.ColorAdjustHighlightsMin;
            dst.ColorAdjustHighlightsMax = src.ColorAdjustHighlightsMax;
            return dst;
        }

        // PostProcessToneMappingSetting conversion
        cross::PostProcessToneMappingSetting Convert(const ::cross::PostProcessToneMappingSetting& src) {
            cross::PostProcessToneMappingSetting dst;
            dst.EnableToneMapping = src.EnableToneMapping;
            dst.FilmSlope = src.FilmSlope;
            dst.FilmToe = src.FilmToe;
            dst.FilmShoulder = src.FilmShoulder;
            dst.FilmBlackClip = src.FilmBlackClip;
            dst.FilmWhiteClip = src.FilmWhiteClip;
            dst.ToneCurveAmount = src.ToneCurveAmount;
            dst.OverlayColor = Convert(src.OverlayColor);
            dst.OverlayScale = Convert(src.OverlayScale);
            return dst;
        }

        // PostProcessColorHSLPhaseSetting conversion
        cross::PostProcessColorHSLPhaseSetting Convert(const ::cross::PostProcessColorHSLPhaseSetting& src) {
            cross::PostProcessColorHSLPhaseSetting dst;
            dst.Enable = src.Enable;
            dst.Red = src.Red;
            dst.Orange = src.Orange;
            dst.Yellow = src.Yellow;
            dst.Green = src.Green;
            dst.Aqua = src.Aqua;
            dst.Blue = src.Blue;
            dst.Purple = src.Purple;
            dst.Magenta = src.Magenta;
            return dst;
        }

        // PostProcessColorHSLSetting conversion
        cross::PostProcessColorHSLSetting Convert(const ::cross::PostProcessColorHSLSetting& src) {
            cross::PostProcessColorHSLSetting dst;
            dst.EnableSRGBAdjustment = src.EnableSRGBAdjustment;
            dst.GlobalLuminance = src.GlobalLuminance;
            dst.GlobalHueShift = src.GlobalHueShift;
            dst.GlobalSaturation = src.GlobalSaturation;
            dst.GlobalVibrance = src.GlobalVibrance;
            dst.sigma = src.sigma;
            dst.HueSensitivity = src.HueSensitivity;
            dst.Hue = Convert(src.Hue);
            dst.Saturation = Convert(src.Saturation);
            dst.Luminance = Convert(src.Luminance);
            return dst;
        }

        // PostProcessManuelLUTSetting conversion
        cross::PostProcessManuelLUTSetting Convert(const ::cross::PostProcessManuelLUTSetting& src) {
            cross::PostProcessManuelLUTSetting dst;
            dst.EnableLUT = src.EnableLUT;
            dst.LUT = nullptr; // src.LUT;
            return dst;
        }

        // TonemapSettings conversion
        cross::TonemapSettings Convert(const ::cross::TonemapSettings& src) {
            cross::TonemapSettings dst;
            dst.WhiteBalanceSetting = Convert(src.WhiteBalanceSetting);
            dst.LinearColorAdajustment = Convert(src.LinearColorAdajustment);
            dst.ACECureveSetting = Convert(src.ACECureveSetting);
            dst.HSLSetting = Convert(src.HSLSetting);
            dst.ManuelLUTSetting = Convert(src.ManuelLUTSetting);
            dst.UseSimpleTonemapping = src.UseSimpleTonemapping;
            return dst;
        }

        // PostProcessHistogramExposureSetting conversion
        cross::PostProcessHistogramExposureSetting Convert(const ::cross::PostProcessHistogramExposureSetting& src) {
            cross::PostProcessHistogramExposureSetting dst;
            dst.enable = src.enable;
            dst.MinBrightness = src.MinBrightness;
            dst.MaxBrightness = src.MaxBrightness;
            dst.SpeedUp = src.SpeedUp;
            dst.SpeedDown = src.SpeedDown;
            dst.AutoExposureBias = src.AutoExposureBias;
            dst.HistogramLogMin = src.HistogramLogMin;
            dst.HistogramLogMax = src.HistogramLogMax;
            dst.HighPercent = src.HighPercent;
            dst.LowPercent = src.LowPercent;
            return dst;
        }

        // ManualExposureSetting conversion
        cross::ManualExposureSetting Convert(const ::cross::ManualExposureSetting& src) {
            cross::ManualExposureSetting dst;
            dst.enable = src.enable;
            dst.Exposure = src.Exposure;
            return dst;
        }

        // ExposureSettings conversion
        cross::PostProcessExposureSetting Convert(const ::cross::PostProcessExposureSetting& src)
        {
            cross::PostProcessExposureSetting dst;
            dst.enable = src.enable;
            dst.mExposureType = static_cast<cross::ExposureType>(src.mExposureType);
            dst.mHistogramExposureSettings = Convert(src.mHistogramExposureSettings);
            dst.mManualExposureSettings = Convert(src.mManualExposureSettings);
            return dst;
        }

        // PostProcessLocalExposureSetting conversion
        cross::PostProcessLocalExposureSetting Convert(const ::cross::PostProcessLocalExposureSetting& src) {
            cross::PostProcessLocalExposureSetting dst;
            dst.enable = src.enable;
            dst.HighlightContrast = src.HighlightContrast;
            dst.ShadowContrast = src.ShadowContrast;
            dst.HighlightThreshold = src.HighlightThreshold;
            dst.ShadowThreshold = src.ShadowThreshold;
            dst.DetailStrength = src.DetailStrength;
            dst.BlurredLuminanceBlend = src.BlurredLuminanceBlend;
            dst.MiddleGreyBias = src.MiddleGreyBias;
            return dst;
        }

        // PostProcessLensFlareSetting conversion
        cross::PostProcessLensFlareSetting Convert(const ::cross::PostProcessLensFlareSetting& src) {
            cross::PostProcessLensFlareSetting dst;
            dst.enable = src.enable;
            dst.Size = src.Size;
            dst.Intesnity = src.Intesnity;
            dst.Threshold = src.Threshold;
            return dst;
        }

        // PostProcessBloomSetting conversion
        cross::PostProcessBloomSetting Convert(const ::cross::PostProcessBloomSetting& src) {
            cross::PostProcessBloomSetting dst;
            dst.enable = src.enable;
            dst.BloomThreshold = src.BloomThreshold;
            dst.BloomIntensity = src.BloomIntensity;
            dst.BloomTint = Convert(src.BloomTint);
            dst.BloomScale = src.BloomScale;
            dst.BloomLuminanceClamp = src.BloomLuminanceClamp;
            return dst;
        }

        // DepthOfFieldSetting conversion
        cross::DepthOfFieldSetting Convert(const ::cross::DepthOfFieldSetting& src) {
            cross::DepthOfFieldSetting dst;
            dst.enable = src.enable;
            dst.BlurRadius = src.BlurRadius;
            dst.FocalLength = src.FocalLength;
            dst.Aperture = src.Aperture;
            dst.FocusDistance = src.FocusDistance;
            dst.DOFAutoFocus = src.DOFAutoFocus;
            dst.FixedFocusDistance = src.FixedFocusDistance;
            dst.AutoInfluenceType = Convert(src.AutoInfluenceType);
            return dst;
        }

        // MotionBlurSetting conversion
        cross::MotionBlurSetting Convert(const ::cross::MotionBlurSetting& src) {
            cross::MotionBlurSetting dst;
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.MaxDistortion = src.MaxDistortion;
            dst.TargetFPS = src.TargetFPS;
            return dst;
        }

        // ScreenBlurSetting conversion
        cross::ScreenBlurSetting Convert(const ::cross::ScreenBlurSetting& src) {
            cross::ScreenBlurSetting dst;
            dst.enable = src.enable;
            dst.BlurRadius = src.BlurRadius;
            dst.BlurStrength = src.BlurStrength;
            dst.BlurClipThreshold = src.BlurClipThreshold;
            return dst;
        }

        // PostProcessWindSetting conversion
        cross::PostProcessWindSetting Convert(const ::cross::PostProcessWindSetting& src) {
            cross::PostProcessWindSetting dst;
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.Direction = Convert(src.Direction);
            dst.Speed = src.Speed;
            return dst;
        }

        // PostProcessRainSetting conversion
        cross::PostProcessRainSetting Convert(const ::cross::PostProcessRainSetting& src) {
            cross::PostProcessRainSetting dst;
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.Speed = src.Speed;
            return dst;
        }

        // PostProcessSnowSetting conversion
        cross::PostProcessSnowSetting Convert(const ::cross::PostProcessSnowSetting& src) {
            cross::PostProcessSnowSetting dst;
            dst.enable = src.enable;
            return dst;
        }

        // PostProcessVolumeSetting conversion
        cross::PostProcessVolumeSetting Convert(const ::cross::PostProcessVolumeSetting& src) {
            cross::PostProcessVolumeSetting dst;
            dst.mType = Convert(src.mType);
            dst.BlendSettings = Convert(src.BlendSettings);
            dst.ChromaticAberration = Convert(src.ChromaticAberration);
            dst.Vignette = Convert(src.Vignette);
            dst.mTonemapSettings = Convert(src.mTonemapSettings);
            dst.mPostProcessExposureSetting = Convert(src.mPostProcessExposureSetting);
            dst.mPostProcessLocalExposureSetting = Convert(src.mPostProcessLocalExposureSetting);
            dst.mPostProcessLensFlareSetting = Convert(src.mPostProcessLensFlareSetting);
            dst.mPostProcessBloomSetting = Convert(src.mPostProcessBloomSetting);
            dst.mDepthOfFieldSetting = Convert(src.mDepthOfFieldSetting);
            dst.mMotionBlurSetting = Convert(src.mMotionBlurSetting);
            dst.mScreenBlurSetting = Convert(src.mScreenBlurSetting);
            dst.mPostProcessWindSetting = Convert(src.mPostProcessWindSetting);
            dst.mPostProcessRainSetting = Convert(src.mPostProcessRainSetting);
            dst.mPostProcessSnowSetting = Convert(src.mPostProcessSnowSetting);
            return dst;
        }

        // 枚举类型的覆写函数
        void Overwrite(::cross::PostProcessVolumeType& dst, const cross::PostProcessVolumeType& src)
        {
            switch (src)
            {
            case cross::PostProcessVolumeType::Global:
                dst = ::cross::PostProcessVolumeType::Global;
                break;
            case cross::PostProcessVolumeType::Local:
                dst = ::cross::PostProcessVolumeType::Local;
                break;
            case cross::PostProcessVolumeType::LocalLerpOnly:
                dst = ::cross::PostProcessVolumeType::LocalLerpOnly;
                break;
            }
        }

        // InfluenceType枚举覆写
        void Overwrite(::cross::InfluenceType& dst, const cross::InfluenceType& src)
        {
            switch (src)
            {
            case cross::InfluenceType::RUN:
                dst = ::cross::InfluenceType::RUN;
                break;
            case cross::InfluenceType::WALK:
                dst = ::cross::InfluenceType::WALK;
                break;
            }
        }

        // Float3覆写
        void Overwrite(::cross::Float3& dst, const cross::Float3& src)
        {
            dst.x = src.x;
            dst.y = src.y;
            dst.z = src.z;
        }

        // Float4覆写
        void Overwrite(::cross::Float4& dst, const cross::Float4& src)
        {
            dst.x = src.x;
            dst.y = src.y;
            dst.z = src.z;
            dst.w = src.w;
        }

        // PostProcessBlendSetting覆写
        void Overwrite(::cross::PostProcessBlendSetting& dst, const cross::PostProcessBlendSetting& src)
        {
            dst.Priority = src.Priority;
            dst.Weight = src.Weight;
            dst.Radius = src.Radius;
        }


        // PostProcessChromaticAberrationSetting覆写
        void Overwrite(::cross::PostProcessChromaticAberrationSetting& dst, const cross::PostProcessChromaticAberrationSetting& src)
        {
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.Range = src.Range;
        }

        // PostProcessVignetteSetting覆写
        void Overwrite(::cross::PostProcessVignetteSetting& dst, const cross::PostProcessVignetteSetting& src)
        {
            dst.enable = src.enable;
            dst.VignetteIntensity = src.VignetteIntensity;
        }

        // PostProcessWhiteBalanceSetting覆写
        void Overwrite(::cross::PostProcessWhiteBalanceSetting& dst, const cross::PostProcessWhiteBalanceSetting& src)
        {
            dst.EnableWhiteBalance = src.EnableWhiteBalance;
            dst.WhiteTemperature = src.WhiteTemperature;
            dst.WhiteTint = src.WhiteTint;
        }

        // PostProcessColorAdjustmentSetting覆写
        void Overwrite(::cross::PostProcessColorAdjustmentSetting& dst, const cross::PostProcessColorAdjustmentSetting& src)
        {
            Overwrite(dst.Saturation, src.Saturation);
            Overwrite(dst.Contrast, src.Contrast);
            Overwrite(dst.Gamma, src.Gamma);
            Overwrite(dst.Gain, src.Gain);
            Overwrite(dst.Offset, src.Offset);
        }

        // PostProcessLinearColorAdjustmentSetting覆写
        void Overwrite(::cross::PostProcessLinearColorAdjustmentSetting& dst, const cross::PostProcessLinearColorAdjustmentSetting& src)
        {
            dst.EnableLinearColorAdjustment = src.EnableLinearColorAdjustment;
            Overwrite(dst.Global, src.Global);
            Overwrite(dst.Shadows, src.Shadows);
            Overwrite(dst.Midtones, src.Midtones);
            Overwrite(dst.Highlights, src.Highlights);
            dst.ColorAdjustShadowsMax = src.ColorAdjustShadowsMax;
            dst.ColorAdjustHighlightsMin = src.ColorAdjustHighlightsMin;
            dst.ColorAdjustHighlightsMax = src.ColorAdjustHighlightsMax;
        }

        // PostProcessToneMappingSetting覆写
        void Overwrite(::cross::PostProcessToneMappingSetting& dst, const cross::PostProcessToneMappingSetting& src)
        {
            dst.EnableToneMapping = src.EnableToneMapping;
            dst.FilmSlope = src.FilmSlope;
            dst.FilmToe = src.FilmToe;
            dst.FilmShoulder = src.FilmShoulder;
            dst.FilmBlackClip = src.FilmBlackClip;
            dst.FilmWhiteClip = src.FilmWhiteClip;
            dst.ToneCurveAmount = src.ToneCurveAmount;
            Overwrite(dst.OverlayColor, src.OverlayColor);
            Overwrite(dst.OverlayScale, src.OverlayScale);
        }

        // PostProcessColorHSLPhaseSetting覆写
        void Overwrite(::cross::PostProcessColorHSLPhaseSetting& dst, const cross::PostProcessColorHSLPhaseSetting& src)
        {
            dst.Enable = src.Enable;
            dst.Red = src.Red;
            dst.Orange = src.Orange;
            dst.Yellow = src.Yellow;
            dst.Green = src.Green;
            dst.Aqua = src.Aqua;
            dst.Blue = src.Blue;
            dst.Purple = src.Purple;
            dst.Magenta = src.Magenta;
        }

        // PostProcessColorHSLSetting覆写
        void Overwrite(::cross::PostProcessColorHSLSetting& dst, const cross::PostProcessColorHSLSetting& src)
        {
            dst.EnableSRGBAdjustment = src.EnableSRGBAdjustment;
            dst.GlobalLuminance = src.GlobalLuminance;
            dst.GlobalHueShift = src.GlobalHueShift;
            dst.GlobalSaturation = src.GlobalSaturation;
            dst.GlobalVibrance = src.GlobalVibrance;
            dst.sigma = src.sigma;
            dst.HueSensitivity = src.HueSensitivity;
            Overwrite(dst.Hue, src.Hue);
            Overwrite(dst.Saturation, src.Saturation);
            Overwrite(dst.Luminance, src.Luminance);
        }

        // PostProcessManuelLUTSetting覆写
        void Overwrite(::cross::PostProcessManuelLUTSetting& dst, const cross::PostProcessManuelLUTSetting& src)
        {
            dst.EnableLUT = src.EnableLUT;
            dst.LUT = src.LUT ? src.LUT : dst.LUT;
        }

        // TonemapSettings覆写
        void Overwrite(::cross::TonemapSettings& dst, const cross::TonemapSettings& src)
        {
            Overwrite(dst.WhiteBalanceSetting, src.WhiteBalanceSetting);
            Overwrite(dst.LinearColorAdajustment, src.LinearColorAdajustment);
            Overwrite(dst.ACECureveSetting, src.ACECureveSetting);
            Overwrite(dst.HSLSetting, src.HSLSetting);
            Overwrite(dst.ManuelLUTSetting, src.ManuelLUTSetting);
            dst.UseSimpleTonemapping = src.UseSimpleTonemapping;
        }

        // PostProcessHistogramExposureSetting覆写
        void Overwrite(::cross::PostProcessHistogramExposureSetting& dst, const cross::PostProcessHistogramExposureSetting& src)
        {
            dst.enable = src.enable;
            dst.MinBrightness = src.MinBrightness;
            dst.MaxBrightness = src.MaxBrightness;
            dst.SpeedUp = src.SpeedUp;
            dst.SpeedDown = src.SpeedDown;
            dst.AutoExposureBias = src.AutoExposureBias;
            dst.HistogramLogMin = src.HistogramLogMin;
            dst.HistogramLogMax = src.HistogramLogMax;
            dst.HighPercent = src.HighPercent;
            dst.LowPercent = src.LowPercent;
        }

        // ManualExposureSetting覆写
        void Overwrite(::cross::ManualExposureSetting& dst, const cross::ManualExposureSetting& src)
        {
            dst.enable = src.enable;
            dst.Exposure = src.Exposure;
        }

        // PostProcessExposureSetting覆写
        void Overwrite(::cross::PostProcessExposureSetting& dst, const cross::PostProcessExposureSetting& src)
        {
            dst.enable = src.enable;
            dst.mExposureType = static_cast<::cross::ExposureType>(src.mExposureType);
            Overwrite(dst.mHistogramExposureSettings, src.mHistogramExposureSettings);
            Overwrite(dst.mManualExposureSettings, src.mManualExposureSettings);
        }
        
        // PostProcessLocalExposureSetting覆写
        void Overwrite(::cross::PostProcessLocalExposureSetting& dst, const cross::PostProcessLocalExposureSetting& src)
        {
            dst.enable = src.enable;
            dst.HighlightContrast = src.HighlightContrast;
            dst.ShadowContrast = src.ShadowContrast;
            dst.HighlightThreshold = src.HighlightThreshold;
            dst.ShadowThreshold = src.ShadowThreshold;
            dst.DetailStrength = src.DetailStrength;
            dst.BlurredLuminanceBlend = src.BlurredLuminanceBlend;
            dst.MiddleGreyBias = src.MiddleGreyBias;
        }

        // PostProcessLensFlareSetting覆写
        void Overwrite(::cross::PostProcessLensFlareSetting& dst, const cross::PostProcessLensFlareSetting& src)
        {
            dst.enable = src.enable;
            dst.Size = src.Size;
            dst.Intesnity = src.Intesnity;
            dst.Threshold = src.Threshold;
        }

        // PostProcessBloomSetting覆写
        void Overwrite(::cross::PostProcessBloomSetting& dst, const cross::PostProcessBloomSetting& src)
        {
            dst.enable = src.enable;
            dst.BloomThreshold = src.BloomThreshold;
            dst.BloomIntensity = src.BloomIntensity;
            Overwrite(dst.BloomTint, src.BloomTint);
            dst.BloomScale = src.BloomScale;
            dst.BloomLuminanceClamp = src.BloomLuminanceClamp;
        }

        // DepthOfFieldSetting覆写
        void Overwrite(::cross::DepthOfFieldSetting& dst, const cross::DepthOfFieldSetting& src)
        {
            dst.enable = src.enable;
            dst.BlurRadius = src.BlurRadius;
            dst.FocalLength = src.FocalLength;
            dst.Aperture = src.Aperture;
            dst.FocusDistance = src.FocusDistance;
            dst.DOFAutoFocus = src.DOFAutoFocus;
            dst.FixedFocusDistance = src.FixedFocusDistance;
            Overwrite(dst.AutoInfluenceType, src.AutoInfluenceType);
        }

        // MotionBlurSetting覆写
        void Overwrite(::cross::MotionBlurSetting& dst, const cross::MotionBlurSetting& src)
        {
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.MaxDistortion = src.MaxDistortion;
            dst.TargetFPS = src.TargetFPS;
        }

        // ScreenBlurSetting覆写
        void Overwrite(::cross::ScreenBlurSetting& dst, const cross::ScreenBlurSetting& src)
        {
            dst.enable = src.enable;
            dst.BlurRadius = src.BlurRadius;
            dst.BlurStrength = src.BlurStrength;
            dst.BlurClipThreshold = src.BlurClipThreshold;
        }

        // PostProcessWindSetting覆写
        void Overwrite(::cross::PostProcessWindSetting& dst, const cross::PostProcessWindSetting& src)
        {
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            Overwrite(dst.Direction, src.Direction);
            dst.Speed = src.Speed;
        }

        // PostProcessRainSetting覆写
        void Overwrite(::cross::PostProcessRainSetting& dst, const cross::PostProcessRainSetting& src)
        {
            dst.enable = src.enable;
            dst.Intensity = src.Intensity;
            dst.Speed = src.Speed;
        }

        // PostProcessSnowSetting覆写
        void Overwrite(::cross::PostProcessSnowSetting& dst, const cross::PostProcessSnowSetting& src)
        {
            dst.enable = src.enable;
        }

        // PostProcessVolumeSetting覆写
        void Overwrite(::cross::PostProcessVolumeSetting& dst, const cross::PostProcessVolumeSetting& src)
        {
            Overwrite(dst.mType, src.mType);
            Overwrite(dst.BlendSettings, src.BlendSettings);
            Overwrite(dst.ChromaticAberration, src.ChromaticAberration);
            Overwrite(dst.Vignette, src.Vignette);
            Overwrite(dst.mTonemapSettings, src.mTonemapSettings);
            Overwrite(dst.mPostProcessExposureSetting, src.mPostProcessExposureSetting);
            Overwrite(dst.mPostProcessLocalExposureSetting, src.mPostProcessLocalExposureSetting);
            Overwrite(dst.mPostProcessLensFlareSetting, src.mPostProcessLensFlareSetting);
            Overwrite(dst.mPostProcessBloomSetting, src.mPostProcessBloomSetting);
            Overwrite(dst.mDepthOfFieldSetting, src.mDepthOfFieldSetting);
            Overwrite(dst.mMotionBlurSetting, src.mMotionBlurSetting);
            Overwrite(dst.mScreenBlurSetting, src.mScreenBlurSetting);
            Overwrite(dst.mPostProcessWindSetting, src.mPostProcessWindSetting);
            Overwrite(dst.mPostProcessRainSetting, src.mPostProcessRainSetting);
            Overwrite(dst.mPostProcessSnowSetting, src.mPostProcessSnowSetting);
        }
    }
}

namespace cesdk::cegf {
    static ::cegf::PostProcessVolumeComponent* GetPostProcessVolumeComponent(const SDKPostProcessVolumeComponent* component)
    {
        if (!component || !component->componentInstance)
        {
            return nullptr;
        }

        return static_cast<::cegf::PostProcessVolumeComponent*>(component->componentInstance);
    }

    cross::PostProcessVolumeSetting SDKPostProcessVolumeComponent::GetPostProcessVolumeSettings() const
    {
        ::cegf::PostProcessVolumeComponent* postProcessVolumeComponent = GetPostProcessVolumeComponent(this);
        if (postProcessVolumeComponent)
        {
            ::cross::PostProcessVolumeSetting postProcessVolumeSetting = postProcessVolumeComponent->GetPropertyPostProcessVolumeSettings();
            return cesdk::postprocess::Convert(postProcessVolumeSetting);
        }
        return cross::PostProcessVolumeSetting();
    }

    void SDKPostProcessVolumeComponent::SetPostProcessVolumeSettings(const cross::PostProcessVolumeSetting& settings)
    {
        ::cegf::PostProcessVolumeComponent* postProcessVolumeComponent = GetPostProcessVolumeComponent(this);
        if (postProcessVolumeComponent)
        {
            ::cross::PostProcessVolumeSetting postProcessVolumeSetting = postProcessVolumeComponent->GetPropertyPostProcessVolumeSettings();
            cesdk::postprocess::Overwrite(postProcessVolumeSetting, settings);
            postProcessVolumeComponent->SetPropertyPostProcessVolumeSettings(postProcessVolumeSetting);
        }
    }
} // namespace cesdk::cegf
