#pragma once
#include "../CrossEngineSDK.h"
namespace cesdk{ 
	namespace cegf {

        enum class CameraProjectionMode : std::uint8_t
        {
            Perspective = 0,
            Orthogonal = 1,
        };

        /**
         * @class SDKCameraComponent
         * @brief Wrapper class for engine CameraComponent
         *
         */
        class AssetExchange_API SDKCameraComponent : public SDKGameObjectComponent{
        public:
            /**
             * @brief Constructor for SDKLightComponent
             *
             * @param lightComponentInstance Pointer to the internal engine LightComponent instance
             */
            SDKCameraComponent(::cegf::GameObjectComponent* CameraComponentInstance)
                : SDKGameObjectComponent(CameraComponentInstance)
            {}

            cesdk::cegf::CameraProjectionMode GetProjectionMode() const;

            void SetProjectionMode(cesdk::cegf::CameraProjectionMode mode);

            void SetAspectRatio(float aspectRatio);

            float GetAspectRatio() const;

            bool GetCameraEnable() const;

            void SetCameraEnable(bool enable);

            void SetFocalLength(float focalLength);

            float GetFocalLength();

            // perspective mode settings
            void SetFOV(float fieldOfView);

            float GetFOV() const;

            // orthogonal mode settings
            float GetOrthoWidth() const;

            bool SetOrthogonalWidth(float width);

            float GetOrthoHeight() const;

            float GetOrthoNearPlane() const;

            float GetOrthoFarPlane() const;

            void SetOrthogonal(float width, float height, float nearDistance, float farDistance);

            bool SetOrthogonalHeight(float height);

            bool SetOrthogonalNear(float nearDistance);

            bool SetOrthogonalFar(float farDistance);

            bool SetAsMainCamera();

            bool SetPerspectiveNear(float nearDistance);
            
            float GetPerspectiveNear() const;

            bool SetPerspectiveFar(float farDistance);
            
            float GetPerspectiveFar() const;

            bool SetMinFocalLength(float minFocalLength);
            
            float GetMinFocalLength() const;

            bool SetMaxFocalLength(float maxFocalLength);
            
            float GetMaxFocalLength() const;

            bool SetSensorWidth(float sensorWidth);
            
            float GetSensorWidth() const;

            bool SetSensorHeight(float sensorHeight);
            
            float GetSensorHeight() const;
		};

	}
}