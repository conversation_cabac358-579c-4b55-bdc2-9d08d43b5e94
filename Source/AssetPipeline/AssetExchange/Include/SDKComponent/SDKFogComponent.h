#pragma once
#include "../CrossEngineSDK.h"

namespace cesdk {
    namespace cross {
        /**
         * @struct FogCommonSetting
         * @brief Common fog settings
         */
        struct FogCommonSetting {
            float Density;                        ///< Density
            float HeightFallOff;                  ///< Height fall off
            float HeightOffset;                   ///< Height offset
            float DensitySecond;                  ///< Secondary density
            float HeightFallOffSecond;            ///< Secondary height fall off
            float HeightOffsetSecond;             ///< Secondary height offset
            float StartDistance;                  ///< Start distance
            float CutOffDistance;                 ///< Cut off distance
            bool UseWGS84;                        ///< Use WGS84
            bool CloudyAtomsphere;                ///< Cloudy atmosphere
        };

        /**
         * @struct ScreenFogDirectionalSetting
         * @brief Screen directional fog settings
         */
        struct ScreenFogDirectionalSetting {
            Float3 DirectionalInscatter;          ///< Directional inscatter
            float AtomsphereContribution;         ///< Atmosphere contribution
            float DirectionalIntensity;           ///< Directional intensity
            float InscatterExponent;              ///< Inscatter exponent
            float DirectionalStartDistance;       ///< Directional start distance
        };

        /**
         * @struct ScreenFogSetting
         * @brief Screen fog settings
         */
        struct ScreenFogSetting {
            float MaxFogOpacity;                  ///< Max fog opacity
            Float3 Inscatter;                     ///< Inscatter
            Float3 InscatterSecond;               ///< Secondary inscatter
            ScreenFogDirectionalSetting Directional; ///< Directional settings
        };


        /**
         * @struct VFogQualityTradeSetting
         * @brief Volumetric fog quality trade settings
         */
        struct VFogQualityTradeSetting {
            int MaxLight;                       ///< Max light
            float MultiSampleNum;                   ///< Multi-sample number
            float MultiSampleJitter;              ///< Multi-sample jitter
            bool UseBlur;                         ///< Use blur
            float BlurSize;                       ///< Blur size
            float BlurStrength;                   ///< Blur strength
            float ResolutionRatio;                ///< Resolution ratio
            float ZSliceNum;                        ///< Z slice number
            float ZVoxelScale;                    ///< Z voxel scale
            bool Temporal;                        ///< Temporal
            float HistoryWeight;                  ///< History weight
            bool LightInjection;                  ///< Light injection
            bool TurnOffDirectionalLight;         ///< Turn off directional light
            bool CpuLightProjection;              ///< CPU light projection
        };

        /**
         * @struct VFogDustSetting
         * @brief Volumetric fog dust settings
         */
        struct VFogDustSetting {
            bool enable;                          ///< Enable
            Float3 DustAlbedo;                    ///< Dust albedo
            float DustDensity;                    ///< Dust density
            float DustScale;                      ///< Dust scale
            float Height;                         ///< Height
            float Spiral;                         ///< Spiral
            float LightAbsorb;                    ///< Light absorption
            Float3 Wind;                          ///< Wind
            bool DustOverride;                    ///< Dust override
            float SFogLightFactor;                ///< Screen fog light factor
        };

        /**
         * @struct VolumetricFogSetting
         * @brief Volumetric fog settings
         */
        struct VolumetricFogSetting {
            bool enable;
            float StartDistance;
            Float3 Albedo;
            float ExtinctionScale;                ///< Extinction scale
            float AtomsphereFactor;               ///< Atmosphere factor
            float MiePhase1;                      ///< Mie phase 1
            float MiePhase2;                      ///< Mie phase 2
            float LightVolumetricFactor;          ///< Light volumetric factor
            bool BoundingFadeOut;                 ///< Bounding fade out
            float FadeOutDistance;                ///< Fade out distance
            bool CloudShadow;                     ///< Cloud shadow
            VFogQualityTradeSetting QualityTrade; ///< Quality trade settings
            VFogDustSetting Dust;                 ///< Dust settings
        };



        /**
         * @struct FFSWeatherLightMaterialSetting
         * @brief FFS weather light material settings
         */
        struct FFSWeatherLightMaterialSetting {
            float Scale_Size_Offset;
            float Scale_Gain_Offset;
            float Intensity_Offset;
            float Intensity_Gain_Offset;
        };

        /**
         * @struct FFSFogSetting
         * @brief FFS fog settings
         */
        struct FFSFogSetting {
            bool enable;                           ///< Enable
            float RVR;                             ///< Runway visibility range
            float RVR_Min;                         ///< Min runway visibility range
            float RVR_Max;                         ///< Max runway visibility range
            float RVRFogTop;                       ///< Runway visibility range fog top
            float FogBaseHeight;                   ///< Fog base height
            float BlwCldVis;                       ///< Below cloud visibility
            float BtmCldBase;                      ///< Bottom cloud base
            float BtwCldVis;                       ///< Between cloud visibility
            float MidCldBase;                      ///< Middle cloud base
            float AbvCldVis;                       ///< Above cloud visibility
            float FogTop;                          ///< Fog top
            float ClearFogProportion;              ///< Clear fog proportion
            float VocanicAshModifier;              ///< Volcanic ash modifier
            float PatchyFogFactor;                 ///< Patchy fog factor
            float StrobeDisExponent;               ///< Strobe distance exponent
            float StrobeDisFallOff;                ///< Strobe distance fall off
            FFSWeatherLightMaterialSetting RunWay_Light;        ///< Runway light
            FFSWeatherLightMaterialSetting VASI_Light;          ///< VASI light
            FFSWeatherLightMaterialSetting ALS_Light;           ///< ALS light
            FFSWeatherLightMaterialSetting Strobe_Light;        ///< Strobe light
            FFSWeatherLightMaterialSetting Dir_Light;           ///< Directional light
            FFSWeatherLightMaterialSetting DirSurround_Light;   ///< Directional surround light
            FFSWeatherLightMaterialSetting OmniDir_Light;       ///< Omni-directional light
            FFSWeatherLightMaterialSetting OmniDirSurround_Light; ///< Omni-directional surround light
        };

        /**
         * @struct FogSetting
         * @brief Fog settings
         */
        struct FogSetting {
            bool enable;
            FogCommonSetting FogCommon;            ///< Common fog settings
            ScreenFogSetting SFog;                 ///< Screen fog settings
            VolumetricFogSetting VFog;             ///< Volumetric fog settings
            FFSFogSetting ffsTempTest;             ///< FFS temporary test
        };
    }

    namespace cegf {

        /**
         * @class SDKFogComponent
         * @brief Wrapper class for engine FogComponent
         *
         * Provides an interface for manipulating post-process volume properties
         */
        class AssetExchange_API SDKFogComponent : public SDKGameObjectComponent {
        public:
            /**
             * @brief Constructor for SDKFogComponent
             *
             * @param FogComponentInstance Pointer to the internal engine FogComponent instance
             */
            SDKFogComponent(::cegf::GameObjectComponent* FogComponentInstance)
                : SDKGameObjectComponent(FogComponentInstance) {
            }

            /**
             * @brief Set post process volume settings
             *
             * @param settings Post process volume settings
             */
            void SetFogSettings(const cross::FogSetting& settings);

            /**
             * @brief Get post process volume settings
             *
             * @return cross::FogSetting Post process volume settings
             */
            cross::FogSetting GetFogSettings() const;
        };
    } // namespace cegf

}