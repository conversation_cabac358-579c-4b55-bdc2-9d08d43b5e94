#pragma once
#include "../CrossEngineSDK.h"

namespace cesdk {
    namespace cross {
        /**
         * @struct SkyAtmosphereConfig
         * @brief Sky atmosphere configuration structure
         */
        struct SkyAtmosphereConfig {
            float MiePhase;
            Float3 MieScattCoeff;
            float MieScattScale;
            Float3 MieAbsorCoeff;
            float MieAbsorScale;
            Float3 RayScattCoeff;
            float RayScattScale;
            Float3 AbsorptiCoeff;
            float AbsorptiScale;
            float PlanetRadius;
            float AtmosHeight;
            float MieScaleHeight;
            float RayScaleHeight;
            Float3 GroundAlbedo3;
            float SFogMieScattScale;
            Float3 SkyLuminanceFactor;
            float MultiScatteringFactor;
        };

        /**
         * @struct SkyAtmosphereOuterParam
         * @brief Sky atmosphere outer parameters structure
         */
        struct SkyAtmosphereOuterParam {
            bool PlanetTopAtWorldOrigin;
            bool RenderSunDisk;
            bool RenderMoonDisk;
            bool MoonPhase;
            float AerialPerspStartDepthKM;
            float Exposure;
            float APScale;
            bool HighQualityAP;
            float ColorTransmittance;
        };
    }

    namespace cegf {

        /**
         * @class SDKSkyAtmosphereComponent
         * @brief Wrapper class for engine SkyAtmosphereComponent
         *
         * Provides interface for manipulating sky atmosphere properties
         */
        class AssetExchange_API SDKSkyAtmosphereComponent : public SDKGameObjectComponent {
        public:
            /**
             * @brief Constructor for SDKSkyAtmosphereComponent
             *
             * @param skyAtmosphereComponentInstance Pointer to the internal engine SkyAtmosphereComponent instance
             */
            SDKSkyAtmosphereComponent(::cegf::GameObjectComponent* skyAtmosphereComponentInstance)
                : SDKGameObjectComponent(skyAtmosphereComponentInstance) {
            }

            /**
             * @brief Gets the sky atmosphere configuration
             * 
             * @return cesdk::cross::SkyAtmosphereConfig Current sky atmosphere configuration
             */
            cesdk::cross::SkyAtmosphereConfig GetSkyAtmosphereConfig() const;

            /**
             * @brief Sets the sky atmosphere configuration
             * 
             * @param inConfig The sky atmosphere configuration to set
             */
            void SetSkyAtmosphereConfig(const cesdk::cross::SkyAtmosphereConfig& inConfig);
            
            /**
             * @brief Gets the sky atmosphere outer parameters
             * 
             * @return cesdk::cross::SkyAtmosphereOuterParam Current sky atmosphere outer parameters
             */
            cesdk::cross::SkyAtmosphereOuterParam GetSkyAtmosphereOuterParam() const;

            /**
             * @brief Sets the sky atmosphere outer parameters
             * 
             * @param inParam The sky atmosphere outer parameters to set
             */
            void SetSkyAtmosphereOuterParam(const cesdk::cross::SkyAtmosphereOuterParam& inParam);
        };
    } // namespace cegf
} // namespace cesdk
