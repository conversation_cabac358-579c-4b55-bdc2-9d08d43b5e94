#pragma once
#include "../CrossEngineSDK.h" 

namespace cesdk { 
	namespace cegf {
        /**
         * @class SDKPhysicsComponent
         * @brief 引擎 PhysicsComponent 的包装类
         *
         * 提供操作物理属性的接口。
         */
    class AssetExchange_API SDKPhysicsComponent : public SDKGameObjectComponent
    {
    public:

        SDKPhysicsComponent(::cegf::GameObjectComponent* physicsComponentInstance);

        void SetUseMeshCollision(bool useMeshCollision);

        void SetCollisionType(uint16_t type);

        void SetCollisionMask(uint16_t mask);
    };
    }
}  // namespace cesdk::cegf