#pragma once
#include "../CrossEngineSDK.h"

namespace cesdk {
    namespace cegf {

        /**
         * @enum LightType
         * @brief Defines the type of light source
         */
        enum class LightType : int
        {
            Directional = 0,    /**< A directional light source (like the sun) */
            Point = 1,          /**< A point light source that emits in all directions */
            Spot = 2,           /**< A spotlight source with a cone of influence */
            Rect = 3,           /**< A rectangular area light source */
            Count               /**< The number of light types */
        };

        /**
         * @enum LightMode
         * @brief Defines the lighting mode for a light source
         */
        enum class LightMode : int
        {
            Realtime = 0x01,            /**< Light is calculated in real-time every frame */
            Baked = 0x02,               /**< Light uses a mix of baked and real-time lighting */
            Mixed = Realtime | Baked,   /**< Light is fully baked into lightmaps */
            Count                       /**< The number of light modes */
        };

        /**
         * @enum LightPriority
         * @brief Defines the priority level of a light source
         */
        enum class LightPriority : int
        {
            Major = 0,
            Minor = 10,
            Count               /**< The number of priority levels */
        };

        /**
         * @enum LightShadowType
         * @brief Defines the shadow calculation type for a light
         */
        enum class LightShadowType : int
        {
            HardShadow = 0,
            SoftShadowPCF_1X1 = 1,
            SoftShadowPCF_3X3 = 2,
            SoftShadowPCF_5X5 = 3,
            SoftShadowPCSS = 4,
            Count               /**< The number of shadow types */
        };

        /**
         * @struct AtmosphereLightConfig
         * @brief Configuration for atmosphere lighting effects
         */
        struct AtmosphereLightConfig
        {
            bool AtmosphereSunLight;
            uint32_t AtmosphereSunLightIndex;
            cesdk::cross::Float3 AtmosphereSunDiscColorScale;
            float AtmosphereSunDiscIntensityScale;
        };


        /**
         * @class SDKLightComponent
         * @brief Wrapper class for engine LightComponent
         *
         * Provides an interface for manipulating light properties, including
         * type, color, intensity, range, shadow settings, etc.
         */
        class AssetExchange_API SDKLightComponent : public SDKGameObjectComponent {
        public:
            /**
             * @brief Constructor for SDKLightComponent
             *
             * @param lightComponentInstance Pointer to the internal engine LightComponent instance
             */
            SDKLightComponent(::cegf::GameObjectComponent* lightComponentInstance)
                : SDKGameObjectComponent(lightComponentInstance) {
            }

            /**
             * @brief Sets the type of light
             *
             * @param lightType The type of light to set
             */
            void SetLightType(LightType lightType);

            /**
             * @brief Gets the current light type
             *
             * @return LightType The current light type
             */
            LightType GetLightType() const;

            /**
             * @brief Sets the color of the light
             *
             * @param color The color vector to set
             */
            void SetLightColor(const cross::Float3& color);

            /**
             * @brief Gets the current light color
             *
             * @return Float3 The current light color
             */
            cross::Float3 GetLightColor() const;

            /**
             * @brief Sets the intensity of the light
             *
             * @param intensity The intensity value to set
             */
            void SetLightIntensity(float intensity);

            /**
             * @brief Gets the current light intensity
             *
             * @return float The current light intensity
             */
            float GetLightIntensity() const;

            /**
             * @brief Sets the PRT intensity of the light
             *
             * @param intensity The PRT intensity value to set
             */
            void SetLightPrtIntensity(float intensity);

            /**
             * @brief Gets the current light PRT intensity
             *
             * @return float The current light PRT intensity
             */
            float GetLightPrtIntensity() const;

            /**
             * @brief Sets the volumetric factor of the light
             *
             * @param factor The volumetric factor value to set
             */
            void SetLightVolumetricFactor(float factor);

            /**
             * @brief Gets the current light volumetric factor
             *
             * @return float The current light volumetric factor
             */
            float GetLightVolumetricFactor() const;

            /**
             * @brief Sets the source angle of the light
             *
             * @param angle The source angle value to set
             */
            void SetLightSourceAngle(float angle);

            /**
             * @brief Gets the current light source angle
             *
             * @return float The current light source angle
             */
            float GetLightSourceAngle() const;

            /**
             * @brief Sets the range of the light
             *
             * @param range The range value to set
             */
            void SetLightRange(float range);

            /**
             * @brief Gets the current light range
             *
             * @return float The current light range
             */
            float GetLightRange() const;

            /**
             * @brief Sets the source width of the light
             *
             * @param width The source width value to set
             */
            void SetLightSourceWidth(float width);

            /**
             * @brief Gets the current light source width
             *
             * @return float The current light source width
             */
            float GetLightSourceWidth() const;

            /**
             * @brief Sets the source height of the light
             *
             * @param height The source height value to set
             */
            void SetLightSourceHeight(float height);

            /**
             * @brief Gets the current light source height
             *
             * @return float The current light source height
             */
            float GetLightSourceHeight() const;

            /**
             * @brief Sets the barn door angle of the light
             *
             * @param angle The barn door angle value to set
             */
            void SetLightBarnDoorAngle(float angle);

            /**
             * @brief Gets the current light barn door angle
             *
             * @return float The current light barn door angle
             */
            float GetLightBarnDoorAngle() const;

            /**
             * @brief Sets the barn door length of the light
             *
             * @param length The barn door length value to set
             */
            void SetLightBarnDoorLength(float length);

            /**
             * @brief Gets the current light barn door length
             *
             * @return float The current light barn door length
             */
            float GetLightBarnDoorLength() const;

            /**
             * @brief Sets whether the light casts shadows
             *
             * @param castShadow True if the light should cast shadows, false otherwise
             */
            void SetLightCastShadow(bool castShadow);

            /**
             * @brief Gets whether the light casts shadows
             *
             * @return bool True if the light casts shadows, false otherwise
             */
            bool GetLightCastShadow() const;

            /**
             * @brief Sets whether the light casts screen space shadows
             *
             * @param castScreenSpaceShadow True if the light should cast screen space shadows, false otherwise
             */
            void SetLightCastScreenSpaceShadow(bool castScreenSpaceShadow);

            /**
             * @brief Gets whether the light casts screen space shadows
             *
             * @return bool True if the light casts screen space shadows, false otherwise
             */
            bool GetLightCastScreenSpaceShadow() const;

            /**
             * @brief Sets the shadow strength of the light
             *
             * @param strength The shadow strength value to set (0.0 to 1.0)
             */
            void SetLightShadowStrength(float strength);

            /**
             * @brief Gets the current shadow strength
             *
             * @return float The current shadow strength
             */
            float GetLightShadowStrength() const;

            /**
             * @brief Sets the light mode
             *
             * @param mode The light mode to set
             */
            void SetLightMode(LightMode mode);

            /**
             * @brief Gets the current light mode
             *
             * @return LightMode The current light mode
             */
            LightMode GetLightMode() const;

            /**
             * @brief Sets the light priority
             *
             * @param priority The light priority to set
             */
            void SetLightPriority(LightPriority priority);

            /**
             * @brief Gets the current light priority
             *
             * @return LightPriority The current light priority
             */
            LightPriority GetLightPriority() const;

            /**
             * @brief Sets the shadow type of the light
             *
             * @param shadowType The shadow type to set
             */
            void SetLightShadowType(LightShadowType shadowType);

            /**
             * @brief Gets the current light shadow type
             *
             * @return LightShadowType The current light shadow type
             */
            LightShadowType GetLightShadowType() const;

            /**
             * @brief Sets the shadow amount of the light
             *
             * @param amount The shadow amount value to set
             */
            void SetLightShadowAmount(float amount);

            /**
             * @brief Gets the current shadow amount
             *
             * @return float The current shadow amount
             */
            float GetLightShadowAmount() const;

            /**
             * @brief Sets the shadow bias of the light
             *
             * @param bias The shadow bias value to set
             */
            void SetLightShadowBias(float bias);

            /**
             * @brief Gets the current shadow bias
             *
             * @return float The current shadow bias
             */
            float GetLightShadowBias() const;

            /**
             * @brief Sets the shadow slope bias of the light
             *
             * @param slopeBias The shadow slope bias value to set
             */
            void SetLightShadowSlopeBias(float slopeBias);

            /**
             * @brief Gets the current shadow slope bias
             *
             * @return float The current shadow slope bias
             */
            float GetLightShadowSlopeBias() const;

            /**
             * @brief Sets the variance bias VSM of the light
             *
             * @param varianceBias The variance bias VSM value to set
             */
            void SetLightVarianceBiasVSM(float varianceBias);

            /**
             * @brief Gets the current variance bias VSM
             *
             * @return float The current variance bias VSM
             */
            float GetLightVarianceBiasVSM() const;

            /**
             * @brief Sets the light leak bias VSM of the light
             *
             * @param lightLeakBias The light leak bias VSM value to set
             */
            void SetLightLightLeakBiasVSM(float lightLeakBias);

            /**
             * @brief Gets the current light leak bias VSM
             *
             * @return float The current light leak bias VSM
             */
            float GetLightLightLeakBiasVSM() const;

            /**
             * @brief Sets the filter size PCF of the light
             *
             * @param filterSize The filter size PCF value to set
             */
            void SetLightFilterSizePCF(float filterSize);

            /**
             * @brief Gets the current filter size PCF
             *
             * @return float The current filter size PCF
             */
            float GetLightFilterSizePCF() const;

            /**
             * @brief Sets the softness PCSS of the light
             *
             * @param softness The softness PCSS value to set
             */
            void SetLightSoftnessPCSS(float softness);

            /**
             * @brief Gets the current softness PCSS
             *
             * @return float The current softness PCSS
             */
            float GetLightSoftnessPCSS() const;

            /**
             * @brief Sets the sample count PCSS of the light
             *
             * @param sampleCount The sample count PCSS value to set
             */
            void SetLightSampleCountPCSS(float sampleCount);

            /**
             * @brief Gets the current sample count PCSS
             *
             * @return float The current sample count PCSS
             */
            float GetLightSampleCountPCSS() const;

            /**
             * @brief Sets the atmosphere light configuration
             *
             * @param config The atmosphere light configuration to set
             */
            void SetLightAtmosphereLightConfig(const AtmosphereLightConfig& config);

            /**
             * @brief Gets the current atmosphere light configuration
             *
             * @return AtmosphereLightConfig The current atmosphere light configuration
             */
            AtmosphereLightConfig GetLightAtmosphereLightConfig() const;

            /**
             * @brief Enables or disables the light
             *
             * @param enable True to enable the light, false to disable
             */
            void SetLightEnable(bool enable);

            /**
             * @brief Checks if the light is enabled
             *
             * @return bool True if the light is enabled, false otherwise
             */
            bool GetLightEnable() const;

            /**
             * @brief Sets the inner cone angle for spot lights
             *
             * @param angle The inner cone angle in degrees
             */
            void SetLightInnerConeAngle(float angle);

            /**
             * @brief Gets the inner cone angle for spot lights
             *
             * @return float The inner cone angle in degrees
             */
            float GetLightInnerConeAngleInDegree() const;

            /**
             * @brief Sets the outer cone angle for spot lights
             *
             * @param angle The outer cone angle in degrees
             */
            void SetLightOuterConeAngle(float angle);

            /**
             * @brief Gets the outer cone angle for spot lights
             *
             * @return float The outer cone angle in degrees
             */
            float GetLightOuterConeAngleInDegree() const;

            /**
             * @brief Sets the cone fade intensity of the light
             *
             * @param intensity The cone fade intensity value to set
             */
            void SetLightConeFadeIntensity(float intensity);

            /**
             * @brief Gets the current cone fade intensity
             *
             * @return float The current cone fade intensity
             */
            float GetLightConeFadeIntensity() const;

            /**
             * @brief Sets the cone overflow length of the light
             *
             * @param length The cone overflow length value to set
             */
            void SetLightConeOverFlowLength(float length);

            /**
             * @brief Gets the current cone overflow length
             *
             * @return float The current cone overflow length
             */
            float GetLightConeOverFlowLength() const;

            /**
             * @brief Sets the spot distance exponent of the light
             *
             * @param distanceExp The spot distance exponent value to set
             */
            void SetLightSpotDistanceExp(float distanceExp);

            /**
             * @brief Gets the current spot distance exponent
             *
             * @return float The current spot distance exponent
             */
            float GetLightSpotDistanceExp() const;

            /**
             * @brief Sets the rendering layer mask of the light
             *
             * @param mask The rendering layer mask value to set
             */
            void SetLightRenderingLayerMask(uint32_t mask);

            /**
             * @brief Gets the current rendering layer mask
             *
             * @return UInt32 The current rendering layer mask
             */
            uint32_t GetLightRenderingLayerMask() const;

            /**
             * @brief Sets whether the light enables transmittance
             *
             * @param enable True to enable transmittance, false otherwise
             */
            void SetLightEnableTransmittance(bool enable);

            /**
             * @brief Gets whether the light enables transmittance
             *
             * @return bool True if transmittance is enabled, false otherwise
             */
            bool GetLightEnableTransmittance() const;
        };
    }
}