#pragma once

#include <cstdint>
#include <type_traits>
#include <functional>
#include "AssetExchangeDefinitions.h"
#include "IMeshAssemble.h"

namespace CEAssetExchange {

// The abstract interface for entity
struct IEntity
{
    bool mHide{false};
};

struct WorldSettings
{
    float mMapSize[2]{800000.0f, 800000.0f};
    float mBlockSize[3]{800000.0f, 1.0f, 800000.0f};
    float mOrigin[3]{0.0f, 0.0f, 0.0f};
    int mPolicy{2};
    bool mAsPrefab{false};
    bool mAutoChangeExtension{true};
};
static_assert(std::is_standard_layout<WorldSettings>::value, "Data structure must be standard layout");

struct Transform
{
    float mTranslation[3]{0.0f, 0.0f, 0.0f};
    float mQuarternion[4]{0.0f, 0.0f, 0.0f, 1.0f};
    float mScale[3]{1.0f, 1.0f, 1.0f};
};
static_assert(std::is_standard_layout<Transform>::value, "Data structure must be standard layout");

struct Hierarchy
{
    Transform* mTransform{nullptr};
    IEntity* mParent{nullptr};
};
static_assert(std::is_standard_layout<Hierarchy>::value, "Data structure must be standard layout");

//=====================================================================================

enum class LightPriority : std::uint8_t
{
    Major = 0,
    Minor = 10
};

enum class LightType : std::uint8_t
{
    Directional = 0,
    Point = 1,
    Spot = 2,
    Rect = 3,

    SkyLight = 222,
};

enum class LightMode : std::uint8_t
{
    Realtime = 0x01,
    Baked = 0x02,
    Mixed = Realtime | Baked,
};

enum class LightShadowType : std::uint8_t
{
    HardShadow = 0,
    SoftShadowPCF_1X1 = 1,
    SoftShadowPCF_3X3 = 2,
    SoftShadowPCF_5X5 = 3,
    SoftShadowPCSS = 4,
};

struct LightData
{
    LightType mType{LightType::Directional};
    float mColor[3]{1.0f, 1.0f, 1.0f};
    float mIntensity{1.0f};
    float mPrtIntensity{1.0f};
    float mVolumetricFactor{1.0f};

    float mRange{1.0f};             // Point/Spot/Rect lights only

    // Spot light only
    float mInnerConeAngle{60.0f};
    float mOuterConeAngle{60.0f};
    uint32_t mVersion{1};

    // rect light only
    float mSourceWidth{64.f};
    float mSourceHeight{64.f};
    float mBarnDoorAngle{88.f};
    float mBarnDoorLength{20.f};

    bool mCastShadow{true};
    bool mCastScreenSpaceShadow{true};
    float mShadowStrength{1.0f};

    LightMode mMode{LightMode::Realtime};

    unsigned int mMask{0xffffffff};   // Control the lighting layers
    LightPriority mPriority{LightPriority::Major};
    LightShadowType mShadowType{LightShadowType::HardShadow};
    float mDepthBias{0.05f};
    float mNormalBias{0.01f};
    // float mVarianceBiasVSM{0.01f};
    // float mLightLeakBiasVSM{0.01f};
    // float mFilterSizePCF{2.f};
    // float mSoftnessPCSS{0.005f};
    // float mSampleCountPCSS{32.f};
    bool mEnable{true};
    // bool mIsBoundaryShow{false};
};
static_assert(std::is_standard_layout<LightData>::value, "Data structure must be standard layout");

//=====================================================================================

struct SubModel
{
    const char* mMaterial{""};
    const char** mLODMaterials{nullptr};
    IMaterialAssemble* mMaterial_A{nullptr};
    IMaterialAssemble* mLODMaterials_A{nullptr};
    uint32_t mLODMaterialsNum{0};
};
static_assert(std::is_standard_layout<SubModel>::value, "Data structure must be standard layout");

struct Model
{
    const char* mAsset{""};
    IMeshAssemble* mAsset_A{nullptr};
    SubModel* mSubModels{nullptr};
    uint32_t mSubModelsNum{0};
};
static_assert(std::is_standard_layout<Model>::value, "Data structure must be standard layout");

struct ModelData
{
    Model* mModels{nullptr};
    uint32_t mModelsNum{0};
    bool mCastShadow{true};
};
static_assert(std::is_standard_layout<ModelData>::value, "Data structure must be standard layout");

//=====================================================================================

struct FoliageLoDSection
{
    const char* mDefaultMaterial{""};
    IMaterialAssemble* mDefaultMaterial_A{nullptr};

    const char** mSubSectionMaterials{nullptr};
    IMaterialAssemble* mSubSectionMaterials_A{nullptr};
    uint32_t mSubSectionMaterialsNum{0};
};
static_assert(std::is_standard_layout<FoliageLoDSection>::value, "Data structure must be standard layout");

struct FoliageInstanceData
{
    Transform* mInstanceData{nullptr};
    uint32_t mInstanceDataNum{0};
};
static_assert(std::is_standard_layout<FoliageInstanceData>::value, "Data structure must be standard layout");

struct InstancedFoliage
{
    const char* mPrimaryMeshAsset{""};
    IMeshAssemble* mPrimaryMeshAsset_A{nullptr};

    const char* mPrimaryMaterial{""};
    IMaterialAssemble* mPrimaryMaterial_A{nullptr};

    FoliageLoDSection* mLoDSections{nullptr};
    uint32_t mLoDSectionsNum{0};

    FoliageInstanceData* mInstanceData;
    bool mCastShadow{true};
};
static_assert(std::is_standard_layout<InstancedFoliage>::value, "Data structure must be standard layout");

//=====================================================================================

enum class EProjectionMode
{
    Perspective = 0,
    Orthogonal = 1,
};

enum class EControllableUnitType
{
    None,

    HostedOrbitFollowCamera,
    ScriptOrbitFollowCamera,

    HostedFppCamera,
    ScriptFppCamera,

    HostedFreeFlightCamera,
    ScriptFreeFlightCamera,

    HostedCharacterMovement,
    ScriptCharacterMovement,

    CurveController,

    VRCamera,

    ControllableAircraft,
    ControllableAirport,

    TestEnvControllableCamera
};

struct ControllableUnitInfo
{
    EControllableUnitType mControllableUnitType = EControllableUnitType::None;
    const char* mCurveCtrResPath = nullptr;
    IEntity* mEntity = nullptr;
    bool mPlayOnStart = false;
    bool mRetargetCurveBindings = false;
    float mPlaySpeed = 1.0f;
};

struct CameraInfo
{
    float mAspectRatio = 1.7777777910232545;
    float mHorizontalFov = 1.27f;
    float mFov = 1.0;
    float mFarPlane = 100000.0;
    float mNearPlane = 1.0;
    float mCurrentFocalLength{35};
    float mMinFocalLength{4};
    float mMaxFocalLength{1000};
    float mSensorWidth{52};
    float mSensorHeight{24};

    float mWidth = 1080.0;
    float mHeight = 720.0;
    float mOrthNearPlane = 100.0;
    float mOrthFarPlane = 10000.0;

    float mNormalBias = 0.0;
    float mDepthBias = 0.0;

    EProjectionMode mProjectionMode = EProjectionMode::Perspective;
    uint32_t mTargetWidth = 800;
    uint32_t mTargetHeight = 800;

    // control
    bool mController = true;
    ControllableUnitInfo* mControllableUnitInfo = nullptr;
};
static_assert(std::is_standard_layout<CameraInfo>::value, "Data structure must be standard layout");

//=====================================================================================

enum class ELuminanceType
{
    // Render the sRGB luminance, using an approximate (on the fly) conversion
    // from 3 spectral radiance values only (see section 14.3 in <a href=
    // "https://arxiv.org/pdf/1612.04336.pdf">A Qualitative and Quantitative
    //  Evaluation of 8 Clear Sky Models</a>).
    APPROXIMATE = 0,
    // Render the sRGB luminance, precomputed from 15 spectral radiance values
    // (see section 4.4 in <a href=
    // "http://www.oskee.wz.cz/stranka/uploads/SCCG10ElekKmoch.pdf">Real-time
    //  Spectral Scattering in Large-scale Natural Participating Media</a>).
    PRECOMPUTED
};

struct SkyAtmosphere
{
    // config
    float MiePhase{0.0004f};
    float MieScattCoeff[3]{0.57f, 0.57f, 0.57f};
    float MieScattScale{0.00396f};
    float MieAbsorCoeff[3]{0.57f, 0.57f, 0.57f};
    float MieAbsorScale{0.f};
    float MieScaleHeight{1.2f};
    float RayScattCoeff[3] = {0.16f, 0.37f, 0.913f};
    float RayScattScale{0.033f};
    float RayScattHeight{6.f};
    float AbsorptiCoeff[3] = {0.32549f, 1.f, 0.004f};
    float AbsorptiScale{0.0018f};
    float PlanetRadius{6373.7f};
    float AtmosHeight{200.f};
    float GroundAlbedo3[3] = {0.001f, 0.001f, 0.001f};
    float SFogMieScattScale{0.05f};

    // outerParam
    float APScale{1.f};
    float ColorTransmittance{0.995f};
};

struct PostProcessVolumeInfo
{
    // Volume properties
    bool mUnbound{true};  // Whether it's a global post process
    int32_t mPriority{0};   // Priority, higher value means higher priority
    
    // Volume bounds if not unbound
    struct {
        float mMin[3]{0.0f, 0.0f, 0.0f};
        float mMax[3]{0.0f, 0.0f, 0.0f};
    } mBounds;

    // Bloom settings
    struct {
        bool mEnable{false};
        float mThreshold{0.5f};
        float mIntensity{0.1f};
        float mTint[3]{1.0f, 1.0f, 1.0f};
        float mScale{1.0f};
        float mLuminanceClamp{10.0f};
    } mBloom;

    // Exposure settings
    struct {
        bool mEnable{false};
        float mMinBrightness{0.03f};
        float mMaxBrightness{8.0f};
        float mSpeedUp{3.0f};
        float mSpeedDown{1.0f};
        float mAutoExposureBias{1.0f};
        float mHistogramLogMin{-8.0f};
        float mHistogramLogMax{4.0f};
        float mHighPercent{0.9f};
    } mExposure;

    // Tonemapper settings
    struct {
        bool mEnable{true};
        float mFilmSlope{0.88f};
        float mFilmToe{0.55f};
        float mFilmShoulder{0.26f};
        float mFilmBlackClip{0.0f};
        float mFilmWhiteClip{0.04f};
        float mToneCurveAmount{1.0f};
        float mOverlayColor[4]{0.0f, 0.0f, 0.0f, 0.0f};
        float mOverlayScale[3]{1.0f, 1.0f, 1.0f};
    } mTonemapper;

    // Color correction
    struct {
        bool mEnable{false};
        float mGamma[3]{1.0f, 1.0f, 1.0f};
        float mGain[3]{1.0f, 1.0f, 1.0f};
        float mOffset[3]{0.0f, 0.0f, 0.0f};
        float mSaturation[4]{1.0f, 1.0f, 1.0f, 1.0f};
        float mContrast[4]{1.0f, 1.0f, 1.0f, 1.0f};
    } mColorCorrection;

    // Depth of field settings
    struct {
        bool mEnable{false};
        float mFocalDistance{0.0f};
        float mFstop{4.0f};
    } mDepthOfField;

    // Motion blur
    struct {
        bool mEnable{false};
        float mAmount{0.5f};
    } mMotionBlur;

    // Chromatic aberration
    struct {
        bool mEnable{false};
        float mIntensity{0.1f};
        float mRange{1.0f};
    } mChromaticAberration;

    // Vignette
    struct {
        bool mEnable{false};
        float mIntensity{0.4f};
    } mVignette;
};
static_assert(std::is_standard_layout<PostProcessVolumeInfo>::value, "Data structure must be standard layout");

enum class TerrainSurfaceType : std::uint8_t
{
    Flat,
    Spherical,
    WGS84,
    NumSurfaceTypes
};

struct TerrainDesc
{
    const char* mTerrainPath;
    uint32_t mGridSizeX = 0;
    uint32_t mGridSizeY = 0;
    uint32_t mBlockSize = 0;
    uint32_t mTileSize = 0;
    float mTexelDensity = 1.f;
    const char* mBaseColorTextures[8];
    const char* mNormalTextures[8];
    const char* mHMRATextures[8];
    const char* mRootDataPath;
    const char* mHeightmapPrefix;
    const char* mAlbedoTexturePrefix;
    const char* mWeightTexturePrefix;
    const char* mMaterialPath;
    const char* mDefaultHeightmapPath;
    const char* mDefaultAlbedoTexturePath;
    bool mHasCollision = false;
};

//=====================================================================================

struct IWorldAssemble
{
    virtual void SaveToFile() = 0;

    virtual void SetSettings(WorldSettings*) = 0;

    virtual bool HasBeenDestroyed() = 0;

    virtual void EndAssemble() = 0;

    virtual bool HasEndAssemble() = 0;

    virtual IEntity* AddEntity(const char* name, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddModel(const char* name, ModelData*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddLight(const char* name, LightData*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddPrefab(const char* name, IWorldAssemble*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddInstancedFoliage(const char* name, InstancedFoliage*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddCamera(const char* name, CameraInfo*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddControllableUnit(const char* name, ControllableUnitInfo*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddSkyAtmosphere(const char* name, SkyAtmosphere*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddPostProcessVolume(const char* name, PostProcessVolumeInfo*, Hierarchy* = nullptr) = 0;

    virtual IEntity* AddTerrain(const char* name, TerrainDesc*, Hierarchy* = nullptr) = 0;

    virtual const char* GetEntityUUID(IEntity* entity) = 0;
};

}   // namespace CEAssetExchange
