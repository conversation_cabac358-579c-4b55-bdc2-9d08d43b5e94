#pragma once

#include <cstdint>
#include <type_traits>
#include <functional>
#include "AssetExchangeDefinitions.h"
namespace CEAssetExchange {
enum ImportColorSpace : std::uint32_t
{
    Linear,
    SRGB
};
enum TextureCompression : std::uint32_t
{
    Uncompressed,
    CompressedBasisHQ,
    CompressedBasisLQ,
    BC1,
    BC2,
    BC3,
    BC4,
    BC5,
    BC6H,
    BC7,
    ASTC,
    PVRTC,
    ETC2
};

enum TextureType : std::uint32_t
{
    ImageTexture = 0,
    RectangularCubeMap,
    NormalTexture,
    DataTexture,
    Texture3D,
    LUT_Cube,
    TEXTURE_UDIM
};

enum ImportTexureSize : std::uint32_t
{
    None = 0,
    SQUARE_64 = 1 << 6,
    SQUARE_128 = 1 << 7,
    SQUARE_256 = 1 << 8,
    SQUARE_512 = 1 << 9,
    SQUARE_1024 = 1 << 10,
    SQUARE_2048 = 1 << 11,
    SQUARE_4096 = 1 << 12,
    SQUARE_8192 = 1 << 13,
};
enum ImportTextureGroup : std::uint32_t
{
    Default = 0,
    Heightmap,
    UI,
    Character,
    VFX,
    Lightmap,
    Terrain,
};
struct TextureImportSettingWrapper
{
    bool AutoImport = true;

    TextureType Type = TextureType::ImageTexture;

    ImportColorSpace ColorSpace = SRGB;

    TextureCompression Compression = BC3;

    bool GenerateMipmap = true;

    bool GeneratePrefilterMipmap = true;

    int GlossBias = 1;

    bool FlipUV = false;

    bool OpenGLESCompatible = true;

    bool IsStreamFile = false;

    bool SaveRawData = true;

    bool IsCook = false;

    bool VirtualTextureStreaming = false;

    bool isUDIM = false;

    float TileX = 0;
    float TileY = 0;

    ImportTexureSize ImportSize = ImportTexureSize::None;

    ImportTextureGroup ImportTextureGroup = ImportTextureGroup::Default;

    bool isAutoRecognition = false;
};
enum EPixelFormat : std::uint32_t
{
    PF_G8 = 3,
    PF_G16 = 4,
    PF_DXT1 = 5,
    PF_DXT3 = 6,
    PF_DXT5 = 7,
    PF_FloatRGBA = 10,   // 16F
    PF_BC5 = 23,
    PF_BC4 = 39,
    PF_R8G8B8A8 = 37,
    PF_BC6H = 55,
    PF_BC7 = 56,
};

struct MipMapData
{
    std::uint32_t SizeX = 0;
    std::uint32_t SizeY = 0;
    std::uint8_t* mData = nullptr;
    std::uint32_t mDataNum = 0;
};
static_assert(std::is_standard_layout<MipMapData>::value, "Data structure must be standard layout");

struct MipMapArray
{
    ImportColorSpace ColorSpace = Linear;
    EPixelFormat Format = EPixelFormat::PF_R8G8B8A8;
    MipMapData* MipMap = nullptr;
    std::uint32_t MipMapNum = 1;
};
static_assert(std::is_standard_layout<MipMapArray>::value, "Data structure must be standard layout");

struct TextureBlock
{
    const char* Path;
    std::uint32_t BlockX = 0;
    std::uint32_t BlockY = 0;
};
static_assert(std::is_standard_layout<MipMapArray>::value, "Data structure must be standard layout");

struct UDIM
{
    TextureBlock* Blocks = nullptr;
    std::uint32_t BlocksNum = 0;
};
static_assert(std::is_standard_layout<MipMapArray>::value, "Data structure must be standard layout");

struct AdjustmentSettings
{
    float brightness = 0.0f;
    float brightnesscurve = 0.0f;
    float vibrance = 0.0f;
    float saturation = 0.0f;
    float hue = 0.0f;
    float minalpha = 0.0f;
    float maxalpha = 0.0f;
    float rgbcurve = 0.0f;
};
static_assert(std::is_standard_layout<MipMapArray>::value, "Data structure must be standard layout");

struct ITextureAssemble
{
    virtual void SaveToFile() = 0;

    virtual bool HasBeenDestroyed() = 0;

    virtual void EndAssemble() = 0;

    virtual bool HasEndAssemble() = 0;

    virtual void SetTextureImportSetting(TextureImportSettingWrapper setting) = 0;

    virtual void SetAdjustmentSettings(AdjustmentSettings*) = 0;

    virtual void AsMipMapArray(MipMapArray*, bool vtstreaming = false) = 0;

    virtual void AsUDIM(UDIM*) = 0;
};

}   // namespace CEAssetExchange
