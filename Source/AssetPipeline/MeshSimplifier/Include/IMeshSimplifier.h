#pragma once

#include <cstdint>
#include <type_traits>
#include <iostream>
#include <vector>
#include "MeshSimplifierDefinitions.h"

namespace CEMeshSimplifier {

struct MeshLODInfo
{
    float mPercentTriangles{0.5f};
    float mPercentVertices{1.0f};
    const std::uint32_t* mSubMeshIndices{nullptr};
    std::uint32_t mSubMeshIndicesNum{0};
};
static_assert(std::is_standard_layout<MeshLODInfo>::value, "Data structure must be standard layout");

struct MeshOptimizeInfo
{
    const char* mInputFile{nullptr};
    const char* mOutputFile{nullptr};
    bool mSmoothNormal{false};
};
static_assert(std::is_standard_layout<MeshOptimizeInfo>::value, "Data structure must be standard layout");

struct MeshReduceInfo
{
    const char* mInputFile{nullptr};
    const char* mOutputFile{nullptr};
    float mPercentTriangles{0.5f};
    float mPercentVertices{1.0f};
    const MeshLODInfo* mLODs{nullptr};
    std::uint32_t mLODsNum{0};
    bool mKeepOriginalMeshLOD0{true};
};
static_assert(std::is_standard_layout<MeshReduceInfo>::value, "Data structure must be standard layout");

struct MergeVertexThresholds
{
public:
    MergeVertexThresholds()
        : ThresholdPosition(0.015f)
        , ThresholdTangentNormal(0.01f)
        , ThresholdUV(1.0f / 1024.f)
        , ThresholdColor(0.05f)
    {}

    /** Threshold use to decide if two vertex position are equal. */
    float ThresholdPosition;

    /** Threshold use to decide if two normal, tangents or bi-normals are equal. */
    float ThresholdTangentNormal;

    /** Threshold use to decide if two UVs are equal. */
    float ThresholdUV;

    /** Threshold use to decide if two Colors are equal. */
    float ThresholdColor;
};
static_assert(std::is_standard_layout<MergeVertexThresholds>::value, "Data structure must be standard layout");

struct InputSubMeshParams
{
    float percentTriangles;
    float percentVertices;
};

struct InputSubMeshBuffer
{
    int originalVertexCount;
    int originalTriangleCount;
    std::uint32_t subMeshIndex;
    std::vector<float> vertices;
    std::vector<std::uint32_t> indices;
    std::vector<std::uint32_t> materialGroup;
};

struct OutSubMeshData
{
    float* vertexBuffer;
    uint32_t* indexBuffer;
    int vertexCount;
    int indexCount;
};

enum class EReduceEngine
{
    UE,
    MeshOptimizer
};

struct IMeshSimplifier
{
    virtual void Release() = 0;

    virtual void SetReduceEngine(const EReduceEngine) = 0;

    virtual void SetMergeVertexThresholds(const MergeVertexThresholds*) = 0;

    virtual int MeshReduce(const MeshReduceInfo*) = 0;

    virtual int MeshOptimize(const MeshOptimizeInfo*) = 0;

    virtual std::string GetResultLog() = 0;

    virtual int SubMeshReduce(const InputSubMeshParams& params, const InputSubMeshBuffer& subMesh, OutSubMeshData* outPut) = 0;
};

// The factory function that creates instances, remember to release it when you're done
extern "C" MeshSimplifier_API IMeshSimplifier* __stdcall CreateMeshSimplifierSDK(const char* staging);

}   // namespace CEMeshSimplifier
