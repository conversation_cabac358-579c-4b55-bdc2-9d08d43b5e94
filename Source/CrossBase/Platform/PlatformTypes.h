#pragma once
#include <cstdint>
#include "Platform/PlatformConfigMacro.h"
#include <cstddef>

/********************************************************************************

 Base integer types for all target OS's and CPU's

 UInt8           8-bit unsigned integer
 SInt8           8-bit signed integer
 UInt16         16-bit unsigned integer
 SInt16         16-bit signed integer
 UInt32         32-bit unsigned integer
 SInt32         32-bit signed integer
 UInt64         64-bit unsigned integer
 SInt64         64-bit signed integer
 SizeType       Platform specific size type
 SSizeType      Platform specific signed size type

 *********************************************************************************/
using SInt8 = std::int8_t;
using UInt8 = std::uint8_t;
using SInt16 = std::int16_t;
using UInt16 = std::uint16_t;
using SInt32 = std::int32_t;
using UInt32 = std::uint32_t;
using SInt64 = std::int64_t;
using UInt64 = std::uint64_t;
using SizeType = std::size_t;
using SSizeType = SInt64;
#if !CROSSENGINE_OSX
#if !CROSSENGINE_ANDROID && !CROSSENGINE_IOS && !CROSSENGINE_WASM
using WCHAR_T = wchar_t;
#endif

#else//!CROSSENGINE_OSX

#define TEXT (char*)
using TCHAR_T =  char;

using WCHAR_T = wchar_t;
using HWND    = void* ;
using HMODULE = void* ;
#define APIENTRY


#endif//!CROSSENGINE_OSX

#if defined(_WIN64)
#if UNICODE
using TCHAR_T = WCHAR_T;
#else
using TCHAR_T = char;
//#define TEXT (char*)
#endif
#endif
