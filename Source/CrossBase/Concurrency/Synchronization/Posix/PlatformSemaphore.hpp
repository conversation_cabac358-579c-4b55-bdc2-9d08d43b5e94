#pragma once

#include <semaphore.h>

namespace cross
{
    class PlatformSemaphore
    {
    public:
        PlatformSemaphore(UInt32 defaultValue)
        {
            sem_init(&mSemaphore, 0, defaultValue);
        }

        ~PlatformSemaphore()
        {
            sem_destroy(&mSemaphore);
        }

        PlatformSemaphore(PlatformSemaphore const&) = delete;
        PlatformSemaphore& operator=(PlatformSemaphore const&) = delete;

    public:
        void Wait()
        {
            sem_wait(&mSemaphore);
        }

        bool WaitFor(MillionSecondCount const& millionSecondCount)
        {
            auto const absTime = std::chrono::time_point_cast<MillionSecondCount>(std::chrono::system_clock::now() + millionSecondCount);
            return WaitUntil(absTime);
        }

        bool WaitUntil(std::chrono::system_clock::time_point const& timePoint)
        {
            auto secondTimePoint = std::chrono::time_point_cast<SecondCount>(timePoint);
            auto nanoSecondsInOneSecond = time::Cast<NanoSecondCount>(timePoint - secondTimePoint);

            timespec ts
            {
                static_cast<std::time_t>(secondTimePoint.time_since_epoch().count()),
                static_cast<long>(nanoSecondsInOneSecond.count())
            };

            // TODO ... error handling
            return sem_timedwait(&mSemaphore, &ts) == 0;
        }

        bool TryWait()
        {
            return sem_trywait(&mSemaphore) == 0;
        }

        void Notify()
        {
            sem_post(&mSemaphore);
        }

        void* GetNativeHandle() noexcept
        {
            return &mSemaphore;
        }

    private:
        sem_t       mSemaphore;
    };
}
