#pragma once

#include "Concurrency/Synchronization/Posix/PlatformMutex.hpp"

namespace cross
{
    class PlatformCondVar final
    {
    public:
        PlatformCondVar()
        {
#if !CROSSENGINE_WASM
            pthread_cond_init(&mCondVar, nullptr);
#endif
        }

        ~PlatformCondVar()
        {
#if !CROSSENGINE_WASM
            pthread_cond_destroy(&mCondVar);
#endif
        }

        PlatformCondVar(PlatformCondVar const&) = delete;
        PlatformCondVar& operator=(PlatformCondVar const&) = delete;

    public:
        void Wait(PlatformMutex* mutex)
        {
#if !CROSSENGINE_WASM
            auto nativeHandle = reinterpret_cast<pthread_mutex_t*>(mutex->GetNativeHandle());
            pthread_cond_wait(&mCondVar, nativeHandle);
#endif
        }

        bool WaitFor(PlatformMutex* mutex, MillionSecondCount const& millionSecondCounts)
        {
#if !CROSSENGINE_WASM
            auto absTime = std::chrono::time_point_cast<MillionSecondCount>(std::chrono::system_clock::now() + millionSecondCounts);
            return WaitUntil(mutex, absTime);
#else
            return false;
#endif
        }

        bool WaitUntil(PlatformMutex* mutex, std::chrono::system_clock::time_point const& timePoint)
        {
#if !CROSSENGINE_WASM
            auto secondTimePoint = std::chrono::time_point_cast<SecondCount>(timePoint);
            auto nanoSecondsInOneSecond = time::Cast<NanoSecondCount>(timePoint - secondTimePoint);

            timespec absTime
            {
                std::chrono::system_clock::to_time_t(timePoint),
                static_cast<long>(nanoSecondsInOneSecond.count())
            };

            // TODO ... error handle
            return pthread_cond_timedwait(&mCondVar, mutex->GetNativeHandle(), &absTime) == 0;
#else
            return false;
#endif
        }

        void NotifyOne()
        {
#if !CROSSENGINE_WASM
            pthread_cond_signal(&mCondVar);
#endif
        }

        void NotifyAll()
        {
#if !CROSSENGINE_WASM
            pthread_cond_broadcast(&mCondVar);
#endif
        }

        void* GetNativeHandle() noexcept
        {
#if !CROSSENGINE_WASM
            return &mCondVar;
#else
            return nullptr;
#endif
        }

    private:
#if !CROSSENGINE_WASM
        pthread_cond_t  mCondVar = PTHREAD_COND_INITIALIZER;
#endif
    };
}
