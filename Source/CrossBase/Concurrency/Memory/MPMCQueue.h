#pragma once

/*
	a lock free queue introduced in Cpp Concurrency
*/

#include <atomic>
#include <memory>

#include "CrossBaseForward.h"

namespace cross
{
	template<typename T>
	class MPMCQueue
	{
	private:
		using ValueType = T;
		using Pointer = T * ;
		using ConstPointer = T * const;

		struct Node;
		using NodePtr = Node *;

		struct CountedNodePtr
		{
			int externalCount;
			NodePtr ptr;
		};

		struct NodeCounter
		{
			unsigned internalCount : 30;
			unsigned externalCounters : 2;
		};

		struct Node
		{
			std::atomic<Pointer> data;
			std::atomic<NodeCounter> count;
			std::atomic<CountedNodePtr> next;

			Node()
			{
				Pointer newData = nullptr;
				data.store(newData);

				NodeCounter newCount;
				newCount.internalCount = 0;
				newCount.externalCounters = 2;
				count.store(newCount);

				CountedNodePtr newNext;
				newNext.ptr = nullptr;
				newNext.externalCount = 0;
				next.store(newNext);
			}

			void ReleaseRef()
			{
				NodeCounter oldCounter = count.load(std::memory_order_relaxed);
				NodeCounter newCounter;
				do {
					newCounter = oldCounter;
					--newCounter.internalCount;
				} while (!count.compare_exchange_strong(
					oldCounter, newCounter, std::memory_order_acquire, std::memory_order_relaxed));
			
				if (
					!newCounter.internalCount &&
					!newCounter.externalCounters)
				{
					delete this;
				}
			}
		};

		std::atomic<CountedNodePtr> mHead;
		std::atomic<CountedNodePtr> mTail;

		NodePtr PopHead()
		{
			NodePtr const oldHead = mHead.load();
			if (oldHead == mTail.load)
			{
				return nullptr;
			}
			mHead.store(oldHead->next);
			return oldHead;
		}

		static void IncreaseExternalCount(
			std::atomic<CountedNodePtr>& counter,	
			CountedNodePtr& oldCounter)
		{
			CountedNodePtr newCounter;
			do{
				newCounter = oldCounter;
				++newCounter.externalCount;
			} while (!counter.compare_exchange_strong(
				oldCounter, newCounter, std::memory_order_acquire, std::memory_order_relaxed));
		
			oldCounter.externalCount = newCounter.externalCount;
		}

		static void FreeExternalCounter(CountedNodePtr &oldNodePtr)
		{
			NodePtr const ptr = oldNodePtr.ptr;
			int const countIncrease = oldNodePtr.externalCount - 2;

			NodeCounter oldCounter = ptr->count.load(std::memory_order_relaxed);
			NodeCounter newCounter;

			do {
				newCounter = oldCounter;
				--newCounter.externalCounters;
				newCounter.internalCount += countIncrease;
			} while (!ptr->count.compare_exchange_strong(
				oldCounter, newCounter, std::memory_order_acquire, std::memory_order_relaxed));

			if (
				!newCounter.internalCount &&
				!newCounter.externalCounters)
			{
				delete ptr;
			}
		}

		void SetNewTail(
			CountedNodePtr &oldTail,
			CountedNodePtr const &newTail)
		{
			NodePtr const currentTailPtr = oldTail.ptr;
			while (
				!mTail.compare_exchange_weak(oldTail, newTail) &&
				oldTail.ptr == currentTailPtr);
			if (oldTail.ptr == currentTailPtr)
				FreeExternalCounter(oldTail);
			else
				currentTailPtr->ReleaseRef();
		}

	public:
	
		MPMCQueue(const MPMCQueue&) = delete;
		MPMCQueue& operator=(const MPMCQueue&) = delete;

		explicit MPMCQueue()
		{
			CountedNodePtr headPtr;
			headPtr.ptr = new Node;
			headPtr.externalCount = 0;
			mHead.store(headPtr);
			mTail.store(headPtr);
		}

		~MPMCQueue()
		{
			while (true)
			{
				CountedNodePtr oldHead = mHead.load();
				if (oldHead.ptr == nullptr)
				{
					break;
				}
				mHead.store(mHead.load().ptr->next);
				delete oldHead.ptr;
			}
		}

		std::unique_ptr<T> pop()
		{
			CountedNodePtr oldHead = mHead.load(std::memory_order_relaxed);
			while (true)
			{
				IncreaseExternalCount(mHead, oldHead);
				NodePtr const ptr = oldHead.ptr;
				if (ptr == mTail.load().ptr)
				{
					return std::unique_ptr<T>();
				}

				CountedNodePtr next = ptr->next.load();
				if (mHead.compare_exchange_strong(oldHead, next))
				{
					Pointer const res = ptr->data.exchange(nullptr);
					FreeExternalCounter(oldHead);
					return std::unique_ptr<T>(res);
				}
				ptr->ReleaseRef();
			}
		}

		void push(T newValue)
		{
			std::unique_ptr<T> newData(new T(newValue));
			CountedNodePtr newNext;
			newNext.ptr = new Node;
			newNext.externalCount = 1;
			CountedNodePtr oldTail = mTail.load();

			while (true)
			{
				IncreaseExternalCount(mTail, oldTail);

				Pointer oldData = nullptr;
				if (oldTail.ptr->data.compare_exchange_strong(oldData, newData.get()))
				{
					/*
						Different assembly:
							CountedNodePtr = oldNext = {0}
						which causes error return for compare_exchange_strong
					*/
					//CountedNodePtr a1 = {};
					//CountedNodePtr b1 = {0};
					CountedNodePtr oldNext = {0, nullptr};
					if (!oldTail.ptr->next.compare_exchange_strong(oldNext, newNext))
					{
						delete newNext.ptr;
						newNext = oldNext;
					}
					SetNewTail(oldTail, newNext);
					newData.release();
					break;
				}
				else
				{
					CountedNodePtr oldNext = {0, nullptr};
					if (oldTail.ptr->next.compare_exchange_strong(oldNext, newNext))
					{
						oldNext = newNext;
						newNext.ptr = new Node;
					}
					SetNewTail(oldTail, oldNext);
				}
			}
		}
	};
}