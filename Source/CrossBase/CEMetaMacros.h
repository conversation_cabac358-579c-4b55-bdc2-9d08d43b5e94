#pragma once

#ifdef __CE_HEAD_TOOL_PARSER__
#    define CEMeta(...) __attribute__((annotate("CEMeta:" #    __VA_ARGS__)))
#else
#    define CEMeta(...)
#endif

#ifdef __CE_HEAD_TOOL_PARSER__
#    define static_assert(...)
#    define __noop(...)
#    define __analysis_assume(...)
#    define Assert(...)
#    define AssertIf(...)
#    define AssertMsg(...)
#    define AssertOnce(...)
#endif

#define CE_SerializeContext                         CEMeta(Serialize) cross::SerializeNode Serialize(cross::SerializeContext& context) const;
#define CE_Serialize2Context                        CEMeta(Serialize) void Serialize(cross::SerializeNode& node, cross::SerializeContext& context) const;
#define CE_DeserializeContext                       CEMeta(Serialize) bool Deserialize(const cross::DeserializeNode& in, cross::SerializeContext& context);
#define CE_PostDeserializeContext                   CEMeta(Serialize) bool PostDeserialize(const cross::DeserializeNode& in, cross::SerializeContext& context);
#define CE_Serialize_Deserialize                    CE_SerializeContext; CE_Serialize2Context; CE_DeserializeContext; CE_PostDeserializeContext;
#define CE_SerializeContextEditor                   CEMeta(Serialize, Editor) cross::SerializeNode Serialize(cross::SerializeContext& context) const;
#define CE_Serialize2ContextEditor                  CEMeta(Serialize, Editor) void Serialize(cross::SerializeNode& node, cross::SerializeContext& context) const;
#define CE_DeserializeContextEditor                 CEMeta(Serialize, Editor) bool Deserialize(const cross::DeserializeNode& in, cross::SerializeContext& context);
#define CE_PostDeserializeContextEditor             CEMeta(Serialize, Editor) bool PostDeserialize(const cross::DeserializeNode& in, cross::SerializeContext& context);
#define CE_Serialize_DeserializeEditor              CE_SerializeContextEditor; CE_Serialize2ContextEditor; CE_DeserializeContextEditor; CE_PostDeserializeContextEditor;
#if defined(_MSC_VER)
#define CE_Virtual_Serialize_Deserialize                                                                                                \
        virtual CE_SerializeContext; virtual CE_Serialize2Context; virtual CE_DeserializeContext; virtual CE_PostDeserializeContext;
#define CE_Virtual_Serialize_DeserializeEditor                                                                                               \
        virtual CE_SerializeContextEditor; virtual CE_Serialize2ContextEditor; virtual CE_DeserializeContextEditor; virtual CE_PostDeserializeContextEditor;
#else
#define CE_Virtual_Serialize_Deserialize                                                                                                \
        _Pragma("GCC diagnostic push") _Pragma(R"(GCC diagnostic ignored "-Winconsistent-missing-override")")                           \
        virtual CE_SerializeContext; virtual CE_Serialize2Context; virtual CE_DeserializeContext; virtual CE_PostDeserializeContext;    \
        _Pragma("GCC diagnostic pop")
#define CE_Virtual_Serialize_DeserializeEditor                                                                                              \
        _Pragma("GCC diagnostic push") _Pragma(R"(GCC diagnostic ignored "-Winconsistent-missing-override")")                           \
        virtual CE_SerializeContextEditor; virtual CE_Serialize2ContextEditor; virtual CE_DeserializeContextEditor; virtual CE_PostDeserializeContextEditor;    \
        _Pragma("GCC diagnostic pop")
#endif

#define MACRO_COMBINE_INNER(A, B, C, D) A##B##C##D
#define MACRO_COMBINE(A, B, C, D)       MACRO_COMBINE_INNER(A, B, C, D)

#ifdef __CE_HEAD_TOOL_PARSER__
#    define CEMetaInternal(...) CEMeta(ClassMeta, __VA_ARGS__) int MACRO_COMBINE(__only_use_for_ce_meta, _, __LINE__, _) = __LINE__;
#else
#    define CEMetaInternal(...)
#endif

#if defined(__CE_HEAD_TOOL_PARSER__) && defined(__CE_NO_GENERATED_BODY__)
#    define CEGeneratedCode(CLASS_NAME) using MACRO_COMBINE(__CEMETA_GENERATED_CODE, _, __LINE__, _)  = int;
#else
#    define CEGeneratedCode(CLASS_NAME) MACRO_COMBINE(CURRENT_FILE_ID, _, CLASS_NAME, _GENERATED_BODY)
#endif
#if defined(__GNUC__) || defined(__clang__)
// GCC  Clang
#    define __CE_FUNCSIG__ __PRETTY_FUNCTION__
#elif defined(_MSC_VER)
// Microsoft Visual Studio
#    define __CE_FUNCSIG__ __FUNCSIG__
#else
// standard __func__
#    define __CE_FUNCSIG__ __func__
#endif
                                                                                                                                                                                                                         

#define CEClass(...)                class CEMeta(__VA_ARGS__)
#define CEStruct(...)               struct CEMeta(__VA_ARGS__)
#define CEEnum(...)                 enum CEMeta(__VA_ARGS__)
#define CEEnumClass(...)            enum class CEMeta(__VA_ARGS__)

#define CEProperty(...)             CEMeta(Property, __VA_ARGS__)
#define CEFunction(...)             CEMeta(Function, __VA_ARGS__)
#define CECSAttribute(...)          CEMeta(CsAttribute(#__VA_ARGS__))
#define CEParam(...)                CEMeta(Param, __VA_ARGS__)

#define CESystemInternal(...)       CEMetaInternal(IsSystem, __VA_ARGS__)
#define CEComponentInternal(...)    CEMetaInternal(IsComponent, __VA_ARGS__)

#define CEGameplayInternal(...)     CEMetaInternal(IsGameplay, __VA_ARGS__)

#define BINDING_SYSTEM(System) \
    CEMetaInternal(SystemType = System) \
    friend class System;

#define DEFINE_COMPONENT_READER_WRITER(Component, Reader, Writer)                                                   \
    CEMetaInternal(AddComponent(ComponentType = Component, ComponentReader = Reader, ComponentWriter = Writer))     \
    using Reader = ecs::ScopedComponentRead<Component>;                                                             \
    using Writer = ecs::ScopedComponentWrite<Component>;


namespace CEClassKeyWords {
enum
{
    Editor,
    Script,
    PartOf,
    Puerts
};
}

namespace CEPropertyKeyWords {
enum
{
    SkipSerialization,
    Editor,
    Script,
    Reflect,
    ScriptReadOnly,
    ScriptReadWrite
    //Getter,
    //Setter,
};
}

namespace CEFunctionKeyWords {
enum
{
    AdditionalDeserialize,
    AdditionalSerialize,
    PrecheckDeserializeNode,
    PrecheckSerializeNode,
    Editor,
    Script,
    Reflect,
    ScriptCallable,
    ScriptImplable,
    ScriptAsProperty,
    //Reflect
};
}

namespace CEComponentInternalKeyWorlds {
enum
{
    SystemType,
};
}

namespace CESystemInternalKeyWorlds {
enum
{
    ComponentType,
    ComponentReader,
    ComponentWriter
};
}

#define CREATE_MEMBER_FUNC_CHECK(func_name)                                                                                                                                                                                                    \
    template<typename, typename T>                                                                                                                                                                                                             \
    struct Has_##func_name                                                                                                                                                                                                                     \
    {};                                                                                                                                                                                                                                        \
                                                                                                                                                                                                                                               \
    template<typename C, typename Ret, typename... Args>                                                                                                                                                                                       \
    struct Has_##func_name<C, Ret(Args...)>                                                                                                                                                                                                    \
    {                                                                                                                                                                                                                                          \
    private:                                                                                                                                                                                                                                   \
        template<typename T>                                                                                                                                                                                                                   \
        static constexpr auto check(T*) -> typename std::is_same<decltype(std::declval<T>().func_name(std::declval<Args>()...)), Ret>::type;                                                                                                   \
        template<typename>                                                                                                                                                                                                                     \
        static constexpr std::false_type check(...);                                                                                                                                                                                           \
        typedef decltype(check<C>(0)) type;                                                                                                                                                                                                    \
                                                                                                                                                                                                                                               \
    public:                                                                                                                                                                                                                                    \
        static constexpr bool value = type::value;                                                                                                                                                                                             \
    };


#define CHECK_MEMBER_FUNC(type_name, func_name, func_sig) Has_##func_name<type_name, func_sig>::value

