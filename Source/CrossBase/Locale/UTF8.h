#pragma once

#include <string>

#include "CrossBaseForward.h"
#include "PlatformDefs.h"

namespace cross
{
    using UnicodeChar = UInt16;

    CROSS_BASE_API bool ConvertUTF8toUTF16(const char* source, int srcLength, UnicodeChar* output, int& outlength);
    CROSS_BASE_API bool ConvertUTF16toUTF8(const UInt16* source, int srcLength, char* output, int& outlength);
    CROSS_BASE_API bool ConvertUTF16toUTF8(const UInt16 utf16character, std::string& utf8);

    CROSS_BASE_API std::wstring ConvertUTF8toUTF16(std::string const& str);
    CROSS_BASE_API std::string ConvertUTF16toUTF8(std::wstring const& str);

    CROSS_BASE_API std::string ConvertUTF32toUTF8(std::u32string const& u32str);
    CROSS_BASE_API std::wstring ConvertUTF32toUTF16(std::u32string const& u32str);
}