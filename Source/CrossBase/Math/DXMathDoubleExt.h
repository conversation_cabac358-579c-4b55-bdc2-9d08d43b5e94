#pragma once

#include "External/DirectXMath/Inc/DirectXMath.h"
namespace DirectX {

#if defined(_XM_AVX_INTRINSICS_)
#    define INTRINSICS_DOUBLE_ALIGNMENT 32   // __m256d
#else
#    define INTRINSICS_DOUBLE_ALIGNMENT 16
#endif

    
    /****************************************************************************
 *
 * Constant definitions
 *
 ****************************************************************************/

#if defined(__XNAMATH_H__) && defined(XM_PI_D)
#    undef XM_PI_D
#    undef XM_2PI_D
#    undef XM_1DIVPI_D
#    undef XM_1DIV2PI_D
#    undef XM_PIDIV2_D
#    undef XM_PIDIV4_D
#    undef XM_SELECT_0_D
#    undef XM_SELECT_1_D
#    undef XM_PERMUTE_0X_D
#    undef XM_PERMUTE_0Y_D
#    undef XM_PERMUTE_0Z_D
#    undef XM_PERMUTE_0W_D
#    undef XM_PERMUTE_1X_D
#    undef XM_PERMUTE_1Y_D
#    undef XM_PERMUTE_1Z_D
#    undef XM_PERMUTE_1W_D
#    undef XM_CRMASK_CR6_D
#    undef XM_CRMASK_CR6TRUE_D
#    undef XM_CRMASK_CR6FALSE_D
#    undef XM_CRMASK_CR6BOUNDS_D
#    undef XM_CACHE_LINE_SIZE_D
#endif

constexpr double XM_PI_D = 3.14159265358979323846;
constexpr double XM_2PI_D = 6.28318530717958647692;
constexpr double XM_1DIVPI_D = 0.318309886183790671538;
constexpr double XM_1DIV2PI_D = 0.159154943091895335769;
constexpr double XM_PIDIV2_D = 1.57079632679489661923;
constexpr double XM_PIDIV4_D = 0.785398163397448309616;

constexpr uint64_t XM_SELECT_0_D = 0x0000000000000000;
constexpr uint64_t XM_SELECT_1_D = 0xFFFFFFFFFFFFFFFF;
              
constexpr uint64_t XM_PERMUTE_0X_D = 0;
constexpr uint64_t XM_PERMUTE_0Y_D = 1;
constexpr uint64_t XM_PERMUTE_0Z_D = 2;
constexpr uint64_t XM_PERMUTE_0W_D = 3;
constexpr uint64_t XM_PERMUTE_1X_D = 4;
constexpr uint64_t XM_PERMUTE_1Y_D = 5;
constexpr uint64_t XM_PERMUTE_1Z_D = 6;
constexpr uint64_t XM_PERMUTE_1W_D = 7;
              
constexpr uint64_t XM_SWIZZLE_X_D = 0;
constexpr uint64_t XM_SWIZZLE_Y_D = 1;
constexpr uint64_t XM_SWIZZLE_Z_D = 2;
constexpr uint64_t XM_SWIZZLE_W_D = 3;
              
constexpr uint64_t XM_CRMASK_CR6_D = 0x00000000000000F0;
constexpr uint64_t XM_CRMASK_CR6TRUE_D = 0x0000000000000080;
constexpr uint64_t XM_CRMASK_CR6FALSE_D = 0x0000000000000020;
constexpr uint64_t XM_CRMASK_CR6BOUNDS_D = XM_CRMASK_CR6FALSE_D;

constexpr size_t XM_CACHE_LINE_SIZE_D = 64;


 // Unit conversion

inline constexpr double XMConvertToRadians(double dDegrees) noexcept { return dDegrees * (XM_PI_D / 180.0); }
inline constexpr double XMConvertToDegrees(double dRadians) noexcept { return dRadians * (180.0 / XM_PI_D); }

#pragma warning(push)
#pragma warning(disable : 4068 4201 4365 4324 4820)
// C4068: ignore unknown pragmas
// C4201: nonstandard extension used : nameless struct/union
// C4365: Off by default noise
// C4324/4820: padding warnings
// 
// 
#ifdef _PREFAST_
#    pragma prefast(push)
#    pragma prefast(disable : 25000, "FXMVECTOR is 16 bytes")
#endif
//------------------------------------------------------------------------------
#if defined(_XM_NO_INTRINSICS_) || defined(_XM_ARM_NEON_INTRINSICS_)
struct __vector256d
{
    union
    {
        double vector4_d64[4];
        uint64_t vector4_u64[4];
    };
};
#endif   // _XM_NO_INTRINSICS_

//------------------------------------------------------------------------------
// Vector intrinsic: Four 64 bit floating point components aligned on a 16 byte
// boundary and mapped to hardware vector registers
#if defined(_XM_AVX_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)
using XMVECTOR256D = __m256d;

#elif defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)
struct alignas(INTRINSICS_DOUBLE_ALIGNMENT) XMVECTOR256D
{
    __m128d xy;
    __m128d zw;
};
#elif defined(_XM_ARM_NEON_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)
using XMVECTOR256D = __vector256d;   // todo: neon SIMDVector4d
#else
using XMVECTOR256D = __vector256d;
#endif

// Fix-up for (1st-3rd) XMVECTOR parameters that are pass-in-register for x86, ARM, ARM64, and vector call; by reference otherwise
#if (defined(_M_IX86) || defined(_M_ARM) || defined(_M_ARM64) || _XM_VECTORCALL_ || __i386__ || __arm__ || __aarch64__) && !defined(_XM_NO_INTRINSICS_)
typedef const XMVECTOR256D FXMVECTOR256D;
#else
typedef const XMVECTOR256D& FXMVECTOR256D;
#endif

// Fix-up for (4th) XMVECTOR parameter to pass in-register for ARM, ARM64, and vector call; by reference otherwise
#if (defined(_M_ARM) || defined(_M_ARM64) || defined(_M_HYBRID_X86_ARM64) || defined(_M_ARM64EC) || _XM_VECTORCALL_ || __arm__ || __aarch64__) && !defined(_XM_NO_INTRINSICS_)
typedef const XMVECTOR256D GXMVECTOR256D;
#else
typedef const XMVECTOR256D& GXMVECTOR256D;
#endif

// Fix-up for (5th & 6th) XMVECTOR parameter to pass in-register for ARM64 and vector call; by reference otherwise
#if (defined(_M_ARM64) || defined(_M_HYBRID_X86_ARM64) || defined(_M_ARM64EC) || _XM_VECTORCALL_ || __aarch64__) && !defined(_XM_NO_INTRINSICS_)
typedef const XMVECTOR256D HXMVECTOR256D;
#else
typedef const XMVECTOR256D& HXMVECTOR256D;
#endif

// Fix-up for (7th+) XMVECTOR parameters to pass by reference
typedef const XMVECTOR256D& CXMVECTOR256D;

//------------------------------------------------------------------------------
// Conversion types for constants
XM_ALIGNED_STRUCT(32) XMVECTORD64
{
    union
    {
        double d[4];
        XMVECTOR256D v;
    };

    inline operator XMVECTOR256D() const noexcept
    {
        return v;
    }
    inline operator const double*() const noexcept
    {
        return d;
    }
};

XM_ALIGNED_STRUCT(32) XMVECTORI64
{
    union
    {
        int64_t i[4];
        XMVECTOR256D v;
    };

    inline operator XMVECTOR256D() const noexcept
    {
        return v;
    }
};

XM_ALIGNED_STRUCT(32) XMVECTORU64
{
    union
    {
        uint64_t u[4];
        XMVECTOR256D v;
    };

    inline operator XMVECTOR256D() const noexcept
    {
        return v;
    }
};

    //------------------------------------------------------------------------------
    // Vector operators
#ifndef _XM_NO_XMVECTOR_OVERLOADS_
XMVECTOR256D XM_CALLCONV operator+(FXMVECTOR256D V) noexcept;
XMVECTOR256D XM_CALLCONV operator-(FXMVECTOR256D V) noexcept;

XMVECTOR256D& XM_CALLCONV operator+=(XMVECTOR256D& V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D& XM_CALLCONV operator-=(XMVECTOR256D& V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D& XM_CALLCONV operator*=(XMVECTOR256D& V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D& XM_CALLCONV operator/=(XMVECTOR256D& V1, FXMVECTOR256D V2) noexcept;

XMVECTOR256D& operator*=(XMVECTOR256D& V, double S) noexcept;
XMVECTOR256D& operator/=(XMVECTOR256D& V, double S) noexcept;

XMVECTOR256D XM_CALLCONV operator+(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D XM_CALLCONV operator-(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D XM_CALLCONV operator*(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D XM_CALLCONV operator/(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
XMVECTOR256D XM_CALLCONV operator*(FXMVECTOR256D V, double S) noexcept;
XMVECTOR256D XM_CALLCONV operator*(double S, FXMVECTOR256D V) noexcept;
XMVECTOR256D XM_CALLCONV operator/(FXMVECTOR256D V, double S) noexcept;
#endif /* !_XM_NO_XMVECTOR_OVERLOADS_ */


struct XMMATRIX64;

// Fix-up for (1st) XMMATRIX parameter to pass in-register for ARM64 and vector call; by reference otherwise
#if (defined(_M_ARM64) || defined(_M_HYBRID_X86_ARM64) || defined(_M_ARM64EC) || _XM_VECTORCALL_ || __aarch64__) && !defined(_XM_NO_INTRINSICS_)
typedef const XMMATRIX64 FXMMATRIX64;
#else
typedef const XMMATRIX64& FXMMATRIX64;
#endif

// Fix-up for (2nd+) XMMATRIX parameters to pass by reference
typedef const XMMATRIX64& CXMMATRIX64;

#ifdef _XM_NO_INTRINSICS_
struct XMMATRIX64
#else
struct alignas(INTRINSICS_DOUBLE_ALIGNMENT) XMMATRIX64
#endif
{
#if defined(_XM_NO_INTRINSICS_) || !defined(_XM_SSE_INTRINSICS_)
    union
    {
        XMVECTOR256D r[4];
        struct
        {
            double _11, _12, _13, _14;
            double _21, _22, _23, _24;
            double _31, _32, _33, _34;
            double _41, _42, _43, _44;
        };
        double m[4][4];
    };
#else
    XMVECTOR256D r[4];
#endif
};
//------------------------------------------------------------------------------
// 2D Vector; 64 bit floating point components
struct XMDOUBLE2
{
    double x;
    double y;

    XMDOUBLE2() = default;

    XMDOUBLE2(const XMDOUBLE2&) = default;
    XMDOUBLE2& operator=(const XMDOUBLE2&) = default;

    XMDOUBLE2(XMDOUBLE2&&) = default;
    XMDOUBLE2& operator=(XMDOUBLE2&&) = default;

    constexpr XMDOUBLE2(double _x, double _y) noexcept
        : x(_x)
        , y(_y)
    {}
    explicit XMDOUBLE2(_In_reads_(2) const double* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
    {}
};

// 2D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(16) XMDOUBLE2A : public XMDOUBLE2
{
    XMDOUBLE2A() = default;

    XMDOUBLE2A(const XMDOUBLE2A&) = default;
    XMDOUBLE2A& operator=(const XMDOUBLE2A&) = default;

    XMDOUBLE2A(XMDOUBLE2A &&) = default;
    XMDOUBLE2A& operator=(XMDOUBLE2A&&) = default;

    constexpr XMDOUBLE2A(double _x, double _y) noexcept
        : XMDOUBLE2(_x, _y)
    {}
    explicit XMDOUBLE2A(_In_reads_(2) const double* pArray) noexcept
        : XMDOUBLE2(pArray)
    {}
};
//------------------------------------------------------------------------------
// 3D Vector; 64 bit floating point components
struct XMDOUBLE3
{
    double x;
    double y;
    double z;

    XMDOUBLE3() = default;

    XMDOUBLE3(const XMDOUBLE3&) = default;
    XMDOUBLE3& operator=(const XMDOUBLE3&) = default;

    XMDOUBLE3(XMDOUBLE3&&) = default;
    XMDOUBLE3& operator=(XMDOUBLE3&&) = default;

    constexpr XMDOUBLE3(double _x, double _y, double _z) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
    {}
    explicit XMDOUBLE3(_In_reads_(3) const double* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
    {}
};

// 3D Vector; 32 bit floating point components aligned on a 32 byte boundary
XM_ALIGNED_STRUCT(32) XMDOUBLE3A : public XMDOUBLE3
{
    XMDOUBLE3A() = default;

    XMDOUBLE3A(const XMDOUBLE3A&) = default;
    XMDOUBLE3A& operator=(const XMDOUBLE3A&) = default;

    XMDOUBLE3A(XMDOUBLE3A &&) = default;
    XMDOUBLE3A& operator=(XMDOUBLE3A&&) = default;

    constexpr XMDOUBLE3A(double _x, double _y, double _z) noexcept
        : XMDOUBLE3(_x, _y, _z)
    {}
    explicit XMDOUBLE3A(_In_reads_(3) const double* pArray) noexcept
        : XMDOUBLE3(pArray)
    {}
};

//------------------------------------------------------------------------------
// 4D Vector; 64 bit floating point components
struct XMDOUBLE4
{
    double x;
    double y;
    double z;
    double w;

    XMDOUBLE4() = default;

    XMDOUBLE4(const XMDOUBLE4&) = default;
    XMDOUBLE4& operator=(const XMDOUBLE4&) = default;

    XMDOUBLE4(XMDOUBLE4&&) = default;
    XMDOUBLE4& operator=(XMDOUBLE4&&) = default;

    constexpr XMDOUBLE4(double _x, double _y, double _z, double _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}
    explicit XMDOUBLE4(_In_reads_(4) const double* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}
};

// 4D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(16) XMDOUBLE4A : public XMDOUBLE4
{
    XMDOUBLE4A() = default;

    XMDOUBLE4A(const XMDOUBLE4A&) = default;
    XMDOUBLE4A& operator=(const XMDOUBLE4A&) = default;

    XMDOUBLE4A(XMDOUBLE4A &&) = default;
    XMDOUBLE4A& operator=(XMDOUBLE4A&&) = default;

    constexpr XMDOUBLE4A(double _x, double _y, double _z, double _w) noexcept
        : XMDOUBLE4(_x, _y, _z, _w)
    {}
    explicit XMDOUBLE4A(_In_reads_(4) const double* pArray) noexcept
        : XMDOUBLE4(pArray)
    {}
};

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgnu-anonymous-struct"
#pragma clang diagnostic ignored "-Wnested-anon-types"
#endif
//------------------------------------------------------------------------------
// 4x4 Matrix: 64 bit floating point components
struct XMDOUBLE4X4
{
    union
    {
        struct
        {
            double _11, _12, _13, _14;
            double _21, _22, _23, _24;
            double _31, _32, _33, _34;
            double _41, _42, _43, _44;
        };
        double m[4][4];
    };

    XMDOUBLE4X4() = default;

    XMDOUBLE4X4(const XMDOUBLE4X4&) = default;
    XMDOUBLE4X4& operator=(const XMDOUBLE4X4&) = default;

    XMDOUBLE4X4(XMDOUBLE4X4&&) = default;
    XMDOUBLE4X4& operator=(XMDOUBLE4X4&&) = default;

    constexpr XMDOUBLE4X4(double m00, double m01, double m02, double m03, double m10, double m11, double m12, double m13, double m20, double m21, double m22, double m23, double m30, double m31, double m32, double m33) noexcept
        : _11(m00)
        , _12(m01)
        , _13(m02)
        , _14(m03)
        , _21(m10)
        , _22(m11)
        , _23(m12)
        , _24(m13)
        , _31(m20)
        , _32(m21)
        , _33(m22)
        , _34(m23)
        , _41(m30)
        , _42(m31)
        , _43(m32)
        , _44(m33)
    {}
    explicit XMDOUBLE4X4(_In_reads_(16) const double* pArray) noexcept;

    double operator()(size_t Row, size_t Column) const noexcept
    {
        return m[Row][Column];
    }
    double& operator()(size_t Row, size_t Column) noexcept
    {
        return m[Row][Column];
    }
};

// 4x4 Matrix: 64 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(32) XMDOUBLE4X4A : public XMDOUBLE4X4
{
    XMDOUBLE4X4A() = default;

    XMDOUBLE4X4A(const XMDOUBLE4X4A&) = default;
    XMDOUBLE4X4A& operator=(const XMDOUBLE4X4A&) = default;

    XMDOUBLE4X4A(XMDOUBLE4X4A &&) = default;
    XMDOUBLE4X4A& operator=(XMDOUBLE4X4A&&) = default;

    constexpr XMDOUBLE4X4A(double m00, double m01, double m02, double m03, double m10, double m11, double m12, double m13, double m20, double m21, double m22, double m23, double m30, double m31, double m32, double m33) noexcept
        : XMDOUBLE4X4(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33)
    {}
    explicit XMDOUBLE4X4A(_In_reads_(16) const double* pArray) noexcept
        : XMDOUBLE4X4(pArray)
    {}
};

#ifdef __clang__
#pragma clang diagnostic pop
#endif

#ifdef _PREFAST_
#pragma prefast(pop)
#endif

#pragma warning(pop)

////////////////////////////////////////////////////////////////////////////////
    /****************************************************************************
     *
     * Data conversion operations
     *
     ****************************************************************************/

    XMVECTOR256D XM_CALLCONV XMConvertVectorFloatToDouble(XMVECTOR VFloat) noexcept;
    XMVECTOR XM_CALLCONV XMConvertVectorDoubleToFloat(FXMVECTOR256D VDouble) noexcept;
    XMVECTOR256D XM_CALLCONV XMConvertVectorInt64ToDouble(FXMVECTOR256D VInt, uint32_t DivExponent) noexcept;
    XMVECTOR256D XM_CALLCONV XMConvertVectorDoubleToInt64(FXMVECTOR256D VDouble, uint32_t MulExponent) noexcept;
    XMVECTOR256D XM_CALLCONV XMConvertVectorUInt64ToDouble(FXMVECTOR256D VUInt, uint32_t DivExponent) noexcept;
    XMVECTOR256D XM_CALLCONV XMConvertVectorDoubleToUInt64(FXMVECTOR256D VDouble, uint32_t MulExponent) noexcept;

    /****************************************************************************
     *
     * Load operations
     *
     ****************************************************************************/

    XMVECTOR256D XM_CALLCONV XMLoadDouble(_In_ const double* pSource) noexcept;

    XMVECTOR256D XM_CALLCONV XMLoadDouble2(_In_ const XMDOUBLE2* pSource) noexcept;
    XMVECTOR256D XM_CALLCONV XMLoadDouble2A(_In_ const XMDOUBLE2A* pSource) noexcept;
    XMVECTOR256D XM_CALLCONV XMLoadDouble3(_In_ const XMDOUBLE3* pSource) noexcept;
    XMVECTOR256D XM_CALLCONV XMLoadDouble3A(_In_ const XMDOUBLE3A* pSource) noexcept;

    XMVECTOR256D XM_CALLCONV XMLoadDouble4(_In_ const XMDOUBLE4* pSource) noexcept;
    XMVECTOR256D XM_CALLCONV XMLoadDouble4A(_In_ const XMDOUBLE4A* pSource) noexcept;
    XMMATRIX64 XM_CALLCONV XMLoadDouble4x4(_In_ const XMDOUBLE4X4* pSource) noexcept;
    XMMATRIX64 XM_CALLCONV XMLoadDouble4x4A(_In_ const XMDOUBLE4X4A* pSource) noexcept;

    /****************************************************************************
     *
     * Store operations
     *
     ****************************************************************************/
    void XM_CALLCONV XMStoreDouble(_Out_ double* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble2(_Out_ XMDOUBLE2* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble2A(_Out_ XMDOUBLE2A* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble3(_Out_ XMDOUBLE3* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble3A(_Out_ XMDOUBLE3A* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble4(_Out_ XMDOUBLE4* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble4A(_Out_ XMDOUBLE4A* pDestination, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMStoreDouble4x4(_Out_ XMDOUBLE4X4* pDestination, _In_ XMMATRIX64 M) noexcept;
    void XM_CALLCONV XMStoreDouble4x4A(_Out_ XMDOUBLE4X4A* pDestination, _In_ XMMATRIX64 M) noexcept;

    /****************************************************************************
     *
     * General vector operations
     *
     ****************************************************************************/

    XMVECTOR256D XM_CALLCONV XMVector256Zero() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Set(double x, double y, double z, double w) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetInt(uint64_t x, uint64_t y, uint64_t z, uint64_t w) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Replicate(double Value) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ReplicatePtr(_In_ const double* pValue) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ReplicateInt(uint64_t Value) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ReplicateIntPtr(_In_ const uint64_t* pValue) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256TrueInt() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256FalseInt() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatX(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatY(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatZ(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatW(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatOne() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatInfinity() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatQNaN() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatEpsilon() noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SplatSignMask() noexcept;

    double XM_CALLCONV XMVector256GetByIndex(FXMVECTOR256D V, size_t i) noexcept;
    double XM_CALLCONV XMVector256GetX(FXMVECTOR256D V) noexcept;
    double XM_CALLCONV XMVector256GetY(FXMVECTOR256D V) noexcept;
    double XM_CALLCONV XMVector256GetZ(FXMVECTOR256D V) noexcept;
    double XM_CALLCONV XMVector256GetW(FXMVECTOR256D V) noexcept;

    void XM_CALLCONV XMVector256GetByIndexPtr(_Out_ double* f, _In_ FXMVECTOR256D V, _In_ size_t i) noexcept;
    void XM_CALLCONV XMVector256GetXPtr(_Out_ double* x, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256GetYPtr(_Out_ double* y, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256GetZPtr(_Out_ double* z, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256GetWPtr(_Out_ double* w, _In_ FXMVECTOR256D V) noexcept;

    uint64_t XM_CALLCONV XMVector256GetIntByIndex(FXMVECTOR256D V, size_t i) noexcept;
    uint64_t XM_CALLCONV XMVector256GetIntX(FXMVECTOR256D V) noexcept;
    uint64_t XM_CALLCONV XMVector256GetIntY(FXMVECTOR256D V) noexcept;
    uint64_t XM_CALLCONV XMVector256GetIntZ(FXMVECTOR256D V) noexcept;
    uint64_t XM_CALLCONV XMVector256GetIntW(FXMVECTOR256D V) noexcept;

    void XM_CALLCONV XMVector256GetIntByIndexPtr(_Out_ uint64_t* x, _In_ FXMVECTOR256D V, _In_ size_t i) noexcept;
    void XM_CALLCONV XMVector256GetIntXPtr(_Out_ uint64_t* x, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256GetIntYPtr(_Out_ uint64_t* y, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256GetIntZPtr(_Out_ uint64_t* z, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256GetIntWPtr(_Out_ uint64_t* w, _In_ FXMVECTOR256D V) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256SetByIndex(FXMVECTOR256D V, double f, size_t i) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetX(FXMVECTOR256D V, double x) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetY(FXMVECTOR256D V, double y) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetZ(FXMVECTOR256D V, double z) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetW(FXMVECTOR256D V, double w) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256SetByIndexPtr(_In_ FXMVECTOR256D V, _In_ const double* f, _In_ size_t i) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetXPtr(_In_ FXMVECTOR256D V, _In_ const double* x) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetYPtr(_In_ FXMVECTOR256D V, _In_ const double* y) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetZPtr(_In_ FXMVECTOR256D V, _In_ const double* z) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetWPtr(_In_ FXMVECTOR256D V, _In_ const double* w) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256SetIntByIndex(FXMVECTOR256D V, uint64_t x, size_t i) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntX(FXMVECTOR256D V, uint64_t x) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntY(FXMVECTOR256D V, uint64_t y) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntZ(FXMVECTOR256D V, uint64_t z) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntW(FXMVECTOR256D V, uint64_t w) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256SetIntByIndexPtr(_In_ FXMVECTOR256D V, _In_ const uint64_t* x, _In_ size_t i) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntXPtr(_In_ FXMVECTOR256D V, _In_ const uint64_t* x) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntYPtr(_In_ FXMVECTOR256D V, _In_ const uint64_t* y) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntZPtr(_In_ FXMVECTOR256D V, _In_ const uint64_t* z) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SetIntWPtr(_In_ FXMVECTOR256D V, _In_ const uint64_t* w) noexcept;

#if defined(__XNAMATH_H__) && defined(XMVector256Swizzle)
#    undef XMVector256Swizzle
#endif

    XMVECTOR256D XM_CALLCONV XMVector256Swizzle(FXMVECTOR256D V, uint64_t E0, uint64_t E1, uint64_t E2, uint64_t E3) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Permute(FXMVECTOR256D V1, FXMVECTOR256D V2, uint64_t PermuteX, uint64_t PermuteY, uint64_t PermuteZ, uint64_t PermuteW) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SelectControl(uint64_t Vector256Index0, uint64_t Vector256Index1, uint64_t Vector256Index2, uint64_t Vector256Index3) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Select(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D Control) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256MergeXY(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256MergeZW(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;

#if defined(__XNAMATH_H__) && defined(XMVector256ShiftLeft)
#    undef XMVector256ShiftLeft
#    undef XMVector256RotateLeft
#    undef XMVector256RotateRight
#    undef XMVector256Insert
#endif

    XMVECTOR256D XM_CALLCONV XMVector256ShiftLeft(FXMVECTOR256D V1, FXMVECTOR256D V2, uint64_t Elements) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256RotateLeft(FXMVECTOR256D V, uint64_t Elements) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256RotateRight(FXMVECTOR256D V, uint64_t Elements) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Insert(FXMVECTOR256D VD, FXMVECTOR256D VS, uint64_t VSLeftRotateElements, uint64_t Select0, uint64_t Select1, uint64_t Select2, uint64_t Select3) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256Equal(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256EqualR(_Out_ uint64_t* pCR, _In_ FXMVECTOR256D V1, _In_ FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256EqualInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256EqualIntR(_Out_ uint64_t* pCR, _In_ FXMVECTOR256D V, _In_ FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256NearEqual(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D Epsilon) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256NotEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256NotEqualInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Greater(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256GreaterR(_Out_ uint64_t* pCR, _In_ FXMVECTOR256D V1, _In_ FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256GreaterOrEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256GreaterOrEqualR(_Out_ uint64_t* pCR, _In_ FXMVECTOR256D V1, _In_ FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Less(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256LessOrEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256InBounds(FXMVECTOR256D V, FXMVECTOR256D Bounds) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256InBoundsR(_Out_ uint64_t* pCR, _In_ FXMVECTOR256D V, _In_ FXMVECTOR256D Bounds) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256IsNaN(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256IsInfinite(FXMVECTOR256D V) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256Min(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Max(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Round(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Truncate(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Floor(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Ceiling(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Clamp(FXMVECTOR256D V, FXMVECTOR256D Min, FXMVECTOR256D Max) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Saturate(FXMVECTOR256D V) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256AndInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256AndCInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256OrInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256NorInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256XorInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector256Negate(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Add(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Sum(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256AddAngles(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Subtract(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SubtractAngles(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Multiply(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256MultiplyAdd(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D V3) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Divide(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256NegativeMultiplySubtract(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D V3) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Scale(FXMVECTOR256D V, double ScaleFactor) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ReciprocalEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Reciprocal(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SqrtEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Sqrt(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ReciprocalSqrtEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ReciprocalSqrt(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Exp2(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Exp10(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ExpE(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Exp(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Log2(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Log10(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256LogE(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Log(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Pow(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Abs(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Mod(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ModAngles(FXMVECTOR256D Angles) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Sin(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SinEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Cos(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256CosEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SinCos(FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256SinCos(_Out_ XMVECTOR256D* pSin, _Out_ XMVECTOR256D* pCos, _In_ FXMVECTOR256D V) noexcept;
    void XM_CALLCONV XMVector256SinCosEst(_Out_ XMVECTOR256D* pSin, _Out_ XMVECTOR256D* pCos, _In_ FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Tan(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256TanEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256SinH(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256CosH(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256TanH(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ASin(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ASinEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ACos(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ACosEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ATan(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ATanEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ATan2(FXMVECTOR256D Y, FXMVECTOR256D X) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256ATan2Est(FXMVECTOR256D Y, FXMVECTOR256D X) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Lerp(FXMVECTOR256D V0, FXMVECTOR256D V1, double t) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256LerpV(FXMVECTOR256D V0, FXMVECTOR256D V1, FXMVECTOR256D T) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256Hermite(FXMVECTOR256D Position0, FXMVECTOR256D Tangent0, FXMVECTOR256D Position1, GXMVECTOR256D Tangent1, double t) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256HermiteV(FXMVECTOR256D Position0, FXMVECTOR256D Tangent0, FXMVECTOR256D Position1, GXMVECTOR256D Tangent1, HXMVECTOR256D T) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256CatmullRom(FXMVECTOR256D Position0, FXMVECTOR256D Position1, FXMVECTOR256D Position2, GXMVECTOR256D Position3, double t) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256CatmullRomV(FXMVECTOR256D Position0, FXMVECTOR256D Position1, FXMVECTOR256D Position2, GXMVECTOR256D Position3, HXMVECTOR256D T) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256BaryCentric(FXMVECTOR256D Position0, FXMVECTOR256D Position1, FXMVECTOR256D Position2, double f, double g) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector256BaryCentricV(FXMVECTOR256D Position0, FXMVECTOR256D Position1, FXMVECTOR256D Position2, GXMVECTOR256D F, HXMVECTOR256D G) noexcept;

    /****************************************************************************
     *
     * 2D vector operations TODO
     * 
     ****************************************************************************/

    /****************************************************************************
     *
     * 3D vector operations
     *
     ****************************************************************************/

    bool XM_CALLCONV XMVector64x3Equal(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x3EqualR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3EqualInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x3EqualIntR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3NearEqual(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D Epsilon) noexcept;
    bool XM_CALLCONV XMVector64x3NotEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3NotEqualInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3Greater(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x3GreaterR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3GreaterOrEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x3GreaterOrEqualR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3Less(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3LessOrEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x3InBounds(FXMVECTOR256D V, FXMVECTOR256D Bounds) noexcept;

    bool XM_CALLCONV XMVector64x3IsNaN(FXMVECTOR256D V) noexcept;
    bool XM_CALLCONV XMVector64x3IsInfinite(FXMVECTOR256D V) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector64x3Dot(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Cross(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3LengthSq(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3ReciprocalLengthEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3ReciprocalLength(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3LengthEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Length(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3NormalizeEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Normalize(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3ClampLength(FXMVECTOR256D V, double LengthMin, double LengthMax) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3ClampLengthV(FXMVECTOR256D V, FXMVECTOR256D LengthMin, FXMVECTOR256D LengthMax) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Reflect(FXMVECTOR256D Incident, FXMVECTOR256D Normal) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Refract(FXMVECTOR256D Incident, FXMVECTOR256D Normal, double RefractionIndex) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3RefractV(FXMVECTOR256D Incident, FXMVECTOR256D Normal, FXMVECTOR256D RefractionIndex) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Orthogonal(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3AngleBetweenNormalsEst(FXMVECTOR256D N1, FXMVECTOR256D N2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3AngleBetweenNormals(FXMVECTOR256D N1, FXMVECTOR256D N2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3AngleBetweenVectors(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3LinePointDistance(FXMVECTOR256D LinePoint1, FXMVECTOR256D LinePoint2, FXMVECTOR256D Point) noexcept;
    void XM_CALLCONV XMVector64x3ComponentsFromNormal(_Out_ XMVECTOR256D* pParallel, _Out_ XMVECTOR256D* pPerpendicular, _In_ FXMVECTOR256D V, _In_ FXMVECTOR256D Normal) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Rotate(FXMVECTOR256D V, FXMVECTOR256D RotationQuaternion) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3InverseRotate(FXMVECTOR256D V, FXMVECTOR256D RotationQuaternion) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Transform(FXMVECTOR256D V, FXMMATRIX64 M) noexcept;
    XMDOUBLE4* XM_CALLCONV XMVector64x3TransformStream(_Out_writes_bytes_(sizeof(XMDOUBLE4) + OutputStride * (VectorCount - 1)) XMDOUBLE4* pOutputStream, _In_ size_t OutputStride,
                                                   _In_reads_bytes_(sizeof(XMDOUBLE3) + InputStride * (VectorCount - 1)) const XMDOUBLE3* pInputStream, _In_ size_t InputStride, _In_ size_t VectorCount, _In_ FXMMATRIX64 M) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3TransformCoord(FXMVECTOR256D V, FXMMATRIX64 M) noexcept;
    XMDOUBLE3* XM_CALLCONV XMVector64x3TransformCoordStream(_Out_writes_bytes_(sizeof(XMDOUBLE3) + OutputStride * (VectorCount - 1)) XMDOUBLE3* pOutputStream, _In_ size_t OutputStride,
                                                        _In_reads_bytes_(sizeof(XMDOUBLE3) + InputStride * (VectorCount - 1)) const XMDOUBLE3* pInputStream, _In_ size_t InputStride, _In_ size_t VectorCount, _In_ FXMMATRIX64 M) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3TransformNormal(FXMVECTOR256D V, FXMMATRIX64 M) noexcept;
    XMDOUBLE3* XM_CALLCONV XMVector64x3TransformNormalStream(_Out_writes_bytes_(sizeof(XMDOUBLE3) + OutputStride * (VectorCount - 1)) XMDOUBLE3* pOutputStream, _In_ size_t OutputStride,
                                                         _In_reads_bytes_(sizeof(XMDOUBLE3) + InputStride * (VectorCount - 1)) const XMDOUBLE3* pInputStream, _In_ size_t InputStride, _In_ size_t VectorCount, _In_ FXMMATRIX64 M) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Project(FXMVECTOR256D V, double ViewportX, double ViewportY, double VpWidth, double VpHeight, double VpMinZ, double VpMaxZ, FXMMATRIX64 P, CXMMATRIX64 VM, CXMMATRIX64 W) noexcept;
    XMDOUBLE3* XM_CALLCONV XMVector64x3ProjectStream(_Out_writes_bytes_(sizeof(XMDOUBLE3) + OutputStride * (VectorCount - 1)) XMDOUBLE3* pOutputStream, _In_ size_t OutputStride,
                                                 _In_reads_bytes_(sizeof(XMDOUBLE3) + InputStride * (VectorCount - 1)) const XMDOUBLE3* pInputStream, _In_ size_t InputStride, _In_ size_t VectorCount, _In_ double ViewportX,
                                                 _In_ double ViewportY, _In_ double ViewportWidth, _In_ double ViewportHeight, _In_ double ViewportMinZ, _In_ double ViewportMaxZ, _In_ FXMMATRIX64 Projection, _In_ CXMMATRIX64 View,
                                                 _In_ CXMMATRIX64 World) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x3Unproject(FXMVECTOR256D V, double ViewportX, double ViewportY, double VpWidth, double VpHeight, double VpMinZ, double VpMaxZ, FXMMATRIX64 P, CXMMATRIX64 VM, CXMMATRIX64 W) noexcept;
    XMDOUBLE3* XM_CALLCONV XMVector64x3UnprojectStream(_Out_writes_bytes_(sizeof(XMDOUBLE3) + OutputStride * (VectorCount - 1)) XMDOUBLE3* pOutputStream, _In_ size_t OutputStride,
                                                   _In_reads_bytes_(sizeof(XMDOUBLE3) + InputStride * (VectorCount - 1)) const XMDOUBLE3* pInputStream, _In_ size_t InputStride, _In_ size_t VectorCount, _In_ double ViewportX,
                                                   _In_ double ViewportY, _In_ double ViewportWidth, _In_ double ViewportHeight, _In_ double ViewportMinZ, _In_ double ViewportMaxZ, _In_ FXMMATRIX64 Projection, _In_ CXMMATRIX64 View,
                                                   _In_ CXMMATRIX64 World) noexcept;

    /****************************************************************************
     *
     * 4D vector operations
     *
     ****************************************************************************/

    bool XM_CALLCONV XMVector64x4Equal(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x4EqualR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4EqualInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x4EqualIntR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4NearEqual(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D Epsilon) noexcept;
    bool XM_CALLCONV XMVector64x4NotEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4NotEqualInt(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4Greater(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x4GreaterR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4GreaterOrEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    uint64_t XM_CALLCONV XMVector64x4GreaterOrEqualR(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4Less(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4LessOrEqual(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    bool XM_CALLCONV XMVector64x4InBounds(FXMVECTOR256D V, FXMVECTOR256D Bounds) noexcept;

    bool XM_CALLCONV XMVector64x4IsNaN(FXMVECTOR256D V) noexcept;
    bool XM_CALLCONV XMVector64x4HasNaN(FXMVECTOR256D V) noexcept;
    bool XM_CALLCONV XMVector64x4IsInfinite(FXMVECTOR256D V) noexcept;
    bool XM_CALLCONV XMVector64x4HasInfinite(FXMVECTOR256D V) noexcept;

    XMVECTOR256D XM_CALLCONV XMVector64x4Dot(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Cross(FXMVECTOR256D V1, FXMVECTOR256D V2, FXMVECTOR256D V3) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4LengthSq(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4ReciprocalLengthEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4ReciprocalLength(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4LengthEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Length(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4NormalizeEst(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Normalize(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4ClampLength(FXMVECTOR256D V, double LengthMin, double LengthMax) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4ClampLengthV(FXMVECTOR256D V, FXMVECTOR256D LengthMin, FXMVECTOR256D LengthMax) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Reflect(FXMVECTOR256D Incident, FXMVECTOR256D Normal) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Refract(FXMVECTOR256D Incident, FXMVECTOR256D Normal, double RefractionIndex) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4RefractV(FXMVECTOR256D Incident, FXMVECTOR256D Normal, FXMVECTOR256D RefractionIndex) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Orthogonal(FXMVECTOR256D V) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4AngleBetweenNormalsEst(FXMVECTOR256D N1, FXMVECTOR256D N2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4AngleBetweenNormals(FXMVECTOR256D N1, FXMVECTOR256D N2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4AngleBetweenVectors(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMVector64x4Transform(FXMVECTOR256D V, FXMMATRIX64 M) noexcept;
    XMDOUBLE4* XM_CALLCONV XMVector64x4TransformStream(_Out_writes_bytes_(sizeof(XMDOUBLE4) + OutputStride * (VectorCount - 1)) XMDOUBLE4* pOutputStream, _In_ size_t OutputStride,
                                                   _In_reads_bytes_(sizeof(XMDOUBLE4) + InputStride * (VectorCount - 1)) const XMDOUBLE4* pInputStream, _In_ size_t InputStride, _In_ size_t VectorCount, _In_ FXMMATRIX M) noexcept;

    /****************************************************************************
     *
     * Matrix operations
     *
     ****************************************************************************/

    bool XM_CALLCONV XMMatrix64IsNaN(FXMMATRIX64 M) noexcept;
    bool XM_CALLCONV XMMatrix64IsInfinite(FXMMATRIX64 M) noexcept;
    bool XM_CALLCONV XMMatrix64IsIdentity(FXMMATRIX64 M) noexcept;

    XMMATRIX64 XM_CALLCONV XMMatrix64Multiply(FXMMATRIX64 M1, CXMMATRIX64 M2) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64MultiplyTranspose(FXMMATRIX64 M1, CXMMATRIX64 M2) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Transpose(FXMMATRIX64 M) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Inverse(_Out_opt_ XMVECTOR256D* pDeterminant, _In_ FXMMATRIX64 M) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64VectorTensorProduct(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept;
    XMVECTOR256D XM_CALLCONV XMMatrix64Determinant(FXMMATRIX64 M) noexcept;

    _Success_(return ) bool XM_CALLCONV XMMatrix64Decompose(_Out_ XMVECTOR256D* outScale, _Out_ XMVECTOR256D* outRotQuat, _Out_ XMVECTOR256D* outTrans, _In_ FXMMATRIX64 M) noexcept;

    XMMATRIX64 XM_CALLCONV XMMatrix64Identity() noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Set(double m00, double m01, double m02, double m03, double m10, double m11, double m12, double m13, double m20, double m21, double m22, double m23, double m30, double m31, double m32, double m33) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Translation(double OffsetX, double OffsetY, double OffsetZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64TranslationFromVector(FXMVECTOR256D Offset) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Scaling(double ScaleX, double ScaleY, double ScaleZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64ScalingFromVector(FXMVECTOR256D Scale) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationX(double Angle) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationY(double Angle) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationZ(double Angle) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationRollPitchYaw(double Pitch, double Yaw, double Roll) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationRollPitchYawFromVector(FXMVECTOR256D Angles) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationNormal(FXMVECTOR256D NormalAxis, double Angle) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationAxis(FXMVECTOR256D Axis, double Angle) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64RotationQuaternion(FXMVECTOR256D Quaternion) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Transformation2D(FXMVECTOR256D ScalingOrigin, double ScalingOrientation, FXMVECTOR256D Scaling, FXMVECTOR256D RotationOrigin, double Rotation, GXMVECTOR256D Translation) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Transformation(FXMVECTOR256D ScalingOrigin, FXMVECTOR256D ScalingOrientationQuat, FXMVECTOR256D Scaling, GXMVECTOR256D RotationOrigin, HXMVECTOR256D RotationQuat, HXMVECTOR256D Translation) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64AffineTransformation2D(FXMVECTOR256D Scaling, FXMVECTOR256D RotationOrigin, double Rotation, FXMVECTOR256D Translation) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64AffineTransformation(FXMVECTOR256D Scaling, FXMVECTOR256D RotationOrigin, FXMVECTOR256D RotationQuaternion, GXMVECTOR256D Translation) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Reflect(FXMVECTOR256D ReflectionPlane) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64Shadow(FXMVECTOR256D ShadowPlane, FXMVECTOR256D LightPosition) noexcept;

    XMMATRIX64 XM_CALLCONV XMMatrix64LookAtLH(FXMVECTOR256D EyePosition, FXMVECTOR256D FocusPosition, FXMVECTOR256D UpDirection) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64LookAtRH(FXMVECTOR256D EyePosition, FXMVECTOR256D FocusPosition, FXMVECTOR256D UpDirection) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64LookToLH(FXMVECTOR256D EyePosition, FXMVECTOR256D EyeDirection, FXMVECTOR256D UpDirection) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64LookToRH(FXMVECTOR256D EyePosition, FXMVECTOR256D EyeDirection, FXMVECTOR256D UpDirection) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveLH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveRH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveFovLH(double FovAngleY, double AspectRatio, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveFovRH(double FovAngleY, double AspectRatio, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveOffCenterLH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveOffCenterRH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicLH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicRH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicOffCenterLH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept;
    XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicOffCenterRH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept;

    /****************************************************************************
     *
     * Quaternion operations
     *
     ****************************************************************************/

    bool XM_CALLCONV XMQuaternion64Equal(FXMVECTOR256D Q1, FXMVECTOR256D Q2) noexcept;
    bool XM_CALLCONV XMQuaternion64NotEqual(FXMVECTOR256D Q1, FXMVECTOR256D Q2) noexcept;

    bool XM_CALLCONV XMQuaternion64IsNaN(FXMVECTOR256D Q) noexcept;
    bool XM_CALLCONV XMQuaternion64IsInfinite(FXMVECTOR256D Q) noexcept;
    bool XM_CALLCONV XMQuaternion64IsIdentity(FXMVECTOR256D Q) noexcept;

    XMVECTOR256D XM_CALLCONV XMQuaternion64Dot(FXMVECTOR256D Q1, FXMVECTOR256D Q2) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Multiply(FXMVECTOR256D Q1, FXMVECTOR256D Q2) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64LengthSq(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64ReciprocalLength(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Length(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64NormalizeEst(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Normalize(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Conjugate(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Inverse(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Ln(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Exp(FXMVECTOR256D Q) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Slerp(FXMVECTOR256D Q0, FXMVECTOR256D Q1, double t) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64SlerpV(FXMVECTOR256D Q0, FXMVECTOR256D Q1, FXMVECTOR256D T) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64Squad(FXMVECTOR256D Q0, FXMVECTOR256D Q1, FXMVECTOR256D Q2, GXMVECTOR256D Q3, double t) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64SquadV(FXMVECTOR256D Q0, FXMVECTOR256D Q1, FXMVECTOR256D Q2, GXMVECTOR256D Q3, HXMVECTOR256D T) noexcept;
    void XM_CALLCONV XMQuaternion64SquadSetup(_Out_ XMVECTOR256D* pA, _Out_ XMVECTOR256D* pB, _Out_ XMVECTOR256D* pC, _In_ FXMVECTOR256D Q0, _In_ FXMVECTOR256D Q1, _In_ FXMVECTOR256D Q2, _In_ GXMVECTOR256D Q3) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64BaryCentric(FXMVECTOR256D Q0, FXMVECTOR256D Q1, FXMVECTOR256D Q2, double f, double g) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64BaryCentricV(FXMVECTOR256D Q0, FXMVECTOR256D Q1, FXMVECTOR256D Q2, GXMVECTOR256D F, HXMVECTOR256D G) noexcept;

    XMVECTOR256D XM_CALLCONV XMQuaternion64Identity() noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64RotationRollPitchYaw(double Pitch, double Yaw, double Roll) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64RotationRollPitchYawFromVector(FXMVECTOR256D Angles) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64RotationNormal(FXMVECTOR256D NormalAxis, double Angle) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64RotationAxis(FXMVECTOR256D Axis, double Angle) noexcept;
    XMVECTOR256D XM_CALLCONV XMQuaternion64RotationMatrix(FXMMATRIX64 M) noexcept;

    void XM_CALLCONV XMQuaternion64ToAxisAngle(_Out_ XMVECTOR256D* pAxis, _Out_ double* pAngle, _In_ FXMVECTOR256D Q) noexcept;

    

    /****************************************************************************
     *
     * Miscellaneous operations
     *
     ****************************************************************************/

    void XM64ScalarSinCos(_Out_ double* pSin, _Out_ double* pCos, double Value) noexcept;
    void XM64ScalarSinCosEst(_Out_ double* pSin, _Out_ double* pCos, double Value) noexcept;

    /****************************************************************************
     *
     * Globals
     *
     ****************************************************************************/

    // The purpose of the following global constants is to prevent redundant
    // reloading of the constants when they are referenced by more than one
    // separate inline math routine called within the same function.  Declaring
    // a constant locally within a routine is sufficient to prevent redundant
    // reloads of that constant when that single routine is called multiple
    // times in a function, but if the constant is used (and declared) in a
    // separate math routine it would be reloaded.

#ifndef XMGLOBALCONST64
#    if defined(__GNUC__) && !defined(__MINGW32__)
#        define XMGLOBALCONST64 extern const __attribute__((weak))
#    else
#        define XMGLOBALCONST64 extern const __declspec(selectany)
#    endif
#endif

    //XMGLOBALCONST64 XMVECTORD64 g_XM64SinCoefficients0 = {{{-0.16666667f, +0.0083333310f, -0.00019840874f, +2.7525562e-06f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64SinCoefficients1 = {{{-2.3889859e-08f, -0.16665852f /*Est1*/, +0.0083139502f /*Est2*/, -0.00018524670f /*Est3*/}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64CosCoefficients0 = {{{-0.5f, +0.041666638f, -0.0013888378f, +2.4760495e-05f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64CosCoefficients1 = {{{-2.6051615e-07f, -0.49992746f /*Est1*/, +0.041493919f /*Est2*/, -0.0012712436f /*Est3*/}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64TanCoefficients0 = {{{1.0f, 0.333333333f, 0.133333333f, 5.396825397e-2f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64TanCoefficients1 = {{{2.186948854e-2f, 8.863235530e-3f, 3.592128167e-3f, 1.455834485e-3f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64TanCoefficients2 = {{{5.900274264e-4f, 2.391290764e-4f, 9.691537707e-5f, 3.927832950e-5f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ArcCoefficients0 = {{{+1.5707963050f, -0.2145988016f, +0.0889789874f, -0.0501743046f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ArcCoefficients1 = {{{+0.0308918810f, -0.0170881256f, +0.0066700901f, -0.0012624911f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ATanCoefficients0 = {{{-0.3333314528f, +0.1999355085f, -0.1420889944f, +0.1065626393f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ATanCoefficients1 = {{{-0.0752896400f, +0.0429096138f, -0.0161657367f, +0.0028662257f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ATanEstCoefficients0 = {{{+0.999866f, +0.999866f, +0.999866f, +0.999866f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ATanEstCoefficients1 = {{{-0.3302995f, +0.180141f, -0.085133f, +0.0208351f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64TanEstCoefficients = {{{2.484f, -1.954923183e-1f, 2.467401101f, XM_1DIVPI}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ArcEstCoefficients = {{{+1.5707288f, -0.2121144f, +0.0742610f, -0.0187293f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64PiConstants0 = {{{XM_PI, XM_2PI, XM_1DIVPI, XM_1DIV2PI}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64IdentityR0 = {{{1.0, 0.0, 0.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64IdentityR1 = {{{0.0, 1.0, 0.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64IdentityR2 = {{{0.0, 0.0, 1.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64IdentityR3 = {{{0.0, 0.0, 0.0, 1.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64NegIdentityR0 = {{{-1.0, 0.0, 0.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64NegIdentityR1 = {{{0.0, -1.0, 0.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64NegIdentityR2 = {{{0.0, 0.0, -1.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64NegIdentityR3 = {{{0.0, 0.0, 0.0, -1.0}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64NegativeZero = {{{0x80000000, 0x80000000, 0x80000000, 0x80000000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64Negate3 = {{{0x80000000, 0x80000000, 0x80000000, 0x00000000}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64MaskXY = {{{0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0, 0}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Mask3 = {{{0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64MaskX = {{{0xFFFFFFFFFFFFFFFF, 0, 0, 0}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64MaskY = {{{0, 0xFFFFFFFFFFFFFFFF, 0, 0}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64MaskZ = {{{0, 0, 0xFFFFFFFFFFFFFFFF, 0}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64MaskW = {{{0, 0, 0, 0xFFFFFFFFFFFFFFFF}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64One = {{{1.0, 1.0, 1.0, 1.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64One3 = {{{1.0, 1.0, 1.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64Zero = {{{0.0, 0.0, 0.0, 0.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64Two = {{{2., 2., 2., 2.}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64Four = {{{4., 4., 4., 4.}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64Six = {{{6., 6., 6., 6.}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64NegativeOne = {{{-1.0, -1.0, -1.0, -1.0}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64OneHalf = {{{0.5, 0.5, 0.5, 0.5}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegativeOneHalf = {{{-0.5f, -0.5f, -0.5f, -0.5f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegativeTwoPi = {{{-XM_2PI, -XM_2PI, -XM_2PI, -XM_2PI}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegativePi = {{{-XM_PI, -XM_PI, -XM_PI, -XM_PI}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64HalfPi = {{{XM_PIDIV2, XM_PIDIV2, XM_PIDIV2, XM_PIDIV2}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64Pi = {{{XM_PI, XM_PI, XM_PI, XM_PI}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ReciprocalPi = {{{XM_1DIVPI, XM_1DIVPI, XM_1DIVPI, XM_1DIVPI}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64TwoPi = {{{XM_2PI, XM_2PI, XM_2PI, XM_2PI}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ReciprocalTwoPi = {{{XM_1DIV2PI, XM_1DIV2PI, XM_1DIV2PI, XM_1DIV2PI}}};
    XMGLOBALCONST64 XMVECTORD64 g_XM64Epsilon = {{{2.2204460492503131e-016, 2.2204460492503131e-016, 2.2204460492503131e-016, 2.2204460492503131e-016}}};
    XMGLOBALCONST64 XMVECTORI64 g_XM64Infinity = {{{0x7FF0000000000000, 0x7FF0000000000000, 0x7FF0000000000000, 0x7FF0000000000000}}};
    XMGLOBALCONST64 XMVECTORI64 g_XM64QNaN = {{{0x7FF0000000000001, 0x7FF0000000000001, 0x7FF0000000000001, 0x7FF0000000000001}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64SignMask = {{{0x8000000000000000, 0x8000000000000000, 0x8000000000000000, 0x8000000000000000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64QNaNTest = {{{0x007FFFFF, 0x007FFFFF, 0x007FFFFF, 0x007FFFFF}}};
    XMGLOBALCONST64 XMVECTORI64 g_XM64AbsMask = {{{0x7FFFFFFFFFFFFFFF, 0x7FFFFFFFFFFFFFFF, 0x7FFFFFFFFFFFFFFF, 0x7FFFFFFFFFFFFFFF}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64FltMin = {{{0x00800000, 0x00800000, 0x00800000, 0x00800000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64FltMax = {{{0x7F7FFFFF, 0x7F7FFFFF, 0x7F7FFFFF, 0x7F7FFFFF}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64NegOneMask = {{{0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64MaskA8R8G8B8 = {{{0x00FF0000, 0x0000FF00, 0x000000FF, 0xFF000000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipA8R8G8B8 = {{{0x00000000, 0x00000000, 0x00000000, 0x80000000}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixAA8R8G8B8 = {{{0.0f, 0.0f, 0.0f, float(0x80000000U)}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NormalizeA8R8G8B8 = {{{1.0f / (255.0f * float(0x10000)), 1.0f / (255.0f * float(0x100)), 1.0f / 255.0f, 1.0f / (255.0f * float(0x1000000))}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64MaskA2B10G10R10 = {{{0x000003FF, 0x000FFC00, 0x3FF00000, 0xC0000000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipA2B10G10R10 = {{{0x00000200, 0x00080000, 0x20000000, 0x80000000}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixAA2B10G10R10 = {{{-512.0f, -512.0f * float(0x400), -512.0f * float(0x100000), float(0x80000000U)}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NormalizeA2B10G10R10 = {{{1.0f / 511.0f, 1.0f / (511.0f * float(0x400)), 1.0f / (511.0f * float(0x100000)), 1.0f / (3.0f * float(0x40000000))}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64MaskX16Y16 = {{{0x0000FFFF, 0xFFFF0000, 0x00000000, 0x00000000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64FlipX16Y16 = {{{0x00008000, 0x00000000, 0x00000000, 0x00000000}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixX16Y16 = {{{-32768.0f, 0.0f, 0.0f, 0.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NormalizeX16Y16 = {{{1.0f / 32767.0f, 1.0f / (32767.0f * 65536.0f), 0.0f, 0.0f}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64MaskX16Y16Z16W16 = {{{0x0000FFFF, 0x0000FFFF, 0xFFFF0000, 0xFFFF0000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64FlipX16Y16Z16W16 = {{{0x00008000, 0x00008000, 0x00000000, 0x00000000}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixX16Y16Z16W16 = {{{-32768.0f, -32768.0f, 0.0f, 0.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NormalizeX16Y16Z16W16 = {{{1.0f / 32767.0f, 1.0f / 32767.0f, 1.0f / (32767.0f * 65536.0f), 1.0f / (32767.0f * 65536.0f)}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NoFraction = {{{8388608.0f, 8388608.0f, 8388608.0f, 8388608.0f}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64MaskByte = {{{0x000000FF, 0x000000FF, 0x000000FF, 0x000000FF}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegateX = {{{-1.0f, 1.0f, 1.0f, 1.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegateY = {{{1.0f, -1.0f, 1.0f, 1.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegateZ = {{{1.0f, 1.0f, -1.0f, 1.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64NegateW = {{{1.0f, 1.0f, 1.0f, -1.0f}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Select0101 = {{{XM_SELECT_0_D, XM_SELECT_1_D, XM_SELECT_0_D, XM_SELECT_1_D}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Select1010 = {{{XM_SELECT_1_D, XM_SELECT_0_D, XM_SELECT_1_D, XM_SELECT_0_D}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64OneHalfMinusEpsilon = {{{0x3EFFFFFD, 0x3EFFFFFD, 0x3DFFFFFD, 0x3EFFFFFD}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Select1000 = {{{XM_SELECT_1_D, XM_SELECT_0_D, XM_SELECT_0_D, XM_SELECT_0_D}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Select1100 = {{{XM_SELECT_1_D, XM_SELECT_1_D, XM_SELECT_0_D, XM_SELECT_0_D}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Select1110 = {{{XM_SELECT_1_D, XM_SELECT_1_D, XM_SELECT_1_D, XM_SELECT_0_D}}};
    XMGLOBALCONST64 XMVECTORU64 g_XM64Select1011 = {{{XM_SELECT_1_D, XM_SELECT_0_D, XM_SELECT_1_D, XM_SELECT_1_D}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixupY16 = {{{1.0f, 1.0f / 65536.0f, 0.0f, 0.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixupY16W16 = {{{1.0f, 1.0f, 1.0f / 65536.0f, 1.0f / 65536.0f}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipY = {{{0, 0x80000000, 0, 0}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipZ = {{{0, 0, 0x80000000, 0}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipW = {{{0, 0, 0, 0x80000000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipYZ = {{{0, 0x80000000, 0x80000000, 0}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipZW = {{{0, 0, 0x80000000, 0x80000000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64FlipYW = {{{0, 0x80000000, 0, 0x80000000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64MaskDec4 = {{{0x3FF, 0x3FF << 10, 0x3FF << 20, static_cast<int>(0xC0000000)}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64XorDec4 = {{{0x200, 0x200 << 10, 0x200 << 20, 0}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64AddUDec4 = {{{0, 0, 0, 32768.0f * 65536.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64AddDec4 = {{{-512.0f, -512.0f * 1024.0f, -512.0f * 1024.0f * 1024.0f, 0}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64MulDec4 = {{{1.0f, 1.0f / 1024.0f, 1.0f / (1024.0f * 1024.0f), 1.0f / (1024.0f * 1024.0f * 1024.0f)}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64MaskByte4 = {{{0xFF, 0xFF00, 0xFF0000, 0xFF000000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64XorByte4 = {{{0x80, 0x8000, 0x800000, 0x00000000}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64AddByte4 = {{{-128.0f, -128.0f * 256.0f, -128.0f * 65536.0f, 0}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64FixUnsigned = {{{32768.0f * 65536.0f, 32768.0f * 65536.0f, 32768.0f * 65536.0f, 32768.0f * 65536.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64MaxInt = {{{65536.0f * 32768.0f - 128.0f, 65536.0f * 32768.0f - 128.0f, 65536.0f * 32768.0f - 128.0f, 65536.0f * 32768.0f - 128.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64MaxUInt = {{{65536.0f * 65536.0f - 256.0f, 65536.0f * 65536.0f - 256.0f, 65536.0f * 65536.0f - 256.0f, 65536.0f * 65536.0f - 256.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64UnsignedFix = {{{32768.0f * 65536.0f, 32768.0f * 65536.0f, 32768.0f * 65536.0f, 32768.0f * 65536.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64srgbScale = {{{12.92f, 12.92f, 12.92f, 1.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64srgbA = {{{0.055f, 0.055f, 0.055f, 0.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64srgbA1 = {{{1.055f, 1.055f, 1.055f, 1.0f}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64ExponentBias = {{{127, 127, 127, 127}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64SubnormalExponent = {{{-126, -126, -126, -126}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64NumTrailing = {{{23, 23, 23, 23}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64MinNormal = {{{0x00800000, 0x00800000, 0x00800000, 0x00800000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64NegInfinity = {{{0xFF800000, 0xFF800000, 0xFF800000, 0xFF800000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64NegQNaN = {{{0xFFC00000, 0xFFC00000, 0xFFC00000, 0xFFC00000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64Bin128 = {{{0x43000000, 0x43000000, 0x43000000, 0x43000000}}};
    //XMGLOBALCONST64 XMVECTORU64 g_XM64BinNeg150 = {{{0xC3160000, 0xC3160000, 0xC3160000, 0xC3160000}}};
    //XMGLOBALCONST64 XMVECTORI64 g_XM64253 = {{{253, 253, 253, 253}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst1 = {{{-6.93147182e-1f, -6.93147182e-1f, -6.93147182e-1f, -6.93147182e-1f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst2 = {{{+2.40226462e-1f, +2.40226462e-1f, +2.40226462e-1f, +2.40226462e-1f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst3 = {{{-5.55036440e-2f, -5.55036440e-2f, -5.55036440e-2f, -5.55036440e-2f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst4 = {{{+9.61597636e-3f, +9.61597636e-3f, +9.61597636e-3f, +9.61597636e-3f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst5 = {{{-1.32823968e-3f, -1.32823968e-3f, -1.32823968e-3f, -1.32823968e-3f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst6 = {{{+1.47491097e-4f, +1.47491097e-4f, +1.47491097e-4f, +1.47491097e-4f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64ExpEst7 = {{{-1.08635004e-5f, -1.08635004e-5f, -1.08635004e-5f, -1.08635004e-5f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst0 = {{{+1.442693f, +1.442693f, +1.442693f, +1.442693f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst1 = {{{-0.721242f, -0.721242f, -0.721242f, -0.721242f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst2 = {{{+0.479384f, +0.479384f, +0.479384f, +0.479384f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst3 = {{{-0.350295f, -0.350295f, -0.350295f, -0.350295f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst4 = {{{+0.248590f, +0.248590f, +0.248590f, +0.248590f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst5 = {{{-0.145700f, -0.145700f, -0.145700f, -0.145700f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst6 = {{{+0.057148f, +0.057148f, +0.057148f, +0.057148f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LogEst7 = {{{-0.010578f, -0.010578f, -0.010578f, -0.010578f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64LgE = {{{+1.442695f, +1.442695f, +1.442695f, +1.442695f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64InvLgE = {{{+6.93147182e-1f, +6.93147182e-1f, +6.93147182e-1f, +6.93147182e-1f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64Lg10 = {{{+3.321928f, +3.321928f, +3.321928f, +3.321928f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_XM64InvLg10 = {{{+3.010299956e-1f, +3.010299956e-1f, +3.010299956e-1f, +3.010299956e-1f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_64UByteMax = {{{255.0f, 255.0f, 255.0f, 255.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_64ByteMin = {{{-127.0f, -127.0f, -127.0f, -127.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_64ByteMax = {{{127.0f, 127.0f, 127.0f, 127.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_64ShortMin = {{{-32767.0f, -32767.0f, -32767.0f, -32767.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_64ShortMax = {{{32767.0f, 32767.0f, 32767.0f, 32767.0f}}};
    //XMGLOBALCONST64 XMVECTORD64 g_64UShortMax = {{{65535.0f, 65535.0f, 65535.0f, 65535.0f}}};

#include "DXMathDoubleExtPermute.inl"
 #include "DXMathDoubleExtConvert.inl"
 #include "DXMathDoubleExtVector.inl"
 #include "DXMathDoubleExtMatrix.inl"
 #include "DXMathDoubleExtMisc.inl"
 };   // namespace DirectX