#pragma once

#include <boost/preprocessor.hpp>
#include <boost/pfr.hpp>

#include "Template/CallableTraits.hpp"

namespace cross
{
    template <typename T>
    struct TReflectInfo;

    template <typename T, typename = void>
    struct THasReflectInfo : std::false_type {};
    template <typename T>
    struct THasReflectInfo<T, std::void_t<decltype(TReflectInfo<std::remove_cv_t<T>>::Name())>> : std::true_type {};
    template <typename T>
    inline bool constexpr THasReflectInfoV = THasReflectInfo<T>::value;

    // let all types to be unreflected
    template <typename T, typename = void>
    struct TIsReflected : std::false_type {};
    
    //template <typename T>
    //struct TIsReflected<T, std::enable_if_t<THasReflectInfoV<T>>> : std::true_type {};
    // 1. types with CROSS_REFLECT macro applied are reflected types
    // 2. tuple types are reflected types
    // 3. powered by magic_get(https://github.com/apolukhin/magic_get)
    /// aggregate types(https://en.cppreference.com/w/cpp/language/aggregate_initialization) are reflected types 
    template <typename T>
    struct TIsReflected<T, std::enable_if_t<std::disjunction_v<
        std::is_aggregate<T>, THasReflectInfo<T>, TIsTuple<T>
    >>> : std::true_type {};
    template <typename T>
    inline constexpr bool TIsReflectedV = TIsReflected<T>::value;
}

#define CROSS_REFLECT_APPLY_MACRO(macro) macro
#define CROSS_REFLECT_ACCESS_MEMBER(c, m) &c::m
#define CROSS_REFLECT_MEMBER_NAME_II(member) std::string_view{ #member }
#define CROSS_REFLECT_MEMBER_NAME_I(member) CROSS_REFLECT_MEMBER_NAME_II(member)

#define CROSS_REFLECT_MEMBER_NAME(r, data, i, t) BOOST_PP_COMMA_IF(i) CROSS_REFLECT_MEMBER_NAME_I(t)
#define CROSS_REFLECT_MEMBER_DATA(r, data, i, t) BOOST_PP_COMMA_IF(i) CROSS_REFLECT_ACCESS_MEMBER(data, t)

#define CROSS_REFLECT_MEMBERS(CLASS, N, ...) \
static constexpr auto MemberNames() noexcept -> std::array<std::string_view, N> { \
    return { BOOST_PP_SEQ_FOR_EACH_I(CROSS_REFLECT_MEMBER_NAME, _, BOOST_PP_VARIADIC_TO_SEQ(__VA_ARGS__)) }; } \
static constexpr auto Members() noexcept { \
    return std::make_tuple(BOOST_PP_SEQ_FOR_EACH_I(CROSS_REFLECT_MEMBER_DATA, CLASS, BOOST_PP_VARIADIC_TO_SEQ(__VA_ARGS__))); } \
static constexpr auto Count() noexcept { return N; }

#define CROSS_REFLECT(CLASS, ...)                                                  \
template <> struct ::cross::TReflectInfo<CLASS> {                          \
    static_assert(std::negation_v<std::conjunction<                             \
        std::is_pointer<CLASS>,                                                 \
        std::is_reference<CLASS>,                                               \
        std::is_array<CLASS>,                                                   \
        std::is_const<CLASS>,                                                   \
        std::is_volatile<CLASS>>>);                                             \
    using type = CLASS;                                                         \
    static constexpr std::string_view Name() noexcept { return #CLASS; }        \
    CROSS_REFLECT_MEMBERS(CLASS, BOOST_PP_VARIADIC_SIZE(__VA_ARGS__), __VA_ARGS__)  \
};

#define reflexpr(expr) ::cross::TReflectInfo<std::remove_cv_t<decltype(expr)>>;
#define refltype(type) ::cross::TReflectInfo<type>;

namespace cross
{
    namespace detail
    {
        /************************************ for each aggregate *******************************************/
        // apply for each aggregate tuple element
        template <size_t Index, typename T, typename F>
        inline void ForEachAggregateApply(T&& t, F&& f)
        {
            using ElementType = decltype(boost::pfr::get<Index>(std::forward<T>(t)));
            using FunctionType = TFuncDecayT<F>;
            if constexpr (std::is_invocable_v<FunctionType, ElementType>)
            {
                std::forward<F>(f)(boost::pfr::get<Index>(std::forward<T>(t)));
            }
            else if constexpr (std::is_invocable_v<FunctionType, ElementType, size_t>)
            {
                std::forward<F>(f)(boost::pfr::get<Index>(std::forward<T>(t)), Index);
            }
            else
            {
                static_assert(std::disjunction_v<
                    std::is_invocable<FunctionType, ElementType>, 
                    std::is_invocable<FunctionType, ElementType, size_t>
                >, "Unsupported function for ForEachAggregate signature.");
            }
        }

        // for each implementation for aggregate type
        template <typename T, typename F, size_t ... Is>
        inline void ForEachAggregate(T&& t, F&& f, std::index_sequence<Is...>)
        {
            (ForEachAggregateApply<Is>(std::forward<T>(t), std::forward<F>(f)), ...);
        }

        // entry point for each elements of aggregate type
        template <typename T, typename F>
        inline void ForEachAggregate(T&& t, F&& f)
        {
            using Type = TRemoveRCVT<T>;
            ForEachAggregate(std::forward<T>(t), std::forward<F>(f), std::make_index_sequence<boost::pfr::tuple_size_v<Type>>{});
        }
        /***************************************************************************************************/

        /************************************ for each reflected type **************************************/
        // apply for each element of reflected type
        template <size_t Index, typename T, typename F, typename Tuple>
        inline void ForEachReflectInfoApply(T&& t, F&& f, Tuple const& tuple)
        {
            using ElementType = decltype(std::forward<T>(t).*(std::get<Index>(tuple)));
            using FunctionType = TFuncDecayT<F>;
            if constexpr (std::is_invocable_v<FunctionType, ElementType>)
            {
                f(std::forward<T>(t).*(std::get<Index>(tuple)));
            }
            else if constexpr (std::is_invocable_v<FunctionType, ElementType, size_t>)
            {
                f(std::forward<T>(t).*(std::get<Index>(tuple)), Index);
            }
            else
            {
                static_assert(std::disjunction_v<
                    std::is_invocable<FunctionType, ElementType>,
                    std::is_invocable<FunctionType, ElementType, size_t>
                > ,"Unsupported function for ForEachReflectInfo signature.");
            }
        }

        // for each implementation for types with TReflectionInfo specialization
        template <typename T, typename F, typename Tuple, size_t ... Is>
        inline void ForEachReflectInfo(T&& t, F&& f, Tuple const& tuple, std::index_sequence<Is...>)
        {
            (ForEachReflectInfoApply<Is>(std::forward<T>(t), std::forward<F>(f), tuple), ...);
        }

        template <typename T, typename F>
        inline void ForEachReflectInfo(T&& t, F&& f)
        {
            using ReflectInfoType = TReflectInfo<TRemoveRCVT<T>>;
            ForEachReflectInfo(std::forward<T>(t), std::forward<F>(f), ReflectInfoType::Members(), std::make_index_sequence<ReflectInfoType::Count()>{});
        }

        // implementation of kv foreach for reflected types
        template <typename T, typename F, typename NameTuple, typename Tuple, size_t ... Is>
        inline void ForEachReflectInfo(T&& t, F&& f, NameTuple const& names, Tuple const& members, std::index_sequence<Is...>)
        {
            (f(std::get<Is>(names), std::forward<T>(t).*(std::get<Is>(members))), ...);
        }
        /***************************************************************************************************/

        /************************************** for each tuple type ****************************************/
        // apply implementation for each tuple element
        template <size_t Index, typename T, typename F>
        inline void ForEachTupleApply(T&& t, F&& f)
        {
            using ElementType = decltype(std::get<Index>(std::forward<T>(t)));
            using FunctionType = TFuncDecayT<F>;
            if constexpr (std::is_invocable_v<FunctionType, ElementType>)
            {
                std::forward<F>(f)(std::get<Index>(std::forward<T>(t)));
            }
            else if constexpr (std::is_invocable_v<FunctionType, ElementType, size_t>)
            {
                std::forward<F>(f)(std::get<Index>(std::forward<T>(t)), Index);
            }
            else
            {
                static_assert(std::disjunction_v<
                    std::is_invocable<FunctionType, ElementType>,
                    std::is_invocable<FunctionType, ElementType, size_t>
                >, "Unsupported function for ForEachTuple signature.");
            }
        }

        template <typename T, typename F, size_t ... Is>
        inline void ForEachTuple(T&& t, F&& f, std::index_sequence<Is...>)
        {
            (ForEachTupleApply<Is>(std::forward<T>(t), std::forward<F>(f)), ...);
        }

        template <typename T, typename F>
        inline void ForEachTuple(T&& t, F&& f)
        {
            using Type = TRemoveRCVT<T>;
            ForEachTuple(std::forward<T>(t), std::forward<F>(f), std::make_index_sequence<std::tuple_size_v<Type>>{});
        }
        /***************************************************************************************************/
    }

    template <typename T, typename F>
    inline auto ForEach(T&& t, F&& f) -> std::enable_if_t<TIsReflectedV<TRemoveRCVT<T>>>
    {
        using Type = TRemoveRCVT<T>;
        if constexpr (THasReflectInfoV<Type>)
        {
            detail::ForEachReflectInfo(std::forward<T>(t), std::forward<F>(f));
        }
        else if constexpr (TIsTupleV<Type>)
        {
            detail::ForEachTuple(std::forward<T>(t), std::forward<F>(f));
        }
        else if constexpr (std::is_aggregate_v<Type>)
        {
            detail::ForEachAggregate(std::forward<T>(t), std::forward<F>(f));
        }
        else
        {
            static_assert(std::disjunction_v<
                THasReflectInfo<Type>,
                TIsTuple<Type>,
                std::is_aggregate<Type>
            >, "Cannot apply static foreach to the type.");
        }
    }

    template <typename T, typename F>
    inline auto ForEachKV(T&& t, F&& f) -> std::enable_if_t<THasReflectInfoV<TRemoveRCVT<T>>>
    {
        using ReflectInfoType = TReflectInfo<TRemoveRCVT<T>>;
        detail::ForEachReflectInfo(std::forward<T>(t), std::forward<F>(f), ReflectInfoType::MemberNames(), ReflectInfoType::Members(), std::make_index_sequence<ReflectInfoType::Count()>{});
    }
}