#pragma once
#include "CrossBaseForward.h"
#include "../Template/EnumClassFlags.h"
#include "fmt/format.h"
#include "magic_enum_all.hpp"

namespace spdlog {
class logger;
class pattern_formatter;

namespace details {
    class thread_pool;
}}   // namespace spdlog::details

namespace cross {

class LogModule;

struct SourceLoc
{
    const char* file;
    const char* func;
    int line;
};

class CROSS_BASE_API Logger
{
public:
    enum class LogLevel
    {
        LOG_LEVEL_TRACE = 0,
        LOG_LEVEL_DEBUG = 1,
        LOG_LEVEL_INFO = 2,
        LOG_LEVEL_WARN = 3,
        LOG_LEVEL_ERROR = 4,
        LOG_LEVEL_CRITICAL = 5,
        LOG_LEVEL_OFF = 6,
    };

    // output logs to rotate files

    auto SetLogLevel(LogLevel level) -> void;
    auto GetLogLevel() const -> LogLevel;
    auto Flush() const -> void;

    auto SetLogFile(const char* filePath = nullptr, uint32_t maxFileSize = 20 * 1024 * 1024, uint32_t maxFileNum = 10) -> bool;
    auto SetConsoleOutput(bool enable) -> void;

    typedef void (*USER_LOGGER)(LogLevel level, const char* message, SourceLoc sourceLoc, void* userData);
    auto SetUserLogger(USER_LOGGER userLoggerFunc, void* userData) -> void;

    auto SetUdpLogger(bool enabled) -> void;

    auto Log(LogLevel log_level, SourceLoc sourceLoc, std::string_view message) -> void;

    template<typename... T>
    auto Log(LogLevel log_level, SourceLoc sourceLoc, fmt::format_string<T...> fmt, T&&... args) -> void
    {
        Log(log_level, sourceLoc, fmt::format(fmt, std::forward<T>(args)...));
    }

public:
    Logger(std::weak_ptr<spdlog::details::thread_pool> tp) noexcept;
    ~Logger();

private:
    Logger(const Logger&) = delete;
    Logger(Logger&&) = delete;
    Logger& operator=(const Logger&) = delete;
    Logger& operator=(Logger&&) = delete;
    friend LogModule;

private:
    std::shared_ptr<spdlog::logger> mLogger;
    std::weak_ptr<spdlog::details::thread_pool> mAsyncLoggerThreadPool;
    std::unique_ptr<spdlog::pattern_formatter> mUserLogFormatter;

    LogLevel mLogLevel = Logger::LogLevel::LOG_LEVEL_DEBUG;
    bool mEnableConsoleOutput = true;
    struct UserLoggerInfo
    {
        USER_LOGGER mUserLoggerFunc = nullptr;
        void* mUserLoggerData = nullptr;
    } mUserLoggerInfo{};

    struct FileLogInfo
    {
        std::string mFilePath = "";
        uint32_t mMaxFileSize = 1000000;
        uint32_t mMaxFileNum = 10;
    } mFileLogInfo;

    struct UdpLogInfo
    {
        bool mEnabled = false;
        std::string mAddress = "127.0.0.1";
        uint16_t mPort = 11091;
    } mUdpLogInfo;

    auto ResetLogger() -> void;
};
}   // namespace cross