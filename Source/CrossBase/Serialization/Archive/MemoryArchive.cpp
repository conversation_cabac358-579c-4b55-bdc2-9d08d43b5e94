#include "PCH/CrossBasePCHPrivate.h"
#include "Serialization/Archive/MemoryArchive.h"

namespace cross
{
    bool MemoryArchive::Write(void const* data, UInt64 size)
    {
        CheckWrite(size);
        auto ptr = reinterpret_cast<UInt8 const*>(data);
        std::copy(ptr, ptr + size, std::back_inserter(mBuffer));
        return true;
    }

    UInt8 const* MemoryArchive::Read(UInt64 size) const
    {
        if (!CheckRead(size))
            return nullptr;

        auto result = mReadCursor;
        mReadCursor += size;
        return result;
    }

    bool MemoryArchive::GetModeReadable() const
    {
        return true;
    }

    UInt8 const* MemoryArchive::Data() const
    {
        return mBuffer.data();
    }

    UInt64 MemoryArchive::Size() const
    {
        return mBuffer.size();
    }

    bool MemoryArchive::AtEnd() const
    {
        return mReadCursor == &(mBuffer.back());
    }

    void MemoryArchive::Seek(UInt64 pos) const
    {
        mReadCursor = mBuffer.data() + pos;
    }

    UInt64 MemoryArchive::Tell() const
    {
        auto distance = mReadCursor == nullptr ? 0 : std::distance(const_cast<UInt8 const*>(mBuffer.data()), mReadCursor);
        return distance;
    }

    void MemoryArchive::CheckWrite(UInt64 size)
    {
        auto remainSize = mBuffer.capacity() - mBuffer.size();
        if (remainSize < size)
        {
            auto distance = mReadCursor == nullptr ? 0 :
                std::distance(const_cast<UInt8 const*>(mBuffer.data()), mReadCursor);
            assert(distance >= 0);
            mBuffer.reserve(mBuffer.capacity() * 2);
            mReadCursor = mBuffer.data() + distance;
        }
    }

    bool MemoryArchive::CheckRead(UInt64 size) const
    {
        auto postReadCursor = mReadCursor + size;
        auto distance = std::distance(const_cast<UInt8 const*>(mBuffer.data()), postReadCursor);
        assert(distance >= 0);
        return distance <= (UInt32)mBuffer.size();
    }
}