#pragma once
#include "PlatformDefs.h"
#include "Serialization/Archive/Archive.h"
#include "FileSystem/memorymappingfile.h"

namespace cross {
/// <summary>
/// Memory mapped file archive, it must hold a mapped file for dynamic streaming
/// Need to think of offset problem(must be page size), currently we only support mapping from 0
/// </summary>
class CROSS_BASE_API MemoryMappingArchive : public Archive
{
public:
    MemoryMappingArchive(filesystem::MemoryMappingFilePtr mmfile);
    virtual ~MemoryMappingArchive();

public:
    static UInt32 Getpagesize();
    bool Write(void const* data, UInt64 size) override 
    {
        return false;
    }
    UInt8 const* Read(UInt64 size) const override;
    UInt64 Read(void* data, UInt64 size) const override;
    bool GetModeReadable() const override
    {
        return true;
    };
    UInt8 const* Data() const override
    {
        return reinterpret_cast<UInt8*>(mMappingFile->GetBuffer());
    };
    UInt64 Size() const override
    {
        return mMappingFile->Tell();//Get mapped bytes
    };
    UInt64 FileSize() const
    {
        return mMappingFile->GetSize();
    };
    bool AtEnd() const override
    {
        return mAnchor == Size();
    };
    void Seek(UInt64 pos) const override;
    void Remap(UInt64 pos, UInt64 newSize) const override;
    UInt64 Tell() const override
    {
        return mAnchor;
    };
    filesystem::MemoryMappingFile* GetMMapedFile() 
    {
        return mMappingFile.get();
    }
private:
    /// pointer to the file contents mapped into memory
    std::unique_ptr<filesystem::MemoryMappingFile> mMappingFile;
    mutable UInt64 mAnchor;
};
}   // namespace cross