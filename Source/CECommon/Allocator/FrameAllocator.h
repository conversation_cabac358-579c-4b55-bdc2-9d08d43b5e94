#pragma once

#include <stdint.h>
#include <vector>
#include <atomic>
#include <thread>
#include <type_traits>
#include <mutex>
#include <map>
#include <set>
#include <deque>
#include <unordered_map>
#include "CECommon/Allocator/IFrameAllocator.h"
#include "CECommon/Common/IFrameContainer.h"
#include "CECommon/Common/CECommonForward.h"
#include "CrossBase/Containers/HashMap/HashMap.hpp"
#include "CrossBase/Template/TypeTraits.hpp"
namespace cross
{
class MemoryBlock;
class IFrameAllocatorListener;

class LinearAllocator
{
public:
    CECOMMON_API LinearAllocator(std::uint32_t frameParamIdx, std::uint32_t LifeTime);

    CECOMMON_API void* Allocate(const std::uint32_t size, const MemoryAlignmentSize align, IFrameAllocatorListener* listener = nullptr);
    CECOMMON_API int GetAllocatedSize() const;
    CECOMMON_API void Reset();
    CECOMMON_API void Froze();
    std::uint32_t GetLifeTime()
    {
        return mLifeTime;
    }
    void SetLifeTime(std::uint32_t LifeTime)
    {
        mLifeTime = LifeTime;
    }

private:
    class LinearMemoryBlock
    {
    public:
        LinearMemoryBlock(MemoryBlock* memBlock);
        void* Allocate(const std::uint32_t size, const MemoryAlignmentSize align);
        inline std::uint32_t GetFreeSize() const { return static_cast<std::uint32_t>(mFreeSize); }
        inline std::uint32_t GetSize() const { return mSize; }
        inline std::uint32_t GetAllocatedSize()const { return mSize - static_cast<std::uint32_t>(mFreeSize); }
        inline MemoryBlock* GetMemoryBlock()const { return mMemBlock; }
    private:
        LinearMemoryBlock() = delete;
        MemoryBlock* mMemBlock{ nullptr };
        void* mAvailableBufferStart{ nullptr };
        size_t mFreeSize{ 0 };
        std::uint32_t mSize{ 0 };
    };

private:
    LinearAllocator(const LinearAllocator&) = delete;
    LinearAllocator& operator=(const LinearAllocator&) = delete;

    int mPrevAllocBlockIdx{ -1 }; // the block used in the last allocation 
    std::vector<LinearMemoryBlock> mMemBlocks;
    std::vector<IFrameAllocatorListener*> mListeners;
    std::uint32_t mFrameParamIdx{ 0 };
    std::uint32_t mLifeTime {1};
    UInt8 mUsedFrames{0};
    bool mFrozen{ false };
};

constexpr std::uint32_t TAG_COUNT = FRAME_GAME_RENDER_STAGE_ALLOCATOR_COUNT;

inline std::uint32_t TagToIndex(const FrameStage& stage, std::uint32_t lifetime)
{
    return (lifetime - 1) * FRAME_GAME_RENDER_STAGE_ALLOCATOR_COUNT + (stage - 1);
}

using TaggedHeap = std::array<LinearAllocator*, TAG_COUNT>;
class FrameAllocator : public IFrameAllocator, public std::pmr::memory_resource
{
public:
    CECOMMON_API FrameAllocator(UInt8 LifeTime, std::uint32_t InstanceCount);
      
    // Allocation is thread-safe
    CECOMMON_API void* Allocate(const std::uint32_t size, const FrameStage lifeTime, const MemoryAlignmentSize align = MEM_ALIGN_DEFAULT);

    // T constructor will be called
    template<typename T>
    T* Allocate(std::uint32_t count, FrameStage lifeTime, MemoryAlignmentSize alignemnt = MEM_ALIGN_DEFAULT);

    template<typename FrameContainerType>
    FrameContainerType* CreateFrameContainer(FrameStage lifeTime, std::uint32_t capacity, MemoryAlignmentSize align = MEM_ALIGN_DEFAULT);

    std::uint32_t GetAllocatedSize() const override;

private:
    FrameAllocator(const FrameAllocator&) = delete;
    FrameAllocator& operator= (const FrameAllocator&) = delete;

    void* Allocate(const std::uint32_t size, const FrameStage lifeTime, const MemoryAlignmentSize align, IFrameAllocatorListener* listener) override;

    void BeginGameStage();
    void EndGameStage();
    void BeginRenderStage();
    void EndRenderStage();

    void* do_allocate(std::size_t bytes, std::size_t alignment) override;
    void do_deallocate(void* p, std::size_t bytes, std::size_t alignment) override;
    bool do_is_equal(const memory_resource& that) const noexcept override;

    // For every thread, there is a tagged heap for every rotating frame param. 
    static thread_local std::array<TaggedHeap, MAX_FRAME_PARAM_COUNT> mStageAllocatorsForFrames; 
    static thread_local CEHashMap<UInt8, LinearAllocator*> mStageAllocatorsForMultiFrames;

public:
    std::uint32_t mFrameParamId;

    std::vector<std::unique_ptr<LinearAllocator>> mGameStageAllocators;
    std::vector<std::unique_ptr<LinearAllocator>> mRenderStageAllocators;
    std::vector<std::unique_ptr<LinearAllocator>> mMultiFrameAllocators;
    std::mutex mRecycleLsitMtx;
    std::mutex mRecycleLsitMtx2;
    std::atomic<UInt8> mFrameStage{ FRAME_STAGE_INVALID };
    UInt8 mLifeTime{1};
    friend class FrameParam;
    template<typename T> friend class FrameArray;
};

template <class T>
class FrameAllocatorForStd
{
public:
    using value_type = T;
    FrameAllocatorForStd() = default;
    FrameAllocatorForStd(FrameAllocator* frameAllocator, FrameStage stage) : mAllocator(frameAllocator), mStage(stage) {};

    template <class U>
    constexpr FrameAllocatorForStd(const FrameAllocatorForStd<U>& src) noexcept
    {
        mAllocator = src.mAllocator;
        mStage = src.mStage;
    }
    inline bool operator==(const FrameAllocatorForStd& other)
    {
        return mAllocator == other.mAllocator && mStage == other.mStage;
    }
    [[nodiscard]] T* allocate(std::size_t n) {
        if (n > UINT32_MAX || n > std::numeric_limits<std::size_t>::max() / sizeof(T))
            throw std::bad_array_new_length();

        return mAllocator->Allocate<T>(ToUInt32(n), mStage);
    }

    void deallocate(T* p, std::size_t n) noexcept {
        return;
    }

    FrameAllocator* mAllocator;
    FrameStage mStage;
};

template<typename T>
T* FrameAllocator::Allocate(std::uint32_t count, FrameStage lifeTime, MemoryAlignmentSize align)
{
#if USE_ASAN_CHECK
    std::allocator<T> alloc;
    T* obj = alloc.allocate(count);
    for (std::uint32_t i = 0; i < count; i++)
    {
        new (&obj[i]) T();
    }
    return obj;
#else
    T* obj = (T*)Allocate(count * sizeof(T), lifeTime, align);
    for (std::uint32_t i = 0; i < count; i++)
    {
        new (&obj[i]) T();
    }
    return obj;
#endif
}

template<typename FrameContainerType>
FrameContainerType* FrameAllocator::CreateFrameContainer(FrameStage lifeTime, std::uint32_t capacity, MemoryAlignmentSize align)
{
    static_assert(std::is_base_of<IFrameContainer, FrameContainerType>::value);

    FrameContainerType* container = (FrameContainerType*)Allocate(sizeof(FrameContainerType), lifeTime);
    new(container) FrameContainerType(this, lifeTime, capacity, align);

    return container;
}
}