#pragma once

#include <algorithm>
#include "CrossBase/Math/CrossMath.h"

class Sphere
{
public:
    Sphere() : m_Center({ .0f, .0f, .0f }), m_Radius(.0f) {}
	Sphere(const cross::Float3& p0, float r)				{ Set(p0, r); }

	void Set(const cross::Float3& p0)						{ m_Center = p0;	m_Radius = 0; }
    void Set(const cross::Float3& p0, float r)
    {
        m_Center = p0;
        m_Radius = r;
    }

	void Set(const cross::Float3& p0, const cross::Float3& p1);

	void Set(const cross::Float3* inVertices, UInt32 inHowmany);

	cross::Float3& GetCenter() { return m_Center; }
	const cross::Float3& GetCenter()const  { return m_Center; }

	float& GetRadius() { return m_Radius; }
	const float& GetRadius()const { return m_Radius; }

	bool IsInside(const Sphere& inSphere)const;
private:
	cross::Float3	m_Center;
	float		m_Radius;
};

float CalculateSqrDistance(const cross::Float3& p, const Sphere& s);
bool Intersect(const Sphere& inSphere0, const Sphere& inSphere1);

inline void Sphere::Set(const cross::Float3& p0, const cross::Float3& p1)
{
	cross::Float3 dhalf = (p1 - p0) * 0.5;

	m_Center = dhalf + p0;
	m_Radius = dhalf.Length();
}

inline bool Sphere::IsInside(const Sphere& inSphere)const
{
	float sqrDist = (GetCenter() - inSphere.GetCenter()).LengthSquared();
    
	if (cross::Squre(GetRadius()) > sqrDist + cross::Squre(inSphere.GetRadius()))
		return true;
	else
		return false;
}

inline bool Intersect(const Sphere& inSphere0, const Sphere& inSphere1)
{
	float sqrDist = (inSphere0.GetCenter() - inSphere1.GetCenter()).LengthSquared();
	if (cross::Squre(inSphere0.GetRadius() + inSphere1.GetRadius()) > sqrDist)
		return true;
	else
		return false;
}

inline float CalculateSqrDistance(const  cross::Float3& p, const  Sphere& s)
{
	return (std::max)(0.0F, (p - s.GetCenter()).LengthSquared() - cross::Squre(s.GetRadius()));
}
