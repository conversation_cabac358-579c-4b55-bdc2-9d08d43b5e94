#include "EnginePrefix.h"
#include "CollisionMesh.h"

#include "CECommon/Geometry/Intersection.h"

namespace cross
{
    CollisionNode::CollisionNode()
    {
        mLeft = nullptr;
        mRight = nullptr;
    }

    CollisionNode::~CollisionNode()
    {
        if (mLeft)
        {
            delete mLeft;
        }
        if (mRight)
        {
            delete mRight;
        }
    }

    CollisionMesh::CollisionMesh()
    {
        mRoot = nullptr;
        mPlanes = nullptr;
    }

    CollisionMesh::~CollisionMesh()
    {
        if (mRoot)
        {
            delete mRoot;
        }
    }

    void CollisionMesh::Clear()
    {
        mVertexList.clear();
        mIndexList.clear();
        if (mRoot)
        {
            delete mRoot;
        }
        mRoot = nullptr;
    }

    std::vector<Float3> &CollisionMesh::GetVertexList()
    {
        return mVertexList;
    }

    std::vector<int> &CollisionMesh::GetIndexList()
    {
        return mIndexList;
    }
	
    std::vector<Float3> const &CollisionMesh::GetVertexList() const
    {
        return mVertexList;
    }

    std::vector<int> const &CollisionMesh::GetIndexList() const
    {
        return mIndexList;
    }

    CollisionNode* CollisionMesh::GetRoot()
    {
        return mRoot;
    }

    Float3A CollisionMesh::GetFaceNormal()
    {
        return mFaceNormal;
    }

    Double3 CollisionMesh::GetFaceNormalDouble()
    {
        return mFaceNormalDouble;
    }

    UInt32 CollisionMesh::GetIntersectCount() 
    {
        return mCount;
    }

    void CollisionMesh::InitCollisionMesh(MeshAssetData &meshAssetData, int allMeshPartIndex)
    {
        meshAssetData.CopyMeshPartVertex(mVertexList, mIndexList, allMeshPartIndex);
        LoadCollisionTree(meshAssetData, allMeshPartIndex);
    }

    void CollisionMesh::LoadCollisionTree(MeshAssetData& meshAssetData, int allMeshPartIndex)
    {
        const MeshPartAssetInfo& meshPart = meshAssetData.GetMeshPartInfo(allMeshPartIndex);
        const std::vector<MeshCollisionNode>& meshAssetCollisionTree = meshPart.mCollisionTree;
        const UInt32 count = static_cast<UInt32>(meshAssetCollisionTree.size());

        std::vector<CollisionNode*> collisionTree;
        collisionTree.resize(count);

        if (collisionTree.size() > 0)
        {
            for (int i = count - 1; i >= 0; i--)
            {
                CollisionNode* collisionNode = new CollisionNode();
                const MeshCollisionNode& meshCollisionNode = meshAssetCollisionTree[i];
                const MeshBound& Bound = meshCollisionNode.Bound;
                auto extent = Bound.Max - Bound.Min;
                auto origin = (Bound.Max + Bound.Min) * 0.5f;
                if (extent.IsValid() && origin.IsValid())
                {
                    collisionNode->mAABB = BoundingBox(origin, extent);
                }
                else
                {
                    collisionNode->mAABB = meshAssetData.GetBoundingBox();
                }

                collisionNode->mTriangleList = meshCollisionNode.TriangleList;
                if (meshCollisionNode.LeftIndex != -1)
                {
                    collisionNode->mLeft = collisionTree[meshCollisionNode.LeftIndex];
                }
                if (meshCollisionNode.RightIndex != -1)
                {
                    collisionNode->mRight = collisionTree[meshCollisionNode.RightIndex];
                }
                collisionTree[i] = collisionNode;
            }
            mRoot = collisionTree[0];
        }
        else
        {
            mRoot = new CollisionNode();
            CalculateAABB_Vertices(mRoot);

            const UInt32 triangleNum = static_cast<UInt32>(mIndexList.size() / 3);
            mRoot->mTriangleList.resize(triangleNum);
            std::iota(std::begin(mRoot->mTriangleList), std::end(mRoot->mTriangleList), 0);
        }
    }

    void CollisionMesh::CalculateAABB_Vertices(CollisionNode* node)
    {
        MeshBound bound;
        UInt32 vertexCount = static_cast<UInt32>(mVertexList.size());

        for (auto i = 0u; i < vertexCount; ++i)
        {
            bound.Encapsulate(mVertexList[i]);
        }

        if (vertexCount > 0)
        {
            auto extent = bound.Max - bound.Min;
            auto origin = (bound.Max + bound.Min) * 0.5f;
            node->mAABB = BoundingBox(origin, extent);
        }
        else
        {
            node->mAABB = BoundingBox(Float3A::Zero(), Float3A::Zero());
        }
    }

    bool CollisionMesh::RayIntersects(Ray* ray, float* minDistance, Float4x4& worldMatrix)
    {
        mRay = *ray;
        mWorldMatrix = worldMatrix;
        mTransform = worldMatrix;
        mMinDistance = *minDistance;
        mRayStart = ray->GetOrigin();

        if (mRoot == nullptr)//some CollisionMesh not be inited 
            return false;

        bool hit = RayIntersectsNode(mRoot);
        *minDistance = mMinDistance;
        return hit; 
    }

    bool CollisionMesh::RayIntersectsDouble(Ray_DoublePrecision* ray, double* minDistance, Double4x4& worldMatrix)
    {
        mRayDouble = *ray;
        mWorldMatrixDouble = worldMatrix;
        mTransformDouble = worldMatrix;
        mMinDistanceDouble = *minDistance;
        mRayStartDouble = ray->GetOrigin();

        if (mRoot == nullptr)   // some CollisionMesh not be inited
            return false;

        bool hit = RayIntersectsNodeDouble(mRoot);
        *minDistance = mMinDistanceDouble;
        return hit;
    }

    bool CollisionMesh::RayIntersectsNode(CollisionNode* node)
    {
        bool flag = false;
        cross::BoundingBox aabb;
        node->mAABB.Transform(aabb, mWorldMatrix);
        float t1, t2;
        bool res = cross::IntersectRayAABB(mRay, aabb, &t1, &t2);
        Float3 dir = mRay.GetDirection();
        if (!res || t2 <= 0)
        {
            return false;
        }
        if (node->mLeft || node->mRight)
        {
            if (node->mLeft)
            {
                if (RayIntersectsNode(node->mLeft))
                {
                    flag = true;
                }
            }
            if (node->mRight)
            {
                if (RayIntersectsNode(node->mRight))
                {
                    flag = true;
                }
            }
        }
        else
        {
            bool hit = false;
            std::vector<int>& triangleList = node->mTriangleList;
            int triangleCount = (int)triangleList.size();
            for (int i = 0; i < triangleCount; i++)
            {
                int triangleIndex = triangleList[i];
                int indexIndex = triangleIndex * 3;
                int index1 = mIndexList[indexIndex];
                int index2 = mIndexList[indexIndex + 1];
                int index3 = mIndexList[indexIndex + 2];
                Float3 point1 = Float3::Transform(mVertexList[index1], mTransform);
                Float3 point2 = Float3::Transform(mVertexList[index2], mTransform);
                Float3 point3 = Float3::Transform(mVertexList[index3], mTransform);
                float distance = 0.0f;
                ++mCount;
                if (IntersectRayTriangle(mRayStart, dir, point1, point2, point3, distance))
                {
                    hit = true;
                    if (distance < mMinDistance)
                    {
                        mMinDistance = distance;
                        mFaceNormal = (point2 - point1).Cross(point3 - point2).Normalized();
                    }
                }
            }
            if (hit)
            {
                flag = true;
            }
        }
        return flag;
    }

    bool CollisionMesh::RayIntersectsNodeDouble(CollisionNode* node) 
    
    {
        bool flag = false;
        cross::BoundingBox aabb;
        cross::Float4x4 Matrix4x4(mWorldMatrixDouble);
        node->mAABB.Transform(aabb, Matrix4x4);
        double t1, t2;
        bool res = cross::IntersectRayAABBDouble(mRayDouble, aabb, &t1, &t2);
        Double3 dir = mRayDouble.GetDirection();
        if (!res || t2 <= 0)
        {
            return false;
        }
        if (node->mLeft || node->mRight)
        {
            if (node->mLeft)
            {
                if (RayIntersectsNodeDouble(node->mLeft))
                {
                    flag = true;
                }
            }
            if (node->mRight)
            {
                if (RayIntersectsNodeDouble(node->mRight))
                {
                    flag = true;
                }
            }
        }
        else
        {
            bool hit = false;
            std::vector<int>& triangleList = node->mTriangleList;
            int triangleCount = static_cast<int>(triangleList.size());
            for (int i = 0; i < triangleCount; i++)
            {
                int triangleIndex = triangleList[i];
                int indexIndex = triangleIndex * 3;
                int index1 = mIndexList[indexIndex];
                int index2 = mIndexList[indexIndex + 1];
                int index3 = mIndexList[indexIndex + 2];
                Double3 point1 = Double3::Transform(Double3(mVertexList[index1]), mTransformDouble);
                Double3 point2 = Double3::Transform(Double3(mVertexList[index2]), mTransformDouble);
                Double3 point3 = Double3::Transform(Double3(mVertexList[index3]), mTransformDouble);
                double distance = 0.0f;
                if (IntersectRayTriangleDouble(mRayStartDouble, dir, point1, point2, point3, distance))
                {
                    hit = true;
                    if (distance < mMinDistanceDouble)
                    {
                        mMinDistanceDouble = distance;
                        mFaceNormalDouble = (point2 - point1).Cross(point3 - point2).Normalized();
                    }
                }
            }
            if (hit)
            {
                flag = true;
            }
        }
        return flag;
    }

    bool CollisionMesh::FrustumContains(Plane2 *planes, int planeCount, Float4x4& worldMatrix)
    {
        mPlanes = planes;
        mPlaneCount = planeCount;
        mWorldMatrix = worldMatrix;
        mTransform =  worldMatrix ;
        bool hit = FrustumContainsNode(mRoot);
        return hit;
    }

    bool CollisionMesh::PointInAllPlanes(Float3 &point)
    {
        for (int i1 = 0; i1 < mPlaneCount; i1++)
        {
            float distance = PointDistanceToPlane(point, mPlanes + i1);
            if (distance > 0.0f)
            {
                return false;
            }
        }
        return true;
    }
    
    bool CollisionMesh::FrustumContainsNode(CollisionNode* node)
    {
        cross::BoundingBox aabb;
        node->mAABB.Transform(aabb, mWorldMatrix);
        if (FrustumIntersectsAABB(aabb, mPlanes, mPlaneCount))
        {
            if (AABBInFrustum(aabb, mPlanes, mPlaneCount))
            {
                return true;
            }
            if (node->mLeft || node->mRight)
            {
                if (node->mLeft)
                {
                    if (FrustumContainsNode(node->mLeft) == false)
                    {
                        return false;
                    }
                }
                if (node->mRight)
                {
                    if (FrustumContainsNode(node->mRight) == false)
                    {
                        return false;
                    }
                }
                return true;
            }
            else
            {
                std::vector<int> &triangleList = node->mTriangleList;
                int triangleCount = (int)triangleList.size();
                for (int i = 0; i < triangleCount; i++)
                {
                    int triangleIndex = triangleList[i];
                    int indexIndex = triangleIndex * 3;
                    int index1 = mIndexList[indexIndex];
                    Float3 point1 = Float3::Transform(mVertexList[index1], mTransform);
                    bool point1InAllPlanes = PointInAllPlanes(point1);
                    if (!point1InAllPlanes)
                    {
                        return false;
                    }
                    int index2 = mIndexList[indexIndex + 1];
                    Float3 point2 = Float3::Transform(mVertexList[index2], mTransform);
                    bool point2InAllPlanes = PointInAllPlanes(point2);
                    if (!point2InAllPlanes)
                    {
                        return false;
                    }
                    int index3 = mIndexList[indexIndex + 2];
                    Float3 point3 = Float3::Transform(mVertexList[index3], mTransform);
                    bool point3InAllPlanes = PointInAllPlanes(point3);
                    if (!point3InAllPlanes)
                    {
                        return false;
                    }
                }
                return true;
            }
        }
        return false;
    }
}
