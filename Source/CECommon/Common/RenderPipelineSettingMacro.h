#pragma once

#define RENDER_PIPELINE_VALUE(Type, Name, DefaultValue, _DisplayName, _ToolTips, _Category)                                                                                                                                                    \
    CEMeta(Reflect, Serialize, Editor)                                                                                                                                                                                                         \
    CECSAttribute(JsonProperty(#Name))                                                                                                                                                                                                         \
    CECSAttribute(PropertyInfo(PropertyType = "Auto", DisplayName = _DisplayName, ToolTips = _ToolTips, Category = _Category))                                                                                                                 \
    Type Name = DefaultValue;

#define RENDER_PIPELINE_VALUE_INVISIBLE(Type, Name, DefaultValue)                                                                                                                                                                              \
    CEMeta(Reflect, Serialize, Editor)                                                                                                                                                                                                         \
    CECSAttribute(JsonProperty(#Name))                                                                                                                                                                                                         \
    CECSAttribute(PropertyInfo(bHide = true))                                                                                                                                                                                                  \
    Type Name = DefaultValue;

#define RENDER_PIPELINE_RESOURCE(Type, Name, DefaultValue, _DisplayName, _ToolTips, _Category)                                                                                                                                                 \
    CEMeta(Serialize, Editor)                                                                                                                                                                                                                  \
    CECSAttribute(JsonProperty(#Name))                                                                                                                                                                                                         \
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", DisplayName = _DisplayName, ToolTips = _ToolTips, Category = _Category, FileTypeDescriptor = "Assets#nda", ObjectClassID1 = ClassIDType.CLASS_##Type))                       \
    std::string Name = DefaultValue;                                                                                                                                                                                                           \
    Type##Ptr Name##Res;                                                                                                                                                                                                                       \
    Type##R* Name##R;

#define RENDER_PIPELINE_RESOURCE_INVISIBLE(Type, Name, DefaultValue, _DisplayName, _ToolTips, _Category)                                                                                                                                       \
    CEMeta(Serialize)                                                                                                                                                                                                                          \
    std::string Name = DefaultValue;                                                                                                                                                                                                           \
    Type##Ptr Name##Res;                                                                                                                                                                                                                       \
    Type##R* Name##R;

#define RENDER_PIPELINE_GEOMETRY(Name, DefaultValue, _DisplayName, _ToolTips, _Category)                                                                                                                                                       \
    CEMeta(Serialize, Editor)                                                                                                                                                                                                                  \
    CECSAttribute(JsonProperty(#Name))                                                                                                                                                                                                         \
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", DisplayName = _DisplayName, ToolTips = _ToolTips, Category = _Category, FileTypeDescriptor = "Assets#nda", ObjectClassID1 = ClassIDType.CLASS_MeshAssetDataResource))        \
    std::string Name = DefaultValue;                                                                                                                                                                                                           \
    MeshAssetDataResourcePtr Name##Res;                                                                                                                                                                                                        \
    MeshAssetData* Name##R;

#define LOAD_RENDER_PIPELINE_MATERIAL(Name)                                                                                                                                                                                                    \
    Name##Res = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously(Name));                                                                                                                                                 \
    Name##R = dynamic_cast<MaterialR*>(Name##Res->GetRenderMaterial());

#define LOAD_RENDER_PIPELINE_COMPUTE_SHADER(Name)                                                                                                                                                                                              \
    Name##Res = TypeCast<resource::ComputeShader>(gAssetStreamingManager->LoadSynchronously(Name));                                                                                                                                            \
    Name##R = dynamic_cast<ComputeShaderR*>(Name##Res->GetRenderObject());

#define LOAD_RENDER_PIPELINE_TEXTURE(Name)                                                                                                                                                                                                     \
    Name##Res = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(Name));                                                                                                                                                  \
    Name##R = dynamic_cast<TextureR*>(Name##Res->GetTextureR());

#define LOAD_RENDER_PIPELINE_GEOMETRY(Name)                                                                                                                                                                                                    \
    {                                                                                                                                                                                                                                          \
        Name##Res = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(Name));                                                                                                                                \
        Name##R = static_cast<MeshAssetData*>(Name##Res->GetAssetData());                                                                                                                                                                      \
        if (Name##R->HasPreDefinedLayout())                                                                                                                                                                                                    \
        {                                                                                                                                                                                                                                      \
            Assert(false);                                                                                                                                                                                                                     \
        }                                                                                                                                                                                                                                      \
    }