#pragma  once

#include "CrossBase/Threading/RenderingThread.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/CECommonForward.h"
#include "memory/allocator/range_allocator.hpp"
namespace cross 
{
class CECOMMON_API RenderingCommandBatcher
{
public:
    RenderingCommandBatcher();

    void Enqueue(std::function<void()>&& cmd);

    void Dispatch();

private:
    std::mutex mMutex;
    static gbf::allocator::RangeAllocatorResourceV2 mMemoryResource;
    std::pmr::vector<std::function<void()>> mCache;
};
}   // namespace cross