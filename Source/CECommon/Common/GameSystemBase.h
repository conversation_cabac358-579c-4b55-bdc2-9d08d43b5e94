
#pragma once
#include <vector>
#include <assert.h>
#include "CECommonForward.h"
#include "CECommon/Common/WorldInternalSystem.h"
#include "CECommon/Common/SystemEvent.h"
#include "ECS/Develop/Framework/Types.h"
namespace cross
{

class GameWorld;
class RenderSystemBase;

struct OnSystemAddToGameWorldEventData
{
};

using OnSystemAddToGameWorldEvent = SystemEvent<OnSystemAddToGameWorldEventData>;

#pragma warning(push)
#pragma warning(disable : 4275)
class CECOMMON_API CEMeta(Cli) GameSystemBase : public WorldInternalSystem
{
    CEMetaInternal(Reflect)
public:
    inline SystemCatalog GetSystemCatalog() const final { return SystemCatalog::SystemCatalogGame; }

    void SetGameWorld(GameWorld* gameWorld);
    GameWorld* GetGameWorld()
    {
        return mGameWorld;
    }
    void BeginFrame(FrameParam* frameParam);

    void FirstUpdate(FrameParam* frameParam);

    void Update(FrameTickStage stage, FrameParam* frameParam);

    void EndFrame(FrameParam* frameParam);

    virtual void BuildTasks(FrameTickStage stage, class FrameParam* frameParam) override final;

    virtual void NotifyAddRenderSystemToRenderWorld() = 0;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    virtual RenderSystemBase* GetRenderSystem() = 0;
    virtual void OnEntityPropChange(ecs::EntityID entity, ecs::EntityDescFlags::EntityProps prop, bool bValue) {}

protected:
    virtual void OnBeginFrame(FrameParam* frameParam) {}
    virtual void OnFirstUpdate(FrameParam* frameParam) {}
    virtual void OnEndFrame(FrameParam* frameParam) {}
    GameWorld* mGameWorld{ nullptr };

    friend class GameWorld;
};
#pragma warning(pop)

struct GameWorldSystemChangedData
{
    GameWorldSystemChangedData() = default;

    GameWorldSystemChangedData(WorldSystemEventFlag flag, GameSystemBase* system) :mFlag(flag), mGameSystem(system) {}

    WorldSystemEventFlag mFlag{ WorldSystemEventFlag::None };
    GameSystemBase* mGameSystem{ nullptr };
};

using GameWorldSystemChangedEvent = SystemEvent<GameWorldSystemChangedData>;

}
