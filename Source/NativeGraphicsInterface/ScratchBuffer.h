#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGIDebug.h"
#include "CrossBase/Threading/TaskSystem.h"
#include "CrossBase/Threading/Task.h"
#include "StageBufferPool.h"
namespace cross {

struct ScratchBuffer
{
    std::unique_ptr<NGIBuffer> Buffer;
    SizeType capacity;
    mutable uint64_t lastAccessed;
    mutable bool AsyncUsing{false};
};

struct ScratchBufferBlock
{
    constexpr static SizeType INVALID_OFFSET = std::numeric_limits<SizeType>::max();

    ScratchBufferBlock();
    ~ScratchBufferBlock();

    SizeType Allocate(NGIBuffer* buffer, NGIBufferUsage usage, SizeType size);

    void BeginFrame(FrameParam* frameparam);

    void EndFrame() {}

    void Refresh(SizeType size)
    {
        mFullSize = size;
        mTail = 0;
        mFrameHead.assign(CmdSettings::Inst().gMaxQueuedFrame, 0);
    }

    SizeType GetOccupiedSize() const
    {
        auto head = mFrameHead.back();
        auto occpiedSize = (mTail + mFullSize - head) % mFullSize;
        return occpiedSize;
    }

    SizeType GetFreeSize() const
    {
        return mFullSize - 1 - GetOccupiedSize();
    }

    SizeType GetTail() const
    {
        return mTail;
    }

    SizeType GetFullSize() const
    {
        return mFullSize;
    }

    SizeType GetCurrentHead() const
    {
        return mFrameHead.front();
    }

private:
    // front is current frame head, back-1 is last frame head, back-2 is is last two frame head
    std::vector<SizeType> mFrameHead{};
    // Empty: mHead == mTail
    // Full: (mTail + 1) % size == mHead
    SizeType mTail = 0;
    SizeType mFullSize = 0;

    std::mutex mMutex;
};

struct ScratchBufferThreadPool : public NGITransientBufferManager
{
    constexpr static uint32_t ACTUAL_NUMBER_POOL = 16;
    ScratchBufferThreadPool(const NGITransientBufferManagerDesc& desc, NGIDevice* device);
    ~ScratchBufferThreadPool() override;
    ScratchBufferWrap AllocateScratch(NGIBufferUsage usage, SizeType size, bool dedicated) override;
    void RecordBufferCopy(NGICommandList* cmdCopyList, NGICommandList* cmdDrawList) override;
    void OnBeginFrame(FrameParam* frameparam) override;
    void EndFrame() override;

private:
    bool CreateNewBuffer(const NGIBufferDesc& desc);
    ScratchBufferWrap WrapAllocate(NGIBufferUsage usage, SizeType blockOffset, SizeType size, uint32_t poolID);
    std::array<ScratchBufferBlock, ACTUAL_NUMBER_POOL> mBlockPool;
    // add a random threadIndex mapping per frame to balance cost for each thread
    std::array<UInt32, ACTUAL_NUMBER_POOL> mThreadIndexMapping;
    // Device Local
    std::unique_ptr<NGIBuffer> mScratchBuffer;

#if NGI_SCRATCH_BUFFER_TYPE == NGI_SCRATCH_BUFFER_HOST

#elif NGI_SCRATCH_BUFFER_TYPE == NGI_SCRATCH_BUFFER_DEVICE
    // Host Visible
    std::unique_ptr<NGIStagingBuffer> mStagingBuffer;

    // DstBuffer, SrcBuffer, Size
    std::vector<std::tuple<NGIBuffer*, NGIStagingBuffer*, SizeType> > mPendingDeleteBufferRecords;
#elif NGI_SCRATCH_BUFFER_TYPE == NGI_SCRATCH_BUFFER_PCIE
#    if NGI_SCRATCH_BUFFER_WRITE_COMBINING
    // Host Visible
    std::vector<uint8_t> mStagingData;
#    endif
#endif

    double mAverageOccupation = 0.0;
    std::shared_mutex mResizeMutex;
    bool mAllocateVailed = false;
};


struct ScratchBufferManager : public NGITransientBufferManager
{
    ScratchBufferManager(const NGITransientBufferManagerDesc& desc, NGIDevice* device);
    ~ScratchBufferManager() override;

    StagingBufferWrap AllocateStaging(NGIBufferUsage usage, SizeType size, bool dedicated) override;
    ScratchBufferWrap AllocateScratch(NGIBufferUsage usage, SizeType size, bool dedicated) override;
    AsyncUsedStageBuffer AllocateStaging_Async(NGIBufferUsage usage, SizeType size, bool dedicated = false) override;
    void OnBeginFrame(FrameParam* frameparam) override;
    void RecordBufferCopy(NGICommandList* cmdCopyList, NGICommandList* cmdDrawList) override;
    void EndFrame() override;

private:
    ScratchBufferThreadPool mScratchBufferThreadPool;
    StageBufferPool mStageBufferPool;
};

}   // namespace cross