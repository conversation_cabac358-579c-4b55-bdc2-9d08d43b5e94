#pragma once
#include "NGI.h"
#ifndef NativeGraphicsInterface_EXPORTS
#define XR_NO_PROTOTYPES
#endif
#include "openxr/openxr.h"

namespace cross
{

struct NGIXRFrameInfo
{
    bool Running;
    XrFrameState FrameState;
    std::vector<XrView> Views;
};

struct NGIXRRuntime
{
    virtual ~NGIXRRuntime() = default;
    virtual void Initialize(NGICommandQueue* queue) {}

    virtual UInt32 GetViewCount() const { return 0; }
    virtual NGISwapchain* GetColorSwapchain(UInt32 viewID) { return nullptr; }
    virtual NGISwapchain* GetDepthSwapchain(UInt32 viewID) { return nullptr; }
    virtual void SetViewClipRange(UInt32 viewID, float zNear, float zFar) {}

    using OnStopHandler = void(*)(void);
    virtual NGIXRFrameInfo BeginGameFrame(OnStopHandler onStop) { return {}; }

    virtual void BeginRenderFrame() {}
    virtual void EndRenderFrame(const NGIXRFrameInfo& info) {}
};

NGI_API NGIXRRuntime* GetXRRuntime();

}