#include "FFXWrapper.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#ifdef WIN32


namespace cross {

FFXWrapper& FFXWrapper::Get()
{
    static FFXWrapper instance;
    return instance;
}

void FFXWrapper::DeleteFGContext()
{
    m_FrameGenerationConfig.frameGenerationEnabled = false;
    m_FrameGenerationConfig.swapChain = m_SwapChain;
    m_FrameGenerationConfig.presentCallback = nullptr;
    m_FrameGenerationConfig.HUDLessColor = FfxApiResource({});
    ffx::Configure(m_FrameGenContext, m_FrameGenerationConfig);
    ffx::ConfigureDescFrameGenerationSwapChainRegisterUiResourceVK uiConfig{};
    uiConfig.uiResource = {};
    ffx::Configure(m_SwapChainContext, uiConfig);
    auto code=ffx::DestroyContext(m_FrameGenContext);
    m_FrameGenContext = nullptr;
    if (code != ffx::ReturnCode::Ok)
    {
        LOG_ERROR("Couldn't destroy the frameGen context: {}", code);
    }

    return;
}


FfxApiSurfaceFormat FFXWrapper::MapSurfaceFormat(VkFormat fmt)
{
    switch (fmt)
    {
    //case (VK_FORMAT_R32G32B32A32_SFLOAT):
    //    return FFX_API_SURFACE_FORMAT_R32G32B32A32_TYPELESS;
    case (VK_FORMAT_R32G32B32A32_UINT):
        return FFX_API_SURFACE_FORMAT_R32G32B32A32_UINT;
    case (VK_FORMAT_R32G32B32A32_SFLOAT):
        return FFX_API_SURFACE_FORMAT_R32G32B32A32_FLOAT;
    case (VK_FORMAT_R16G16B16A16_SFLOAT):
        return FFX_API_SURFACE_FORMAT_R16G16B16A16_FLOAT;
    case (VK_FORMAT_R32G32_SFLOAT):
        return FFX_API_SURFACE_FORMAT_R32G32_FLOAT;
    case (VK_FORMAT_R32_UINT):
        return FFX_API_SURFACE_FORMAT_R32_UINT;
    //case (VK_FORMAT_R8G8B8A8_UNORM):
    //    return FFX_API_SURFACE_FORMAT_R8G8B8A8_TYPELESS;
    case (VK_FORMAT_R8G8B8A8_UNORM):
        return FFX_API_SURFACE_FORMAT_R8G8B8A8_UNORM;
    case (VK_FORMAT_R8G8B8A8_SNORM):
        return FFX_API_SURFACE_FORMAT_R8G8B8A8_SNORM;
    case (VK_FORMAT_R8G8B8A8_SRGB):
        return FFX_API_SURFACE_FORMAT_R8G8B8A8_SRGB;
    case (VK_FORMAT_B8G8R8A8_SRGB):
        return FFX_API_SURFACE_FORMAT_B8G8R8A8_SRGB;
    case (VK_FORMAT_B10G11R11_UFLOAT_PACK32):
        return FFX_API_SURFACE_FORMAT_R11G11B10_FLOAT;
    case (VK_FORMAT_A2B10G10R10_UNORM_PACK32):
        return FFX_API_SURFACE_FORMAT_R10G10B10A2_UNORM;
    case (VK_FORMAT_R16G16_SFLOAT):
        return FFX_API_SURFACE_FORMAT_R16G16_FLOAT;
    case (VK_FORMAT_R16G16_UINT):
        return FFX_API_SURFACE_FORMAT_R16G16_UINT;
    case (VK_FORMAT_R16G16_SINT):
        return FFX_API_SURFACE_FORMAT_R16G16_SINT;
    case (VK_FORMAT_R16_SFLOAT):
        return FFX_API_SURFACE_FORMAT_R16_FLOAT;
    case (VK_FORMAT_R16_UINT):
        return FFX_API_SURFACE_FORMAT_R16_UINT;
    case (VK_FORMAT_R16_UNORM):
        return FFX_API_SURFACE_FORMAT_R16_UNORM;
    case (VK_FORMAT_R16_SNORM):
        return FFX_API_SURFACE_FORMAT_R16_SNORM;
    case (VK_FORMAT_R8_UNORM):
        return FFX_API_SURFACE_FORMAT_R8_UNORM;
    case (VK_FORMAT_R8_UINT):
        return FFX_API_SURFACE_FORMAT_R8_UINT;
    case (VK_FORMAT_R8G8_UNORM):
        return FFX_API_SURFACE_FORMAT_R8G8_UNORM;
    case (VK_FORMAT_R8G8_UINT):
        return FFX_API_SURFACE_FORMAT_R8G8_UINT;
    case (VK_FORMAT_R32_SFLOAT):
        return FFX_API_SURFACE_FORMAT_R32_FLOAT;
    case (VK_FORMAT_E5B9G9R9_UFLOAT_PACK32):
        return FFX_API_SURFACE_FORMAT_R9G9B9E5_SHAREDEXP;
    case (VK_FORMAT_UNDEFINED):
        return FFX_API_SURFACE_FORMAT_UNKNOWN;

    default:
        LOG_ERROR("Format not yet supported");
        return FFX_API_SURFACE_FORMAT_UNKNOWN;
    }
}


//FfxApiSurfaceFormat FFXWrapper::MapSurfaceFormat(VkFormat format) {
//    switch (format)
//    {
//    //case (VK_FORMAT_R32G32B32A32_SFLOAT):
//    //    return FFX_API_SURFACE_FORMAT_R32G32B32A32_TYPELESS;
//    case (VK_FORMAT_R32G32B32A32_UINT):
//        return FFX_API_SURFACE_FORMAT_R32G32B32A32_UINT;
//    case (VK_FORMAT_R32G32B32A32_SFLOAT):
//        return FFX_API_SURFACE_FORMAT_R32G32B32A32_FLOAT;
//    //case (VK_FORMAT_R16G16B16A16_SFLOAT):
//    //    return FFX_API_SURFACE_FORMAT_R16G16B16A16_TYPELESS;
//    case (VK_FORMAT_R16G16B16A16_SFLOAT):
//        return FFX_API_SURFACE_FORMAT_R16G16B16A16_FLOAT;
//    case (VK_FORMAT_RGB32_FLOAT):
//        return FFX_API_SURFACE_FORMAT_R32G32B32_FLOAT;
//    case (VK_FORMAT_RG32_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_R32G32_TYPELESS;
//    case (VK_FORMAT_RG32_FLOAT):
//        return FFX_API_SURFACE_FORMAT_R32G32_FLOAT;
//    case (VK_FORMAT_R8_UINT):
//        return FFX_API_SURFACE_FORMAT_R8_UINT;
//    case (VK_FORMAT_R32_UINT):
//        return FFX_API_SURFACE_FORMAT_R32_UINT;
//    case (VK_FORMAT_RGBA8_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_R8G8B8A8_TYPELESS;
//    case (VK_FORMAT_RGBA8_UNORM):
//        return FFX_API_SURFACE_FORMAT_R8G8B8A8_UNORM;
//    case (VK_FORMAT_RGBA8_SNORM):
//        return FFX_API_SURFACE_FORMAT_R8G8B8A8_SNORM;
//    case (VK_FORMAT_RGBA8_SRGB):
//        return FFX_API_SURFACE_FORMAT_R8G8B8A8_SRGB;
//    case (VK_FORMAT_BGRA8_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_B8G8R8A8_TYPELESS;
//    case (VK_FORMAT_BGRA8_UNORM):
//        return FFX_API_SURFACE_FORMAT_B8G8R8A8_UNORM;
//    case (VK_FORMAT_BGRA8_SRGB):
//        return FFX_API_SURFACE_FORMAT_B8G8R8A8_SRGB;
//    case (VK_FORMAT_RG11B10_FLOAT):
//        return FFX_API_SURFACE_FORMAT_R11G11B10_FLOAT;
//    case (VK_FORMAT_RGB9E5_SHAREDEXP):
//        return FFX_API_SURFACE_FORMAT_R9G9B9E5_SHAREDEXP;
//    case (VK_FORMAT_RGB10A2_UNORM):
//        return FFX_API_SURFACE_FORMAT_R10G10B10A2_UNORM;
//    case (VK_FORMAT_RGB10A2_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_R10G10B10A2_TYPELESS;
//    case (VK_FORMAT_RG16_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_R16G16_TYPELESS;
//    case (VK_FORMAT_RG16_FLOAT):
//        return FFX_API_SURFACE_FORMAT_R16G16_FLOAT;
//    case (VK_FORMAT_RG16_UINT):
//        return FFX_API_SURFACE_FORMAT_R16G16_UINT;
//    case (VK_FORMAT_RG16_SINT):
//        return FFX_API_SURFACE_FORMAT_R16G16_SINT;
//    case (VK_FORMAT_R16_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_R16_TYPELESS;
//    case (VK_FORMAT_R16_FLOAT):
//        return FFX_API_SURFACE_FORMAT_R16_FLOAT;
//    case (VK_FORMAT_R16_UINT):
//        return FFX_API_SURFACE_FORMAT_R16_UINT;
//    case (VK_FORMAT_R16_UNORM):
//        return FFX_API_SURFACE_FORMAT_R16_UNORM;
//    case (VK_FORMAT_R16_SNORM):
//        return FFX_API_SURFACE_FORMAT_R16_SNORM;
//    case (VK_FORMAT_R8_TYPELESS):
//        return FFX_API_SURFACE_FORMAT_R8_TYPELESS;
//    case (VK_FORMAT_R8_UNORM):
//        return FFX_API_SURFACE_FORMAT_R8_UNORM;
//    case VK_FORMAT_RG8_TYPELESS:
//        return FFX_API_SURFACE_FORMAT_R8G8_TYPELESS;
//    case VK_FORMAT_RG8_UNORM:
//        return FFX_API_SURFACE_FORMAT_R8G8_UNORM;
//    case VK_FORMAT_RG8_UINT:
//        return FFX_API_SURFACE_FORMAT_R8G8_UINT;
//    case VK_FORMAT_R32_TYPELESS:
//        return FFX_API_SURFACE_FORMAT_R32_TYPELESS;
//    case VK_FORMAT_R32_FLOAT:
//    case VK_FORMAT_D32_FLOAT:
//        return FFX_API_SURFACE_FORMAT_R32_FLOAT;
//    case (VK_FORMAT_Unknown):
//        return FFX_API_SURFACE_FORMAT_UNKNOWN;
//    default:
//        cauldron::CauldronCritical(L"ValidationRemap: Unsupported format requested. Please implement.");
//        return FFX_API_SURFACE_FORMAT_UNKNOWN;
//    }
//}

//FfxApiResource FFXWrapper::TransferTextureToFSR(REDTextureView* texview, uint32_t state, uint32_t additionalUsages)
//{
//    if (texview == nullptr)
//    {
//        return ffxApiGetResourceVK(nullptr, FfxApiResourceDescription(), state);
//    }
//    REDTexture* tex = texview->GetTexture();
//    auto texNative = tex->GetNativeTexture();
//    VkImage image = reinterpret_cast<VkImage>(texNative->GetNativeHandle());
//    auto createInfo = CreateImageCreateInfoForFSR(tex->GetDesc());
//    return ffxApiGetResourceVK((void*)image, ffxApiGetImageResourceDescriptionVK(image, createInfo, additionalUsages), state);
//}

void FFXWrapper::CreateFGContext(ffx::CreateBackendVKDesc backendDesc, UInt2 resInfo, bool s_InvertedDepth)
{
    ffx::CreateContextDescFrameGeneration createFg{};
    createFg.displaySize = {resInfo.x, resInfo.y};
    createFg.maxRenderSize = {resInfo.x, resInfo.y};
    if (s_InvertedDepth)
        createFg.flags |= FFX_FRAMEGENERATION_ENABLE_DEPTH_INVERTED | FFX_FRAMEGENERATION_ENABLE_DEPTH_INFINITE;
    createFg.flags |= FFX_FRAMEGENERATION_ENABLE_HIGH_DYNAMIC_RANGE;

    // m_EnableAsyncCompute = m_PendingEnableAsyncCompute;
    if (false)
    {
        createFg.flags |= FFX_FRAMEGENERATION_ENABLE_ASYNC_WORKLOAD_SUPPORT;
    }
    //TODO:fix
    //Use callback
    //VkFormat testFormat = VkFormat::VK_FORMAT_R16G16B16A16_SFLOAT;
    //manual call fg
    //VkFormat testFormat = VkFormat::VK_FORMAT_R8G8B8A8_UNORM;
    createFg.backBufferFormat = MapSurfaceFormat(m_SwapChainFormat);
    //createFg.backBufferFormat = MapSurfaceFormat(testFormat);
    ffx::ReturnCode retCode;
    retCode = ffx::CreateContext(m_FrameGenContext, nullptr, createFg, backendDesc);
    if (retCode != ffx::ReturnCode::Ok)
    {
        LOG_ERROR("Couldn't create the frameGen context: {}", retCode);
    }


    void* ffxSwapChain;

    ffxSwapChain = m_SwapChain;


    // Configure frame generation
   // FfxApiResource hudLessResource = ffxGetResourceApiTexture(m_pHudLessTexture[m_curUiTextureIndex]->GetResource(), FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);

    m_FrameGenerationConfig.frameGenerationEnabled = false;

    /*m_FrameGenerationConfig.frameGenerationCallback = [](ffxDispatchDescFrameGeneration* params, void* pUserCtx) -> ffxReturnCode_t { return ffxDispatch(reinterpret_cast<ffxContext*>(pUserCtx), &params->header); };
    m_FrameGenerationConfig.frameGenerationCallbackUserContext = &m_FrameGenContext;*/

    m_FrameGenerationConfig.frameGenerationCallback = nullptr;
    m_FrameGenerationConfig.frameGenerationCallbackUserContext = nullptr;

    m_FrameGenerationConfig.presentCallback = nullptr;
    m_FrameGenerationConfig.presentCallbackUserContext = nullptr;
    m_FrameGenerationConfig.swapChain = ffxSwapChain;
    m_FrameGenerationConfig.HUDLessColor = FfxApiResource({});

    m_FrameGenerationConfig.frameID = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
    retCode = ffx::Configure(m_FrameGenContext, m_FrameGenerationConfig);


    FfxApiEffectMemoryUsage gpuMemoryUsageFrameGeneration;
    ffx::QueryDescFrameGenerationGetGPUMemoryUsage frameGenGetGPUMemoryUsage{};
    frameGenGetGPUMemoryUsage.gpuMemoryUsageFrameGeneration = &gpuMemoryUsageFrameGeneration;
    ffx::Query(m_FrameGenContext, frameGenGetGPUMemoryUsage);


    FfxApiEffectMemoryUsage gpuMemoryUsageFrameGenerationSwapchain;

    ffx::QueryFrameGenerationSwapChainGetGPUMemoryUsageVK frameGenSwapchainGetGPUMemoryUsage{};
    frameGenSwapchainGetGPUMemoryUsage.gpuMemoryUsageFrameGenerationSwapchain = &gpuMemoryUsageFrameGenerationSwapchain;
    ffx::Query(m_SwapChainContext, frameGenSwapchainGetGPUMemoryUsage);

    ffx::ConfigureDescFrameGenerationSwapChainKeyValueVK m_swapchainKeyValueConfig{};
    m_swapchainKeyValueConfig.key = FFX_API_CONFIGURE_FG_SWAPCHAIN_KEY_FRAMEPACINGTUNING;
    m_swapchainKeyValueConfig.ptr = &framePacingTuning;
    framePacingTuning.safetyMarginInMs = m_SafetyMarginInMs;
    framePacingTuning.varianceFactor = m_VarianceFactor;
    framePacingTuning.allowHybridSpin = m_AllowHybridSpin;
    framePacingTuning.hybridSpinTime = m_HybridSpinTime;
    framePacingTuning.allowWaitForSingleObjectOnFence = m_AllowWaitForSingleObjectOnFence;
    ffx::Configure(m_SwapChainContext, m_swapchainKeyValueConfig);
    return;
}


}   // namespace cross
#endif