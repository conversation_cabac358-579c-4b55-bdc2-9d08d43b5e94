#pragma once
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#ifdef WIN32
#include "Vulkan/VulkanResource.h"
#include "ffx_api/ffx_api.hpp"
#include "ffx_api/vk/ffx_api_vk.hpp"
#include "ffx_api/ffx_framegeneration.hpp"
#include "ffx_api/ffx_api_types.h"
namespace cross {


inline VkImageType MapTextureTypeFFX(NGITextureType type)
{
    switch (type)
    {
    case NGITextureType::Texture1D:
    case NGITextureType::Texture1DArray:
        return VK_IMAGE_TYPE_1D;
    case NGITextureType::Texture2D:
    case NGITextureType::Texture2DArray:
    case NGITextureType::TextureCube:
    case NGITextureType::TextureCubeArray:
        return VK_IMAGE_TYPE_2D;
    case NGITextureType::Texture3D:
        return VK_IMAGE_TYPE_3D;
    default:
        AssertMsg(false, "Should not run to this branch");
        return VK_IMAGE_TYPE_MAX_ENUM;
    }
}

inline VkFormat MapGraphicsFormatFFX(GraphicsFormat format)
{
    switch (format)
    {
    case GraphicsFormat::Unknown:
        return VK_FORMAT_UNDEFINED;
    case GraphicsFormat::R8_SRGB:
        return VK_FORMAT_R8_SRGB;
    case GraphicsFormat::R8G8_SRGB:
        return VK_FORMAT_R8G8_SRGB;
    case GraphicsFormat::R8G8B8_SRGB:
        return VK_FORMAT_R8G8B8_SRGB;
    case GraphicsFormat::R8G8B8A8_SRGB:
        return VK_FORMAT_R8G8B8A8_SRGB;
    case GraphicsFormat::R8_UNorm:
        return VK_FORMAT_R8_UNORM;
    case GraphicsFormat::R8G8_UNorm:
        return VK_FORMAT_R8G8_UNORM;
    case GraphicsFormat::R8G8B8_UNorm:
        return VK_FORMAT_R8G8B8_UNORM;
    case GraphicsFormat::R8G8B8A8_UNorm:
        return VK_FORMAT_R8G8B8A8_UNORM;
    case GraphicsFormat::R8_SNorm:
        return VK_FORMAT_R8_SNORM;
    case GraphicsFormat::R8G8_SNorm:
        return VK_FORMAT_R8G8_SNORM;
    case GraphicsFormat::R8G8B8_SNorm:
        return VK_FORMAT_R8G8B8_SNORM;
    case GraphicsFormat::R8G8B8A8_SNorm:
        return VK_FORMAT_R8G8B8A8_SNORM;
    case GraphicsFormat::R8_UInt:
        return VK_FORMAT_R8_UINT;
    case GraphicsFormat::R8G8_UInt:
        return VK_FORMAT_R8G8_UINT;
    case GraphicsFormat::R8G8B8_UInt:
        return VK_FORMAT_R8G8B8_UINT;
    case GraphicsFormat::R8G8B8A8_UInt:
        return VK_FORMAT_R8G8B8A8_UINT;
    case GraphicsFormat::R8_SInt:
        return VK_FORMAT_R8_SINT;
    case GraphicsFormat::R8G8_SInt:
        return VK_FORMAT_R8G8_SINT;
    case GraphicsFormat::R8G8B8_SInt:
        return VK_FORMAT_R8G8B8_SINT;
    case GraphicsFormat::R8G8B8A8_SInt:
        return VK_FORMAT_R8G8B8A8_SINT;
    case GraphicsFormat::R16_UNorm:
        return VK_FORMAT_R16_UNORM;
    case GraphicsFormat::R16G16_UNorm:
        return VK_FORMAT_R16G16_UNORM;
    case GraphicsFormat::R16G16B16_UNorm:
        return VK_FORMAT_R16G16B16_UNORM;
    case GraphicsFormat::R16G16B16A16_UNorm:
        return VK_FORMAT_R16G16B16A16_UNORM;
    case GraphicsFormat::R16_SNorm:
        return VK_FORMAT_R16_SNORM;
    case GraphicsFormat::R16G16_SNorm:
        return VK_FORMAT_R16G16_SNORM;
    case GraphicsFormat::R16G16B16_SNorm:
        return VK_FORMAT_R16G16B16_SNORM;
    case GraphicsFormat::R16G16B16A16_SNorm:
        return VK_FORMAT_R16G16B16A16_SNORM;
    case GraphicsFormat::R16_UInt:
        return VK_FORMAT_R16_UINT;
    case GraphicsFormat::R16G16_UInt:
        return VK_FORMAT_R16G16_UINT;
    case GraphicsFormat::R16G16B16_UInt:
        return VK_FORMAT_R16G16B16_UINT;
    case GraphicsFormat::R16G16B16A16_UInt:
        return VK_FORMAT_R16G16B16A16_UINT;
    case GraphicsFormat::R16_SInt:
        return VK_FORMAT_R16_SINT;
    case GraphicsFormat::R16G16_SInt:
        return VK_FORMAT_R16G16_SINT;
    case GraphicsFormat::R16G16B16_SInt:
        return VK_FORMAT_R16G16B16_SINT;
    case GraphicsFormat::R16G16B16A16_SInt:
        return VK_FORMAT_R16G16B16A16_SINT;
    case GraphicsFormat::R32_UInt:
        return VK_FORMAT_R32_UINT;
    case GraphicsFormat::R32G32_UInt:
        return VK_FORMAT_R32G32_UINT;
    case GraphicsFormat::R32G32B32_UInt:
        return VK_FORMAT_R32G32B32_UINT;
    case GraphicsFormat::R32G32B32A32_UInt:
        return VK_FORMAT_R32G32B32A32_UINT;
    case GraphicsFormat::R32_SInt:
        return VK_FORMAT_R32_SINT;
    case GraphicsFormat::R32G32_SInt:
        return VK_FORMAT_R32G32_SINT;
    case GraphicsFormat::R32G32B32_SInt:
        return VK_FORMAT_R32G32B32_SINT;
    case GraphicsFormat::R32G32B32A32_SInt:
        return VK_FORMAT_R32G32B32A32_SINT;
    case GraphicsFormat::R16_SFloat:
        return VK_FORMAT_R16_SFLOAT;
    case GraphicsFormat::R16G16_SFloat:
        return VK_FORMAT_R16G16_SFLOAT;
    case GraphicsFormat::R16G16B16_SFloat:
        return VK_FORMAT_R16G16B16_SFLOAT;
    case GraphicsFormat::R16G16B16A16_SFloat:
        return VK_FORMAT_R16G16B16A16_SFLOAT;
    case GraphicsFormat::R32_SFloat:
        return VK_FORMAT_R32_SFLOAT;
    case GraphicsFormat::R32G32_SFloat:
        return VK_FORMAT_R32G32_SFLOAT;
    case GraphicsFormat::R32G32B32_SFloat:
        return VK_FORMAT_R32G32B32_SFLOAT;
    case GraphicsFormat::R32G32B32A32_SFloat:
        return VK_FORMAT_R32G32B32A32_SFLOAT;
    case GraphicsFormat::B8G8R8_SRGB:
        return VK_FORMAT_B8G8R8_SRGB;
    case GraphicsFormat::B8G8R8A8_SRGB:
        return VK_FORMAT_B8G8R8A8_SRGB;
    case GraphicsFormat::B8G8R8_UNorm:
        return VK_FORMAT_B8G8R8_UNORM;
    case GraphicsFormat::B8G8R8A8_UNorm:
        return VK_FORMAT_B8G8R8A8_UNORM;
    case GraphicsFormat::B8G8R8_SNorm:
        return VK_FORMAT_B8G8R8_SNORM;
    case GraphicsFormat::B8G8R8A8_SNorm:
        return VK_FORMAT_B8G8R8A8_SNORM;
    case GraphicsFormat::B8G8R8_UInt:
        return VK_FORMAT_B8G8R8_UINT;
    case GraphicsFormat::B8G8R8A8_UInt:
        return VK_FORMAT_B8G8R8A8_UINT;
    case GraphicsFormat::B8G8R8_SInt:
        return VK_FORMAT_B8G8R8_SINT;
    case GraphicsFormat::B8G8R8A8_SInt:
        return VK_FORMAT_B8G8R8A8_SINT;

    case GraphicsFormat::R4G4B4A4_UNormPack16:
        return VK_FORMAT_R4G4B4A4_UNORM_PACK16;
    case GraphicsFormat::R5G6B5_UNormPack16:
        return VK_FORMAT_R5G6B5_UNORM_PACK16;
    case GraphicsFormat::R5G5B5A1_UNormPack16:
        return VK_FORMAT_R5G5B5A1_UNORM_PACK16;

    case GraphicsFormat::R9G9B9E5_UFloatPack32:
        return VK_FORMAT_E5B9G9R9_UFLOAT_PACK32;
    case GraphicsFormat::R11G11B10_UFloatPack32:
        return VK_FORMAT_B10G11R11_UFLOAT_PACK32;
    case GraphicsFormat::A2B10G10R10_UNormPack32:
        return VK_FORMAT_A2B10G10R10_UNORM_PACK32;
    case GraphicsFormat::A2B10G10R10_UIntPack32:
        return VK_FORMAT_A2B10G10R10_UINT_PACK32;
    case GraphicsFormat::A2B10G10R10_SIntPack32:
        return VK_FORMAT_A2B10G10R10_SINT_PACK32;
    case GraphicsFormat::A2R10G10B10_UNormPack32:
        return VK_FORMAT_A2R10G10B10_UNORM_PACK32;
    case GraphicsFormat::A2R10G10B10_UIntPack32:
        return VK_FORMAT_A2R10G10B10_UINT_PACK32;
    case GraphicsFormat::A2R10G10B10_SIntPack32:
        return VK_FORMAT_A2R10G10B10_SINT_PACK32;

    case GraphicsFormat::A2R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A2R10G10B10_XRUNormPack32:
    case GraphicsFormat::R10G10B10_XRSRGBPack32:
    case GraphicsFormat::R10G10B10_XRUNormPack32:
    case GraphicsFormat::A10R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A10R10G10B10_XRUNormPack32:
        Assert(false);
        return VK_FORMAT_UNDEFINED;

    case GraphicsFormat::D16_UNorm:
        return VK_FORMAT_D16_UNORM;
    case GraphicsFormat::D24_UNorm_X8:
        return VK_FORMAT_X8_D24_UNORM_PACK32;
    case GraphicsFormat::D24_UNorm_S8_UInt:
        return VK_FORMAT_D24_UNORM_S8_UINT;
    case GraphicsFormat::D32_SFloat:
        return VK_FORMAT_D32_SFLOAT;
    case GraphicsFormat::D32_SFloat_S8_UInt:
        return VK_FORMAT_D32_SFLOAT_S8_UINT;
    case GraphicsFormat::S8_UInt:
        return VK_FORMAT_S8_UINT;

    case GraphicsFormat::RGB_BC1_SRGB:
        return VK_FORMAT_BC1_RGB_SRGB_BLOCK;
    case GraphicsFormat::RGB_BC1_UNorm:
        return VK_FORMAT_BC1_RGB_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC1_SRGB:
        return VK_FORMAT_BC1_RGBA_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC1_UNorm:
        return VK_FORMAT_BC1_RGBA_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC2_SRGB:
        return VK_FORMAT_BC2_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC2_UNorm:
        return VK_FORMAT_BC2_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC3_SRGB:
        return VK_FORMAT_BC3_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC3_UNorm:
        return VK_FORMAT_BC3_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_UNorm:
        return VK_FORMAT_BC4_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_SNorm:
        return VK_FORMAT_BC4_SNORM_BLOCK;
    case GraphicsFormat::RG_BC5_UNorm:
        return VK_FORMAT_BC5_UNORM_BLOCK;
    case GraphicsFormat::RG_BC5_SNorm:
        return VK_FORMAT_BC5_SNORM_BLOCK;
    case GraphicsFormat::RGB_BC6H_UFloat:
        return VK_FORMAT_BC6H_UFLOAT_BLOCK;
    case GraphicsFormat::RGB_BC6H_SFloat:
        return VK_FORMAT_BC6H_SFLOAT_BLOCK;
    case GraphicsFormat::RGBA_BC7_SRGB:
        return VK_FORMAT_BC7_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC7_UNorm:
        return VK_FORMAT_BC7_UNORM_BLOCK;

    case GraphicsFormat::RGB_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG;

    case GraphicsFormat::RGB_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK;
    case GraphicsFormat::RGB_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_UNorm:
        return VK_FORMAT_EAC_R11_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_SNorm:
        return VK_FORMAT_EAC_R11_SNORM_BLOCK;
    case GraphicsFormat::RG_EAC_UNorm:
        return VK_FORMAT_EAC_R11G11_UNORM_BLOCK;
    case GraphicsFormat::RG_EAC_SNorm:
        return VK_FORMAT_EAC_R11G11_SNORM_BLOCK;

    case GraphicsFormat::RGBA_ASTC4X4_SRGB:
        return VK_FORMAT_ASTC_4x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UNorm:
        return VK_FORMAT_ASTC_4x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UFloat:
        return VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X4_SRGB:
        return VK_FORMAT_ASTC_5x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UNorm:
        return VK_FORMAT_ASTC_5x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UFloat:
        return VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X5_SRGB:
        return VK_FORMAT_ASTC_5x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UNorm:
        return VK_FORMAT_ASTC_5x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UFloat:
        return VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X5_SRGB:
        return VK_FORMAT_ASTC_6x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UNorm:
        return VK_FORMAT_ASTC_6x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UFloat:
        return VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X6_SRGB:
        return VK_FORMAT_ASTC_6x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UNorm:
        return VK_FORMAT_ASTC_6x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UFloat:
        return VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X5_SRGB:
        return VK_FORMAT_ASTC_8x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UNorm:
        return VK_FORMAT_ASTC_8x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UFloat:
        return VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X6_SRGB:
        return VK_FORMAT_ASTC_8x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UNorm:
        return VK_FORMAT_ASTC_8x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UFloat:
        return VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X8_SRGB:
        return VK_FORMAT_ASTC_8x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UNorm:
        return VK_FORMAT_ASTC_8x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UFloat:
        return VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X5_SRGB:
        return VK_FORMAT_ASTC_10x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UNorm:
        return VK_FORMAT_ASTC_10x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UFloat:
        return VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X6_SRGB:
        return VK_FORMAT_ASTC_10x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UNorm:
        return VK_FORMAT_ASTC_10x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UFloat:
        return VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X8_SRGB:
        return VK_FORMAT_ASTC_10x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UNorm:
        return VK_FORMAT_ASTC_10x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UFloat:
        return VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X10_SRGB:
        return VK_FORMAT_ASTC_10x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UNorm:
        return VK_FORMAT_ASTC_10x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UFloat:
        return VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X10_SRGB:
        return VK_FORMAT_ASTC_12x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UNorm:
        return VK_FORMAT_ASTC_12x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UFloat:
        return VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X12_SRGB:
        return VK_FORMAT_ASTC_12x12_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UNorm:
        return VK_FORMAT_ASTC_12x12_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UFloat:
        return VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT;
    default:
        Assert(false);
        return VK_FORMAT_UNDEFINED;
    }
}

inline VkSampleCountFlagBits MapSampleCountFFX(UInt32 count)
{
    switch (count)
    {
    case 1:
        return VK_SAMPLE_COUNT_1_BIT;
    case 2:
        return VK_SAMPLE_COUNT_2_BIT;
    case 4:
        return VK_SAMPLE_COUNT_4_BIT;
    case 8:
        return VK_SAMPLE_COUNT_8_BIT;
    case 16:
        return VK_SAMPLE_COUNT_16_BIT;
    case 32:
        return VK_SAMPLE_COUNT_32_BIT;
    case 64:
        return VK_SAMPLE_COUNT_64_BIT;
    default:
        AssertMsg(false, "Invalid sample count");
        return VK_SAMPLE_COUNT_FLAG_BITS_MAX_ENUM;
    }
}

inline VkImageUsageFlags MapTextureUsageFFX(NGITextureUsage usage)
{
    VkImageUsageFlags vkUsageFlags = 0;
    if (EnumHasAnyFlags(usage, NGITextureUsage::CopySrc))
        vkUsageFlags |= VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::CopyDst))
        vkUsageFlags |= VK_IMAGE_USAGE_TRANSFER_DST_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::RenderTarget))
        vkUsageFlags |= VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::DepthStencil))
        vkUsageFlags |= VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::UnorderedAccess))
        vkUsageFlags |= VK_IMAGE_USAGE_STORAGE_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::ShaderResource))
        vkUsageFlags |= VK_IMAGE_USAGE_SAMPLED_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::SubpassInput))
        vkUsageFlags |= VK_IMAGE_USAGE_INPUT_ATTACHMENT_BIT, vkUsageFlags |= VK_IMAGE_USAGE_SAMPLED_BIT;
    return vkUsageFlags;
}

inline VkImageCreateInfo CreateImageCreateInfoForFSR(const NGITextureDesc& desc, bool shared = false)
{
    VmaAllocationCreateInfo allocCreateInfo{
        VMA_ALLOCATION_CREATE_USER_DATA_COPY_STRING_BIT,
        VMA_MEMORY_USAGE_GPU_ONLY,
    };
    // use const_cast to pass andrioid;
    // allocCreateInfo.pUserData = const_cast<char*>(pDebugName);
    VkImageCreateInfo createInfo{
        VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO,
        nullptr,
        desc.MutableFormat ? VK_IMAGE_CREATE_MUTABLE_FORMAT_BIT : 0u,
        MapTextureTypeFFX(desc.Dimension),
        MapGraphicsFormatFFX(desc.Format),
        {
            desc.Width,
            0,
            0,
        },
        desc.MipCount,
        0,
        MapSampleCountFFX(desc.SampleCount),
        allocCreateInfo.usage == VMA_MEMORY_USAGE_GPU_TO_CPU ? VK_IMAGE_TILING_LINEAR : VK_IMAGE_TILING_OPTIMAL,
        MapTextureUsageFFX(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
        VK_IMAGE_LAYOUT_UNDEFINED,
    };
    switch (desc.Dimension)
    {
    case NGITextureType::Texture1D:
        Assert(desc.Height == 1);
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture1DArray:
        Assert(desc.Height == 1);
        Assert(desc.Depth == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture2D:
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture2DArray:
        Assert(desc.Depth == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture3D:
        Assert(desc.ArraySize == 1);
        createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = desc.Depth;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::TextureCube:
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 6);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        // createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 6;
        break;
    case NGITextureType::TextureCubeArray:
        Assert(desc.ArraySize % 6 == 0);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        // createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    default:
        Assert(false);
        break;
    }

    VkExternalMemoryImageCreateInfo extImageCreateInfo = {};
    if (shared)
    {
        extImageCreateInfo.sType = VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO;
#    if CROSSENGINE_WIN
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_BIT;
#    else
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_FD_BIT_KHR;
#    endif
        createInfo.pNext = &extImageCreateInfo;

        // allocCreateInfo.pool = GetDevice<VulkanDevice>()->GetExportableVMAPool();
    }
    return createInfo;
}


static inline FfxApiResource ffxGetResourceApiTexture(const REDTextureView* texture, uint32_t state = FFX_API_RESOURCE_STATE_COMPUTE_READ, uint32_t additionalUsages = 0)
{
    VkImage image = reinterpret_cast<VkImage>(texture->GetNativeTextureView()->GetNativeHandle());
    auto imageCreateInfo = CreateImageCreateInfoForFSR(texture->mTexture->mDesc);
    return ffxApiGetResourceVK((void*)image, ffxApiGetImageResourceDescriptionVK(image, imageCreateInfo, additionalUsages), state);
}



class NGI_API FFXWrapper
{
public:
    FFXWrapper() {}
   
    static FFXWrapper& Get();
    FFXWrapper(const FFXWrapper&) = delete;
    FFXWrapper(FFXWrapper&&) = delete;
    FFXWrapper& operator=(const FFXWrapper&) = delete;
    FFXWrapper& operator=(FFXWrapper&&) = delete;

    void DeleteFGContext();
    void CreateFGContext(ffx::CreateBackendVKDesc backendDesc, UInt2 resInfo, bool s_InvertedDepth);
    uint64_t preFrameID = 0;

    FfxApiSurfaceFormat MapSurfaceFormat(VkFormat format);

    //FG
    ffx::Context m_FrameGenContext = nullptr;
    ffx::Context m_SwapChainContext = nullptr;
    VkSwapchainKHR m_SwapChain;
    VkFormat m_SwapChainFormat;
    ffx::ConfigureDescFrameGeneration m_FrameGenerationConfig{};

    // Set Swapchain Frame pacing Tuning
    float m_SafetyMarginInMs = 0.1f;   // in Millisecond
    float m_VarianceFactor = 0.1f;     // valid range [0.0,1.0]
    bool m_AllowHybridSpin=false;
    uint32_t m_HybridSpinTime=2;
    bool m_AllowWaitForSingleObjectOnFence=false;
    FfxApiSwapchainFramePacingTuning framePacingTuning;
    bool m_FrameGenOn = false;
};
}

#endif
