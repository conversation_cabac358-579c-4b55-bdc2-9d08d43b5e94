#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "RenderEngine/Texture/GPUTexture.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine//RenderMaterial.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RenderWorldConst.h"

namespace cross
{
struct CubemapCamera
{
    std::array<cross::ecs::EntityID, 6> mFaceCameras;
};

struct ProbeRenderTextureInfo
{
    REDUniquePtr<REDResidentTexture> mRenderCubeTexture;
    REDUniquePtr<REDResidentTextureView> mRenderCubeTextureView;
    std::vector<REDUniquePtr<REDResidentTextureView>> mPerMipCubeViews;
    std::vector<REDUniquePtr<REDResidentTextureView>> mPerMip2DArrayViews;
    std::vector<std::array<REDUniquePtr<REDResidentTextureView>, REFLECTION_PROBE_CAMERA_NUM>> mPerMipPerFaceViews;
    std::string mRenderTextureName;
};

struct ComponentDesc;
struct ReflectionProbeCameraComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

protected:
    ReflectionProbeType mRefleProbeType;
    ReflectionProbeShapeType mRefleProbeShapeType;
    float mSphereRadius;
    Float3 mBoxSize;
    float mNearPlane;
    float mBlendDistance;
    float mIntensity;
    ReflectionProbeRefreshMode mRefreshMode;
    ReflectionProbeTimeSlicing mTimeSlicing;
    bool mGenerateMipmapForCapture{ false };
    bool mBoxProjection{ false };

    CubemapCamera mCameras;
    GPUTexture* mReflectionCubeMap;
    ProbeRenderTextureInfo mRenderTextureInfo[2];
    int mCaptureIndex{0};
    bool mCaptureTextureDirty{false};
    bool mDirty{ true };
    bool mEnable{ false };
    friend class ReflectionProbeSystemR;
};

//////////////////////////////////////////////////////////////////////////
class RENDER_ENGINE_API ReflectionProbeSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect) 
    using ReflectionProbeCameraH = ecs::ComponentHandle<ReflectionProbeCameraComponentR>;
    using ReflectionProbeCameraReader = ecs::ScopedComponentRead<ReflectionProbeCameraComponentR>;
    using ReflectionProbeCameraWriter = ecs::ScopedComponentWrite<ReflectionProbeCameraComponentR>;

    friend class RenderCamera;
public:
    static ReflectionProbeSystemR* CreateInstance() { return new ReflectionProbeSystemR(); };

    virtual void Release() override { delete this; };

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    void SetEnable(ReflectionProbeCameraH comp, bool isEnable);

    const CubemapCamera* GetReflectionProbeCamera(const ReflectionProbeCameraReader& comp) const;

    void SetReflectionProbeShow(ecs::EntityID entity, bool isShow);

    void SetTransparentColorMaterial(MaterialR* material){ mTransparentColorMaterial = material; }

    MaterialR* GetTransparentColorMaterial() { return mTransparentColorMaterial; }

    void SetLineColorMaterial(MaterialR* material) { mLineColorMaterial = material; }

    MaterialR* GetLineColorMaterial() { return mLineColorMaterial; }

    void SetReflectionProbeCamerasRenderTarget(ecs::EntityID entity, const UInt32& cameraIndex, RenderTextureR* renderTexture);

    void SetReflectionProbeCameraEntityId(ecs::EntityID entity, const UInt32& cameraIndex, const ecs::EntityID& id);

    void InitializeProbeRenderTextureInfo(ecs::EntityID entity);

    void BindCameraToReflectionPipeline(std::vector<ecs::EntityID> entitys, IRenderPipeline* pipeline);

#define REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(PROP, TYPE)\
    void SetReflectionProbeCamera##PROP(ecs::EntityID entity, const TYPE& val);                                                                       \
    const TYPE GetReflectionProbeCamera##PROP(const ReflectionProbeCameraReader& comp) const;                                                         \

    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(RefleProbeType, ReflectionProbeType);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(RefleProbeShapeType, ReflectionProbeShapeType);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(SphereRadius, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(BoxSize, Float3);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(NearPlane, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(BlendDistance, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(Intensity, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(RefreshMode, ReflectionProbeRefreshMode);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(TimeSlicing, ReflectionProbeTimeSlicing);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(BoxProjection, bool);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(Dirty, bool);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_STATE(Enable, bool);

    void SetReflectionProbeCameraReflectionCubeMap(ecs::EntityID entity, GPUTexture* val);

    GPUTexture* GetReflectionProbeCameraReflectionCubeMap(const ReflectionProbeCameraReader& comp) const;

    inline auto& GetReflectionProbeCameraRealtimeCubeMapView(const ReflectionProbeCameraReader& comp) const
    {
        return comp->mRenderTextureInfo[comp->mCaptureIndex].mRenderCubeTextureView;
    }

    inline bool IsRefleProbeEnableForRuntime(const ReflectionProbeCameraReader& reader) const { return reader->mEnable; }

    inline const CubemapCamera GetReflectionProbeCameras(const ReflectionProbeCameraReader& comp)
    {
        return comp->mCameras;
    }

    inline auto& GetRenderTextureInfo(const ReflectionProbeCameraReader& reader) { return reader->mRenderTextureInfo[reader->mCaptureIndex]; }

    inline void GotoNextTextureInfo(const ReflectionProbeCameraWriter& writer)
    {
        writer->mCaptureIndex = (writer->mCaptureIndex + 1) % 2;
        writer->mCaptureTextureDirty = true;
    }

    inline bool FetchSubBugdetNum(const ReflectionProbeCameraReader& reader)
    {
        if (reader->mRefreshMode == ReflectionProbeRefreshMode::ViaScripting)
        {
            return mBudgetProbeNum.fetch_sub(1) > 0;
        }
        else
        {
            return true;
        }
    }

    inline bool GetCaptureTextureDirty(const ReflectionProbeCameraReader& reader) 
    {
        return reader->mCaptureTextureDirty;
    }

    inline void ClearCaptureTextureDirty(const ReflectionProbeCameraWriter& writer)
    {
        writer->mCaptureTextureDirty = false;
    }

    inline auto& GetRenderTextureInfoCompleted(const ReflectionProbeCameraReader& reader)
    {
        return reader->mRenderTextureInfo[(reader->mCaptureIndex + 1) % 2];
    }

    inline void SetShowReflectionProbState(bool state) { mIsShowRPs = state; };

    inline const UInt32 GetReflectionProbeMipmapNum(const ReflectionProbeCameraReader& reader)
    {
        Assert(reader->mRenderTextureInfo[reader->mCaptureIndex].mRenderCubeTexture);
        return static_cast<UInt32>(std::log2(reader->mRenderTextureInfo[reader->mCaptureIndex].mRenderCubeTexture->GetDesc().Width)) + 1;
    }

    inline void SetReflectionProbsDirtyState(bool state) { mProbesDirty = state; };

    inline bool GetReflectionProbsDirtyState()
    {
        return mProbesDirty;
    }

    const ReflectionIndirectSetting* mIndirectSetting = nullptr;

protected:
    std::unordered_map<ecs::EntityID, std::array<PrimitiveData, 2>> mShowingReflecProbePrimitives;
    void DrawShowingReflectionProbes(FrameAllocator* curAllocator);
    MaterialR* mTransparentColorMaterial{ nullptr };
    MaterialR* mLineColorMaterial{ nullptr };
    bool mIsShowRPs{ true };
    bool mProbesDirty{ false };
    std::atomic<int> mBudgetProbeNum{0};
};

}
