#pragma once
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/REDImGui.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/VisualizerImpl.h"

namespace cross
{
class  RendererSystemR;
struct REDRegion;
struct RenderingExecutionDescriptor;

inline NameID gREDVisualizerViewTextureName = "REDVisualizerViewTexture";
inline std::string_view gREDVisualizerPassName = "REDVisualizer";

struct REDThumbnailInfo
{
    NGITexture* SourceTexture;
    UInt32 SourceTextureSubresource;
    NGITextureView* SourceTextureView;
    NGIResourceState SourceTextureState;
    NGITexture* Thumbnail;
    NGITextureView* ThumbnailView;
    NGIResourceState* ThumbnailBeforeFrameState;
    UInt8* ThumbnailData;
};

//Later Rename REDImGuiVisualizer, succeed to REDVisualizer
struct RENDER_ENGINE_API REDVisualizer : public REDImGui
{
    constexpr static UInt32 ThumbnailWidth = 128;
    inline static Float2 ThumbnailSize{ static_cast<float>(ThumbnailWidth), static_cast<float>(ThumbnailWidth) };
    constexpr static float ResourceNodeHeight = 70;
    constexpr static Float2 NodeSpacing{ 130, 10 };

    REDVisualizer(RendererSystemR* rendererSys);

    virtual ~REDVisualizer();

    void SetShader(resource::Shader* IMGUIDisplayShader, resource::Shader* blitColorShader, resource::Shader* blitDepthShader, resource::Shader* blitStencilShader)
    {
        mIMGUIDisplayShader = IMGUIDisplayShader;
        mBlitColorShader = blitColorShader;
        mBlitDepthShader = blitDepthShader;
        mBlitStencilShader = blitStencilShader;
    }

    void BlitThumbnails(NGICommandList* cmdList, const std::vector<REDThumbnailInfo*> infos);

    void BlitSelectResNode(NGICommandList* cmdList);

    void BeginFrame(UInt64 frameID)
    {
        mThumbnails.clear();
    };

    void ClearGraphNode() 
    {
        mVisEditor->ClearGraphNode();
    }

   void OnWindowClosed()
   {
       mVisEditor->OnWindowClosed();
   }

   void GenerateGUI() override
   {
       PrepareNode();
       mVisEditor->GenerateNodeGraph();
   }

   void MarkBakeThumbnailData(bool state) 
   { 
       mIsBakeThumbnailData = state;
       mVisImguiWS->MarkBakeThumbnailData(state);
   };

   std::unique_ptr<VisualizerImpl> mVisEditor;
   std::unique_ptr<VisualizerImpl> mVisImguiWS;
   SelectResInfo mSelectResInfo;
   void PrepareNode();

    bool mIsBakeThumbnailData = false;

    //Interactive with RED
    RenderingExecutionDescriptor* mRED;
    std::map<REDResourceRangeState*, REDThumbnailInfo> mThumbnails;

private:
    float mControlPaneWidth = 400.0f;
    float mNodeGraphWidth = 800.0f;

    // blit
    resource::Shader* mBlitColorShader;
    resource::Shader* mBlitDepthShader;
    resource::Shader* mBlitStencilShader;
    std::unique_ptr<NGIRenderPass> mBlitRenderPass;
};

}
