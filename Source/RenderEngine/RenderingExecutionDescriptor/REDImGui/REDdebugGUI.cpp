#include "EnginePrefix.h"
#include "REDdebugGUI.h"
#include <vector>


cross::REDdebugGUI::REDdebugGUI(RendererSystemR* rendererSys)
{
    mRendererSys = rendererSys;
    SetName(gREDdebugGUIViewTextureName, gREDdebugGUIPassName);

    InitializeGui();
}

cross::REDdebugGUI::~REDdebugGUI()
{
}

void cross::REDdebugGUI::GenerateGUI()
{
    ImGui::SetCurrentContext(mGuiContext);
    ImGui::NewFrame();

    auto& io = ImGui::GetIO();
    ImGui::SetNextWindowPos(ImVec2{ 0, 0 });//absolute pos
    ImGui::SetNextWindowSize(io.DisplaySize);

    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);

    ImGui::Begin(
        "REDDebugUI",
        nullptr,
        ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse | ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoBringToFrontOnFocus);

    if (mShowTexture)
    {
        ImGui::Image(mShowTexture->GetNGITextureView(), io.DisplaySize);
    }
    else
    {
        ImGui::BeginHorizontal("horizontal");
        ImGui::Spacing();
        ImGui::BeginVertical("vertical");
        ImGui::Spacing();
        ImGui::Text("No texture to show");
        ImGui::Spacing();
        ImGui::EndVertical();
        ImGui::Spacing();
        ImGui::EndHorizontal();
    }

    ImGui::End();
    ImGui::PopStyleVar(2);

    ImGui::Render();
}