#pragma once
#include "RenderPipeline/Effects/VolumetricFog.h"

namespace cross {

    struct RENDER_ENGINE_API FogComponentR : ecs::IComponent
    {
        CEFunction(Reflect) static ecs::ComponentDesc* GetDesc();

    protected:
        FogSetting mSetting;

        friend class FogSystemR;
        friend class PostProcessVolumeSystemR;
    };

    class RENDER_ENGINE_API FogSystemR : public RenderSystemBase
    {
        CEMetaInternal(Reflect) using FogHandle = ecs::ComponentHandle<FogComponentR>;
        using FogWriter = ecs::ScopedComponentWrite<FogComponentR>;

        FogSystemR() = default;

    public:

        static FogSystemR* CreateInstance();

        virtual void Release() override;

        void SetFogSettings(ecs::EntityID entity, const FogSetting& val);
    };
}
