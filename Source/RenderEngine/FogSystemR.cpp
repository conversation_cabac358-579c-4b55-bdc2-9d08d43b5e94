#include "FogSystemR.h"

namespace cross {
    ecs::ComponentDesc* FogComponentR::GetDesc()
    {
        return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::FogComponentR>(false);
    }

    FogSystemR* FogSystemR::CreateInstance()
    {
        return new FogSystemR();
    }

    void FogSystemR::Release()
    {
        delete this;
    }

    void FogSystemR::SetFogSettings(ecs::EntityID entity, const FogSetting& val)
    {
        auto comp = mRenderWorld->GetComponent<FogComponentR>(entity);
        comp.Write()->mSetting = val;
    }

}