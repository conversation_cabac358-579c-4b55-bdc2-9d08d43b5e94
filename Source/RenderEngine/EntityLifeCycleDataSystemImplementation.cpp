//#include "EnginePrefix.h"
//#include "EntityLifeCycleDataSystemImplementation.h"
//
//namespace cross
//{
//void EntityLifeCycleDataSystemImplementation::AddEntity(EntityLifeCycleData& data, EntityLifeCycleListType type, ecs::EntityID entity)
//{
//    auto& list = data.mEntityList[ToUnderlying(type)];
//    list.emplace_back(entity);
//    const UInt32 index = ToUInt32(list.size() - 1);
//    OnListChanged(data, type, index, index);
//    return;
//}
//
//void EntityLifeCycleDataSystemImplementation::LockList(EntityLifeCycleData& data, EntityLifeCycleListType type)
//{
//    if(data.mLocks[ToUnderlying(type)] == UINT32_MAX)
//    {
//        data.mLocks[ToUnderlying(type)] = ToUInt32(data.mEntityList[ToUnderlying(type)].size());
//    }
//}
//
//void EntityLifeCycleDataSystemImplementation::OnListChanged(EntityLifeCycleData& data, EntityLifeCycleListType type, UInt32 firstIndex, UInt32 lastIndex)
//{
//    CheckLocks(data);
//
//    EntityLifeCycleChangedEvent event;
//    event.mData.mType = type;
//    event.mData.mFirstIndex = firstIndex;
//    event.mData.mLastIndex = lastIndex;
//    DispatchImmediateEvent(event);
//}
//
//void EntityLifeCycleDataSystemImplementation::CheckLocks(EntityLifeCycleData& data) const
//{
//    // Check Locks
//    for (UInt8 i = 0; i < ToUnderlying(EntityLifeCycleListType::Count); ++i)
//    {
//        if (data.mLocks[i] != UINT32_MAX)
//        {
//            if(ToUInt32(data.mEntityList[i].size()) > data.mLocks[i])
//            {
//                Assert(false);
//                LOG_ERROR("Entity Life cycle list changed after it's locked. List Type: {}", static_cast<EntityLifeCycleListType>(i));
//            }
//        }
//    }
//}
//}
