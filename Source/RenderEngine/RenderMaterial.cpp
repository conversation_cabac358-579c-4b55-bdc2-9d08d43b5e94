#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/ResourceManager.h"
#include "RenderMaterial.h"
#include "Resource/Shader.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/WorldSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "RenderEngine/RenderMaterialVirtualTextureStack.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
using CrossSchema::ShaderStageBit;


namespace cross
{

    const Float4x4 DefaultMatrix = Float4x4::Identity();

    void FillNumberAsType(CrossSchema::ShaderVariableType type, void* dst, const std::vector<UInt8>& src)
    {
        switch (type)
        {
        case CrossSchema::ShaderVariableType::Int32:
        case CrossSchema::ShaderVariableType::UInt32:
        {
            const float* numbers = reinterpret_cast<const float*>(src.data());
            size_t count = src.size() / sizeof(float);
            for (int i = 0; i < count; i++)
            {
                reinterpret_cast<int*>(dst)[i] = static_cast<int>(numbers[i]);
            }
            break;
        }
        case CrossSchema::ShaderVariableType::Bool:
        case CrossSchema::ShaderVariableType::Float:
            memcpy(dst, src.data(), src.size());
            break;
        default:
            Assert(false);
            //Type not support
            break;
        }
    }

    void FillNumberAsType(CrossSchema::ShaderVariableType type, StagingBufferWrap bufferWrap, size_t bufferWrapOffset, const std::vector<UInt8>& src)
    {
        switch (type)
        {
        case CrossSchema::ShaderVariableType::Int32:
        case CrossSchema::ShaderVariableType::UInt32:
        {
            const float* numbers = reinterpret_cast<const float*>(src.data());
            size_t count = src.size() / sizeof(float);
            for (int i = 0; i < count; i++)
            {
                int v = static_cast<int>(numbers[i]);
                bufferWrap.MemWrite(bufferWrapOffset + sizeof(int) * i, &v, sizeof(int));
            }
            break;
        }
        case CrossSchema::ShaderVariableType::Bool:
        case CrossSchema::ShaderVariableType::Float:
            bufferWrap.MemWrite(bufferWrapOffset, src.data(), src.size());
            break;
        default:
            Assert(false);
            // Type not support
            break;
        }
    }

    MaterialR::~MaterialR()
    {
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        rdrSys->AddMaterialCacheByteSize(-static_cast<int>(mVariantByteSize));

        if (mConstantBuffer)
        {
            Assert(mConstantBufferAllocation.IsValid() && mConstantBufferAllocationIndex >= 0);
            auto allocator = rdrSys->GetConstantBufferAllocator();
            allocator->Deallocate(mConstantBufferAllocation, mConstantBufferAllocationIndex);
        }
    }

    UInt64 MaterialR::FillBuffer(resource::ShaderBufferLayout const* layout, UInt8* buffer)
    {
        for (const auto& v : layout->Members)
        {
            uint32_t len = v.Size;
            uint32_t offset = v.Offset;
            const auto& iter = mValueProps.find(v.Name);
            if (iter != mValueProps.end())
            {
                Assert(iter->second.size() == len);//Value size not match
                Assert(offset + len <= layout->ByteSize);
                //memcpy(buffer + offset, iter->second.data(), iter->second.size() * sizeof(float));
                FillNumberAsType(v.Type, buffer + offset, iter->second);
            }
            else
            {
                //default value should come with fx, Now we just use zero 
                std::memset(buffer + offset, 0, v.Size);
            }
        }
        return reinterpret_cast<UInt64>(buffer); //Hash is not used here
    }

    void MaterialR::FillBuffer(resource::ShaderBufferLayout const* layout, StagingBufferWrap bufferWrap, SizeType bufferWrapOffset)
    {
        for (const auto& v : layout->Members)
        {
            uint32_t len = v.Size;
            uint32_t offset = v.Offset;
            Assert(offset + bufferWrapOffset + len <= bufferWrap.GetMapSize());
            const auto& iter = mValueProps.find(v.Name);
            if (iter != mValueProps.end())
            {
                Assert(iter->second.size() == len);   // Value size not match
                Assert(offset + len <= layout->ByteSize);
                // memcpy(buffer + offset, iter->second.data(), iter->second.size() * sizeof(float));
                FillNumberAsType(v.Type, bufferWrap, offset + bufferWrapOffset, iter->second);
            }
            else
            {
                // default value should come with fx, Now we just use zero
                Assert(offset + len <= layout->ByteSize);
                bufferWrap.MemSet(offset + bufferWrapOffset, 0, v.Size);
            }
        }
        //return reinterpret_cast<UInt64>(buffer);   // Hash is not used here
    }

    void MaterialR::CreateVTStack()
    {
        mVTStack.reset(new RenderMaterialVirtualTextureStack());
        mVTStack->AllocateVirtualTextureStack(this);
    }
    bool EndsWith(const std::string str1, std::string str2)
    {
        return str2.length() > 0 && str1.length() >= str2.length() && !strncmp(str1.c_str() + (str1.length() - str2.length()), str2.c_str(), str2.length());
    }
    NGIResourceGroup* MaterialR::GetResourceBinding(resource::Shader::ProgramDesc* program)
    {
        using namespace CrossSchema;

        auto& mtlLayout = program->ResourceGroupLayouts[ShaderParamGroup_Material];

        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* frameMemPool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        auto* cbAllocator = rdrSys->GetScratchBuffer();
        auto* groupPool = rdrSys->GetResourceGroupPool();

        FrameStdVector<NGIResourceBinding> bindings(frameMemPool);
        bindings.reserve(mtlLayout.ResourceLayouts.size() + mtlLayout.ConstantBufferLayouts.size());


        bool VTEnabled = false;
        cross::IAllocatedVirtualTexture* AllocatedVT = nullptr;
        if (mVTStack != nullptr)
        {
            VTEnabled = mVTStack->FoundValidLayer();
            AllocatedVT = mVTStack->GetAllocatedVT();
        }

        // Update Resource Binding
        for (auto& res : mtlLayout.ResourceLayouts)
        {
            switch (res.Type)
            {
            case ShaderResourceType::Texture1D:
            case ShaderResourceType::Texture1DArray:
            case ShaderResourceType::Texture2D:
            case ShaderResourceType::Texture2DArray:
            case ShaderResourceType::Texture2DMS:
            case ShaderResourceType::Texture2DMSArray:
            case ShaderResourceType::Texture3D:
            case ShaderResourceType::TextureCube:
            case ShaderResourceType::TextureCubeArray:
                // VT Begin
                if (VTEnabled && EndsWith(res.ID.GetName(), "VTPageTable"))
                {
                    /// Warning: This line of code require the texture container must be sequential container!!!!!!
                    for (auto& tex : mTextureProps)
                    {
                        if (tex.second->IsVTTexture())
                        {
                            UInt8 pageTableIndex = 0;   // ue support 2pagetable in one material, it can use 8 virtualtexture.
                            auto* pageTableTexture = AllocatedVT->GetPageTableTextureSRV(pageTableIndex);
                            bindings.emplace_back(NGIResourceBinding::BindTexture(res.ID, pageTableTexture));
                            break;
                        }
                    }
                }
                else if (auto iter = mTextureProps.find(res.ID); iter != mTextureProps.end())
                {
                    if (iter->second->IsVTTexture())
                    {
                        if (AllocatedVT && VTEnabled)
                        {
                            // update vt physical texture to shader resource
                            UInt8 vtLayerIndex = AllocatedVT->GetLayerIndexByName(VTProducerHandle(iter->second->GetVTProducerHandle()));

                            auto* physicalTexture = AllocatedVT->GetPhysicalTextureSRV(vtLayerIndex);
                            bindings.emplace_back(physicalTexture ? NGIResourceBinding::BindTexture(res.ID, physicalTexture) : rdrSys->GetRenderPrimitives()->GetDefaultBinding(res, 0));
                        }
                        else
                        {
                            // its vt, but not show in virtual texture material
                            // for now, its thumbnail
                            bindings.emplace_back(iter->second->GetNGITextureView() ? NGIResourceBinding::BindTexture(res.ID, iter->second->GetNGITextureView()) : rdrSys->GetRenderPrimitives()->GetDefaultBinding(res, 0));
                        }
                    }
                    // VT End
                    else
                    {
                        Assert(iter->second->GetNGITextureView());
                        bindings.emplace_back(NGIResourceBinding::BindTexture(res.ID, iter->second->GetNGITextureView()));
                    }
                }
                else
                {
                    bindings.emplace_back(rdrSys->GetRenderPrimitives()->GetDefaultBinding(res, 0));
                }
                break;
                
            case ShaderResourceType::Sampler:
                if (auto ret = mSamplerStates.find(res.ID); ret != mSamplerStates.end())
                {
                    bindings.emplace_back(NGIResourceBinding::BindSampler(res.ID, ret->second));
                }
                else
                {
                    bindings.emplace_back(rdrSys->GetRenderPrimitives()->GetDefaultBinding(res, 0));
                }
                break;
            default:
                bindings.emplace_back(rdrSys->GetRenderPrimitives()->GetDefaultBinding(res, 0));
                break;
            }
        }

        if (mtlLayout.ConstantBufferLayouts.size() != 0)
        {
            Assert(mVariantConstBufferCache.find(program->MaterialProtoTypeID) != mVariantConstBufferCache.end());
            bindings.emplace_back(mVariantConstBufferCache[program->MaterialProtoTypeID].resourceBinding);

            // For analyzing
            const auto& cbLayout = mtlLayout.ConstantBufferLayouts.front();
            rdrSys->AddMtlScratchByte(cbLayout.ByteSize);
        }

        std::sort(bindings.begin(), bindings.end());

        return groupPool->Allocate(program->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Material], (UInt32)bindings.size(), bindings.data());
    }

    const void* MaterialR::GetShaderConst(resource::Shader::ProgramDesc* program)
    {
        if (auto& layout = program->ShaderConstantLayout; layout)
        {
            auto data = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator()->Allocate<UInt8>(layout->ByteSize, FrameStage::FRAME_STAGE_RENDER);
            FillBuffer(&*layout, data);
            return data;
        }
        else
        {
            return nullptr;
        }
    }

    const NGIDepthStencilStateDesc& MaterialR::GetDepthStencilState(NameID const& passID) const
    {
        auto pID = GetValidPassID(passID);
        Assert(mPassDataBlockMap.find(pID) != mPassDataBlockMap.end());
        return mPassDataBlockMap.at(pID).mDepthStencilState;
    }

    const NGIRasterizationStateDesc& MaterialR::GetRasterizationState(NameID const& passID) const
    {
        auto pID = GetValidPassID(passID);
        Assert(mPassDataBlockMap.find(pID) != mPassDataBlockMap.end());
        return mPassDataBlockMap.at(pID).mRasterizationState;
    }

    const NGIBlendStateDesc& MaterialR::GetBlendState(NameID const& passID) const
    {
        auto pID = GetValidPassID(passID);
        Assert(mPassDataBlockMap.find(pID) != mPassDataBlockMap.end());
        return mPassDataBlockMap.at(pID).mBlendState;
    }

    MaterialR::MaterialRenderState MaterialR::GetMaterialRenderState(NameID const & passID, const PropertySet* objCtx, const PropertySet* passCtx)
    {
        auto pID = GetValidPassID(passID);
        Assert(mPassDataBlockMap.find(pID) != mPassDataBlockMap.end());
        auto& passBlock = mPassDataBlockMap.at(pID);

        auto& shader = mFxPtr.lock()->GetPass(pID).mShaderPtr;
        resource::ShaderVariationKey tempKey(shader.get());
        FillShaderKey(tempKey);

        if (objCtx) 
        {
            objCtx->FillShaderKey(tempKey);
        }

        if (passCtx)
        {
            passCtx->FillShaderKey(tempKey);
        }

        if (std::ranges::none_of(magic_enum::enum_entries<MaterialUsage>(), [&](auto& entry) { return tempKey.IsMacroEnable(std::get<1>(entry)); }))
        {
            tempKey.SetMacro(magic_enum::enum_name<MaterialUsage::USED_WITH_DEFAULT>(), true);
        }

        auto program = shader->GetProgramDesc(tempKey);
        MaterialRenderState renderState
        {
            .mProgram = program,
            .mRenderGroup = passBlock.mRenderGroup,
            .mShaderConstants = GetShaderConst(program),
            .mDepthStencilState = &passBlock.mDepthStencilState,
            .mRaterizationState = &passBlock.mRasterizationState,
            .mBlendState = &passBlock.mBlendState,
            .mDynamicState = passBlock.mDynamicState,
            .mObjCtx = objCtx,
            .mMtlResourceGroup = GetResourceBinding(program),
        };
        return renderState;
    }

    std::vector<NameID> MaterialR::GetPasses() const
    {
        std::vector<NameID> passes;
        for (const auto& passData : mPassDataBlockMap)
        {
            passes.push_back(passData.first);
        }

        return passes; // automatic move
    }

    UInt16 MaterialR::GetRenderGroup(NameID const & passID) const
    {
        auto pID = GetValidPassID(passID);
        Assert(mPassDataBlockMap.find(pID) != mPassDataBlockMap.end());
        return mPassDataBlockMap.at(pID).mRenderGroup;
    }

    void MaterialR::SetEnable(NameID const& passID, bool val) 
    {
        auto pID = GetValidPassID(passID);
        if (mPassDataBlockMap.find(pID) != mPassDataBlockMap.end())
        {
            mPassDataBlockMap[pID].mEnable = val;
        }
        else
        {
            Assert(false);
        }
    }

    bool MaterialR::IsEnable(NameID const& passID) const 
    {
        auto pID = GetValidPassID(passID);
        Assert(mPassDataBlockMap.find(pID) != mPassDataBlockMap.end());
        return mPassDataBlockMap.at(pID).mEnable;
    }

    void MaterialR::SetRenderGroup(NameID const& passID, UInt16 val)
    {
        auto pID = GetValidPassID(passID);
        if (mPassDataBlockMap.find(pID) != mPassDataBlockMap.end())
        {
            mPassDataBlockMap[pID].mRenderGroup = val;
        }
        else
        {
            Assert(false);
        }
    }

    void MaterialR::UpdateCache()
    {
        std::lock_guard<std::mutex> lock(mMtlCacheMutex);
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* cbAllocator = rdrSys->GetScratchBuffer();
        auto* frameMemPool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        if (!mConstantBuffer)
            return;
        auto totalSize = mConstantBufferAllocation.mSize;
        if (totalSize == 0)
            return;
        auto constBufferWrap = cbAllocator->AllocateStaging(NGIBufferUsage::CopySrc, totalSize);
        
        for (auto& [protoType, cacheData] : mVariantConstBufferCache)
        {
            Assert(protoType != GPUProtoType::InvalidID);
            auto offset = cacheData.resourceBinding.ConstBufferView.Offset - mConstantBufferAllocation.mOffset;
            auto& layout = cacheData.layout;
            Assert(layout.ByteSize == cacheData.resourceBinding.ConstBufferView.Range);
            FillBuffer(&layout, constBufferWrap, offset);
            if (mVTStack && mVTStack->FoundValidLayer())
            {
                PropertySet VTProps(frameMemPool);
                mCachedVtParameters.FillPropertiesSet(VTProps);
                VTProps.FillBuffer(layout, constBufferWrap, offset);
            }
        }

        rdrSys->UpdateBuffer(mConstantBuffer, constBufferWrap.GetNGIBuffer(), NGICopyBuffer{constBufferWrap.GetNGIOffset(), mConstantBufferAllocation.mOffset, totalSize}, NGIResourceState::Undefined, NGIResourceState::ConstantBufferBit);

        bMtlCached = true;
    }

    resource::Fx* MaterialR::GetFx() const
    {
        auto fx = mFxPtr.lock();
        if (fx)
        {
            return fx.get();
        }
        else
        {
            return TypeCast<cross::resource::Fx>(resource::Fx::GetDefault()).get();
        }
    }

    void MaterialR::Initialize(FxPtr fxPtr)
    {
        Assert(fxPtr);
        mFxPtr = std::move(fxPtr);

        mValueProps.clear();
        mTextureProps.clear();
        mSamplerStates.clear();
        mPassDataBlockMap.clear();
        for (auto const& [id, pass] : mFxPtr.lock()->GetAllPass())
        {
            PassDataBlock blk;
            mPassDataBlockMap.emplace(id, blk);
        }

        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        UInt32 byteSize = 0;
        std::lock_guard<std::mutex> lock(mMtlCacheMutex);

        mVariantConstBufferCache.clear();

#if NGI_SUPPORTS_VULKAN
        VulkanDevice* device = dynamic_cast<VulkanDevice*>(&GetNGIDevice());
        Assert(device);
        const UInt32 alignment = VulkanBuffer::CalculateBufferAlignment(device, NGIBufferUsage::ConstantBuffer | NGIBufferUsage::CopyDst, false);
#else
        const UInt32 alignment = 1;
#endif

        for (auto const& [id, pass] : mFxPtr.lock()->GetAllPass())
        {
            const auto& varientMap = pass.mShaderPtr->GetVariantLayouts();
            for (const auto& [protoTypeId, layout] : varientMap)
            {
                if (mVariantConstBufferCache.count(protoTypeId) == 0)
                {
                    ConstBufferData cbData;
                    UInt32 alignedSize = AlignUp(layout.ByteSize, alignment);
                    cbData.resourceBinding = NGIResourceBinding::BindConstBuffer(layout.ID, nullptr, byteSize, layout.ByteSize);
                    cbData.layout = layout;
                    mVariantConstBufferCache[protoTypeId] = std::move(cbData);
                    byteSize += alignedSize;
                }
            }
        }

        auto allocator = rdrSys->GetConstantBufferAllocator();
        if (byteSize)
        {
            if (mConstantBufferAllocation.mSize < byteSize)
            {
                auto [allocation, index] = allocator->Allocate(byteSize);
                mConstantBufferAllocation = allocation;
                mConstantBufferAllocationIndex = index;
                mConstantBuffer = allocator->GetBuffer(index);
            }

            Assert(mConstantBuffer);
            for (auto& [_, cacheData] : mVariantConstBufferCache)
            {
                cacheData.buffer = mConstantBuffer;
                auto& view = cacheData.resourceBinding.ConstBufferView;
                view.ConstBuffer = mConstantBuffer;
                view.Offset += mConstantBufferAllocation.mOffset;
            }
        }
        else if (mConstantBuffer)
        {
            Assert(mConstantBufferAllocationIndex >= 0);
            allocator->Deallocate(mConstantBufferAllocation, mConstantBufferAllocationIndex);
            mConstantBufferAllocation = {nullptr, 0, 0, 0};
            mConstantBufferAllocationIndex = -1;
            mConstantBuffer = nullptr;
        }

        rdrSys->MarkMaterialCacheDirty(this);
        bMtlCached = false;
        rdrSys->AddMaterialCacheByteSize(static_cast<int>(byteSize) - static_cast<int>(mVariantByteSize));

        mVariantByteSize = byteSize;

    }

    void MaterialR::NotifyChange()
    {
        auto* worldSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<WorldSystemR>();
        for (auto beg = worldSystem->BeginWorld(); beg != worldSystem->EndWorld(); beg++)
        {
            if ((*beg)->IsPendingToDestroy())
                continue;
            auto renderNodeSys = (*beg)->GetRenderSystem<RenderNodeSystemR>();
            renderNodeSys->NotifyMaterialChange(this);
        }
    }

    void MaterialR::FillShaderKey(resource::ShaderVariationKey& key)
    {
        for (UInt32 i = 0; i < key.GetMacroCount(); i++)
        {
            auto name = key.GetMacroName(i);
            if (auto enable = GetBool(name.data()); enable)
                key.SetMacro(name.data(), *enable);
        }
    }

    UInt8 MaterialR::SetValue(NameID const& name, const UInt8* val, size_t len)
    {
        Assert(len);
        
        if (GetFx() && val)
        {
            auto flag = GetFx()->GetPropertyFlag(name);

            if (flag & resource::Fx::Shader_Macro)
            {
                auto iter = mValueProps.find(name);
                if (iter != mValueProps.end())
                {
                    Assert(len == 4);
                    Assert(iter->second.size() == 4);

                    memcpy(iter->second.data(), val, len);
                }
                else
                {
                    mValueProps.emplace(name, std::vector<UInt8>(len));
                    memcpy(mValueProps[name].data(), val, len);
                }
            }

            if ((flag & (1 << ShaderParamGroup_Material)) || (flag & resource::Fx::Shader_SpMember))
            {
                std::lock_guard<std::mutex> lock(mMtlCacheMutex);
                bMtlCached = false;
                auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                rdrSys->MarkMaterialCacheDirty(this);

                auto iter = mValueProps.find(name);
                if (iter != mValueProps.end() && iter->second.size() == len && !memcmp(iter->second.data(), val, len))
                {
                    mPropertyVersion++;
                    return 0;
                }
                else if (iter == mValueProps.end())
                {
                    mValueProps.emplace(name, std::vector<UInt8>(len));
                    memcpy(mValueProps[name].data(), val, len);
                }
                else
                {
                    if (iter->second.size() != len)
                        iter->second.resize(len);
                    memcpy(iter->second.data(), val, len);
                }
                mPropertyVersion++;
            }
            return flag;
        }
        return 0;
    }

    UInt8 MaterialR::SetValueProp(NameID const & name, const float * val, size_t len)
    {
        auto res = SetValue(name, reinterpret_cast<const UInt8*>(val), len * sizeof(float));
        return res;
    }

    UInt8 MaterialR::SetValueProp(NameID const& name, const UInt8* val, size_t len)
    {
        auto res = SetValue(name, val, len);
        return res;
    }

    UInt8 MaterialR::SetBool(NameID const & name, bool val)
    {
        UInt32 shaderVal = val ? 0xffffffff : 0;
        auto res = SetValue(name, reinterpret_cast<const UInt8*>(&shaderVal), sizeof(UInt32));
        return res;
    }

    std::optional<bool> MaterialR::GetBool(NameID const & name) const
    {
        auto iter = mValueProps.find(name);
        if (iter != mValueProps.end() && iter->second.size() != 0)
            return iter->second[0] != 0;

        return {};
    }

    std::optional<Float4> MaterialR::GetVector(NameID const & name) const
    {
        if (const auto iter = mValueProps.find(name); iter != mValueProps.end() && iter->second.size() == sizeof(Float4))
        {
            return *reinterpret_cast<const Float4*>(iter->second.data());
        }
        else
        {
            return {};
        }
    }

    std::optional<float> MaterialR::GetFloat(NameID const& name) const
    {
        if (const auto iter = mValueProps.find(name); iter != mValueProps.end() && iter->second.size() == sizeof(float))
        {
            return *reinterpret_cast<const float*>(iter->second.data());
        }
        else
        {
            return {};
        }
    }

    bool MaterialR::GetPropertyRawData(NameID const& name, std::size_t size, void* data) const
    {
        // too lazy to implement a non-copy version
        if (const auto iter = mValueProps.find(name); iter != mValueProps.end() && iter->second.size() == size)
        {
            std::memcpy(data, iter->second.data(), size);
            return true;
        }

        return false;
    }

    GPUTexture * MaterialR::GetTexture(NameID const & name)
    {
        const auto iter = mTextureProps.find(name);
        if (iter == mTextureProps.end())
            return nullptr;

        return iter->second;
    }

    void MaterialR::SetTexture(NameID const& inName, cross::IGPUTexture* inIGpuTex)
    {
        GPUTexture* gpuTexture = TYPE_CAST(GPUTexture*, inIGpuTex);
        //Assert(gpuTexture->GetNGITextureView());
        SetTexture(inName, gpuTexture);
    }

    void MaterialR::SetTexture(NameID const & name, GPUTexture* texture)
    {
        auto flag = GetFx()->GetPropertyFlag(name);

        if(flag & (1 << ShaderParamGroup_Material))
        {
            if (texture)
                mTextureProps[name] = texture;
            else if (mTextureProps.find(name) != mTextureProps.end())
                mTextureProps.erase(name);
            mPropertyVersion++;
        }
    }

    NGITextureAddressMode MapAddressMode(TextureAddressMode addressMode)
    {
        switch (addressMode)
        {
            default:
            case TextureAddressMode::Wrap:
                return NGITextureAddressMode::Wrap;
            case TextureAddressMode::Clamp:
                return NGITextureAddressMode::Clamp;
            case TextureAddressMode::Mirror:
                return NGITextureAddressMode::Mirror;
        }
    }

    NGIFilter MapFilter(TextureFilter filter)
    {
        switch (filter)
        {
        case TextureFilter::Nearest:
            return NGIFilter::MinMagMipPoint;
        case TextureFilter::Bilinear:
            return NGIFilter::MinMagLinearMipPoint;
        case TextureFilter::Trilinear:
            return NGIFilter::MinMagMipLinear;
        case TextureFilter::Anisotropic:
            return NGIFilter::Anisotropic;
        default:
            return NGIFilter::MinMagMipLinear;
        }
    }

    void MaterialR::SetSamplerState(NameID const& name, const SamplerState& samplerState)
    {
        auto flag = GetFx()->GetPropertyFlag(name);

        if (flag & (1 << ShaderParamGroup_Material))
        {
            auto filter = MapFilter(samplerState.Filter);
            switch (samplerState.MipValueMode)
            {
                case TextureMipValueMode::MipLevel:
                case TextureMipValueMode::MipBias:
                    if (filter == NGIFilter::Anisotropic)
                    {
                        filter = NGIFilter::MinMagMipLinear;
                    }
                break;
            }
            auto addressMode = MapAddressMode(samplerState.AddressMode);
            NGISamplerDesc desc
            {
                .Filter = filter,
                .WrapU = addressMode,
                .WrapV = addressMode,
                .WrapW = addressMode,
                .MaxAnisotropy = samplerState.AnisotropicLevel,
                .ComparisonOp = NGIComparisonOp::Unknown,
                .BorderColor = NGIBorderColor::FloatOpaqueBlack,
                .MaxLOD = 1000,
            };
            auto persistResMgr = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetPersistentResourceManager();
            mSamplerStates[name] = persistResMgr->AllocateSampler(desc);
            mPropertyVersion++;
        }
    }

    void MaterialR::SetDepthStencilState(NameID const& passID, const NGIDepthStencilStateDesc & depthStencilState)
    {
        auto pID = (passID == 0 ? mFxPtr.lock()->GetDefaultPass().name : passID);
        auto& block = mPassDataBlockMap[pID];
        block.mDepthStencilState = depthStencilState;
    }

    void MaterialR::SetRasterizationState(NameID const& passID, const NGIRasterizationStateDesc & rasterizerState)
    {
        auto pID = (passID == 0 ? mFxPtr.lock()->GetDefaultPass().name : passID);
        auto& block = mPassDataBlockMap[pID];
        block.mRasterizationState = rasterizerState;
    }

    void MaterialR::SetBlendState(NameID const& passID, const NGIBlendStateDesc & blendDesc)
    {
        auto pID = (passID == 0 ? mFxPtr.lock()->GetDefaultPass().name : passID);
        auto& block = mPassDataBlockMap[pID];
        block.mBlendState = blendDesc;
    }

    void MaterialR::SetDynamicState(NameID const& passID, const NGIDynamicStateDesc& desc)
    {
        auto pID = (passID == 0 ? mFxPtr.lock()->GetDefaultPass().name : passID);
        auto& block = mPassDataBlockMap[pID];
        block.mDynamicState = desc;
    }

    void MaterialR::SetVtParameters(cross::IAllocatedVirtualTexture* allocatedVT, const std::vector<UInt8>& vtLayerIndices)
    {
        std::lock_guard<std::mutex> lock(mMtlCacheMutex);
        if (allocatedVT)
        {
            mCachedVtParameters.Init(allocatedVT);

            for (const auto v : vtLayerIndices)
            {
                mCachedVtParameters.FillParameters(allocatedVT, v);
            }
        }
        bMtlCached = false;
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        rdrSys->MarkMaterialCacheDirty(this);
    }

    void MaterialR::VTConstParameters::Init(cross::IAllocatedVirtualTexture* allocatedVT) 
    {
        if (allocatedVT)
        {
            UInt4 PTU[3];
            allocatedVT->GetPackedPageTableUniform(PTU);
            PageTableUniform0 = {static_cast<float>(PTU[0].x), static_cast<float>(PTU[0].y), static_cast<float>(PTU[0].z), static_cast<float>(PTU[0].w)};
            PageTableUniform1 = {static_cast<float>(PTU[1].x), static_cast<float>(PTU[1].y), static_cast<float>(PTU[1].z), static_cast<float>(PTU[1].w)};
            PageTableUniform2 = {BitcastUInt32ToFloat(PTU[2].x), BitcastUInt32ToFloat(PTU[2].y), static_cast<float>(PTU[2].z), static_cast<float>(PTU[2].w)};
        }
    }

    void MaterialR::VTConstParameters::FillParameters(cross::IAllocatedVirtualTexture* allocatedVT, UInt8 vtLayerIndex) 
    {
        // property
        allocatedVT->GetPackedUniform(&PackedVTUniform, vtLayerIndex);
        LayerRemap.data()[vtLayerIndex] = static_cast<float>(allocatedVT->GetPageTableLayerIndex(vtLayerIndex));
    }

    void MaterialR::VTConstParameters::FillPropertiesSet(PropertySet& VTProps)
    {
        Float4 PackedUniform{static_cast<float>(PackedVTUniform.x), static_cast<float>(PackedVTUniform.y), static_cast<float>(PackedVTUniform.z), static_cast<float>(PackedVTUniform.w)};
        VTProps.SetProperty(NAME_ID("_VTPackedUniform"), PackedUniform);
        // why use int4 is wrong? bugs exists
        VTProps.SetProperty(NAME_ID("_VTLayerRemap"), LayerRemap);
        VTProps.SetProperty(NAME_ID("_TextureEnableVT"), UINT32_MAX);
        VTProps.SetProperty(NAME_ID("_VTPageTableUniform0"), PageTableUniform0);
        VTProps.SetProperty(NAME_ID("_VTPageTableUniform1"), PageTableUniform1);
        VTProps.SetProperty(NAME_ID("_VTPageTableUniform2"), PageTableUniform2);
    }
}   // namespace cross
