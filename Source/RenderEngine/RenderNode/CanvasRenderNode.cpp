#include "CanvasRenderNode.h"

namespace cross {

void CanvasRenderNode::GenerateDrawUnits(const cross::RenderNode::GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    for (UInt32 i = 0; i < mItemBatches.size(); ++i)
    {
        auto& batch = mItemBatches[i];

        MaterialR* material;
        UInt16 renderGroup;
        if (params.IsDrawable(batch.mMaterial, material, renderGroup))
        {
            collector.AddCustumPriorityIsolatedDrawUnit(mLayer, renderGroup, &mRenderMesh->GetRenderGeometry(i), material);
        }
    }
}

}   // namespace cross