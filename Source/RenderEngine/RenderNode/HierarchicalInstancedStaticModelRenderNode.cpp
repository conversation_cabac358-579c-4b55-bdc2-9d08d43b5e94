#include "EnginePrefix.h"
#include "HierarchicalInstancedStaticModelRenderNode.h"

namespace cross {

void HierarchicalInstancedStaticModelRenderNode::SetRenderModelInstancedImpl(RenderModel&& renderModel, InstanceDataResourcePtr instanceDataResource)
{
    InstancedStaticModelRenderNode::SetRenderModelInstancedImpl(std::move(renderModel), instanceDataResource);

    // Combine submodel BoundingBox
    mInstanceBoundingBox = BoundingBox(BoundingBox::Flags::MergeIdentity);
    for (auto& singleLODModel : mRenderModel.mLODModels)
    {
        for (auto& subModel : singleLODModel.mSubModels)
        {
            BoundingBox::CreateMerged(mInstanceBoundingBox, mInstanceBoundingBox, subModel.mBoundingBox);
        }
    }

    if (instanceDataResource->mClusterNodes.empty())
    {
        BuildTree();
    }
}

void HierarchicalInstancedStaticModelRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    SCOPED_CPU_TIMING(GroupRendering ,"HISM::GenerateDrawUnits");

    if (mInstanceCount == 0)
    {
        return;
    }

    HISMCullInstanceParam hismParam{};

    // We assume all SubModels have same LOD count
    const size_t lodCount = mRenderModel.mLODModels.size();

    // LOD distance planes
    {
        const float boundingSphereRadius = mInstanceBoundingBox.GetExtent().Length();
        auto& projMatrix = params.cameraLODSel->GetProjMatrix();

        hismParam.LODDistancePlanes.resize(lodCount);
        float finalCulling = mDistanceCulling.maxCullingDistance > 1e-5 ? mDistanceCulling.maxCullingDistance : FLT_MAX;
        for (size_t lodIndex = 1; lodIndex < lodCount; lodIndex++)
        {
            const float t = (std::max)(projMatrix.m00, projMatrix.m11) * 0.5f;

            // screenSize = t * boundingSphereRadius / max(1.0f, dist) * 2.0f
            const float lodScreenSize = mLodSetting->mLevelSettings[lodIndex-1].mScreenReleativeTransitionHeight;
            hismParam.LODDistancePlanes[lodIndex - 1] = MathUtils::Min(finalCulling, t * boundingSphereRadius * 2.0f / lodScreenSize);
        }
        hismParam.LODDistancePlanes[lodCount - 1] = finalCulling;
    }

    //set cluster node bounding box test to hism local coordinate
    BoundingFrustum frustum = params.camera->GetFrustum();
    BoundingOrientedBox orthBox = params.camera->GetOrthBox();
    Float4x4 world2hism = mWorldTransform.RelativeMatrix.Inverted();
    if (params.camera->GetProjectionMode() == CameraProjectionMode::Perspective)
    {
        frustum.Transform(frustum, params.camera->GetInvertViewMatrix());
        frustum.Transform(frustum, 1, Quaternion::Identity(), (params.camera->GetTilePosition() - mWorldTransform.TilePosition) * LENGTH_PER_TILE);
        frustum.Transform(frustum, world2hism);
        hismParam.cameraMode = CameraProjectionMode::Perspective;
        hismParam.bounding = frustum;
    }
    else
    {
        orthBox.Transform(orthBox, params.camera->GetInvertViewMatrix());
        orthBox.Transform(orthBox, (params.camera->GetTilePosition() - mWorldTransform.TilePosition) * LENGTH_PER_TILE);
        orthBox.Transform(orthBox, world2hism);
        hismParam.cameraMode = CameraProjectionMode::Orthogonal;
        hismParam.bounding = orthBox;
    }  
    hismParam.CameraRelPosition = Float4x4::TransformPointF3(world2hism,
                                                          GetLargeCoordinateReltvPosition(params.cameraLODSel->GetCameraOrigin(), params.cameraLODSel->GetTilePosition(), mWorldTransform.TilePosition));
    Traverse(hismParam, 0, 0, static_cast<SInt32>(lodCount), false);
    
    // for each render node in entity
    for (UInt8 lodIndex = 0; lodIndex < lodCount; lodIndex++)
    {
        for (auto& subModel : mRenderModel.mLODModels[lodIndex].mSubModels)
        {
            auto* geometry = subModel.mGeometry;
            if (!geometry)
            {
                continue;
            }

            auto* material = subModel.mMaterial;
            if (!material)
            {
                continue;
            }

            auto& drawUnitsDesc = params.drawUnitsDesc;
            const auto& passName = drawUnitsDesc.TagName;

            MaterialR* finalMaterial;
            UInt16 renderGroup;
            if (params.IsDrawable(material, finalMaterial, renderGroup))
            {
                Assert(subModel.GPUSceneAlloc.PrimitiveAlloc && subModel.GPUSceneAlloc.ObjectAlloc);

                auto& lodRuns = hismParam.LODRuns[lodIndex];
                for (UInt32 i = 0; i < lodRuns.size(); i += 2)
                {
                    UInt32 firstInstance = lodRuns[i];
                    UInt32 lastInstance = lodRuns[i + 1];
                    UInt32 instanceCount = lastInstance - firstInstance + 1;

                    REDDrawUnitFlag flags{};

                    if (mRenderModel.mReceiveDecals)
                    {
                        flags |= REDDrawUnitFlag::ReceiveDecal;
                    }

                    if (mNeedReverseCullingFace)
                    {
                        flags |= REDDrawUnitFlag::ReverseFaceOrder;
                    }

                    UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, mObjectProperties, passName, flags, lodIndex);

                    if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
                    {
                        collector.AddOpaqueBatchableDrawUnit(renderGroup,
                                                             geometry,
                                                             finalMaterial,
                                                             stateBucketID,
                                                             instanceCount,
                                                             subModel.mObjectCullingGUIDStart + firstInstance,
                                                             subModel.GPUSceneAlloc.ObjectAlloc->IndexStart + firstInstance,
                                                             subModel.GPUSceneAlloc.ObjectAlloc->BufferView,
                                                             subModel.GPUSceneAlloc.PrimitiveAlloc->BufferView,
                                                             flags,
                                                             this);
                    }
                    else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI - 1u)) || math::InBetween(gRenderGroupGizmoWithSceneDepth, renderGroup, static_cast<UInt16>(gRenderGroupOverlay - 1u)))
                    {
                        // Emm..
                        assert(false);
                    }
                }
            }
        }
    }
}

void HierarchicalInstancedStaticModelRenderNode::BuildTree()
{
    if (mInstanceCount > 0)
    {
        ClusterBuilder builder(mInstanceDataResource, mInstanceBoundingBox);
        builder.BuildTreeAndResource(mGlobalScale);
    }

}

void HierarchicalInstancedStaticModelRenderNode::Traverse(HISMCullInstanceParam& param, SInt32 index, SInt32 minLOD, SInt32 maxLOD, bool isFullyContain) const
{
    const ClusterNode& node = mInstanceDataResource->mClusterNodes[index];

    const Float3 boundCenter = (node.BoundMin + node.BoundMax) * 0.5f;
    const Float3 boundExtent = (node.BoundMax - node.BoundMin) * 0.5f;
    const BoundingBox bound(boundCenter, boundExtent);

    if (!isFullyContain)
    {
        if (ContainmentType type = 
            (param.cameraMode == CameraProjectionMode::Perspective ? 
                std::get<BoundingFrustum>(param.bounding).Contains(bound) : 
                std::get<BoundingOrientedBox>(param.bounding).Contains(bound))
            )
        {
            isFullyContain = type == ContainmentType::ContainmentContain;
        }
        else
        {
            return;
        }
    }

    if (minLOD != maxLOD)
    {
        const Float3 center = bound.GetCenter();
        const float distance = (center - param.CameraRelPosition).Length();
        const float radius = bound.GetExtent().Length();
        const float nearDist = distance - radius;
        const float farDist = distance + radius;

        while (minLOD < maxLOD && nearDist > param.LODDistancePlanes[minLOD])
        {
            minLOD++;
        }

        while (minLOD < maxLOD && farDist < param.LODDistancePlanes[maxLOD - 1])
        {
            maxLOD--;
        }
    }
    //a cpu culling based on cluster node;
    if (minLOD >= param.LODDistancePlanes.size())
    {
        return;
    }
    const bool isShouldGroup = node.FirstChild < 0 || ((node.LastInstance - node.FirstInstance + 1) < 2);
    const bool isSplit = (!isFullyContain || minLOD < maxLOD) && (!isShouldGroup);

    if (!isSplit)
    {
        param.AddRun(minLOD, static_cast<UInt32>(node.FirstInstance), static_cast<UInt32>(node.LastInstance));
        return;
    }

    assert(node.FirstChild >= 0);
    assert(node.LastChild >= 0);

    if (node.FirstChild >= 0)
    {
        for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
        {
            Traverse(param, childIndex, minLOD, maxLOD, isFullyContain);
        }
    }
}
}   // namespace cross