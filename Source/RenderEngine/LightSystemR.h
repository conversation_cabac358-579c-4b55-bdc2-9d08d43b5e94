#pragma once

#include "TransformSystemR.h"
#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "CECommon/Common/LightCommon.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/PrimitiveGenerator.h"

namespace cross {
    struct RenderingExecutionDescriptor;

    struct LightDataForGpu
    {
        ecs::EntityID id;
        bool enable;
        LightType type;
        UInt32 mask;
        LightPriority priority;
        Float3 color;

        float intensity;
        float volumetricFactor;
        float range;
        float outerHorCosHalfAngle;
        float outerVerCosHalfAngle;
    };

    struct LightTransform
    {
        Float3 translate;
#if defined(CE_USE_DOUBLE_TRANSFORM)
        Float3 tilePos;
#endif
        Quaternion rotation;
    };

    struct LightRenderData
    {
    protected:
        LightType mType{LightType::Directional};
        Float3 mColor;
        float mIntensity{1.0f};
        float mPrtIntensity{1.0f};
        float mSpecularIntensity{1.0f};
        float mVolumetricFactor{1.0f};
        float mSourceAngle{0.5357f};

        // Point/Spot/Rect lights
        float mRange{500.0f};

        // Spot light only
        float mInnerCosAngle = 0.5f;   // cos(120/2)
        float mOuterCosAngle = 0.5f;   // cos(120/2)
        float mConeFadeIntensity = 1.0f;
        float mConeOverFlowLength = 0.0f;
        float mSpotDistanceExp = 1.0f;

        // Rect light only
        float mSourceWidth{64.f};
        float mSourceHeight{64.f};
        float mBarnDoorAngle{88.f};
        float mBarnDoorLength{20.f};

        bool mCastShadow{false};              // Realtime shadow
        bool mCastScreenSpaceShadow{false};   // Screen space shadow
        float mShadowStrength{1.0f};          // Realtime shadow
        int mShadowDataIndex{-1};
        int mDepthPyramidIndex{-1};

        LightMode mMode{LightMode::Realtime};

        LightPriority mPriority{LightPriority::Major};
        LightShadowType mShadowType{LightShadowType::SoftShadowPCF_3X3};
        float mShadowAmount{1.0};
        float mShadowBias{0.6f};
        float mShadowSlopeBias{0.6f};
        float mVarianceBiasVSM{0.01f};
        float mLightLeakBiasVSM{0.01f};
        float mFilterSizePCF{2.f};
        float mSoftnessPCSS{0.005f};
        float mSampleCountPCSS{32.f};
        bool mEnable{false};
        bool mDirty_LightInstance{true};
        bool mDirty_ShadowMap{true};
        AtmosphereLightConfig mAtmosphereLightConfig;
        UInt32 mRenderingLayerMask{ static_cast<UInt32>(RenderingLayer::Default) };
        bool mEnableTransmittance{true};

        friend class LightSystemR;
        friend struct IRenderPipeline;
    };

    struct LightComponentR : ecs::IComponent
    {
        CEFunction(Reflect)
        RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

        protected:
        std::shared_ptr<LightRenderData> mLightData = std::make_shared<LightRenderData>();

        friend class LightSystemR;
    };

    class LightSystemR : public RenderSystemBase
    {
        CEMetaInternal(Reflect) 
    public: 
        using RenderLightComponentHandle = ecs::ComponentHandle<LightComponentR>;
        using RenderLightComponentReader = ecs::ScopedComponentRead<LightComponentR>;
        using RenderLightComponentWriter = ecs::ScopedComponentWrite<LightComponentR>;

        RENDER_ENGINE_API static LightSystemR* CreateInstance();

        virtual void Release() override;
        
        virtual void OnFirstUpdate(FrameParam* frameParam) override;

        virtual void OnBeginFrame(FrameParam* frameParam) override;

        virtual void OnEndFrame(FrameParam* frameParam) override;

        CEFunction(Reflect)
        virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

        virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    public:
        #define RENDER_LIGHT_COMPONENT_PROPERTY(PROP, TYPE) \
            inline void SetLight##PROP(ecs::EntityID entity, TYPE val)\
                {\
                    auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);\
                    {auto writer = lightHandle.Write();\
                    writer->mLightData->m##PROP = val;}\
                    SetLightDirty(entity);\
                    AddChangeData(entity);\
                }\
            inline TYPE GetLight##PROP(const RenderLightComponentReader& reader) const { return reader->mLightData->m##PROP; } \
            inline TYPE GetLight##PROP(const UInt32 lightIdx) const { if (lightIdx < mLightDataList.size()) return std::get<1>(mLightDataList[lightIdx])->m##PROP; else return TYPE(); }

        RENDER_LIGHT_COMPONENT_PROPERTY(Enable, bool);
        RENDER_LIGHT_COMPONENT_PROPERTY(Type, LightType);
        RENDER_LIGHT_COMPONENT_PROPERTY(Intensity, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(PrtIntensity, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(SpecularIntensity, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(VolumetricFactor, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(SourceAngle, float);

        // NOTE: Write Get Set methods for LightRange manually, Use Scale Tool to control light range
        // RENDER_LIGHT_COMPONENT_PROPERTY(Range, float);
        inline void SetLightRange(ecs::EntityID entity, float val)
        {
            auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);
            {
                auto writer = lightHandle.Write();
                writer->mLightData->mRange = val;
            }
            SetLightDirty(entity);
            AddChangeData(entity);
        }
        float GetLightRange(const RenderLightComponentReader& reader) const;
        
        inline float GetLightRange(const UInt32 lightIdx) const
        {
            auto lightComp = mRenderWorld->GetComponent<LightComponentR>(GetLightEntityID(lightIdx));
            return GetLightRange(lightComp.Read());
        }

        RENDER_LIGHT_COMPONENT_PROPERTY(InnerCosAngle, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(OuterCosAngle, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(ConeFadeIntensity,    float);
        RENDER_LIGHT_COMPONENT_PROPERTY(ConeOverFlowLength, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(SpotDistanceExp, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(ShadowStrength, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(Mode, LightMode);
        RENDER_LIGHT_COMPONENT_PROPERTY(Priority, LightPriority);
        RENDER_LIGHT_COMPONENT_PROPERTY(SourceWidth, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(SourceHeight, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(BarnDoorAngle, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(BarnDoorLength, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(ShadowType, LightShadowType);
        RENDER_LIGHT_COMPONENT_PROPERTY(ShadowAmount, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(ShadowBias, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(ShadowSlopeBias, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(VarianceBiasVSM, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(LightLeakBiasVSM, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(FilterSizePCF, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(SoftnessPCSS, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(SampleCountPCSS, float);
        RENDER_LIGHT_COMPONENT_PROPERTY(AtmosphereLightConfig, AtmosphereLightConfig);
        RENDER_LIGHT_COMPONENT_PROPERTY(RenderingLayerMask, UInt32);
        RENDER_LIGHT_COMPONENT_PROPERTY(EnableTransmittance, bool);

        inline void SetLightShadowDataIndex(const RenderLightComponentWriter& writer, int val)
        {
            writer->mLightData->mShadowDataIndex = val;
        }

        inline void SetLightShadowDataIndex(UInt32 lightIdx, int val)
        {
            if (lightIdx < mLightDataList.size())
                std::get<1>(mLightDataList[lightIdx])->mShadowDataIndex = val;
        }

        inline int GetLightShadowDataIndex(const RenderLightComponentReader& reader) const
        {
            return reader->mLightData->mShadowDataIndex;
        }

        inline int GetLightShadowDataIndex(UInt32 lightIdx) const
        {
            if (lightIdx < mLightDataList.size())
                return std::get<1>(mLightDataList[lightIdx])->mShadowDataIndex;
            else
                return -1;
        }

        inline ecs::EntityID GetLightEntityID(UInt32 lightIdx) const
        {
            if (lightIdx < mLightDataList.size())
                return std::get<0>(mLightDataList[lightIdx]);
            else
                return ecs::EntityID::InvalidHandle();
        }

        inline void SetLightDepthPyramidIndex(ecs::EntityID entity, int val)
        {
            auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);
            auto writer = lightHandle.Write();
            writer->mLightData->mDepthPyramidIndex = val;
        }
        inline int GetLightDepthPyramidIndex(const RenderLightComponentReader& reader) const
        {
            return reader->mLightData->mDepthPyramidIndex;
        }

        inline void SetLightDirty(ecs::EntityID entity)
        {
            auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);
            auto writer = lightHandle.Write();
            writer->mLightData->mDirty_LightInstance = true;
            writer->mLightData->mDirty_ShadowMap = true;
        }

        inline void SetLightUndirty_LightInstance(ecs::EntityID entity) {
            auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);
            auto writer = lightHandle.Write();
            writer->mLightData->mDirty_LightInstance = false;
        }

        inline void SetLightUndirty_ShadowMap(ecs::EntityID entity)
        {
            auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);
            auto writer = lightHandle.Write();
            writer->mLightData->mDirty_ShadowMap = false;
        }

        inline bool GetLightDirty_LightInstance(const RenderLightComponentReader& reader) const
        {
            return reader->mLightData->mDirty_LightInstance;
        }

        inline bool GetLightDirty_ShadowMap(const RenderLightComponentReader& reader) const
        {
            return reader->mLightData->mDirty_ShadowMap;
        }

        inline void AddChangeData(ecs::EntityID entity) 
        {
            mChangeList.EmplaceChangeData(entity);
        }

        inline const auto& GetChangeList() const
        {
            return mChangeList;
        }

        inline void SetLightColor(ecs::EntityID entity, const Float3& val)
        {
            auto lightHandle = mRenderWorld->GetComponent<LightComponentR>(entity);
            {
                auto writer = lightHandle.Write();
                writer->mLightData->mColor = val;
            }
            SetLightDirty(entity);
            AddChangeData(entity);
        }
        
        inline Float3 GetLightColor(const RenderLightComponentReader& reader) const
        {
            return reader->mLightData->mColor;
        }

        inline Float3 GetLightColor(UInt32 idx) const
        {
            if (idx >= mLightDataList.size())
                return Float3(); 
            else
            {
                auto lightData = std::get<1>(mLightDataList[idx]);
                return lightData->mColor;
            }
        }

        inline std::shared_ptr<LightRenderData> GetLightRenderData(UInt32 idx) const
        {
            if (idx> mLightDataList.size())
            {
                return nullptr;
            }

            return std::get<1>(mLightDataList[idx]);
        }

        inline bool GetEnableTransmittance(const RenderLightComponentReader& reader) const { return reader->mLightData->mEnableTransmittance; }

        inline bool GetEnableTransmittance(UInt32 idx) const
        {
            if (idx >= mLightDataList.size())
                return false;
            else
            {
                auto lightData = std::get<1>(mLightDataList[idx]);
                return lightData->mEnableTransmittance;
            }
        }

        inline Float3 GetLightOutSpaceColor(const RenderLightComponentReader& reader) const
        {
            return GetLightColor(reader) * GetLightIntensity(reader);
        }

        inline Float3 GetLightOutSpaceColor(UInt32 lightIdx) const
        {
            return GetLightColor(lightIdx) * GetLightIntensity(lightIdx);
        }

        Float3 GetLightTransmittance(const RenderLightComponentReader& reader) const;

        float GetSunOnEarthHalfApexAngleRadian(const RenderLightComponentReader& reader) const
        {
            const float SunOnEarthApexAngleDegree = std::max(reader->mLightData->mSourceAngle, 0.0f);   // Apex angle == angular diameter
            return 0.5f * SunOnEarthApexAngleDegree * MathUtils::MathPi / 180.0f;
        }

        void GetSunLuminanceAndIlluminance(ecs::EntityID inLightEntity, Float3& outSunLuminance, Float3& outSunIlluminance);

        inline bool IsLightEnableForRuntime(const RenderLightComponentReader& reader) const
        {
            return reader->mLightData->mEnable && (static_cast<UInt32>(reader->mLightData->mMode) & static_cast<UInt32>(LightMode::Realtime));
        }

        inline bool GetLightCastShadow(const RenderLightComponentReader& comp) const
        {
            return comp->mLightData->mCastShadow;
        }

        RENDER_ENGINE_API void SetLightCastShadow(ecs::EntityID entity, bool val);

        inline bool GetLightCastScreenSpaceShadow(const RenderLightComponentReader& comp) const
        {
            return comp->mLightData->mCastScreenSpaceShadow;
        }

        LightTransform GetLightTransform(UInt32 lightIdx) const
        {
            if (lightIdx < mLightDataList.size())
                return std::get<2>(mLightDataList[lightIdx]);
            else
                return LightTransform();
        }

        RENDER_ENGINE_API void SetLightCastScreenSpaceShadow(ecs::EntityID entity, bool isScreenSpaceCastShadow);
                
        RENDER_ENGINE_API void SetBoundaryShow(ecs::EntityID entity, bool isShow);

    public:
        inline bool IsAtmosphereSunLight(ecs::EntityID entity) const
        {
            if (entity == ecs::EntityID::InvalidHandle())
            {
                return false;
            }

            for (UInt32 i = 0; i < MAX_ATMOSPHERE_LIGHTS_NUM; i++)
            {
                if (entity == GetAtmosphereLightEntity(i))
                {
                    return true;
                }
            }
            return false;
        }

        inline bool HasAtmosphereLight(UInt32 index) const
        {
            Assert(index < MAX_ATMOSPHERE_LIGHTS_NUM);
            return static_cast<bool>(GetAtmosphereLightEntity(index));
        }

        inline ecs::EntityID GetAtmosphereLightEntity(UInt32 index) const
        {
            Assert(index < MAX_ATMOSPHERE_LIGHTS_NUM);
            return mAtmosphereLights[index];
        }

        inline const AtmosphereLightData& GetAtmosphereLightData(UInt32 index) const
        {
            Assert(index < MAX_ATMOSPHERE_LIGHTS_NUM);
            return mAtmosphereLightsData[index];
        }

        void FillAtmosphereLightConstantBuffer(RenderingExecutionDescriptor* RED, ecs::EntityID SceneCamera = ecs::EntityID::InvalidHandle()) const;

        const std::vector<std::tuple<ecs::EntityID, std::shared_ptr<LightRenderData>, LightTransform>>& GetLightDataList() const { return mLightDataList; };

        std::vector<ecs::EntityID> FilterLightsByRenderingLayerMask(UInt32 mask) const;

        inline size_t GetLightCount() const { return mLightDataList.size(); }

    private:
        void ProcessAddAtmosphereLight(const RenderLightComponentReader& comp);

        inline void ResetAtmosphereLights() {
            mAtmosphereLights[0] = ecs::EntityID::InvalidHandle();
            mAtmosphereLights[1] = ecs::EntityID::InvalidHandle();
            mAtmosphereLights[2] = ecs::EntityID::InvalidHandle();
            mAtmosphereLightsData[0] = {};
            mAtmosphereLightsData[1] = {};
            mAtmosphereLightsData[2] = {};
        }

        void FillAtmosphereLightsData(const RenderLightComponentReader& comp, UInt32 index, bool reversed, UInt32 reversedLightRadius);

        void UpdateLightCompactData();

        void UpdateShadowCameras();

    protected:
        TaskFunction* GetLightUpdateTask()
        {
            return mUpdateLightListTask;
        }

    protected:
        std::unordered_map<ecs::EntityID, std::array<PrimitiveData, 2>> mShowingBoundaryPrimitives;

        void DrawShowingBoundary(FrameAllocator* curAllocator);

        ecs::EntityID mAtmosphereLights[MAX_ATMOSPHERE_LIGHTS_NUM];

        AtmosphereLightData mAtmosphereLightsData[MAX_ATMOSPHERE_LIGHTS_NUM];
        Float3A mLightIlluminanceOnGroundPostTransmittance[MAX_ATMOSPHERE_LIGHTS_NUM]{};   // LightIlluminanceOuterSpace * transmittance 

        std::vector<std::tuple<ecs::EntityID, std::shared_ptr<LightRenderData>, LightTransform>> mLightDataList;
        
        std::map<ecs::EntityID, UInt32> mCompactLightIdx;

        TaskFunction* mUpdateLightListTask;

    private:
        FrameChangeList<ecs::EntityID> mChangeList;
        std::vector<ecs::EntityID> mTransformChangeList;
    };
}
