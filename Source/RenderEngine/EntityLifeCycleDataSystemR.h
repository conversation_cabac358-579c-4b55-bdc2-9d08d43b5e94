//#pragma once
//#include "RenderEngineForward.h"
//#include "ECS/Develop/Framework/Types.h"
//#include "CECommon/Common/SystemEvent.h"
//#include "CECommon/Common/RenderSystemBase.h"
//#include "EntityLifeCycleDataSystemImplementation.h"
//
//namespace cross
//{
//struct EntityLifeCycleDataComponentR : ecs::IComponent
//{
//    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();
//
//protected:
//    EntityLifeCycleData mData;
//
//    friend class EntityLifeCycleDataSystemR;
//};
//
//class RenderWorld;
//class RENDER_ENGINE_API EntityLifeCycleDataSystemR : public RenderSystemBase
//{
//    using EntityLifeCycleDataComponentH = ecs::ComponentHandle<EntityLifeCycleDataComponentR>;
//
//public:
//    static SystemDesc& GetDesc();
//
//    SystemDesc& GetSystemDesc() const override { return GetDesc(); }
//
//    static EntityLifeCycleDataSystemR* CreateInstance();
//
//    void Release() override;
//
//    void OnBeginFrame(FrameParam* frameParam) override;
//
//    void OnEndFrame(FrameParam* frameParam) override;
//
//public:
//    template<typename Event>
//    bool SubscribeEvent(SystemEventReceiver* receiver, UInt32 priority) { return mImp.SubscribeEvent<Event>(receiver, priority); }
//
//    // for systems
//    void LockList(const EntityLifeCycleDataComponentH& comp, EntityLifeCycleListType type) { const auto writer = comp.Write(); mImp.LockList(writer->mData, type); }
//
//    const std::vector<ecs::EntityID>& GetList(const EntityLifeCycleDataComponentH& comp, EntityLifeCycleListType type) { const auto reader = comp.Read(); return mImp.GetList(reader->mData, type); }
//
//protected:
//    // for worlds
//    void AddEntity(const EntityLifeCycleDataComponentH& comp, EntityLifeCycleListType type, ecs::EntityID entity) { const auto writer = comp.Write(); mImp.AddEntity(writer->mData, type, entity); }
//
//    template <typename It>
//    void AddEntities(const EntityLifeCycleDataComponentH& comp, EntityLifeCycleListType type, It entityBegin, It entityEnd) { const auto writer = comp.Write(); mImp.AddEntities(writer->mData, type, entityBegin, entityEnd); }
//
//    friend class RenderWorld;
//private:
//    EntityLifeCycleDataSystemImplementation mImp;
//};
//}
