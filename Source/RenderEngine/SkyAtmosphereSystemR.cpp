#include "EnginePrefix.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameCounter.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/SkyAtmosphere/Constants.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include "RenderEngine/ModelSystemR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "PostProcessVolumeSystemR.h"

namespace cross {

Float2 GetAzimuthAndElevation(const Float3& Direction, const Float3& AxisX, const Float3& AxisY, const Float3& AxisZ)
{
    const Float3 NormalDir = Direction.Normalized();
    // Find projected point (on AxisX and AxisY, remove AxisZ component)
    const Float3 NoYProjDir = (NormalDir - NormalDir.Dot(AxisY) * AxisY).Normalized();
    // Figure out if projection is on right or left.
    const float AzimuthSign = ((NoYProjDir.Dot(AxisZ)) < 0.f) ? -1.f : 1.f;
    const float ElevationSin = NormalDir.Dot(AxisY);
    const float AzimuthCos = NoYProjDir.Dot(AxisX);

    // Convert to Angles in Radian.
    return Float2(static_cast<float>(std::acos(AzimuthCos) * AzimuthSign), static_cast<float>(std::asin(ElevationSin)));
}

// todo(chopperlin) : cloud should affect sky transmittance
Float3 GetTransmittanceAtGroundLevel(const Float3& SunDirection, const atmosphere::AtmosphereParameter& atmosphereParameter, const Float3& CameraNormalizedPos, float ColorTransmittance)
{
    // The following code is from SkyAtmosphere.usf and has been converted to lambda functions.
    // It compute transmittance from the origin towards a sun direction.

    auto RayIntersectSphere = [&](Float3 RayOrigin, Float3 RayDirection, Float3 SphereOrigin, float SphereRadius) {
        Float3 LocalPosition = RayOrigin - SphereOrigin;
        float LocalPositionSqr = LocalPosition.Dot(LocalPosition);

        Float3 QuadraticCoef;
        QuadraticCoef.x = RayDirection.Dot(RayDirection);
        QuadraticCoef.y = 2.0f * RayDirection.Dot(LocalPosition);
        QuadraticCoef.z = LocalPositionSqr - SphereRadius * SphereRadius;

        float Discriminant = QuadraticCoef.y * QuadraticCoef.y - 4.0f * QuadraticCoef.x * QuadraticCoef.z;

        // Only continue if the ray intersects the sphere
        Float2 Intersections = {-1.0f, -1.0f};
        if (Discriminant >= 0)
        {
            float SqrtDiscriminant = std::sqrt(Discriminant);
            Intersections.x = (-QuadraticCoef.y - 1.0f * SqrtDiscriminant) / (2 * QuadraticCoef.x);
            Intersections.y = (-QuadraticCoef.y + 1.0f * SqrtDiscriminant) / (2 * QuadraticCoef.x);
        }
        return Intersections;
    };

    // Nearest intersection of ray r,mu with sphere boundary
    auto raySphereIntersectNearest = [&](Float3 RayOrigin, Float3 RayDirection, Float3 SphereOrigin, float SphereRadius) {
        Float2 sol = RayIntersectSphere(RayOrigin, RayDirection, SphereOrigin, SphereRadius);
        float sol0 = sol.x;
        float sol1 = sol.y;
        if (sol0 < 0.0f && sol1 < 0.0f)
        {
            return -1.0f;
        }
        if (sol0 < 0.0f)
        {
            return MathUtils::Max(0.0f, sol1);
        }
        else if (sol1 < 0.0f)
        {
            return MathUtils::Max(0.0f, sol0);
        }
        return MathUtils::Max(0.0f, MathUtils::Min(sol0, sol1));
    };

    auto OpticalDepth = [&](Float3 RayOrigin, Float3 RayDirection) {
        float TMax = raySphereIntersectNearest(RayOrigin, RayDirection, Float3(0.0f, 0.0f, 0.0f), atmosphereParameter.top_radius);

        Float3 OpticalDepthRGB = Float3::Zero();
        Float3 VectorZero = Float3::Zero();
        if (TMax > 0.0f)
        {
            const float SampleCount = 15.0f;
            const float SampleStep = 1.0f / SampleCount;
            const float SampleLength = SampleStep * TMax;
            for (float SampleT = 0.0f; SampleT < 1.0f; SampleT += SampleStep)
            {
                Float3 Pos = RayOrigin + RayDirection * (TMax * SampleT);
                const float viewHeight = (Float3::Distance(Pos, VectorZero) - atmosphereParameter.bottom_radius);
                const float MieDensityExpScale = atmosphereParameter.mie_density.layers[1].exp_scale;
                const float RayleighDensityExpScale = atmosphereParameter.rayleigh_density.layers[1].exp_scale;
                const float AbsorptionDensity0LayerWidth = atmosphereParameter.absorption_density.layers[0].width;
                const float AbsorptionDensity0LinearTerm = atmosphereParameter.absorption_density.layers[0].linear_term;
                const float AbsorptionDensity0ConstantTerm = atmosphereParameter.absorption_density.layers[0].constant_term;
                const float AbsorptionDensity1LinearTerm = atmosphereParameter.absorption_density.layers[1].linear_term;
                const float AbsorptionDensity1ConstantTerm = atmosphereParameter.absorption_density.layers[1].constant_term;

                const float densityMie = MathUtils::Max(0.0f, std::exp(MieDensityExpScale * viewHeight));
                const float densityRay = MathUtils::Max(0.0f, std::exp(RayleighDensityExpScale * viewHeight));

                const float densityOzo = MathUtils::Clamp(
                    viewHeight < AbsorptionDensity0LayerWidth ? AbsorptionDensity0LinearTerm * viewHeight + AbsorptionDensity0ConstantTerm : AbsorptionDensity1LinearTerm * viewHeight + AbsorptionDensity1ConstantTerm, 0.0f, 1.0f);

                const Float3 MieExtinction = atmosphereParameter.mie_extinction;
                const Float3 RayleighScattering = atmosphereParameter.rayleigh_scattering;
                const Float3 AbsorptionExtinction = atmosphereParameter.absorption_extinction;

                Float3 SampleExtinction = densityMie * MieExtinction + densityRay * RayleighScattering + densityOzo * AbsorptionExtinction;
                OpticalDepthRGB += SampleLength * SampleExtinction;
            }
        }

        return OpticalDepthRGB;
    };

    // Assuming camera is along Z on (0,0,earthRadius + 500m)
    const Float3 WorldPos = Float3(0.0f, 0.0f, atmosphereParameter.bottom_radius + 0.5f);
    const Float3 YVector = CameraNormalizedPos;
    const Float3 XVector = MathUtils::Abs(YVector.Dot(Float3(0, 1, 0))) > 0.999f ? Float3(1, 0, 0) : YVector.Cross(Float3(0, 0, 1));
    const Float3 ZVector = XVector.Cross(YVector);

    const Float3 SunDirectionRHS = {SunDirection.x, SunDirection.z, SunDirection.y};
    //Float2 AzimuthElevation = GetAzimuthAndElevation(SunDirection, Float3(1, 0, 0), Float3(0, 1, 0), Float3(0, 0, 1));   // TODO(ue5): make it work over the entire virtual planet with a local basis
    Float2 AzimuthElevation = GetAzimuthAndElevation(SunDirection, XVector, YVector, ZVector);   // TODO(ue5): make it work over the entire virtual planet with a local basis
    const float TransmittanceMinLightElevationAngle = -90.0f;
    AzimuthElevation.y = MathUtils::Max(MathUtils::ConvertToRadians(TransmittanceMinLightElevationAngle), AzimuthElevation.y);
    const Float3 WorldDir = Float3(std::cos(AzimuthElevation.y), 0.0f, std::sin(AzimuthElevation.y));   // no need to take azimuth into account as transmittance is symmetrical around zenith axis.
    Float3 OpticalDepthRGB = OpticalDepth(WorldPos, WorldDir);
    Float3 ret = Float3(std::exp(-OpticalDepthRGB.x), std::exp(-OpticalDepthRGB.y), std::exp(-OpticalDepthRGB.z));
    Float3 grey = Float3::One() * ret.Dot(Float3(0.3f, 0.59f, 0.11f));

    ColorTransmittance = std::clamp(ColorTransmittance, 0.0f, 1.0f);
    return Float3::Lerp(grey, ret, ColorTransmittance);
}

ecs::ComponentDesc* cross::SkyAtmosphereComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::SkyAtmosphereComponentR>(false);
}

SkyAtmosphereSystemR* SkyAtmosphereSystemR::CreateInstance()
{
    SkyAtmosphereSystemR* system = new SkyAtmosphereSystemR();
    return system;
}

SkyAtmosphereSystemR::SkyAtmosphereSystemR() {}

SkyAtmosphereSystemR::~SkyAtmosphereSystemR() {}

void SkyAtmosphereSystemR::CreateTexture(std::string_view name, UInt32 width, UInt32 height, UInt16 depth, REDUniquePtr<REDResidentTexture>& texPtr, REDUniquePtr<REDResidentTextureView>& texViewPtr, REDUniquePtr<REDResidentTextureView>& texViewReadOnlyPtr)
{
    auto RED = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor();

    auto dim = depth == 1 ? NGITextureType::Texture2D : NGITextureType::Texture3D;

    texPtr = RED->CreateTexture(name, NGITextureDesc{GraphicsFormat::R32G32B32A32_SFloat, dim, 1, 1, width, height, depth, 1, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource});

    texViewPtr = RED->CreateTextureView(texPtr.get(),
        NGITextureViewDesc{ NGITextureUsage::UnorderedAccess,
                           GraphicsFormat::R32G32B32A32_SFloat,
                           dim,
                           {
                               NGITextureAspect::Color,
                               0,
                               1,
                               0,
                               1,
                           } });

    texViewReadOnlyPtr = RED->CreateTextureView(texPtr.get(),
                                                NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                   GraphicsFormat::R32G32B32A32_SFloat,
                                                                   dim,
                                                                   {
                                                                       NGITextureAspect::Color,
                                                                       0,
                                                                       1,
                                                                       0,
                                                                       1,
                                                                   }});
}

void SkyAtmosphereSystemR::OnFirstUpdate(FrameParam* frameParam) {}

void SkyAtmosphereSystemR::Release()
{
    delete this;
}

void SkyAtmosphereSystemR::OnEndFrame(FrameParam* frameParam) {}

void SkyAtmosphereSystemR::SetAtmosphereEnable(ecs::EntityID entity, bool enable)
{
    auto atmoComponent = mRenderWorld->GetComponent<SkyAtmosphereComponentR>(entity);
    if (!atmoComponent.IsValid())
        return;
    {
        auto writer = atmoComponent.Write();
        writer->dirty = true;
        writer->enable = enable;
    }
}

void SkyAtmosphereSystemR::SetAtmosphereModel(ecs::EntityID entity, std::shared_ptr<atmosphere::Model> model)
{
    auto atmoComponent = mRenderWorld->GetComponent<SkyAtmosphereComponentR>(entity);
    if (!atmoComponent.IsValid())
        return;
    {
        auto writer = atmoComponent.Write();
        writer->dirty = true;
        writer->model = model;
    }
}

void SkyAtmosphereSystemR::SetAtmosphereOuterParam(ecs::EntityID entity, SkyAtmosphereOuterParam outerParam, SkyAtmosphereConfig config)
{
    auto atmoComponent = mRenderWorld->GetComponent<SkyAtmosphereComponentR>(entity);
    if (!atmoComponent.IsValid())
        return;
    {
        auto writer = atmoComponent.Write();
        writer->outerParam = outerParam;
        writer->skyLuminanceFactor = config.SkyLuminanceFactor;
        writer->MultiScatteringFactor = config.MultiScatteringFactor;
    }
}

bool SkyAtmosphereSystemR::GetVSMAvailiable() const
{
    const auto* RenderPipelineSetting = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetRenderPipelineSetting();
    const auto* FFSPipelineSettings = TYPE_CAST(const FFSRenderPipelineSetting*, RenderPipelineSetting);

    // Pipeline is not valid
    if (FFSPipelineSettings == nullptr)
    {
        return false;
    }

    // Shadow is not enabled
    if (!FFSPipelineSettings->Shadow)
    {
        return false;
    }

    return FFSPipelineSettings->mVirtualShadowMapSettings.EnableVirtualShadowMap();
}

void SkyAtmosphereSystemR::PrepareRenderSkyAtmosphere()
{
    SCOPED_CPU_TIMING(GroupRendering, "PrepareRenderSkyAtmosphere");
    auto queryResult = mRenderWorld->Query<SkyAtmosphereComponentR>();

    // Create precomputed textures only once on demand
    if (mTransmittanceTexture == nullptr && queryResult.GetEntityNum() > 0)
    {
        CreateTexture("Transmittance", atmosphere::TRANSMITTANCE_TEXTURE_WIDTH, atmosphere::TRANSMITTANCE_TEXTURE_HEIGHT, 1, mTransmittanceTexture, mTransmittanceTextureView, mTransmittanceTextureViewReadOnly);
        CreateTexture("MultiScatTexture", atmosphere::MULTI_SCATTERIGN_LUT_RES, atmosphere::MULTI_SCATTERIGN_LUT_RES, 1, mMultiScatTexture, mMultiScatTextureView, mMultiScatTextureViewReadOnly);
        CreateTexture("SkyViewLutTexture", atmosphere::SKY_VIEW_LUT_WIDTH, atmosphere::SKY_VIEW_LUT_HEIGHT, 1, mSkyViewLutTexture, mSkyViewLutTextureView, mSkyViewLutTextureViewReadOnly);
        CreateTexture("DistantSkyLightTexture", 1, 1, 1, mDistantSkyLightTexture, mDistantSkyLightTextureView, mDistantSkyLightTextureViewReadOnly);
        CreateTexture("AtmosphereCameraScatteringVolume",
                      atmosphere::CAMERA_VOLUME_LUT_RES,
                      atmosphere::CAMERA_VOLUME_LUT_RES,
                      atmosphere::CAMERA_VOLUME_LUT_DEPTH,
                      mAtmosphereCameraScatteringVolume,
                      mAtmosphereCameraScatteringVolumeView,
                      mAtmosphereCameraScatteringVolumeViewReadOnly);
    }

    mSkyAtmoRenderContextReady = false;
    for (const auto& skComp : queryResult)
    {
        {
            auto reader = skComp.Read();
            if (!reader->enable)
            {
                continue;
            }
            // todo:
            // check why model is nullptr
            if (reader->model == nullptr)
            {
                mSkyAtmoRenderContextReady = reader->model != nullptr;
                break;
            }

            // Record wgs84 mode state
            mPlanetTopAtWorldOrigin = reader->outerParam.PlanetTopAtWorldOrigin;

            // Base Pass Context is used for compute shader and rendering pass both
            {
                const atmosphere::AtmosphereParameter& atmosphereParameter = reader->model->GetAtmosphereParameter();
                const atmosphere::SkyParameter& skyParameter = reader->model->GetSkyParameter();
                mSetBasePassContextFunc = [&,
                                           this,
                                           PlanetTopAtWorldOrigin = reader->outerParam.PlanetTopAtWorldOrigin,
                                           MoonPhase = reader->outerParam.MoonPhase,
                                           RenderSunDisk = reader->outerParam.RenderSunDisk,
                                           RenderMoonDisk = reader->outerParam.RenderMoonDisk,
                                           HighQualityAP = reader->outerParam.HighQualityAP,
                                           APStart = reader->outerParam.AerialPerspStartDepthKM](SkyAtmoShaderParamBase& passParam) mutable -> bool {
                    /*
                     *   Bind global constants
                     */
                    passParam.AtmosphereModel.mDataPtr = reinterpret_cast<const void*>(&atmosphereParameter);
                    passParam.SkySpectralRadianceToLuminance.mValue = skyParameter.SkySpectralRadianceToLuminance;
                    passParam.SunSpectralRadianceToLuminance.mValue = skyParameter.SunSpectralRadianceToLuminance;
                    passParam.LuminanceFromRadiance.mValue = Float4x4::Identity();
                    passParam.PlanetTopAtWorldOrigin.mValue = PlanetTopAtWorldOrigin;
                    passParam.MoonPhase.mValue = MoonPhase;
                    passParam.RenderSunDisk.mValue = RenderSunDisk;
                    passParam.RenderMoonDisk.mValue = RenderMoonDisk;
                    passParam.AerialPerspStartDepthKM.mValue = std::max(0.0001f, APStart);
                    passParam.UnitM.mValue = false;
                    passParam.HighQualityAP.mValue = HighQualityAP;
                    passParam.UseVSM.mValue = GetVSMAvailiable();
                    
                    return true;
                };

                // RenderingPassContext is used for rendering pass
                mSetGlobalRenderContextFunc = [&, this, Exposure = reader->outerParam.Exposure, APScale = reader->outerParam.APScale, ColorTrans = reader->outerParam.ColorTransmittance](auto* red, bool realTimeCapture) mutable -> bool {
                    SkyAtmoShaderParamBase BaseParam;
                    mSetBasePassContextFunc(BaseParam);
                    SkyRenderingPSParamNGI renderingParam;
                    renderingParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly->GetNativeTextureView();
                    renderingParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly->GetNativeTextureView();
                    renderingParam.SkyViewLutTexture.mValue = realTimeCapture ? mSkyViewReflectionRealTimeCaptureLutTextureViewReadOnly->GetNativeTextureView() : mSkyViewLutTextureViewReadOnly->GetNativeTextureView();
                    renderingParam.DistantSkyLightTexture.mValue = mDistantSkyLightTextureViewReadOnly->GetNativeTextureView();
                    renderingParam.AtmosphereCameraScatteringVolume.mValue = mAtmosphereCameraScatteringVolumeViewReadOnly->GetNativeTextureView();

                    // renderingParam.SunSize.mValue = std::cos(MathUtils::ConvertToRadians(lightAngle) * 0.5f);
                    renderingParam.Exposure.mValue = std::max(0.0f, Exposure);
                    renderingParam.APScale.mValue = std::max(0.0f, APScale);
                    renderingParam.ColorTrans.mValue = std::clamp(ColorTrans, 0.0f, 1.0f);

                    SetPassParameters(BaseParam, red);
                    SetPassParameters(renderingParam, red);
                    return true;
                };

                // RenderingPassContext is used for rendering pass
                mSetRenderingPassContextFunc = [&, this, Exposure = reader->outerParam.Exposure, APScale = reader->outerParam.APScale,
                    ColorTrans = reader->outerParam.ColorTransmittance, SkyLuminanceFactor = reader->skyLuminanceFactor](
                                                   auto* pass, bool realTimeCapture, bool useREDView, bool useDisk, bool useVSM) mutable -> bool {
                    SkyAtmoShaderParamBase BaseParam;

                    mSetBasePassContextFunc(BaseParam);

                    // override the parameters
                    BaseParam.RenderSunDisk.mValue &= useDisk;
                    BaseParam.RenderMoonDisk.mValue &= useDisk;
                    BaseParam.UseVSM.mValue = useVSM;
                    BaseParam.SkyLuminanceFactor.mValue = SkyLuminanceFactor;
                    
                    SetPassParameters(BaseParam, pass);
                    if (useREDView)
                    {
                        SkyRenderingPSParamRED renderingParam;
                        renderingParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly.get();
                        renderingParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly.get();
                        renderingParam.SkyViewLutTexture.mValue = realTimeCapture ? mSkyViewReflectionRealTimeCaptureLutTextureViewReadOnly.get() : mSkyViewLutTextureViewReadOnly.get();
                        renderingParam.DistantSkyLightTexture.mValue = mDistantSkyLightTextureViewReadOnly.get();
                        renderingParam.AtmosphereCameraScatteringVolume.mValue = mAtmosphereCameraScatteringVolumeViewReadOnly.get();
                        // renderingParam.SunSize.mValue = std::cos(MathUtils::ConvertToRadians(lightAngle) * 0.5f);
                        renderingParam.Exposure.mValue = std::max(0.0f, Exposure);
                        renderingParam.APScale.mValue = std::max(0.0f, APScale);
                        renderingParam.ColorTrans.mValue = std::clamp(ColorTrans, 0.0f, 1.0f);
                        SetPassParameters(renderingParam, pass);
                    }
                    else
                    {
                        SkyRenderingPSParamNGI renderingParam;
                        renderingParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly->GetNativeTextureView();
                        renderingParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly->GetNativeTextureView();
                        renderingParam.SkyViewLutTexture.mValue = realTimeCapture ? mSkyViewReflectionRealTimeCaptureLutTextureViewReadOnly->GetNativeTextureView() : mSkyViewLutTextureViewReadOnly->GetNativeTextureView();
                        renderingParam.DistantSkyLightTexture.mValue = mDistantSkyLightTextureViewReadOnly->GetNativeTextureView();
                        renderingParam.AtmosphereCameraScatteringVolume.mValue = mAtmosphereCameraScatteringVolumeViewReadOnly->GetNativeTextureView();
                        // renderingParam.SunSize.mValue = std::cos(MathUtils::ConvertToRadians(lightAngle) * 0.5f);
                        renderingParam.Exposure.mValue = std::max(0.0f, Exposure);
                        renderingParam.APScale.mValue = std::max(0.0f, APScale);
                        renderingParam.ColorTrans.mValue = std::clamp(ColorTrans, 0.0f, 1.0f);
                        SetPassParameters(renderingParam, pass);
                    }
                    return true;
                };

                // if PlanetTopAtWorldOrigin is true, we assume that the scene is flat the camera is always at the top of the planet.
                // else, the scene size is earth level so we should consider the camera position.
                // Note: CameraPosDir is Normalized(CameraPosition)
                if (reader->outerParam.PlanetTopAtWorldOrigin)
                {
                    mComputeTransmittanceTowardsSunAtGroundLevelFunc = [&, this, colorTrans = reader->outerParam.ColorTransmittance, atmo = atmosphereParameter](const Float3& LightDir, const Float3& CameraPosDir) mutable -> Float3 {
                        return GetTransmittanceAtGroundLevel(LightDir, atmo, Float3(0, 1, 0), colorTrans);
                    };
                }
                else
                {
                    mComputeTransmittanceTowardsSunAtGroundLevelFunc = [&, this, colorTrans = reader->outerParam.ColorTransmittance, atmo = atmosphereParameter](const Float3& LightDir, const Float3& CameraPosDir) mutable -> Float3 {
                        return GetTransmittanceAtGroundLevel(LightDir, atmo, CameraPosDir, colorTrans);
                    };
                }
            }
            mSkyAtmoRenderContextReady = true;

            // pre-compute Transmittance and Scattering textures before pipeline assemble
            PreComputeTransmittanceAndScattering();
            break;
        }
    }
}

void SkyAtmosphereSystemR::SetRenderCameraCaptureViewContext(REDPass* Pass, const RenderCamera* Camera)
{
    Float3 capturePos = Camera->GetCameraOrigin();
    auto invViewMat = Camera->GetInvertViewMatrix();
    Float3 forward = -Float3(invViewMat.m20, invViewMat.m21, invViewMat.m22).Normalized();
#ifdef CE_USE_DOUBLE_TRANSFORM
    auto tilePos = Camera->GetTilePosition<false>();
    capturePos += tilePos * LENGTH_PER_TILE_F;
#endif

    Float3 SkyCameraOrigin;
    Float4 SkyPlanetCenterAndViewHeight;
    Float4x4 SkyViewLutReferential;
    
    SetSkyViewCaptureViewContext(Pass, capturePos, forward,
        SkyCameraOrigin, SkyPlanetCenterAndViewHeight, SkyViewLutReferential);

    Pass->SetProperty(BuiltInProperty::ce_View, Camera->GetViewMatrix());
    Pass->SetProperty(BuiltInProperty::ce_InvView, Camera->GetInvertViewMatrix());
    Pass->SetProperty(BuiltInProperty::ce_Projection, Camera->GetProjMatrix());
    Pass->SetProperty(BuiltInProperty::ce_InvProjection, Camera->GetInvertProjMatrix());
}

void SkyAtmosphereSystemR::SetSkyCameraCaptureViewContext(REDPass* Pass)
{
    // Setup a constant referential for each of the faces of the dynamic reflection capture.
    const Float3 SkyViewLutReferentialForward = Float3(1.f, 0.f, 0.f);

    auto queryResult = mRenderWorld->Query<SkyLightComponentR>();
    auto skyLightEntity = queryResult[0].GetEntityID();
    auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(skyLightEntity);
    Float3 capturePos = transformSys->GetWorldTranslation(transformComp.Read());
#ifdef CE_USE_DOUBLE_TRANSFORM
    Float3 tilePos = transformSys->GetTilePosition(skyLightEntity);
    capturePos += tilePos * LENGTH_PER_TILE_F;
#endif

    Float3 SkyCameraOrigin;
    Float4 SkyPlanetCenterAndViewHeight;
    Float4x4 SkyViewLutReferential;
    
    // TODO(scolu): CapturePos use Double3
    SetSkyViewCaptureViewContext(Pass, capturePos, SkyViewLutReferentialForward,
        SkyCameraOrigin, SkyPlanetCenterAndViewHeight, SkyViewLutReferential);
}

void SkyAtmosphereSystemR::SetSkyViewCaptureViewContext(REDPass* Pass, const Float3& CapturePos, const Float3& ViewForward,
    Float3& SkyCameraOrigin, Float4& SkyPlanetCenterAndViewHeight, Float4x4& SkyViewLutReferential)
{
    // Should match the one in Common.hlsl PLANET_RADIUS_OFFSET
    const float PlanetRadiusOffset = 0.005f;
    constexpr float KM2CM = 1e5f;
    constexpr float CM2KM = 1e-5f;
    const float Offset = PlanetRadiusOffset * KM2CM;
    
    auto queryResult = mRenderWorld->Query<SkyAtmosphereComponentR>();
    auto skyAtmoComp = queryResult[0];
    auto reader = skyAtmoComp.Read();
    float BottomRadiusKM = reader->model->GetAtmosphereParameter().bottom_radius;
    float BottomRadiusCM =  BottomRadiusKM * KM2CM;

    Float3 PlanetCenterWorld;
    if (GetPlanetTopAtWorldOrigin())
    {
        PlanetCenterWorld = Float3(0.f, 0.f, -BottomRadiusCM);
    }
    else
    {
        auto transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
        PlanetCenterWorld = transformSys->GetWorldTranslation(skyAtmoComp.GetEntityID());
#ifdef CE_USE_DOUBLE_TRANSFORM
        auto tilePos = transformSys->GetTilePosition(skyAtmoComp.GetEntityID());
        PlanetCenterWorld += tilePos * LENGTH_PER_TILE_F;
#endif
        PlanetCenterWorld = CEVec3ToUE(PlanetCenterWorld);
    }
    
    const Float3 WorldCameraOrigin = CEVec3ToUE(CapturePos);
    const Float3 PlanetCenterToCamera = WorldCameraOrigin - PlanetCenterWorld;
    const float DistanceToPlanetCenter = PlanetCenterToCamera.Length();
    if (DistanceToPlanetCenter < BottomRadiusCM + Offset)
    {
        SkyCameraOrigin = PlanetCenterWorld + (BottomRadiusCM + Offset) * (PlanetCenterToCamera / DistanceToPlanetCenter);
    }
    else
    {
        SkyCameraOrigin = WorldCameraOrigin;
    }
    SkyPlanetCenterAndViewHeight = Float4(PlanetCenterWorld.x, PlanetCenterWorld.y, PlanetCenterWorld.z, (SkyCameraOrigin - PlanetCenterWorld).Length());

    Float3 PlanetCenterToCameraPos = (SkyCameraOrigin - PlanetCenterWorld) * CM2KM;

    const Float3 Up = PlanetCenterToCameraPos.Normalized();
    Float3 Forward = CEVec3ToUE(ViewForward);
    Float3 Left = Forward.Cross(Up).Normalized();
    const float DotMainDir = Abs(Up.Dot(Forward));
    if (DotMainDir > 0.999f)
    {
        // When it becomes hard to generate a referential, generate it procedurally.
        // [ Duff et al. 2017, "Building an Orthonormal Basis, Revisited" ]
        const float Sign = Up.z >= 0.0f ? 1.0f : -1.0f;
        const float a = -1.0f / (Sign + Up.z);
        const float b = Up.x * Up.y * a;
        Forward = Float3(1 + Sign * a * Pow(Up.x, 2.f), Sign * b, -Sign * Up.x);
        Left = Float3(b, Sign + a * Pow(Up.y, 2.f), -Up.y);
    }
    else
    {
        // This is better as it should be more stable with respect to camera forward.
        Forward = Up.Cross(Left);
        Forward.Normalize();
    }
    
    SkyViewLutReferential.m00 = Forward.x;
    SkyViewLutReferential.m01 = Forward.y;
    SkyViewLutReferential.m02 = Forward.z;
    SkyViewLutReferential.m10 = Left.x;
    SkyViewLutReferential.m11 = Left.y;
    SkyViewLutReferential.m12 = Left.z;
    SkyViewLutReferential.m20 = Up.x;
    SkyViewLutReferential.m21 = Up.y;
    SkyViewLutReferential.m22 = Up.z;
    
    Pass->SetProperty(NAME_ID("SkyCameraOrigin"), SkyCameraOrigin);
    Pass->SetProperty(NAME_ID("SkyPlanetCenterAndViewHeight"), SkyPlanetCenterAndViewHeight);
    Pass->SetProperty(NAME_ID("SkyViewLutReferential"), SkyViewLutReferential.Transpose());
}

void SkyAtmosphereSystemR::PreComputeTransmittanceAndScattering()
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = rendererSystem->GetRenderingExecutionDescriptor();

    RED->BeginRegion(fmt::format("PreComputeSkyAtmosphere for world: {}", mRenderWorld->GetName().GetCString()));
    RED->SetProperty(BuiltInProperty::USE_SECONDARY_LIGHT, false);
    SkyAtmoShaderParamBase BaseParam;
    /*
     *   Dispatch tasks.
     *   Group size in shader is (8, 8, 1)
     */
    auto precomputepass = RED->AllocatePass("ComputeTransmittance", true);
    SkyTransmittanceCSParam skyTransmittanceCSParam;
    mSetBasePassContextFunc(BaseParam);
    skyTransmittanceCSParam.TransmittanceLutTexture.mValue = mTransmittanceTextureView.get();
    BaseParam.MultiScatterApproxEnabled.mValue = false;
    
    SetPassParameters(skyTransmittanceCSParam, precomputepass);
    SetPassParameters(BaseParam, precomputepass);

    // only main world will do this
    auto CamSys = mRenderWorld->GetRenderSystem<CameraSystemR>();
    auto camEntity = CamSys->GetMainCamera();
    if (camEntity != ecs::EntityID::InvalidHandle())
    {
        auto camComp = mRenderWorld->GetComponent<CameraComponentR>(camEntity);
        auto cam = CamSys->GetRenderCamera(camComp.Read());
        precomputepass->SetProperty(BuiltInProperty::ce_InvViewProjMatrix, cam->GetInvertViewMatrix());
        precomputepass->SetProperty(BuiltInProperty::ce_InvProjection, cam->GetInvertProjMatrix());
    }
    precomputepass->Dispatch(mPrecomputeShader, "ComputeTransmittance", atmosphere::TRANSMITTANCE_TEXTURE_WIDTH / 8, atmosphere::TRANSMITTANCE_TEXTURE_HEIGHT / 8, 1);

    precomputepass = RED->AllocatePass("NewMultiScattCS", true);
    SkyMultiScatteringCSParam skyMultiScatteringCSParam;
    mSetBasePassContextFunc(BaseParam);
    skyMultiScatteringCSParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly.get();
    skyMultiScatteringCSParam.MultiScatTexture.mValue = mMultiScatTextureView.get();
    BaseParam.MultiScatterApproxEnabled.mValue = false;
    BaseParam.IlluminanceIsOne.mValue = true;
    
    auto skyAtmoQueryResult = mRenderWorld->Query<SkyAtmosphereComponentR>();
    if (skyAtmoQueryResult.GetEntityNum() > 0)
    {
        BaseParam.MultiScatteringFactor.mValue = skyAtmoQueryResult[0].Read()->MultiScatteringFactor;
    }
    SetPassParameters(skyMultiScatteringCSParam, precomputepass);
    SetPassParameters(BaseParam, precomputepass);

    //precomputepass->GetContext().SetProperty("MULTISCATAPPROX_ENABLED", false);
    precomputepass->Dispatch(mPrecomputeShader, "NewMultiScattCS", atmosphere::MULTI_SCATTERIGN_LUT_RES / 8, atmosphere::MULTI_SCATTERIGN_LUT_RES / 8, 1);

    RED->FlushState(mTransmittanceTexture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->FlushState(mMultiScatTexture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->EndRegion();
}

void SkyAtmosphereSystemR::PrecomputeSkyViewReflectionRealTimeCaptureLutTexture()
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = rendererSystem->GetRenderingExecutionDescriptor();

    RED->BeginRegion(fmt::format("PreComputeSkyViewReflectionRealTimeCapture for view in world: {}", mRenderWorld->GetName().GetCString()));
    // Prepare texture
    if (mSkyViewReflectionRealTimeCaptureLutTexture == nullptr)
    {
        CreateTexture("RealtimeSkyViewLutTexture", atmosphere::SKY_VIEW_LUT_WIDTH, atmosphere::SKY_VIEW_LUT_HEIGHT, 1, mSkyViewReflectionRealTimeCaptureLutTexture, mSkyViewReflectionRealTimeCaptureLutTextureView, mSkyViewReflectionRealTimeCaptureLutTextureViewReadOnly);
    }

    SkyAtmoShaderParamBase BaseParam;
    auto precomputepass = RED->AllocatePass("ComputeSkyViewRealtimeCapture", true);
    SkyViewCSParam skyViewCSParam;
    mSetBasePassContextFunc(BaseParam);
    skyViewCSParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly.get();
    skyViewCSParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly.get();
    skyViewCSParam.SkyViewLutTexture.mValue = mSkyViewReflectionRealTimeCaptureLutTextureView.get();
    //skyViewCSParam.SkyViewLutTexture.mValue = mSkyViewLutTextureView;
    BaseParam.MultiScatterApproxEnabled.mValue = true;
    BaseParam.IlluminanceIsOne.mValue = false;
    
    SetPassParameters(skyViewCSParam, precomputepass);
    SetPassParameters(BaseParam, precomputepass);
    
    SetSkyCameraCaptureViewContext(precomputepass);

    precomputepass->Dispatch(mPrecomputeShader, "ComputeSkyView",
        atmosphere::SKY_VIEW_LUT_WIDTH / 8, atmosphere::SKY_VIEW_LUT_HEIGHT / 6, 1);

    RED->FlushState(mSkyViewReflectionRealTimeCaptureLutTexture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->EndRegion();
}

void SkyAtmosphereSystemR::RenderSkyWithComputeShader(REDTextureView* RenderTarget) {
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = rendererSystem->GetRenderingExecutionDescriptor();

    auto precomputepass = RED->AllocatePass("RenderSkyRealtimeCapture", true);

    auto skysys = mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    precomputepass->SetProperty(NAME_ID("USE_WGS84"), !skysys->GetPlanetTopAtWorldOrigin());

    // Sky atmosphere render context
    mSetRenderingPassContextFunc(RED, true, false, true, false);
    // set render target
    precomputepass->SetProperty("_RenderSkyTarget", RenderTarget);
    SetSkyCameraCaptureViewContext(precomputepass);
    
    // Dispatch
    precomputepass->Dispatch(mPrecomputeShader, "RenderSkyCS", 128 / 8, 128 / 8, 1);

}

void SkyAtmosphereSystemR::ClearSkyWithComputeShader(REDTextureView* RenderTarget)
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = rendererSystem->GetRenderingExecutionDescriptor();

    auto precomputepass = RED->AllocatePass("ClearSkyRealtimeCapture", true);
    // set render target
    precomputepass->GetContext().SetProperty("_RenderSkyTarget", RenderTarget);
    // Dispatch
    precomputepass->Dispatch(mPrecomputeShader, "ClearSkyCS", 128 / 8, 128 / 8, 1);
}


void SkyAtmosphereSystemR::PreComputeDistantSkyLightTextures()
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = rendererSystem->GetRenderingExecutionDescriptor();
    SkyAtmoShaderParamBase BaseParam;

    RED->BeginRegion(fmt::format("PreComputeDistantSkyLight for view in world: {}", mRenderWorld->GetName().GetCString()));
    auto precomputepass = RED->AllocatePass("ComputeDistantSkyLight", true);
    DistantSkyLightCSParam skyLightCSParam;
    mSetBasePassContextFunc(BaseParam);
    skyLightCSParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly.get();
    skyLightCSParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly.get();
    skyLightCSParam.DistantSkyLightTexture.mValue = mDistantSkyLightTextureView.get();
    BaseParam.MultiScatterApproxEnabled.mValue = true;
    BaseParam.IlluminanceIsOne.mValue = false;
    SetPassParameters(skyLightCSParam, precomputepass);
    SetPassParameters(BaseParam, precomputepass);

    SetSkyCameraCaptureViewContext(precomputepass);
    
    precomputepass->Dispatch(mPrecomputeShader, "ComputeDistantSkyLight", 1, 1, 1);

    RED->FlushState(mSkyViewLutTexture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->FlushState(mAtmosphereCameraScatteringVolume.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->FlushState(mDistantSkyLightTexture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->EndRegion();
}

void SkyAtmosphereSystemR::UpdateCloudShadowContext(REDPass* pass) const
{
    auto cloudSystem = mRenderWorld->GetRenderSystem<CloudSystemR>();
    auto gsettings = cloudSystem->GetGlobalSetting();
    if (gsettings.CloudShadow.CloudCastShadow)
    {
        cloudSystem->UpdateCloudShadowContext(pass);
    }
    else
    {
        cloudSystem->UpdateThunderstormProperties(pass);
    }
}

void SkyAtmosphereSystemR::PreComputeTextures(const RenderCamera* Camera)
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = rendererSystem->GetRenderingExecutionDescriptor();

    RED->BeginRegion(fmt::format("PreComputeSkyAtmosphere for view in world: {}", mRenderWorld->GetName().GetCString()));

    SkyAtmoShaderParamBase BaseParam;

    auto precomputepass = RED->AllocatePass("ComputeSkyView", true);
    SkyViewCSParam skyViewCSParam;
    mSetBasePassContextFunc(BaseParam);
    skyViewCSParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly.get();
    skyViewCSParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly.get();
    skyViewCSParam.SkyViewLutTexture.mValue = mSkyViewLutTextureView.get();
    BaseParam.MultiScatterApproxEnabled.mValue = true;
    BaseParam.IlluminanceIsOne.mValue = false;
    auto skyAtmoQueryResult = mRenderWorld->Query<SkyAtmosphereComponentR>();
    if (skyAtmoQueryResult.GetEntityNum() > 0)
    {
        BaseParam.SkyLuminanceFactor.mValue = skyAtmoQueryResult[0].Read()->skyLuminanceFactor;
    }
    
    SetRenderCameraCaptureViewContext(precomputepass, Camera);
    
    SetPassParameters(skyViewCSParam, precomputepass);
    SetPassParameters(BaseParam, precomputepass);
    precomputepass->Dispatch(mPrecomputeShader, "ComputeSkyView", atmosphere::SKY_VIEW_LUT_WIDTH / 8, atmosphere::SKY_VIEW_LUT_HEIGHT / 6, 1);

    precomputepass = RED->AllocatePass("ComputeCameraVolume", true);
    SkyCameraVolumeCSParam skyCameraVolumeCSParam;
    mSetBasePassContextFunc(BaseParam);
    skyCameraVolumeCSParam.TransmittanceLutTexture.mValue = mTransmittanceTextureViewReadOnly.get();
    skyCameraVolumeCSParam.MultiScatTexture.mValue = mMultiScatTextureViewReadOnly.get();
    skyCameraVolumeCSParam.SkyViewLutTexture.mValue =  mSkyViewLutTextureViewReadOnly.get();
    skyCameraVolumeCSParam.AtmosphereCameraScatteringVolume.mValue = mAtmosphereCameraScatteringVolumeView.get();
    BaseParam.MultiScatterApproxEnabled.mValue = true;
    BaseParam.IlluminanceIsOne.mValue = false;
    BaseParam.SampleCloudShadow.mValue = true;
    SetPassParameters(skyCameraVolumeCSParam, precomputepass);
    SetPassParameters(BaseParam, precomputepass);
    UpdateCloudShadowContext(precomputepass);
    
    SetRenderCameraCaptureViewContext(precomputepass, Camera);
    
    precomputepass->Dispatch(mPrecomputeAPShader, "ComputeCameraVolume", atmosphere::CAMERA_VOLUME_LUT_RES / 8, atmosphere::CAMERA_VOLUME_LUT_RES / 8, atmosphere::CAMERA_VOLUME_LUT_DEPTH);

    RED->FlushState(mSkyViewLutTexture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->FlushState(mAtmosphereCameraScatteringVolume.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    RED->EndRegion();
}

Float3 SkyAtmosphereSystemR::GetTransmittanceTowardsSunAtRenderCamera(const Float3& inLightDir, const RenderCamera* cam)
{
    auto const& camWorldMat = cam->GetInvertViewMatrix();
    Float3 camPos = {camWorldMat.m30, camWorldMat.m31, camWorldMat.m32};
    auto camTile = cam->GetTilePosition();

#if defined(CE_USE_DOUBLE_TRANSFORM)
    Float3 camTilePos = cam->GetTilePosition();
    Double3 camWorldPos = GetAbsolutePosition(camTilePos, camPos);
    camPos = static_cast<Float3>(camWorldPos.Normalized());
#else
    camPos = camPos.Normalized();
#endif

    return GetTransmittanceTowardsSunAtGroundLevel(inLightDir, camPos);
}

Float3 SkyAtmosphereSystemR::GetTransmittanceTowardsSunAtMainCamera(const Float3& inLightDir) 
{
    auto transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto CamSys = mRenderWorld->GetRenderSystem<CameraSystemR>();
    auto camEntity = CamSys->GetMainCamera();
    
    if (camEntity == camEntity.InvalidHandle())
    {
        return Float3::One();
    }

    auto camComp = mRenderWorld->GetComponent<CameraComponentR>(camEntity);
    auto cam = CamSys->GetRenderCamera(camComp.Read());
    if (cam == nullptr)
    {
        return Float3::One();
    }

    return GetTransmittanceTowardsSunAtRenderCamera(inLightDir, cam);
}
}   // namespace cross
