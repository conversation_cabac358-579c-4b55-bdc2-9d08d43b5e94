#include "EnginePrefix.h"
#include "Resource/AssetStreaming.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/TileBasedCoordinates.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/SkeletonSystemR.h"

#include "VertexStreamLayoutPolicy.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/SkeletonComponentR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/SkeletonGpuSkin.h"
#include "RenderEngine/TransformSystemR.h"

namespace cross {


SkeletonSystemR* SkeletonSystemR::CreateInstance()
{
    SkeletonSystemR* system = new SkeletonSystemR();
    DispatchRenderingCommandWithToken([system] { system->Initialize(); });
    return system;
}

void SkeletonSystemR::Initialize() 
{
    mPoseRingBufferPtr = new PoseMatricesFrameBuffer();
    LOAD_SKELETON_COMPUTE_SHADER(SkinningComputeShader);
    LOAD_SKELETON_COMPUTE_SHADER(SkinningPrePosComputeShader);
}

SkeletonSystemR::SkeletonSystemR() {}

SkeletonSystemR::~SkeletonSystemR() {}

void SkeletonSystemR::OnFirstUpdate(FrameParam* frameParam)
{
    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    if (settingMgr->GetGPUSkinEnable())
        mPoseRingBufferPtr->Initialize();
}

void SkeletonSystemR::OnBeginFrame(FrameParam* frameParam)
{
    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    if (settingMgr->GetGPUSkinEnable())
        mPoseRingBufferPtr->OnRenderFrameBegin();

    mPrimaryModelToBufferOffset.clear();
}

void SkeletonSystemR::OnEndFrame(FrameParam* frameParam) {
    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    if (settingMgr->GetGPUSkinEnable())
        mPoseRingBufferPtr->OnRenderFrameEnd();
}

void SkeletonSystemR::Release()
{
    delete this;
}

void SkeletonSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [this] 
    {
        SCOPED_CPU_TIMING(GroupDispatch, "SkeletonUpdateR");

        MeshBuildSystemR* meshBuilderSys = mRenderWorld->GetRenderSystem<MeshBuildSystemR>();
        ModelSystemR* modelSys = mRenderWorld->GetRenderSystem<ModelSystemR>();
        CameraSystemR* cameraSys = mRenderWorld->GetRenderSystem<CameraSystemR>();
        AABBSystemR* aabbSys = mRenderWorld->GetRenderSystem<AABBSystemR>();
        TransformSystemR* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();

        auto mainCameraE = cameraSys->GetMainCamera();
        if (mRenderWorld->IsEntityAlive(mainCameraE) == false)
            return;

        auto&& [mainCameraComp, mainTransComp, cameraMetaComp] = mRenderWorld->GetComponent<CameraComponentR, TransformComponentR, ecs::EntityMetaComponentG>(mainCameraE);
        auto mainCameraInst = cameraSys->GetRenderCamera(mainCameraComp.Read());
        auto mainCameraView = mainCameraInst->GetCameraView();

#if defined(CE_USE_DOUBLE_TRANSFORM) 
        auto mainCameraTileComp = mRenderWorld->GetComponent<TilePositionComponentR>(mainCameraE);
        auto mainCameraTileIndex = transformSys->GetTilePosition(mainCameraTileComp.Read());
        auto mainCameraTileOffset = transformSys->GetWorldTranslation(mainTransComp.Read());

        Double3 mainCameraWorldPosition = GetAbsolutePosition(mainCameraTileIndex, mainCameraTileOffset);
#else
        Float3 mainCameraWorldPosition = transformSys->GetWorldTranslation(transComp.Read());
#endif

        // Dispatch Refresh model component's pose buffer in following MeshBuildSystemR 
        auto queryResult = mRenderWorld->Query<
            SkeletonComponentR,         // required for dispatch pose to model 
            ModelComponentR,            // required for skinning target
            ecs::EntityIDComponent,     // required for debug
            AABBComponentR,             // required for culling
            TransformComponentR,        // required for culling
            RenderNodeComponentR>();    // required for culling
        for (auto&& [skComp, modelComp, entityIDComp, aabbComp, transComp, renderNodeComp] : queryResult)
        {
            // for now, we assume model is always enabled
            if (!skComp.Read()->mEnable /*|| !modelComp.Read()->mEnable*/)
                continue;

            // guarantee a valid mesh builder exists
            if ((UInt32)(modelSys->GetBatchInfo(modelComp.Read()).GetBatchFlag()) == 0)
                continue;

            // static mesh should not come into here
            if (!modelSys->IsSkeletalModel(modelComp.Read()))
                continue;

            // culling with main camera frustum
            auto aabb = aabbSys->GetWorldAABB(aabbComp.Read());   // in entity's tile space if large coordinate activated
            auto aabbRadius = aabb.GetExtent().Length();

#if defined(CE_USE_DOUBLE_TRANSFORM) 
            auto tilePositionComp = mRenderWorld->GetComponent<TilePositionComponentR>(transComp.GetEntityID());
            auto tileIndex = transformSys->GetTilePosition(tilePositionComp.Read());
            auto tileOffset = transformSys->GetWorldTranslation(transComp.Read());
            Double3 worldPosition = GetAbsolutePosition(tileIndex, aabb.GetCenter());
#else
            Float3 worldPosition = transformSys->GetWorldTranslation(transComp.Read());
#endif

            Double3 entityPositionInCameraTileSpace = GetRelativePosition(worldPosition, mainCameraTileIndex);
            Float3 entityPositionInCameraSpace = Float4x4::TransformPointF3(mainCameraView.mViewMatrix, static_cast<Float3>(entityPositionInCameraTileSpace));
            auto entityBoundingInCameraSpace = BoundingBox(entityPositionInCameraSpace, aabb.GetExtent());
            // All models with skeleton components are skinned once whether or not they are culling by the camera.
            if (mainCameraView.mFrustum.Contains(entityBoundingInCameraSpace) == false && modelSys->GetModelSkinLevel(modelComp.Read(), 0) == SkinLevel::CurLod)
                continue;

            // skinned mesh should got an opportunity to refresh lod levels & mesh properties
            auto entity = entityIDComp.Read()->mEntityID;
            meshBuilderSys->BuildMesh(entity, modelSys->GetBatchInfo(modelComp.Read()));
        }
    });
}

void SkeletonSystemR::NotifyShutDownEngine()
{
    SAFE_DELETE(mPoseRingBufferPtr);
}

void SkeletonSystemR::UpdatePoseStagingBuffer(VertexStreamLayoutSkinParameter const& inSkinParam, threading::TaskEventArray& outTaskEventArray) 
{
    Assert(inSkinParam.SkinMode != GeomertySkinnedMode::CpuSkin);

    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    if (settingMgr->GetGPUSkinEnable() == false)
        return;

    auto poseOffsetH = mPoseRingBufferPtr->UpdatePoseStagingBuffer(inSkinParam.SkinningMatrices, inSkinParam.AllSubModelMaterials, inSkinParam.SkinMode, outTaskEventArray);

    PoseMatricesBufferHandle poseBufferH(&inSkinParam);
    Assert(mPrimaryModelToBufferOffset.find(poseBufferH) == mPrimaryModelToBufferOffset.end());
    mPrimaryModelToBufferOffset.emplace(poseBufferH, poseOffsetH);
}

void SkeletonSystemR::UploadPoseStroagingffer()
{
    auto skSys = static_cast<SkeletonSystemR*>(mRenderWorld->GetRenderSystem<SkeletonSystemR>());
    auto renderSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // Upload gpu storage buffer from above staging buffer.
    auto poseStagingBuffer = skSys->GetPoseStagingBuffer();
    auto poseStorageBuffer = skSys->GetPoseStorageBuffer();

    if (poseStorageBuffer != nullptr && poseStorageBuffer != nullptr && poseStagingBuffer.Cursor != 0)
    {
        NGICopyBuffer region{poseStagingBuffer.BufferWrap.GetNGIOffset(), 0, poseStagingBuffer.Cursor * sizeof(float)};
        renderSys->UpdateBuffer(poseStorageBuffer, poseStagingBuffer.BufferWrap.GetNGIBuffer(), region, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    }

    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    if (settingMgr->GetGPUSkinEnable())
        mPoseRingBufferPtr->OnRenderFrameEnd();
}

}   // namespace cross



//// Create Texture & view for matrices
//void SkeletonSystemR::InitGPUSkin(UInt32 maxMatrixCount)
//{
//    NGITextureDesc desc{GraphicsFormat::R32_SFloat, NGITextureType::Texture2D, 1, 1, MaxPoseTextureWidth, MaxPoseTextureHeight, 1, 1, NGITextureUsage::CopyDst | NGITextureUsage ::ShaderResource};
//    NGITextureViewDesc viewDesc{NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
//                                GraphicsFormat::R32_SFloat,
//                                NGITextureType::Texture2D,
//                                {
//                                    NGITextureAspect::Color,
//                                    0,
//                                    1,
//                                    0,
//                                    1,
//                                }};
//
//    for (int i = 0; i < mBoneMatrixTexture.size(); i++)
//    {
//        mBoneMatrixTexture[i].reset(GetNGIDevice().CreateTexture(desc, "BoneTexture"));
//        mBoneMatrixViews[i].reset(GetNGIDevice().CreateTextureView(mBoneMatrixTexture[i].get(), viewDesc));
//    }
//}


// Call once per frame, no abuse
//NGITextureView* SkeletonSystemR::GetBoneTextureView()
//{
//    if (mSkinDirty)
//    {
//        prevTexID++;
//        prevTexID %= TextureRingSize;
//
//        // UpdateBoneTexture
//        NGICopyBufferTexture region{
//            mStagingBufferOffset,
//            mStagingBufferSize,
//            MaxPoseTextureWidth * sizeof(float),
//
//            NGICalcSubresource(0, 0, 0, 1, 1),
//            {
//                0,
//                0,
//                0,
//            },
//            {MaxPoseTextureWidth, MaxPoseTextureHeight, 1},
//        };
//
//        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
//        rendererSystem->UpdateTexture(mBoneMatrixTexture[prevTexID].get(), mStagingBuffer, region, NGIResourceState::Undefined, NGIResourceState::ShaderResourceBit | NGIResourceState::VertexShaderBit);
//
//        mSkinDirty = false;
//    }
//    return mBoneMatrixViews.at(prevTexID).get();
//}
