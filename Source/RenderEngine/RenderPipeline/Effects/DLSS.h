#pragma once

#include "PassBase.h"
#include "PlatformDefs.h"
#ifdef WIN32
// Streamline Core
#include <sl.h>
#include <sl_consts.h>
#include <sl_hooks.h>
#include <sl_version.h>

// Streamline Features
#include <sl_dlss.h>
#include <sl_reflex.h>
#include <sl_nis.h>
#include <sl_dlss_g.h>
#include <sl_deepdvc.h>
#endif
//crossengine
#include "RenderEngine/RenderPipelineSystemR.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/SLWrapper.h"
#include"CrossBase/Math/CrossMath.h"
#include "RenderEngine/ComputeShaderR.h"
#include "Resource/Resource.h"
 namespace cross {
#ifdef WIN32
 // Set this to a game's specific sdk version
 static constexpr uint64_t SDK_VERSION = sl::kSDKVersion;

 // We define a few functions to help with format conversion
 inline sl::float2 make_sl_float2(cross::Float2 ceF)
 {
     return sl::float2{ceF.x, ceF.y};
 }
 inline sl::float3 make_sl_float3(cross::Float3 ceF)
 {
     return sl::float3{ceF.x, ceF.y, ceF.z};
 }
 inline sl::float4 make_sl_float4(cross::Float4 ceF)
 {
     return sl::float4{ceF.x, ceF.y, ceF.z, ceF.w};
 }
 inline sl::float4x4 make_sl_float4x4(cross::Float4x4 ceF4x4)
 {
     sl::float4x4 outF4x4;

     outF4x4.setRow(0, make_sl_float4(cross::Float4(ceF4x4.m00, ceF4x4.m01, ceF4x4.m02, ceF4x4.m03)));
     outF4x4.setRow(1, make_sl_float4(cross::Float4(ceF4x4.m10, ceF4x4.m11, ceF4x4.m12, ceF4x4.m13)));
     outF4x4.setRow(2, make_sl_float4(cross::Float4(ceF4x4.m20, ceF4x4.m21, ceF4x4.m22, ceF4x4.m23)));
     outF4x4.setRow(3, make_sl_float4(cross::Float4(ceF4x4.m30, ceF4x4.m31, ceF4x4.m32, ceF4x4.m33)));
     return outF4x4;
 }
#endif
 enum class CEMeta(Editor) DLSSMode
 {
     eOff,
     eUltraPerformance,
     eMaxPerformance,
     eBalanced,
     eMaxQuality,
     eDLAA,
 };

 enum class CEMeta(Editor) DLSSPreset
 {
     //! Default behavior, may or may not change after an OTA
     eDefault,
     //! Fixed DL models
     ePresetA,
     ePresetB,
     ePresetC,
     ePresetD,
     ePresetE,
     ePresetF,
     ePresetG,
 };

 class CEMeta(Editor, PartOf(FFSRenderPipelineSetting)) RENDER_ENGINE_API DLSSSetting : public PassSetting
 {
 public:
     DLSSSetting()
     {
#ifdef WIN32
         if (SLWrapper::Get().GetDLSSAvailable())
            enable = true;
         else
#endif
             enable = false;
     }
     CE_Virtual_Serialize_Deserialize;
     RENDER_PIPELINE_RESOURCE(ComputeShader, DLSSComputeShader, "PipelineResource/FFSRP/Shader/Features/DLSS/DLSS.compute.nda", "Get MotionVector For DLSS", "", "");
     RENDER_PIPELINE_RESOURCE(ComputeShader, MergeDepthComputeShader, "PipelineResource/FFSRP/Shader/Features/DLSS/DLSS.compute.nda", "Merge Depth For DLSS", "", "");


     void Initialize() override{
         LOAD_RENDER_PIPELINE_COMPUTE_SHADER(DLSSComputeShader);
     }

     float GetUpscaleValue(ViewType type) const
     {
         if (type == ViewType::GameView || type == ViewType::SceneView || type == ViewType::MaterialEditorPreview)
         {
             switch (DLSS_Mode)
             {
             case DLSSMode::eUltraPerformance:
                 return 3.0f;
             case DLSSMode::eMaxPerformance:
                 return 2.0f;
             case DLSSMode::eBalanced:
                 return 1.72f;
             case DLSSMode::eMaxQuality:
                 return 1.5f;
             default:
                 return 1.0f;
             }
         }
         else
         {
             return 1.0f;
         }
     }

#ifdef WIN32
     sl::Boolean IsColorBuffersHDR()const
     { return colorBuffersHDR ? sl::Boolean::eTrue : sl::Boolean::eFalse;
     }

     sl::Boolean IsUseAutoExposure() const { return useAutoExposure ? sl::Boolean::eTrue : sl::Boolean::eFalse; }
#endif 
     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     DLSSMode DLSS_Mode = DLSSMode::eOff;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "When boost mode is enabled, DLSS will always perform 3x upscale, then downscale to target resolution"))
     bool bBoostMode{false};

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     float sharpness = 0.0f;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     float preExposure = 1.0f;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     float exposureScale = 1.0f;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     bool colorBuffersHDR = true;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     bool indicatorInvertAxisX = false;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     bool indicatorInvertAxisY = false;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     DLSSPreset dlaaPreset = DLSSPreset::eDefault;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     DLSSPreset qualityPreset = DLSSPreset::eDefault;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     DLSSPreset balancedPreset = DLSSPreset::eDefault;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     DLSSPreset performancePreset = DLSSPreset::eDefault;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     DLSSPreset ultraPerformancePreset = DLSSPreset::eDefault;


     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     bool useAutoExposure = false;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     bool alphaUpscalingEnabled = false;

     CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
     bool mSeparateTranslucencyBeforeDLSS = false;
 };

#ifdef WIN32
 class RENDER_ENGINE_API DLSSPass
 {
 public:
     constexpr std::string_view GetPassName()
     {
         return std::string_view("DLSS");
     }
     void FillInput(const GameContext& gameContext);
     void Execute(const GameContext& gameContext);

     void Init(const GameContext& gameContext);
 private:
     //std::shared_ptr<sl::ViewportHandle> m_viewport = std::make_shared<sl::ViewportHandle>(0) ;
     DLSSSetting mSetting = {};
     sl::DLSSOptimalSettings dlssSettings = {};

     sl::DLSSOptions dlssOptions = {};
     SLWrapper::DLSSSettings recommendedDLSSSettings;

     UInt2 m_RenderingRectSize = {0, 0};
     UInt2 m_DisplaySize;


     DLSSPreset DLSS_last_presets[5] = {DLSSPreset::eDefault};

     DLSSMode DLSS_Last_Mode = DLSSMode::eOff;

     UInt2 DLSS_Last_DisplaySize = {0, 0};

     bool lastEnable = false;

     bool CheckDLSSPresentChanged(const DLSSSetting setting)
     {
         if (DLSS_last_presets[0] != setting.dlaaPreset || DLSS_last_presets[1] != setting.qualityPreset || DLSS_last_presets[2] != setting.balancedPreset || DLSS_last_presets[3] != setting.performancePreset ||
             DLSS_last_presets[4] != setting.ultraPerformancePreset)
         {
             DLSS_last_presets[0] = setting.dlaaPreset;
             DLSS_last_presets[1] = setting.qualityPreset;
             DLSS_last_presets[2] = setting.balancedPreset;
             DLSS_last_presets[3] = setting.performancePreset;
             DLSS_last_presets[4] = setting.ultraPerformancePreset;
             return true;
         }
         return false;
     }

 };
#endif
 }   // namespace cross
