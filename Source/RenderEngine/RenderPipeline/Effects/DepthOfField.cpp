#include "DepthOfField.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "Resource/AssetStreaming.h"

namespace cross {
void DepthOfFieldSetting::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(DepthOfFieldAutoFocusComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(DepthOfFieldComputeShader);
}
void DepthOfFieldInput::GenerateInputData(const GameContext& gameContext) 
{
    inputcolor = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
    depthstencilview = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>();
}

void DepthOfFieldOutput::SetOutputData(const GameContext& gameContext) 
{
    gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = outputcolor;
}

void DepthOfField::FillInput(const GameContext& gameContext) 
{
    mInput.GenerateInputData(gameContext);
    mSetting = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mDepthOfFieldSetting;
    auto ffspipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    if (!ffspipe)
        return;
    mDepthOfFieldAutoFocusComputeShader = ffspipe->GetSetting()->mDepthOfFieldSetting.DepthOfFieldAutoFocusComputeShaderR;
    mDepthOfFieldComputeShader = ffspipe->GetSetting()->mDepthOfFieldSetting.DepthOfFieldComputeShaderR;
}

void DepthOfField::Execute(const GameContext& gameContext)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    if (mSetting.enable)
    {
        auto gameViewWidth = mInput.inputcolor->mTexture->mDesc.Width;
        auto gameViewHeight = mInput.inputcolor->mTexture->mDesc.Height;

        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        auto* depthView = red->AllocateTextureView(mInput.depthstencilview->mTexture,
                                                   NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                      mInput.depthstencilview->mDesc.Format,
                                                                      NGITextureType::Texture2D,
                                                                      NGITextureSubRange{
                                                                          NGITextureAspect::Depth,
                                                                          0,
                                                                          1,
                                                                          0,
                                                                          1,
                                                                      }});

        /*REDTextureView* AutoFocusTexView = IRenderPipeline::CreateTextureView2D("AutoFocus Texture",
            static_cast<UInt16>(2), static_cast<UInt16>(1), GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);*/

        REDTextureView* AutoFocusTexView;
        NGITextureDesc focusTexDesc{
            GraphicsFormat::R16G16B16A16_SFloat,
            NGITextureType::Texture2D,
            1,
            1,
            2,
            1,
            1,
            1,
            NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
        };
        NGITextureViewDesc focusTexViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
                                            GraphicsFormat::R16G16B16A16_SFloat,
                                            NGITextureType::Texture2D,
                                            {
                                                NGITextureAspect::Color,
                                                0,
                                                1,
                                                0,
                                                1,
                                            }};
        if (red->Validate(mAutoFocusTex))
        {
            AutoFocusTexView = red->AllocateTextureView(mAutoFocusTex, focusTexViewDesc);
        }
        else
        {
            auto* exposureTex = red->AllocateTexture("Auto Focus Texture", focusTexDesc);
            AutoFocusTexView = red->AllocateTextureView(exposureTex, focusTexViewDesc);
            NGIClearValue clearValue{{0, 0, 0, 0}};
            red->AllocatePass("Clear Auto Focus Texture")->ClearTexture(AutoFocusTexView, clearValue);
        }
        mAutoFocusTex = red->AllocateTexture("AutoFocus Texture", focusTexDesc);
        mAutoFocusTex->ExtendLifetime();

        auto* CoCView = IRenderPipeline::CreateTextureView2D(
            "CoC Texture", static_cast<UInt16>(gameViewWidth), static_cast<UInt16>(gameViewHeight), GraphicsFormat::R16_SFloat /*GraphicsFormat::R8_UNorm*/, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        auto* DOFView = IRenderPipeline::CreateTextureView2D(
            "DOF Texture", static_cast<UInt16>(gameViewWidth / 2), static_cast<UInt16>(gameViewHeight / 2), GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        auto* DOFTempView = IRenderPipeline::CreateTextureView2D(
            "DOF Temp Texture", static_cast<UInt16>(gameViewWidth / 2), static_cast<UInt16>(gameViewHeight / 2), GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        mOutput.outputcolor = IRenderPipeline::CreateTextureView2D(
            "DOF Output Texture", static_cast<UInt16>(gameViewWidth), static_cast<UInt16>(gameViewHeight), GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        Float2 texelSize{1.f / gameViewWidth, 1.f / gameViewHeight};
        float nearPlane = gameContext.mRenderCamera->GetNearPlane();
        float farPlane = gameContext.mRenderCamera->GetFarPlane();
        Float4x4 projMat = gameContext.mRenderCamera->GetProjMatrix();
        Float4 fullTexelSize = Float4(texelSize.x, texelSize.y, -texelSize.x, -texelSize.y);
        Float4 doubleTexelSize = Float4(texelSize.x * 2.0f, texelSize.y * 2.0f, -texelSize.x * 2.0f, -texelSize.y * 2.0f);
        Float4 ZParamsPack = Float4(1.0f - farPlane / nearPlane, farPlane / nearPlane, 1.0f / farPlane - 1.0f / nearPlane, 1.0f / nearPlane);   // x = 1 - f / n      y = f/n     z = 1/f - 1/n     w = 1/n
        float _ApertureFilmHeightx2 = mSetting.Aperture * mSetting.FilmHeight * 2;
        // TODO(anonymous): Time.deltaTime
        float _DeltaTime = 0.3333f;

        ComputeShaderR* mDepthOfFieldCS = nullptr;
        if (mSetting.DOFAutoFocus)
        {
            mDepthOfFieldCS = mDepthOfFieldAutoFocusComputeShader;
            // AutoFocus pass
            auto* AutoFocusPass = red->AllocatePass("auto_focus");
            AutoFocusPass->SetProperty(NAME_ID("_FocalLength"), mSetting.FocalLength);
            AutoFocusPass->SetProperty(NAME_ID("_ApertureFilmHeightx2"), _ApertureFilmHeightx2);
            AutoFocusPass->SetProperty(NAME_ID("_SmoothTime"), mSetting.SmoothTime);
            AutoFocusPass->SetProperty(NAME_ID("_DeltaTime"), _DeltaTime);
            AutoFocusPass->SetProperty(NAME_ID("_VoteBias"), mSetting.VoteBias);
            AutoFocusPass->SetProperty(NAME_ID("_FixedFocusDistance"), mSetting.FixedFocusDistance);
            AutoFocusPass->SetProperty(NAME_ID("_Influence"), static_cast<float>(mSetting.AutoInfluenceType));
            AutoFocusPass->SetProperty(NAME_ID("_ProjMat"), projMat);
            AutoFocusPass->SetProperty(NAME_ID("_DepthMap"), depthView);
            AutoFocusPass->SetProperty(NAME_ID("_AutoFocusRWTex"), AutoFocusTexView);
            AutoFocusPass->Dispatch(mDepthOfFieldCS, "CSFocus", 1, 1, 1);
        }
        else
        {
            mDepthOfFieldCS = mDepthOfFieldComputeShader;
        }

        // pass1:CoC calculation
        auto* cocCalculationPass = red->AllocatePass("dof_coc_calc");
        cocCalculationPass->SetProperty(NAME_ID("_DepthMap"), depthView);
        cocCalculationPass->SetProperty(NAME_ID("_ProjMatrix"), projMat);
        cocCalculationPass->SetProperty(NAME_ID("_MainTex_TexelSize"), fullTexelSize);
        cocCalculationPass->SetProperty(NAME_ID("_CoCDst"), CoCView);
        cocCalculationPass->SetProperty(NAME_ID("_ZBufferParams"), ZParamsPack);
        cocCalculationPass->SetProperty(NAME_ID("_ProjMat"), projMat);
        // MtlParams
        cocCalculationPass->SetProperty(NAME_ID("_CoCRadiusInPixels"), mSetting.BlurRadius);
        if (mSetting.DOFAutoFocus)
        {
            cocCalculationPass->SetProperty(NAME_ID("_AutoFocusRWTex"), AutoFocusTexView);
        }
        else
        {
            cocCalculationPass->SetProperty(NAME_ID("_FocalLength"), mSetting.FocalLength);
            cocCalculationPass->SetProperty(NAME_ID("_Aperture"), mSetting.Aperture);
            cocCalculationPass->SetProperty(NAME_ID("_FocusDistance"), mSetting.FocusDistance);
            cocCalculationPass->SetProperty(NAME_ID("_FilmHeight"), mSetting.FilmHeight);
        }
        UInt3 groupSize_pass1;
        mDepthOfFieldCS->GetThreadGroupSize("CSCoC", groupSize_pass1.x, groupSize_pass1.y, groupSize_pass1.z);
        cocCalculationPass->Dispatch(mDepthOfFieldCS, "CSCoC", (gameViewWidth + groupSize_pass1.x - 1) / groupSize_pass1.x, (gameViewHeight + groupSize_pass1.y - 1) / groupSize_pass1.y, 1);

        // pass2:Prefilter: downsampling and premultiplying
        auto* downsamplePrefilterPass = red->AllocatePass("dof_downsample_prefilter");
        downsamplePrefilterPass->SetProperty(NAME_ID("_MainMap"), mInput.inputcolor);
        downsamplePrefilterPass->SetProperty(NAME_ID("_CoCMap"), CoCView);
        downsamplePrefilterPass->SetProperty(NAME_ID("_ZBufferParams"), ZParamsPack);
        downsamplePrefilterPass->SetProperty(NAME_ID("_MainTex_TexelSize"), doubleTexelSize);
        downsamplePrefilterPass->SetProperty(NAME_ID("_DOFDst"), DOFView);
        // MtlParams
        downsamplePrefilterPass->SetProperty(NAME_ID("_CoCRadiusInPixels"), mSetting.BlurRadius);
        if (mSetting.DOFAutoFocus)
        {
            downsamplePrefilterPass->SetProperty(NAME_ID("_AutoFocusRWTex"), AutoFocusTexView);
        }
        else
        {
            downsamplePrefilterPass->SetProperty(NAME_ID("_FocalLength"), mSetting.FocalLength);
            downsamplePrefilterPass->SetProperty(NAME_ID("_Aperture"), mSetting.Aperture);
            downsamplePrefilterPass->SetProperty(NAME_ID("_FocusDistance"), mSetting.FocusDistance);
            downsamplePrefilterPass->SetProperty(NAME_ID("_FilmHeight"), mSetting.FilmHeight);
        }
        UInt3 groupSize_pass2;
        mDepthOfFieldCS->GetThreadGroupSize("CSPrefilter", groupSize_pass2.x, groupSize_pass2.y, groupSize_pass2.z);
        downsamplePrefilterPass->Dispatch(mDepthOfFieldCS, "CSPrefilter", (gameViewWidth / 2 + groupSize_pass2.x - 1) / groupSize_pass2.x, (gameViewHeight / 2 + groupSize_pass2.y - 1) / groupSize_pass2.y, 1);

        // pass3:Bokeh Filter(Medium Blur kernel)
        auto* blurPass = red->AllocatePass("dof_bokeh_blur");
        blurPass->SetProperty(NAME_ID("_DOFMap"), DOFView);
        blurPass->SetProperty(NAME_ID("_ZBufferParams"), ZParamsPack);
        blurPass->SetProperty(NAME_ID("_MainTex_TexelSize"), doubleTexelSize);
        blurPass->SetProperty(NAME_ID("_DOFTempDst"), DOFTempView);
        // MtlParams
        blurPass->SetProperty(NAME_ID("_CoCRadiusInPixels"), mSetting.BlurRadius);
        if (mSetting.DOFAutoFocus)
        {
            blurPass->SetProperty(NAME_ID("_AutoFocusRWTex"), AutoFocusTexView);
        }
        else
        {
            blurPass->SetProperty(NAME_ID("_FocalLength"), mSetting.FocalLength);
            blurPass->SetProperty(NAME_ID("_Aperture"), mSetting.Aperture);
            blurPass->SetProperty(NAME_ID("_FocusDistance"), mSetting.FocusDistance);
            blurPass->SetProperty(NAME_ID("_FilmHeight"), mSetting.FilmHeight);
        }
        UInt3 groupSize_pass3;
        mDepthOfFieldCS->GetThreadGroupSize("CSBlur", groupSize_pass3.x, groupSize_pass3.y, groupSize_pass3.z);
        blurPass->Dispatch(mDepthOfFieldCS, "CSBlur", (gameViewWidth / 2 + groupSize_pass3.x - 1) / groupSize_pass3.x, (gameViewHeight / 2 + groupSize_pass3.y - 1) / groupSize_pass3.y, 1);

        // pass4:Postfilter blur
        auto* postBlurPass = red->AllocatePass("dof_post_blur");
        postBlurPass->SetProperty(NAME_ID("_DOFTempMap"), DOFTempView);
        postBlurPass->SetProperty(NAME_ID("_ZBufferParams"), ZParamsPack);
        postBlurPass->SetProperty(NAME_ID("_MainTex_TexelSize"), doubleTexelSize);
        postBlurPass->SetProperty(NAME_ID("_DOFDst"), DOFView);
        // MtlParams
        postBlurPass->SetProperty(NAME_ID("_CoCRadiusInPixels"), mSetting.BlurRadius);
        if (mSetting.DOFAutoFocus)
        {
            postBlurPass->SetProperty(NAME_ID("_AutoFocusRWTex"), AutoFocusTexView);
        }
        else
        {
            postBlurPass->SetProperty(NAME_ID("_FocalLength"), mSetting.FocalLength);
            postBlurPass->SetProperty(NAME_ID("_Aperture"), mSetting.Aperture);
            postBlurPass->SetProperty(NAME_ID("_FocusDistance"), mSetting.FocusDistance);
            postBlurPass->SetProperty(NAME_ID("_FilmHeight"), mSetting.FilmHeight);
        }
        UInt3 groupSize_pass4;
        mDepthOfFieldCS->GetThreadGroupSize("CSPostBlur", groupSize_pass4.x, groupSize_pass4.y, groupSize_pass4.z);
        postBlurPass->Dispatch(mDepthOfFieldCS, "CSPostBlur", (gameViewWidth / 2 + groupSize_pass4.x - 1) / groupSize_pass4.x, (gameViewHeight / 2 + groupSize_pass4.y - 1) / groupSize_pass4.y, 1);

        // pass5:Combine with source
        auto* combinePass = red->AllocatePass("dof_combine");
        combinePass->SetProperty(NAME_ID("_MainMap"), mInput.inputcolor);
        combinePass->SetProperty(NAME_ID("_CoCMap"), CoCView);
        combinePass->SetProperty(NAME_ID("_DOFMap"), DOFView);
        combinePass->SetProperty(NAME_ID("_ZBufferParams"), ZParamsPack);
        combinePass->SetProperty(NAME_ID("_MainTex_TexelSize"), fullTexelSize);
        combinePass->SetProperty(NAME_ID("_OutputDst"), mOutput.outputcolor);
        // MtlParams
        combinePass->SetProperty(NAME_ID("_CoCRadiusInPixels"), mSetting.BlurRadius);
        if (mSetting.DOFAutoFocus)
        {
            combinePass->SetProperty(NAME_ID("_AutoFocusRWTex"), AutoFocusTexView);
        }
        else
        {
            combinePass->SetProperty(NAME_ID("_FocalLength"), mSetting.FocalLength);
            combinePass->SetProperty(NAME_ID("_Aperture"), mSetting.Aperture);
            combinePass->SetProperty(NAME_ID("_FocusDistance"), mSetting.FocusDistance);
            combinePass->SetProperty(NAME_ID("_FilmHeight"), mSetting.FilmHeight);
        }
        UInt3 groupSize_pass5;
        mDepthOfFieldCS->GetThreadGroupSize("CSCombine", groupSize_pass5.x, groupSize_pass5.y, groupSize_pass5.z);
        combinePass->Dispatch(mDepthOfFieldCS, "CSCombine", (gameViewWidth + groupSize_pass5.x - 1) / groupSize_pass5.x, (gameViewHeight + groupSize_pass5.y - 1) / groupSize_pass5.y, 1);
    }
    else
    {
        mOutput.outputcolor = mInput.inputcolor;
    }
    mOutput.SetOutputData(gameContext);
}
}   // namespace cross

