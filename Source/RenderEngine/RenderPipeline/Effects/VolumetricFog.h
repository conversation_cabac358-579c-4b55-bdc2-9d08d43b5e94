#pragma once
#include "PassBase.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
namespace cross {

struct CEMeta(Editor, WorkflowType, Cli, Puerts) RENDER_ENGINE_API FogCommonSetting
{
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float Density = 0.05f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float HeightFallOff = 0.0005f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float HeightOffset = 0.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float DensitySecond = 0.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float HeightFallOffSecond = 0.5f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float HeightOffsetSecond = 0.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float StartDistance{10.f};
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float CutOffDistance{100000.f};
    CEMeta(Serialize, Editor, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool UseWGS84 = false;
    CEMeta(Serialize, Editor, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool CloudyAtomsphere = false;
    CE_Virtual_Serialize_Deserialize;
};

struct CEMeta(Editor, Reflect, Cli, Puerts) RENDER_ENGINE_API ScreenFogDirectionalSetting
{
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Float3AsColor", ToolTips = "", bKeyFrame = true)) Float3 DirectionalInscatter{Float3(0.f, 0.f, 0.f)};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float AtomsphereContribution{1.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float DirectionalIntensity{1.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float InscatterExponent{4.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float DirectionalStartDistance{10000.f};
    CE_Virtual_Serialize_Deserialize;
};

struct CEMeta(Editor, WorkflowType, Cli, Puerts) RENDER_ENGINE_API ScreenFogSetting
{
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) float MaxFogOpacity{1.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Fog Color", bKeyFrame = true)) Float3 Inscatter{Float3(1.f, 1.f, 1.f)};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Float3AsColor", ToolTips = "Fog Color for other use, such as VocanicAsh", bKeyFrame = true)) Float3 InscatterSecond{Float3(1.f, 1.f, 1.f)};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "")) ScreenFogDirectionalSetting Directional;

    CE_Virtual_Serialize_Deserialize;
};


struct CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API FFSWeatherLightMaterialSetting
{
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) float Scale_Size_Offset{0.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) float Scale_Gain_Offset{0.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) float Intensity_Offset{0.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) float Intensity_Gain_Offset{0.f};

    CE_Serialize_Deserialize;
};

enum class FogDistributionType
{
    SOLID_FOG = 0,
    PATCHY_FOG = 1
};

struct CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API FFSFogSetting
{
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool enable = false;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float RVR{1000.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float RVR_Min{1000.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float RVR_Max{1000.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float RVRFogTop{2000.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float FogBaseHeight{0.f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float BlwCldVis{24900000.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float BtmCldBase{0.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float BtwCldVis{24900000.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float MidCldBase{0.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float AbvCldVis{24900000.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float FogTop{360000.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float ClearFogProportion{0.2f};

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float VocanicAshModifier{0.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float PatchyFogFactor{1.0f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float StrobeDisExponent{0.3f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float StrobeDisFallOff{0.1f};

    // TODO : should not remian in engine code
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting RunWay_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting VASI_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting ALS_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting Strobe_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting Dir_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting DirSurround_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting OmniDir_Light;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSWeatherLightMaterialSetting OmniDirSurround_Light;
    CE_Virtual_Serialize_Deserialize;
    FogDistributionType FogDistribution{FogDistributionType::SOLID_FOG};
};

struct CEMeta(Editor, Reflect, WorkflowType, Cli, Puerts) RENDER_ENGINE_API VFogQualityTradeSetting
{
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    int MaxLight = 20;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float MultiSampleNum = 4.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float MultiSampleJitter = 0.5f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool UseBlur = true;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float BlurSize = 8.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float BlurStrength = 1.f;

    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float ResolutionRatio = 5.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float ZSliceNum = 128.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float ZVoxelScale = 128.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool Temporal = true;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = ""))
    float HistoryWeight = 0.9f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool LightInjection = true;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool TurnOffDirectionalLight = false;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool CpuLightProjection = false;

    CE_Virtual_Serialize_Deserialize;
};

struct CEMeta(Editor, Reflect, WorkflowType, Cli, Puerts) RENDER_ENGINE_API VFogDustSetting
{
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool enable = false;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Float3AsColor", ToolTips = "", bKeyFrame = true))
    Float3 DustAlbedo{Float3(1.f, 1.f, 1.f)};
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float DustDensity = 1.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float DustScale = 1.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float Height = 2000.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float Spiral = 0.5f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", ValueMin = "0.0", ValueMax = "1.0", bKeyFrame = true))
    float LightAbsorb = 0.f;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    Float3 Wind = Float3(0.f, 0.f, 0.f);
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool DustOverride = false;
    CEMeta(Serialize, Editor, ScriptReadWrite, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", ValueMin = "0.0", ValueMax = "1.0", bKeyFrame = true))
    float SFogLightFactor = 1.f;

    CE_Virtual_Serialize_Deserialize;
};

struct CEMeta(Editor, Reflect, WorkflowType, Cli, Puerts) RENDER_ENGINE_API VolumetricFogSetting
{
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool enable = false;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool EnableAdaptiveVolume = false;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float AdaptiveSpeed = 0.1f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float AdaptiveVolumeMinLength = 10000.f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float AdaptiveVolumeMaxLength = 100000.f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float StartDistance = 0.f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float CutOffDistance = 6000.f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Float3AsColor", ToolTips = "", bKeyFrame = true)) Float3 Albedo{Float3(1.f, 1.f, 1.f)};

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float ExtinctionScale = 1.f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float AtomsphereFactor = 1.f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float MiePhase1 = 0.75f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true))
    float MiePhase2 = 0.75f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true))
    float LightVolumetricFactor = 1.0f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool BoundingFadeOut = false;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    float FadeOutDistance = 6000.f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool CloudShadow = true;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    VFogQualityTradeSetting QualityTrade;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    VFogDustSetting Dust;

    CE_Virtual_Serialize_Deserialize;
};

class CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API FogSetting : public PassSetting
{
public:
    FogSetting()
    {
        enable = false;
    }
    float mTargetWidthRatio = 1.0f;
    float mTargetHeightRatio = 1.0f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FogCommonSetting FogCommon;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    ScreenFogSetting SFog;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    VolumetricFogSetting VFog;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = ""))
    FFSFogSetting ffsTempTest;
    virtual void Initialize() override{};
    CE_Virtual_Serialize_Deserialize;
};

struct VolumetricFogParam
{
public:
    PassFloatParam FogExpParam{NAME_ID("FogParam"), 0.f};
    PassFloatParam FogExpParamSecond{NAME_ID("FogParamSecond"), 0.f};

    PassFloat3Param FogGridSize{NAME_ID("FogGridSize"), Float3()};
    PassFloatParam FogDensity{NAME_ID("FogDensity"), 0.f};
    PassFloatParam FogHeightFallOff{NAME_ID("FogHeightFallOff"), 0.f};
    PassFloatParam FogHeight{NAME_ID("FogHeight"), 0.0f};

    PassFloatParam FogDensitySecond{NAME_ID("FogDensitySecond"), 0.f};
    PassFloatParam FogHeightFallOffSecond{NAME_ID("FogHeightFallOffSecond"), 0.f};
    PassFloatParam FogHeightSecond{NAME_ID("FogHeightSecond"), 0.0f};

    PassFloatParam FogNear{NAME_ID("VFogNear"), 0.0f};
    PassFloatParam FogFar{NAME_ID("VFogFar"), 0.0f};
    PassFloat3Param ZDepthParam{NAME_ID("ZDepthParam"), Float3()};
    PassFloatParam Phase1{NAME_ID("Phase1"), 0.0f};
    PassFloatParam Phase2{NAME_ID("Phase2"), 0.0f};
    PassFloat3Param FogAlbedo{NAME_ID("FogAlbedo"), Float3()};
    PassFloatParam FogExtinctionScale{NAME_ID("FogExtinctionScale"), 0.0f};
    PassFloatParam FogAtmosphereContribution{NAME_ID("FogAtmosphereContribution"), 0.0f};
    PassFloatParam FogFadeOutDistance{NAME_ID("FogFadeOutDistance"), 0.0f};
    PassFloatParam LightVolumetricFactor{NAME_ID("LightVolumetricFactor"), 1.0f};

    PassFloatParam FrameNum{NAME_ID("ceFrameNum"), 0.0f};
    PassFloatParam MultiSampleNum{NAME_ID("MultiSampleNum"), 0.0f};
    PassFloatParam MultiSampleJitter{NAME_ID("LightScatteringSampleJitterMultiplier"), 0.0f};
    PassBoolParam CloudShadow{NAME_ID("useCloudShadow"), false};
    PassBoolParam UseWGS84{NAME_ID("UseWGS84"), false};
    PassFloatParam UnitFactor{NAME_ID("UnitFactor"), 1.f};
    PassFloatParam HistoryWeight{NAME_ID("HistoryWeight"), 0.0f};

    PassBoolParam UseDust{NAME_ID("UseDust"), false};
    PassFloatParam DustDensity{NAME_ID("DustDensity"), 1.f};
    PassFloatParam DustScale{NAME_ID("DustScale"), 1.f};
    PassFloatParam Spiral{NAME_ID("Spiral"), 1.f};
    PassFloatParam DustHeight{NAME_ID("DustHeight"), 1.f};
    PassFloatParam DustLightAbsorb{NAME_ID("DustLightAbsorb"), 1.f};
    PassFloat3Param WindDir{NAME_ID("WindDir"), Float3()};
    PassFloat3Param DustAlbedo{NAME_ID("DustAlbedo"), Float3()};

    PassBoolParam Cloudy{NAME_ID("Cloudy"), false};
};

class RENDER_ENGINE_API VolumetricFog : public PassBase<FogSetting, VolumetricFog>
{
public:
    VolumetricFog(){};
    static PassDesc GetPassDesc();

public:
    UInt32 mWidth{0};
    UInt32 mHeight{0};
    const RenderCamera* mCamera;
    Float3 mCamPos;
    RenderWorld* mWorld;
    uint64_t mFrameNum;

    ComputeShaderR* mVolumetricFogComputeShader = nullptr;
    ComputeShaderR* mLightInjectionComputeShader = nullptr;
    ComputeShaderR* mDispatchSizeComputeShader = nullptr;

    MaterialR* mPostMat;
    void UpdateFogApplyContext(RenderContext& context, IRenderPipeline* renderPipeline);

private:
    void UpdateVolumetricLight(const GameContext& gameContext);
    Float3 GetWGS84Projection(Float3 world);
    inline bool ValidHistorySample()
    {
        return mHistoryFrame + 1 == mFrameNum;
    }

    UInt32 PreCalculateLightInjection(UInt32 lightCnt, const std::vector<ecs::EntityID>& lightList, const VolumetricFogParam& params, std::vector<UInt32>& boundGrid, std::vector<UInt32>& lightUseGroup, std::vector<UInt32>& blockLightUseGroup);
    void SetAdaptiveVolume(float distanceMin, float distanceMax);

    NGITexture* mHistoryLightScatterTex = nullptr;
    NGITextureView* mHistoryLightScatterTexView = nullptr;
    uint64_t mHistoryFrame = 0;
    float mCurrentLightDistanceMin = -1.0, mCurrentLightDistanceMax = -1.0;
    ScratchBufferWrap mLightVolumetricIntensityBuffer;

protected:
    bool ExecuteImp(const GameContext& gameContext, REDTextureView* depthOnlyView, const ShadowProperties* shadowData, REDTextureView*& integratedView, REDTextureView*& outFoggedView);
    VolumetricFogParam CalculateFogParams();

    friend PassBase<FogSetting, VolumetricFog>;
};
}   // namespace cross