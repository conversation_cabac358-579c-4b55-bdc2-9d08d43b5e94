#include "WindowEffectPass.h"
#include "RenderEngine/CameraSystemR.h"
#include "ParticlePass.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include <RenderEngine/RenderPipeline/FFSRenderPipeline.h>
#include "RenderEngine/InstanceCulling/InstanceCulling.h"
namespace cross {
void ParticlePass::FillInput(const GameContext& gameContext)
{
    mInput.GenerateInputData(gameContext);
}
//void ParticlePass::Execute(const GameContext& gameContext, REDTextureView* GPassDepthPyramidCurrFrameView)
void ParticlePass::Execute(const GameContext& gameContext)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    auto* camSys = gameContext.mRenderWorld->GetRenderSystem<CameraSystemR>();
    auto* skyLightSys = gameContext.mRenderWorld->GetRenderSystem<SkyLightSystemR>();
    auto* atmosSystem = gameContext.mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    auto* cloudSystem = gameContext.mRenderWorld->GetRenderSystem<CloudSystemR>();

    bool useTestCullingCamera = false;
    if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
    {
        if (camSys->GetTestCullingCamera())
        {
            useTestCullingCamera = true;
        }
    }
    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    if (!ffspipeline)
        return;
    auto* drawCamera = useTestCullingCamera ? gameContext.mRenderPipeline->GetCullingRenderCamera() : gameContext.mRenderPipeline->GetRenderCamera();

    // NGICopyTexture region{0, {}, 0, {}, {RTWidth, RTHeight, 1}};
    auto RTWidth = mInput.mInputColor->mTexture->mDesc.Width;
    auto RTHeight = mInput.mInputColor->mTexture->mDesc.Height;
    // we support write depth in transparent passes.
    // copy a scene depth and use it as the depth/stencil target for transparent object, so any write into this target will not affect other passes.
    // auto sceneDepth = CreateTextureView2D("DepthCopy", RTWidth, RTHeight, depthStencilView->mTexture->mDesc.Format, NGITextureUsage::CopyDst | NGITextureUsage::DepthStencil);
    // mRED->AllocatePass("CopySceneDepth")->CopyTextureToTexture(sceneDepth->mTexture, depthStencilView->mTexture, 1, &region);

    REDColorTargetDesc LightingPassColorTargets[]{{
        mInput.mInputColor,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGIClearValue{0, 0, 0, 0},
    }};

    REDDepthStencilTargetDesc depthStencilTarget{
        mInput.mInputDepthStencil,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGILoadOp::Load,
        NGIStoreOp::Store,
    };
    cross::REDDrawUnitList* drawUnitList = nullptr;
    auto* cullingResult = red->Cull(REDCullingDesc{gameContext.mRenderWorld, const_cast<RenderCamera*>(drawCamera)});
    //drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"forward", gRenderGroupAfterFSR, 7000 - 1});
     drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"particle", gRenderGroupTransparent + 500, gRenderGroupUI - 3});
   
     /*auto [_foliagePass, objectIndexBufferSRV] =
         InstanceCulling::AssembleInstanceCullingPass(gameContext, drawUnitList, dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline)->GetGPassDepthPyramidCurrFrameView(), "ParticleSystemPassCulling", "forward", true, true);*/

    red->BeginRenderPass("ParticleSystemPass", static_cast<UInt32>(ArrayCount(LightingPassColorTargets)), LightingPassColorTargets, &depthStencilTarget);
    {
        NGIRenderPassTargetIndex colorTargets[]{NGIRenderPassTargetIndex::Target0};
        auto* particlePass = red->AllocateSubRenderPass("ParticleRenderPass", 0, nullptr, static_cast<UInt32>(ArrayCount(colorTargets)), colorTargets, REDPassFlagBit::NeedDepth | REDPassFlagBit::DepthReadOnly | REDPassFlagBit::NeedStencil);

        particlePass->SetProperty(BuiltInProperty::ce_Scene_Color, mInput.mSceneColorBeforeTransparent);
        particlePass->SetProperty(BuiltInProperty::ce_Scene_Depth, mInput.mInputDepthOnly);
        particlePass->SetProperty(BuiltInProperty::ce_SkyLightIntensity, skyLightSys->GetSkyLightIntensity());

        /*=====ForwardSSR Lighting=====*/
        particlePass->SetProperty(NAME_ID("FORWARD_DEAL"), true);

        // TODO(scolu): Get exposure from PPV System
        float manual_exposure = 1.0f;
        auto PostProcessSetting = ffspipeline->GetPostProcessVolumeSetting();
        if (PostProcessSetting->BaseSettings.mPostProcessExposureSetting.enable)
        {
            if (PostProcessSetting->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.enable)
            {
                manual_exposure = PostProcessSetting->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.Exposure;
                particlePass->SetProperty(NAME_ID("ManualExposure"), manual_exposure);
            }
        }

        auto& SSRSetting = ffspipeline->GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.mSSRSetting;
        if (SSRSetting.enable)
        {
            Float4 forwardSSRParams = Float4(SSRSetting.MaxTraceDistance, static_cast<float>(SSRSetting.NumSteps), static_cast<float>(ffspipeline->GetFrameCount() % 8), static_cast<float>(SSRSetting.StartMipLevel));
            Float2 HZBUvFactor(RTWidth / static_cast<float>(2 * ffspipeline->mGPassDepthPyramidLastFrameView->GetWidth()), RTHeight / static_cast<float>(2 * ffspipeline->mGPassDepthPyramidLastFrameView->GetHeight()));
            Float4 HZBUvFactorAndInvFactor(HZBUvFactor.x, HZBUvFactor.y, 1.0f / HZBUvFactor.x, 1.0f / HZBUvFactor.y);
            particlePass->SetProperty(NAME_ID("FORWARD_SSR"), true);
            particlePass->SetProperty(NAME_ID("ce_FurthestHZBTexture"), ffspipeline->mGPassDepthPyramidLastFrameView);
            particlePass->SetProperty(NAME_ID("ce_PrevSceneColorTexture"), mInput.mColorLastFrame);
            particlePass->SetProperty(NAME_ID("ce_DepthTexture"), mInput.mInputDepthOnly);
            particlePass->SetProperty(NAME_ID("ce_ForwardSSRParams"), forwardSSRParams);
            particlePass->SetProperty(NAME_ID("ce_HZBUvFactorAndInvFactor"), HZBUvFactorAndInvFactor);
        }
        if (ffspipeline->GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.enable)
        {
            particlePass->SetProperty(NAME_ID("_LightGridZParamsB"), ffspipeline->mReflectionIndirect.mBOS.x);
            particlePass->SetProperty(NAME_ID("_LightGridZParamsO"), ffspipeline->mReflectionIndirect.mBOS.y);
            particlePass->SetProperty(NAME_ID("_LightGridZParamsS"), ffspipeline->mReflectionIndirect.mBOS.z);
            particlePass->SetProperty(NAME_ID("_CulledGridSizeX"), ffspipeline->mReflectionIndirect.mlightGridSizeX);
            particlePass->SetProperty(NAME_ID("_CulledGridSizeY"), ffspipeline->mReflectionIndirect.mlightGridSizeY);
            particlePass->SetProperty(NAME_ID("_CulledGridSizeZ"), ffspipeline->mReflectionIndirect.mlightGridSizeZ);
            particlePass->SetProperty(NAME_ID("_LightGridPixelSizeShift"), ffspipeline->mReflectionIndirect.mLightGridPixelSizeShift);
            particlePass->SetProperty(NAME_ID("_NumReflectionCaptures"), ffspipeline->mReflectionIndirect.mReflectionProbeNum);
            particlePass->SetProperty(NAME_ID("_ReflectionCapturesMipNum"), ffspipeline->mReflectionIndirect.mReflectionCapturesMipNum);
            if (ffspipeline->mReflectionIndirect.mCaptureReflectionView && red->Validate(ffspipeline->mReflectionIndirect.mCaptureReflectionView->mTexture))
            {
                particlePass->SetProperty(NAME_ID("_CaptureReflectionCubemaps"), ffspipeline->mReflectionIndirect.mCaptureReflectionView);
            }
        }
        particlePass->SetProperty(NAME_ID("_NumCulledLightsGrid"), ffspipeline->mNumCulledLightsGrid, NGIResourceState::PixelShaderUnorderedAccess);
        particlePass->SetProperty(NAME_ID("_CulledLightsDataGrid"), ffspipeline->mCulledLightDataGrid, NGIResourceState::PixelShaderUnorderedAccess);
        //if (ffspipeline->GetSetting()->DeferLightingType == DeferredLightingType::TileBased)
        {
            particlePass->SetProperty(NAME_ID("ENABLE_TILE_BASED_LIGHTING"), true);
            particlePass->SetProperty(NAME_ID("_NumLocalLights"), ffspipeline->mTileBasedDeferredLighting.mLocalLightsNum);
        }

        particlePass->SetProperty(NAME_ID("useSFog"), ffspipeline->ShouldRenderScreenSpaceFog());
        particlePass->SetProperty(NAME_ID("useVFog"), ffspipeline->ShouldRenderVolumetricFog());
        particlePass->SetProperty(NAME_ID("useCloud"), cloudSystem->EnableVolumetricCloud());

        // if (gameContext.mRenderPipeline->GetSetting()->InstanceCulling)
        // {
        //     particlePass->SetProperty(NAME_ID("_ObjectIndexBuffer"), objectIndexBufferSRV);
        // }

        /*if (cloudSystem->EnableVolumetricCloud())
        {
            particlePass->SetProperty(NAME_ID("View_CloudColor"), ffspipeline->GetCloudResources().mCloudColor);
            particlePass->SetProperty(NAME_ID("View_CloudDepth"), ffspipeline->GetCloudResources().mCloudDepth);
        }*/
        auto gsettings = cloudSystem->GetGlobalSetting();
        if (gsettings.CloudShadow.CloudCastShadow)
        {
            ffspipeline->UpdateCloudShadowContext(particlePass);
        }

        if (ffspipeline->ShouldRenderVolumetricFog())
            ffspipeline->mVolumetricFog.UpdateFogApplyContext(particlePass->GetContext(), ffspipeline);

        if (ffspipeline->ShouldUseMultiLayerFog())
            ffspipeline->mMultiLayerFog.UpdateFogApplyContext(particlePass->GetContext());
        else
            ffspipeline->mExponentialFog.UpdateFogApplyContext(particlePass->GetContext());

        {
            ffspipeline->UpdateContext(particlePass->GetContext(), gameContext.mRenderWorld, nullptr, true);
            {
                particlePass->OnCulling([=](REDPass* pass) { particlePass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView()); });
                particlePass->SetProperty(BuiltInProperty::CE_INSTANCING, true);
                particlePass->RenderDrawUnits({drawUnitList});
            }
        }
    }
    red->EndRenderPass();
    mOutput.mOutColor = mInput.mInputColor;
    mOutput.SetOutputData(gameContext);
}
void ParticlePassOutput::SetOutputData(const GameContext& gameContext)
{
    gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = mOutColor;
}
}   // namespace cross
