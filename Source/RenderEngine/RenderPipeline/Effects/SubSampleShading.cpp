#include "SubSampleShading.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"

namespace cross
{
    void SubSampleShadingSetting::Initialize()
    {
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ResolveGBufferMS2ComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ResolveGBufferMS4ComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ResolveGBufferMS8ComputeShader);

    }
    REDTextureView* SubSampleShading::CreateMSAAOverideDepthView(REDTextureView* depthStencilView)
    {
        mDepthMSAAView = IRenderPipeline::CreateTextureView2D("Depth Texture MSAA", depthStencilView->GetWidth(), depthStencilView->GetHeight(), depthStencilView->mTexture->mDesc.Format,
            NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc, NGITextureUsage::DepthStencil, 1, static_cast<UInt16>(mSetting.MSAASampleCount));

        return mDepthMSAAView;
    }
    void SubSampleShading::CreateMSAAOverrideGbuffer(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* sceneColorView, REDTextureView* reactiveMask)
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* renderGraphContext = rendererSystem->GetRenderingExecutionDescriptor();

        for (int i = 0; i < gBufferViews.size(); i++)
        {
            auto view = gBufferViews[i];

            mMSAAGbuffers[i] = IRenderPipeline::CreateTextureView2D(view->mTexture->GetName() + "_MSAA",
                view->mTexture->GetDesc().Width, view->mTexture->GetDesc().Height, view->mTexture->GetDesc().Format, 
                view->mTexture->GetDesc().Usage | (view->mTexture->GetDesc().Format == GraphicsFormat::R8G8B8A8_SRGB ? NGITextureUsage(0) : NGITextureUsage::UnorderedAccess),
                1, static_cast<UInt16>(mSetting.MSAASampleCount), view->mTexture->GetDesc().MutableFormat);

            if (view->mDesc.Format == GraphicsFormat::R8G8B8A8_SRGB)
            {
                auto desc = view->GetDesc();
                desc.Format = view->mDesc.Format;
                desc.Usage &= (~NGITextureUsage::UnorderedAccess);
                mMSAAGbuffers[i] = renderGraphContext->AllocateTextureView(mMSAAGbuffers[i]->mTexture, desc);
            }

            if (mSetting.EnableAGAA)
            {
                auto textureDesc = view->mTexture->GetDesc();
                mSecondAggregateGbuffer[i] = IRenderPipeline::CreateTextureView2D(view->mTexture->GetName() + "_Agaa",
                    textureDesc.Width, textureDesc.Height, textureDesc.Format, textureDesc.Usage | (textureDesc.Format == GraphicsFormat::R8G8B8A8_SRGB ? NGITextureUsage(0) :
                        NGITextureUsage::UnorderedAccess), ToUInt16(textureDesc.MipCount), ToUInt16(textureDesc.SampleCount), textureDesc.MutableFormat
                );

                if (view->mDesc.Format == GraphicsFormat::R8G8B8A8_SRGB)
                {
                    auto desc = view->GetDesc();
                    desc.Format = view->mDesc.Format;
                    desc.Usage &= (~NGITextureUsage::UnorderedAccess);
                    mSecondAggregateGbuffer[i] = renderGraphContext->AllocateTextureView(mSecondAggregateGbuffer[i]->mTexture, desc);
                }
            }
        }

        mSceneColorMSAAView = IRenderPipeline::CreateTextureView2D(sceneColorView->mTexture->GetName() + "_MSAA",
            sceneColorView->mTexture->GetDesc().Width, sceneColorView->mTexture->GetDesc().Height, sceneColorView->mTexture->GetDesc().Format, 
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | sceneColorView->mTexture->GetDesc().Usage,
            1, static_cast<UInt16>(mSetting.MSAASampleCount));
        
        if (reactiveMask)
        {
            mReactiveMSAAView = IRenderPipeline::CreateTextureView2D(reactiveMask->mTexture->GetName() + "_MSAA",
                                                                     reactiveMask->mTexture->GetDesc().Width,
                                                                     reactiveMask->mTexture->GetDesc().Height,
                                                                     reactiveMask->mTexture->GetDesc().Format,
                                                                     NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | reactiveMask->mTexture->GetDesc().Usage,
                                                                     1,
                                                                     static_cast<UInt16>(mSetting.MSAASampleCount));
        }
    }
    void SubSampleShading::OverrideColorTarget(std::vector<REDColorTargetDesc>& colorTaget, std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* sceneColorView, REDTextureView* reactiveMask)
    {
        Assert(colorTaget.size() == mMSAAGbuffers.size() + (!!reactiveMask ? 2 : 1));

        // base Color
        colorTaget[0].Target = mMSAAGbuffers[0];
        colorTaget[0].StoreOp = NGIStoreOp::Store;
        //colorTaget[0].ResolveTarget = gBufferViews[0];

        // normal and roughness
        colorTaget[1].Target = mMSAAGbuffers[1];
        colorTaget[1].StoreOp = NGIStoreOp::Store;

        // frenel
        colorTaget[2].Target = mMSAAGbuffers[2];
        colorTaget[2].StoreOp = NGIStoreOp::Store;


        colorTaget[3].Target = mMSAAGbuffers[3];
        colorTaget[3].StoreOp = NGIStoreOp::Store;
        //colorTaget[4].ResolveTarget = gBufferViews[4];

        //mSceneColor, Emissive

        colorTaget[4].Target = mSceneColorMSAAView;
        colorTaget[4].StoreOp = mSetting.EmssiveConfig.EmissiveCustomResolve ? NGIStoreOp::Store : NGIStoreOp::Resolve;
        colorTaget[4].ResolveTarget = mSetting.EmssiveConfig.EmissiveCustomResolve ?  nullptr : sceneColorView;

        if (reactiveMask)
        {
            colorTaget[5].Target = mReactiveMSAAView;
            colorTaget[5].StoreOp = NGIStoreOp::Resolve;
            colorTaget[5].ResolveTarget = reactiveMask;
        }
    }

    void SubSampleShading::AggregateGeneration(const GameContext& gameContext, REDTextureView* sceneColorView)
    {
        if (mSetting.EnableAGAA)
        {
            auto RED = gameContext.mRenderPipeline->RED();

            auto size = gameContext.TargetScreenSize();

            mDepth2 = IRenderPipeline::CreateTextureView2D("depth resolved Agaa", size, GraphicsFormat::R32_SFloat, 
                NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst, 1, 1);

            mAggregateCoverage = IRenderPipeline::CreateTextureView2D("AGAACoverage", size, GraphicsFormat::R8_UNorm,
                NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst, 1, 1);

            auto sceneColorDesc = sceneColorView->mTexture->GetDesc();

            mSecondceneColorView = IRenderPipeline::CreateTextureView2D("SecondSceneColor", size, sceneColorDesc.Format, sceneColorDesc.Usage, ToUInt16(sceneColorDesc.MipCount), ToUInt16(sceneColorDesc.SampleCount));

            RED->AllocatePass("Clear AGAA Depth Tex")->ClearTexture(mDepth2, NGIClearValue{ 0, 0, 0, 0 });
            RED->AllocatePass("Clear AGAA GBuffer1 Tex")->ClearTexture(mSecondAggregateGbuffer[1], NGIClearValue{ 0, 0, 0, 0 });
            RED->AllocatePass("Clear AGAA Gbuffer2 Tex")->ClearTexture(mSecondAggregateGbuffer[2], NGIClearValue{ 0, 0, 0, 0 });
            RED->AllocatePass("Clear AGAA Coverage Tex")->ClearTexture(mAggregateCoverage, NGIClearValue{ 0, 0, 0, 0 });
            if (mSetting.EnableAggregatedBaseColor)
            {
                RED->AllocatePass("Clear AGAA GBuffer0 Tex")->ClearTexture(mSecondAggregateGbuffer[0], NGIClearValue{0, 0, 0, 0});
            }

            // for now ,we assume every pixel is lighting by 2 times.
            //TODO(yazhenyuan): the actual agrregate buffer
        }
    }
    bool SubSampleShading::ExecuteImp(const GameContext& gameContext, std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* sceneColorView, REDTextureView*& manuallyResolvedDepth)
    {
        auto RED = gameContext.mRenderPipeline->RED();

        auto RTWidth = gameContext.mRenderPipeline->GetTargetView()->mTexture->mDesc.Width;
        auto RTHeight = gameContext.mRenderPipeline->GetTargetView()->mTexture->mDesc.Height;

        //  Create AggregateBuffer
        AggregateGeneration(gameContext, sceneColorView);

        // Compute Pass - manually resolve gbuffer 0,1,2,4

        //srgb, need special handling
        NGITextureViewDesc baseColorViewDesc = gBufferViews[0]->GetDesc();
        baseColorViewDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;
        baseColorViewDesc.Usage |= NGITextureUsage::UnorderedAccess;

        auto baseColorView = RED->AllocateTextureView(gBufferViews[0]->mTexture, baseColorViewDesc);

        NGITextureViewDesc msaaBaseColorViewDesc = mMSAAGbuffers[0]->GetDesc();
        msaaBaseColorViewDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;

        auto baseColorView_msaa = RED->AllocateTextureView(mMSAAGbuffers[0]->mTexture, msaaBaseColorViewDesc);

        NGITextureViewDesc msaa_depth = mDepthMSAAView->GetDesc();
        msaa_depth.Usage |= NGITextureUsage::ShaderResource;
        msaa_depth.SubRange.Aspect = NGITextureAspect::Depth;
        auto msaa_depth_for_read = RED->AllocateTextureView(mDepthMSAAView->mTexture, msaa_depth);

        auto depthResolvedView = IRenderPipeline::CreateTextureView2D("depth resolved", mDepthMSAAView->mTexture->GetDesc().Width, mDepthMSAAView->mTexture->GetDesc().Height, GraphicsFormat::R32_SFloat,
            NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst);

        RED->AllocatePass("Clear baseColorBuffer", true)->ClearTexture(baseColorView, NGIClearValue{ 0, 0, 0, 0 });
        RED->AllocatePass("Clear NormalBuffer", true)->ClearTexture(gBufferViews[1], NGIClearValue{ 0, 0, 0, 0 });
        RED->AllocatePass("Clear fresnelShadingModeBuffer", true)->ClearTexture(gBufferViews[2], NGIClearValue{ 0, 0, 0, 0 });
        RED->AllocatePass("Clear depth", true)->ClearTexture(depthResolvedView, NGIClearValue{ 0, 0, 0, 0 });
        RED->AllocatePass("Clear MotionVectorOpacityAO", true)->ClearTexture(gBufferViews[3], NGIClearValue{ 0, 0, 0, 0 });
        if (mSetting.EmssiveConfig.EmissiveCustomResolve)
        {
            RED->AllocatePass("Clear SceneColor", true)->ClearTexture(sceneColorView, NGIClearValue{ 0, 0, 0, 0 });
        }



        constexpr SInt32 numThread = 8;
        auto* resolveNormalBufferPass = RED->AllocatePass("ResolveNormalBuffer");
        //resolveNormalBufferPass->ClearTexture(gBufferViews[1], NGIClearValue{ 0, 0, 0, 0 });
        //resolveNormalBufferPass->ClearTexture(gBufferViews[2], NGIClearValue{ 0, 0, 0, 0 });

        resolveNormalBufferPass->SetProperty(NAME_ID("Width"), RTWidth);
        resolveNormalBufferPass->SetProperty(NAME_ID("Height"), RTHeight);
        resolveNormalBufferPass->SetProperty(NAME_ID("SampleCount"), UInt32(mSetting.MSAASampleCount));
        resolveNormalBufferPass->SetProperty(NAME_ID("ComplexPixelThreshold"), mSetting.ComplexPixelThreshold);
        resolveNormalBufferPass->SetProperty(NAME_ID("DepthWeight"), mSetting.DepthWeight);
        resolveNormalBufferPass->SetProperty(NAME_ID("NormalWeight"), mSetting.NormalWeight);
        resolveNormalBufferPass->SetProperty(NAME_ID("RoughnessWeight"), mSetting.RoughnessWeight);

        resolveNormalBufferPass->SetProperty(NAME_ID("in_depthBufferMS"), msaa_depth_for_read);
        resolveNormalBufferPass->SetProperty(NAME_ID("in_baseColorBufferMS"), baseColorView_msaa);
        resolveNormalBufferPass->SetProperty(NAME_ID("in_NormalBufferMS"), mMSAAGbuffers[1]);
        resolveNormalBufferPass->SetProperty(NAME_ID("in_fresnelShadingModeMS"), mMSAAGbuffers[2]);
        resolveNormalBufferPass->SetProperty(NAME_ID("in_SceneColorMS"), mSceneColorMSAAView);
        resolveNormalBufferPass->SetProperty(NAME_ID("in_MotionVectorOpacityAOMS"), mMSAAGbuffers[3]);

        resolveNormalBufferPass->SetProperty(NAME_ID("out_baseColorBuffer"), baseColorView);
        resolveNormalBufferPass->SetProperty(NAME_ID("out_NormalBuffer"), gBufferViews[1]);
        resolveNormalBufferPass->SetProperty(NAME_ID("out_fresnelShadingModeBuffer"), gBufferViews[2]);
        resolveNormalBufferPass->SetProperty(NAME_ID("out_depth"), depthResolvedView);
        resolveNormalBufferPass->SetProperty(NAME_ID("out_MotionVectorOpacityAO"), gBufferViews[3]);
        resolveNormalBufferPass->SetProperty(NAME_ID("out_SceneColor"), sceneColorView);

        resolveNormalBufferPass->SetProperty(NAME_ID("EmssiveResolve"), mSetting.EmssiveConfig.EmissiveCustomResolve ? 1 : 0 );
        resolveNormalBufferPass->SetProperty(NAME_ID("ResolveFilterDiameter"), mSetting.EmssiveConfig.EmssiveSampleDiameter);
        resolveNormalBufferPass->SetProperty(NAME_ID("FilterMethod"), static_cast<int>(mSetting.EmssiveConfig.ResolveMethod));

        ComputeShaderR* shader = nullptr;
        switch (mSetting.MSAASampleCount)
        {
        case SubSampleMSAASampleCount::MSAA_2:
            shader = mSetting.ResolveGBufferMS2ComputeShaderR;
            break;
        case SubSampleMSAASampleCount::MSAA_4:
            shader = mSetting.ResolveGBufferMS4ComputeShaderR;
            break;
        case SubSampleMSAASampleCount::MSAA_8:
            shader = mSetting.ResolveGBufferMS8ComputeShaderR;
            break;
        default: 
            shader = mSetting.ResolveGBufferMS4ComputeShaderR;
        }

        if (mSetting.EnableAGAA)
        {
            NGITextureViewDesc fersenlView2Desc = mSecondAggregateGbuffer[2]->GetDesc();
            fersenlView2Desc.Format = GraphicsFormat::R8G8B8A8_UNorm;
            fersenlView2Desc.Usage |= NGITextureUsage::UnorderedAccess;
            auto fersenl2View = RED->AllocateTextureView(mSecondAggregateGbuffer[2]->mTexture, fersenlView2Desc);

            resolveNormalBufferPass->SetProperty(NAME_ID("out_depth2"), mDepth2);
            resolveNormalBufferPass->SetProperty(NAME_ID("out_NormalBuffer2"), mSecondAggregateGbuffer[1]);
            resolveNormalBufferPass->SetProperty(NAME_ID("out_fresnelShadingModeBuffer2"), fersenl2View);
            resolveNormalBufferPass->SetProperty(NAME_ID("out_AgaaCoverage"), mAggregateCoverage);
            resolveNormalBufferPass->SetProperty(NAME_ID("out_SceneColor2"), mSecondceneColorView);
            if (mSetting.EnableAggregatedBaseColor)
            {
                NGITextureViewDesc basecolorView2Desc = mSecondAggregateGbuffer[0]->GetDesc();
                basecolorView2Desc.Format = GraphicsFormat::R8G8B8A8_UNorm;
                basecolorView2Desc.Usage |= NGITextureUsage::UnorderedAccess;
                auto basecolor2View = RED->AllocateTextureView(mSecondAggregateGbuffer[0]->mTexture, basecolorView2Desc);
                resolveNormalBufferPass->SetProperty(NAME_ID("out_BaseColorBuffer2"), basecolor2View);

                resolveNormalBufferPass->Dispatch(shader, "ResolveMultiSampledGBufferToAggregateBufferPass_aggregatedBasecolor", 
                    (RTWidth + numThread - 1) / numThread, (RTHeight + numThread - 1) / numThread, 1);
            }
            else
            {
                resolveNormalBufferPass->Dispatch(shader, "ResolveMultiSampledGBufferToAggregateBufferPass", 
                    (RTWidth + numThread - 1) / numThread, (RTHeight + numThread - 1) / numThread, 1);
            }
        }
        else
        {
            resolveNormalBufferPass->Dispatch(shader, "ResolveMultiSampledGBufferPass", 
                (RTWidth + numThread - 1) / numThread, (RTHeight + numThread - 1) / numThread, 1);
        }

        auto manuallyResolvedDepthViewDesc = depthResolvedView->GetDesc();
        manuallyResolvedDepthViewDesc.Usage &= (~NGITextureUsage::UnorderedAccess);
        manuallyResolvedDepth = RED->AllocateTextureView(depthResolvedView->mTexture, manuallyResolvedDepthViewDesc);
        
        
        for (int i = 0; i < gBufferViews.size(); i++)
        {
            gBufferViews[i] = IRenderPipeline::CreateTextureView2D(gBufferViews[i], NGITextureUsage::ShaderResource);
        }
        
        return true;
    }
}