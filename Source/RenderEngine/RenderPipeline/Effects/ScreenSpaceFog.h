#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "VolumetricFog.h"
namespace cross {

struct FogMultiLayerProfileParam
{
    PassFloatParam RVR{NAME_ID("RVR"), 0.0f};
    PassFloatParam FogBaseHeight{NAME_ID("FogBaseHeight"), 0.0f};
    PassFloatParam RVRFogTop{NAME_ID("RVRFogTop"), 0.0f};
    PassFloatParam BlwCldVis{NAME_ID("BlwCldVis"), 24900000.0f};
    PassFloatParam BtmCldBase{NAME_ID("BtmCldBase"), 0.0f};
    PassFloatParam BtmCldCoverage{NAME_ID("BtmCldCoverage"), 0.0f};
    PassFloatParam BtwCldVis{NAME_ID("BtwCldVis"), 24900000.0f};
    PassFloatParam MidCldBase{NAME_ID("MidCldBase"), 0.0f};
    PassFloatParam MidCldCoverage{NAME_ID("MidCldCoverage"), 0.0f};
    PassFloatParam AbvCldVis{NAME_ID("AbvCldVis"), 24900000.0f};
    PassFloatParam FogTop{NAME_ID("FogTop"), 360000.0f};
    PassFloatParam ClearFogProportion{NAME_ID("ClearFogProportion"), 0.2f};
    PassFloatParam VocanicAshModifier{NAME_ID("VocanicAshModifier"), 0.0f};
    PassFloatParam CameraAltitude{NAME_ID("CameraAltitude"), 0.0f};
    PassFloat3Param CamPosNormal{NAME_ID("CamPosNormal"), Float3{1, 0, 0}};
    PassFloatParam StrobeDisExponent{NAME_ID("StrobeDisExponent"), 0.3f};
    PassFloatParam StrobeDisFallOff{NAME_ID("StrobeDisFallOff"), 0.1f};
};

class RENDER_ENGINE_API ScreenSpaceFog : public PassBase<FogSetting, ScreenSpaceFog>
{
public:
    ScreenSpaceFog() {}
    static PassDesc GetPassDesc();

public:
    UInt32 mWidth{0};
    UInt32 mHeight{0};
    const RenderCamera* mCamera;
    Float3 mCamPos;

    MaterialR* mPostMat;
    RenderWorld* mWorld;

    virtual void UpdateFogApplyContext(RenderContext& context) = 0;

protected:
    static Float3 GetWGS84Projection(Float3 world);

    virtual bool ExecuteImp(const GameContext& gameContext, REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView = nullptr) = 0;

    friend PassBase<FogSetting, ScreenSpaceFog>;
};

class RENDER_ENGINE_API ExponentialFog : public ScreenSpaceFog
{
public:
    ExponentialFog() {}
    static PassDesc GetPassDesc();

public:
    void UpdateFogApplyContext(RenderContext& context) override;

protected:
    bool ExecuteImp(const GameContext& gameContext, REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView = nullptr) override;
};

class RENDER_ENGINE_API MultiLayerFog : public ScreenSpaceFog
{
public:
    MultiLayerFog() {}
    static PassDesc GetPassDesc();

public:
    void UpdateFogApplyContext(RenderContext& context) override;

protected:
    bool ExecuteImp(const GameContext& gameContext, REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView = nullptr) override;
};
}   // namespace cross