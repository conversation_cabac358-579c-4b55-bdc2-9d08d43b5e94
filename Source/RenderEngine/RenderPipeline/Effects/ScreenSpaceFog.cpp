#include "ScreenSpaceFog.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include "Resource/AssetStreaming.h"

namespace cross {

Float3 ScreenSpaceFog::GetWGS84Projection(Float3 world)
{
    world *= 0.01f;
    const float a = 6378136.f;
    const float b = 6356755.f;
    const float c = 6378136.f;
    float x0 = world.x;
    float y0 = world.y;
    float z0 = world.z;

    float ydx = y0 / x0;
    float zdx = z0 / x0;

    float factor = 1.f / (a * a) + (ydx * ydx) / (b * b) + (zdx * zdx) / (c * c);
    float x = 1.f / std::sqrt(factor);
    float y = ydx * x;
    float z = zdx * x;

    Float3 ret = Float3(x, y, z);
    if (world.Dot(ret) < 0)
        ret = -ret;

    return ret * 100.f;
}

PassDesc ScreenSpaceFog::GetPassDesc()
{
    return PassDesc("ScreenSpaceFog", "Screen Space Fog");
}

PassDesc ExponentialFog::GetPassDesc()
{
    return PassDesc("ExponentialHeightDepthFog", "Screen Space Exponential Fog");
}

Double3 CartesianCoordinateTransform_ToWGS84(const double x, const double y, const double z, Double3& normal)
{
    constexpr double a = 637813700.0;        // unit cm
    constexpr double b = 635675231.4245;   // unit cm
    constexpr double a2 = a * a;
    constexpr double b2 = b * b;
    constexpr double e2 = (a2 - b2) / a2;
    constexpr double c2 = (a2 - b2) / b2;
    double p = sqrt(z * z + x * x);
    double R = sqrt(p * p + y * y);
    double tanBeta = (b * y) / (a * p) * (1 + c2 * b / R);
    double beta = atan(tanBeta);
    double sinBeta = sin(beta);
    double cosBeta = cos(beta);
    double tanLat = (y + c2 * b * sinBeta * sinBeta * sinBeta) / (p - e2 * a * cosBeta * cosBeta * cosBeta);
    double tanLon = x / (-z);
    double lat = atan(tanLat);
    double lon = atan(tanLon);
    lon = z > 0 ? (x > 0 ? lon + PI : lon - PI) : lon;
    double v = a / sqrt(1 - e2 * sin(lat) * sin(lat));
    double alt = p * cos(lat) + y * sin(lat) - a2 / v;

    normal.x = cos(lat) * sin(lon);
    normal.y = sin(lat);
    normal.z = -cos(lat) * cos(lon);
    normal.Normalize();

    lat = MathUtils::ConvertToDegrees(lat);
    lon = MathUtils::ConvertToDegrees(lon);

    return Double3(lat, lon, alt);
}

void ExponentialFog::UpdateFogApplyContext(RenderContext& context)
{
    auto& commonSettings = mSetting.FogCommon;
    auto& sFogSettings = mSetting.SFog;
    auto& dustSettings = mSetting.VFog.Dust;

    float unitFactor = 1.f;
    context.SetProperty(NAME_ID("UnitFactor"), unitFactor);
    context.SetProperty(NAME_ID("UseWGS84"), mSetting.FogCommon.UseWGS84);

    context.SetProperty(NAME_ID("MinOpacity"), 1 - sFogSettings.MaxFogOpacity);
    
    context.SetProperty(NAME_ID("SFogColor"), sFogSettings.Inscatter);                                                                               
    context.SetProperty(NAME_ID("SFogColorSecond"), sFogSettings.InscatterSecond);                                                                               
    context.SetProperty(NAME_ID("ENABLE_MULTI_LAYER_FOG"), false);
    context.SetProperty(NAME_ID("FfsFogTop"), mSetting.ffsTempTest.FogTop);

    context.SetProperty(NAME_ID("DustDensity"), dustSettings.DustDensity);
    context.SetProperty(NAME_ID("DustHeight"), dustSettings.Height);
    context.SetProperty(NAME_ID("DustLightAbsorb"), dustSettings.LightAbsorb);
    context.SetProperty(NAME_ID("DustAlbedo"), dustSettings.DustAlbedo);
    context.SetProperty(NAME_ID("DustOverride"), dustSettings.enable && dustSettings.DustOverride);
    context.SetProperty(NAME_ID("SFogDustAbsorbFactor"), dustSettings.SFogLightFactor);
    
    context.SetProperty(NAME_ID("FogHeightFallOff"), std::max(commonSettings.HeightFallOff / 1000.f, 0.f));
    context.SetProperty(NAME_ID("FogHeightFallOffSecond"), std::max(commonSettings.HeightFallOffSecond / 1000.f, 0.f));
    context.SetProperty(NAME_ID("FogHeight"), commonSettings.HeightOffset);
    context.SetProperty(NAME_ID("FogHeightSecond"), commonSettings.HeightOffsetSecond);
    context.SetProperty(NAME_ID("FogDensity"), std::max(commonSettings.Density / 1000.f, 0.f));
    context.SetProperty(NAME_ID("FogDensitySecond"), std::max(commonSettings.DensitySecond / 1000.f, 0.f));
    context.SetProperty(NAME_ID("FogNear"), commonSettings.StartDistance);
    context.SetProperty(NAME_ID("FogFar"), commonSettings.CutOffDistance);

    //context.SetProperty(NAME_ID("FogHeightFallOff"), std::max(commonSettings.HeightFallOff, 0.f));
    context.SetProperty(NAME_ID("Cloudy"), commonSettings.CloudyAtomsphere);

    float camHeight = mCamPos.y * unitFactor;
    if (mSetting.FogCommon.UseWGS84)
    {
        Float3 onEarth = GetWGS84Projection(mCamPos * unitFactor);
        camHeight = std::sqrt((mCamPos * unitFactor).Dot(mCamPos * unitFactor)) - std::sqrt(onEarth.Dot(onEarth));
    }
    /*float expPara = -fallOff * (camHeight - mSetting.FogCommon.Height * unitFactor);
    expPara = std::max(-126.f + 1.f, std::min(127.f - 1.f, expPara));
    context.SetProperty(NAME_ID("FogParam"), mSetting.FogCommon.Density * std::powf(2.0f, expPara));
    context.SetProperty(NAME_ID("FogOutParam"), camHeight);

    float expParaSecond = -fallOffSecond * (camHeight - mSetting.FogCommon.HeightSecond * unitFactor);
    expParaSecond = std::max(-126.f + 1.f, std::min(127.f - 1.f, expParaSecond));
    context.SetProperty(NAME_ID("FogParamSecond"), mSetting.FogCommon.DensitySecond * std::powf(2.0f, expParaSecond));*/

    // directional inscatter params:
    context.SetProperty(NAME_ID("DirectionalInscatterColor"), sFogSettings.Directional.DirectionalInscatter);
    context.SetProperty(NAME_ID("DirectionalIntensity"), sFogSettings.Directional.DirectionalIntensity);
    context.SetProperty(NAME_ID("AtmosphereContribution"), sFogSettings.Directional.AtomsphereContribution);
    context.SetProperty(NAME_ID("InscatterExponent"), sFogSettings.Directional.InscatterExponent);
    context.SetProperty(NAME_ID("DirectionalInscatterDistance"), sFogSettings.Directional.DirectionalStartDistance);
}

bool ExponentialFog::ExecuteImp(const GameContext& gameContext, REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView)
{
    Assert(mPostMat);
    auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto* pipe = gameContext.mRenderPipeline;
    auto* ffsPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);

    auto* sceneColor =
        pipe->CreateTextureView2D("Scene Color Texture", sceneView->GetWidth(), sceneView->GetHeight(), sceneView->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
    pipe->PostProcessUtil([&](REDPass* pass) { pass->SetProperty(NAME_ID("_ColorTex"), sceneView, NGIResourceState::PixelShaderShaderResource); }, mPostMat, RED, "blit", true, sceneColor);

    if (mWorld)
    {
        auto* atmosSystem = mWorld->GetRenderSystem<SkyAtmosphereSystemR>();
        pipe->PostProcessUtil(
            [&](REDPass* pass) {
                auto& context = pass->GetContext();
                UpdateFogApplyContext(context);
                pass->SetProperty(NAME_ID("sceneColor"), sceneColor, NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("src_depth"), depthOnlyView, NGIResourceState::PixelShaderShaderResource);

                bool enableVFog = ffsPipe->ShouldRenderVolumetricFog() && volumetricFogView;
                pass->SetProperty(NAME_ID("useVFog"), enableVFog);
                if (enableVFog)
                {
                    pass->SetProperty(NAME_ID("volumetricFogTex"), volumetricFogView, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("VFogFar"), gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::VFogVolumeCutOffDistance>());
                }
            },
            mPostMat,
            RED,
            "fog_screen_exponential",
            true,
            sceneView);
    }
    return true;
}

PassDesc MultiLayerFog::GetPassDesc()
{
    return PassDesc("MultiLayerFog", "Screen Space Multi Layer Fog");
}

void MultiLayerFog::UpdateFogApplyContext(RenderContext& context)
{
    Assert(mWorld && mCamera);
    auto& commonSettings = mSetting.FogCommon;
    auto& sFogSettings = mSetting.SFog;
    auto& dustSettings = mSetting.VFog.Dust;
    auto ffsSettings = mSetting.ffsTempTest;

    float unitFactor = 1.f;
    context.SetProperty(NAME_ID("FogNear"), mSetting.FogCommon.StartDistance);
    context.SetProperty(NAME_ID("FogFar"), mSetting.FogCommon.CutOffDistance);
    context.SetProperty(NAME_ID("UnitFactor"), unitFactor);
    context.SetProperty(NAME_ID("UseWGS84"), mSetting.FogCommon.UseWGS84);

    context.SetProperty(NAME_ID("MinOpacity"), 1 - sFogSettings.MaxFogOpacity);
    context.SetProperty(NAME_ID("SFogColor"), sFogSettings.Inscatter);
    context.SetProperty(NAME_ID("SFogColorSecond"), sFogSettings.InscatterSecond);
    context.SetProperty(NAME_ID("ENABLE_MULTI_LAYER_FOG"), true);
    context.SetProperty(NAME_ID("FfsFogTop"), mSetting.ffsTempTest.FogTop);

    context.SetProperty(NAME_ID("DustDensity"), dustSettings.DustDensity);
    context.SetProperty(NAME_ID("DustHeight"), dustSettings.Height);
    context.SetProperty(NAME_ID("DustLightAbsorb"), dustSettings.LightAbsorb);
    context.SetProperty(NAME_ID("DustAlbedo"), dustSettings.DustAlbedo);
    context.SetProperty(NAME_ID("DustOverride"), dustSettings.enable && dustSettings.DustOverride);
    context.SetProperty(NAME_ID("SFogDustAbsorbFactor"), dustSettings.SFogLightFactor);
    
    FogMultiLayerProfileParam profile;

    auto cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    float rvr = ffsSettings.RVR;
    Float3 CamPos = mCamera->GetCameraOrigin();
    Double3 CamPosD = GetAbsolutePosition(mCamera->GetTilePosition(), CamPos);

    if (ffsSettings.FogDistribution == FogDistributionType::PATCHY_FOG)
    {
        Double3 normal;
        Float3 posWgs84 = static_cast<Float3>(CartesianCoordinateTransform_ToWGS84(CamPosD.x, CamPosD.y, CamPosD.z, normal));
        float X = posWgs84.x * 3600.f;
        float Y = posWgs84.y * 3600.f;
        float H = posWgs84.z - ffsSettings.FogBaseHeight;

        auto fGenerator = [](float inX, float inY) {
            return std::sinf(inX) + std::sinf(2.0f * inX) * std::sinf(3.0f * inX) + std::sinf(4.0f * inX) * std::sinf(5.0f * inX) + std::sinf(inY) + std::sinf(2.0f * inY) * std::sinf(3.0f * inY) +
                    std::sinf(4.0f * inY) * std::sinf(5.0f * inY);
        };
        const float generatorMin = -1.975f * 2.0f;
        const float oscillator = ffsSettings.PatchyFogFactor;
        float rvrFactor = fGenerator(oscillator * X, oscillator * Y);
        // float rvrFactor = 0.5f * std::sinf(0.3f * X) + 0.3f * std::cosf(1.1f * X) + 0.2f * sinf(2.3f * X);
        // rvrFactor += 0.5f * std::sinf(0.3f * Y) + 0.3f * std::cosf(1.1f * Y) + 0.2f * sinf(2.3f * Y);
        float heightFactor = 10.0f * H / ffsSettings.RVRFogTop - 9.0f;
        heightFactor = std::max(std::min(heightFactor, 1.0f), 0.0f);

        // remap rvrFactor from (-generatorMin, +generatorMin) to (0, 1)
        rvrFactor = (rvrFactor + generatorMin) / (2.0f * generatorMin);

        rvrFactor = Lerp(rvrFactor, 1, heightFactor);
        rvr = rvrFactor * (ffsSettings.RVR_Max - ffsSettings.RVR_Min) + ffsSettings.RVR_Min;
        // rvr = ffsSettings.RVR_Min;
    }
    // float density = -std::log2f(ffsSettings.DefineInvisible) / rvr;
    // float topDensity = -std::log2f(ffsSettings.DefineVisible) / (density * 10000.f);
    // float falloff = std::max(0.0000001f, std::min(-1.f / ffsSettings.FogTop * std::log2f(topDensity), 0.02f));
    // context.SetProperty(NAME_ID("FogDensity"), density);
    // context.SetProperty(NAME_ID("FogHeightFallOff"), falloff * ffsSettings.PatchyFogFactor);
    // context.SetProperty(NAME_ID("FogHeight"), ffsSettings.BaseHeight);
    // context.SetProperty(NAME_ID("FogTop"), ffsSettings.FogTop);
    // context.SetProperty(NAME_ID("FogVisibility"), rvr);
    // context.SetProperty(NAME_ID("FogDensitySecond"), 0.f);
    // context.SetProperty(NAME_ID("FogHeightFallOffSecond"), 0.f);
    // context.SetProperty(NAME_ID("FogHeightSecond"), 0.f);

    profile.RVR.mValue = rvr;
    profile.FogBaseHeight.mValue = ffsSettings.FogBaseHeight;
    profile.RVRFogTop.mValue = ffsSettings.RVRFogTop < -5000.0f ? -100000.0f : ffsSettings.RVRFogTop;   // std::max(-100000.0f, ffsSettings.RVRFogTop);
    profile.BlwCldVis.mValue = std::max(0.0f, ffsSettings.BlwCldVis);
    profile.BtmCldBase.mValue = std::max(profile.RVRFogTop.mValue, ffsSettings.BtmCldBase);
    profile.BtmCldCoverage.mValue = 0.f; //   cloudSys->GetCloudCoverage(0);
    profile.BtwCldVis.mValue = std::max(0.0f, ffsSettings.BtwCldVis);
    profile.MidCldBase.mValue = std::max(profile.BtmCldBase.mValue, ffsSettings.MidCldBase);
    profile.MidCldCoverage.mValue = 0.f;   // cloudSys->GetCloudCoverage(1);
    profile.AbvCldVis.mValue = std::max(0.0f, ffsSettings.AbvCldVis);
    profile.FogTop.mValue = std::max(profile.MidCldBase.mValue, ffsSettings.FogTop);
    profile.ClearFogProportion.mValue = MathUtils::Clamp(ffsSettings.ClearFogProportion, 0.0f, 0.9999f);
    profile.VocanicAshModifier.mValue = 0.0f;

    const bool isVolcanicAsh = cloudSys->GetVolcanicAsh();
    const bool ShouldRenderThunderstorm = cloudSys->ShouldRenderThunderstorm();
    const float fogTopOverride = 4200000.0f * 0.3048f - profile.FogBaseHeight.mValue;
    if (isVolcanicAsh)
    {
        profile.FogTop.mValue = fogTopOverride;
        profile.VocanicAshModifier.mValue = 255.0f * (1.0f - std::clamp(profile.AbvCldVis.mValue / 30000000.0f, 0.0f, 1.0f));
    }

    if (ShouldRenderThunderstorm)
    {
        profile.FogTop.mValue = fogTopOverride;
    }

    Double3 lla, normal;
    lla = CartesianCoordinateTransform_ToWGS84(CamPosD.x, CamPosD.y, CamPosD.z, normal);
    profile.CameraAltitude.mValue = static_cast<float>(lla.z);
    profile.CamPosNormal.mValue = static_cast<Float3>(normal);

    profile.StrobeDisExponent.mValue = ffsSettings.StrobeDisExponent;
    profile.StrobeDisFallOff.mValue = ffsSettings.StrobeDisFallOff;
    
    context.SetProperty(NAME_ID("FogHeightFallOff"), std::max(commonSettings.HeightFallOff, 0.f));
    SetPassParameters(profile, &context);

    context.SetProperty(NAME_ID("Cloudy"), commonSettings.CloudyAtomsphere);

    float camHeight = mCamPos.y * unitFactor;
    if (mSetting.FogCommon.UseWGS84)
    {
        Float3 onEarth = GetWGS84Projection(mCamPos * unitFactor);
        camHeight = std::sqrt((mCamPos * unitFactor).Dot(mCamPos * unitFactor)) - std::sqrt(onEarth.Dot(onEarth));
    }
    /*float expPara = -fallOff * (camHeight - mSetting.FogCommon.Height * unitFactor);
    expPara = std::max(-126.f + 1.f, std::min(127.f - 1.f, expPara));
    context.SetProperty(NAME_ID("FogParam"), mSetting.FogCommon.Density * std::powf(2.0f, expPara));
    context.SetProperty(NAME_ID("FogOutParam"), camHeight);

    float expParaSecond = -fallOffSecond * (camHeight - mSetting.FogCommon.HeightSecond * unitFactor);
    expParaSecond = std::max(-126.f + 1.f, std::min(127.f - 1.f, expParaSecond));
    context.SetProperty(NAME_ID("FogParamSecond"), mSetting.FogCommon.DensitySecond * std::powf(2.0f, expParaSecond));*/

    // directional inscatter params:
    context.SetProperty(NAME_ID("DirectionalInscatterColor"), sFogSettings.Directional.DirectionalInscatter);
    context.SetProperty(NAME_ID("DirectionalIntensity"), sFogSettings.Directional.DirectionalIntensity);
    context.SetProperty(NAME_ID("AtmosphereContribution"), sFogSettings.Directional.AtomsphereContribution);
    context.SetProperty(NAME_ID("InscatterExponent"), sFogSettings.Directional.InscatterExponent);
    context.SetProperty(NAME_ID("DirectionalInscatterDistance"), sFogSettings.Directional.DirectionalStartDistance);
}

bool MultiLayerFog::ExecuteImp(const GameContext& gameContext, REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView)
{
    Assert(mPostMat && mCamera);
    auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto* pipe = gameContext.mRenderPipeline;

    auto* sceneColor =
        pipe->CreateTextureView2D("Scene Color Texture", sceneView->GetWidth(), sceneView->GetHeight(), sceneView->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
    pipe->PostProcessUtil([&](REDPass* pass) { pass->SetProperty(NAME_ID("_ColorTex"), sceneView, NGIResourceState::PixelShaderShaderResource); }, mPostMat, RED, "blit", true, sceneColor);

    auto* atmosSystem = mWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    pipe->PostProcessUtil(
        [&](REDPass* pass) {
            auto& context = pass->GetContext();
            UpdateFogApplyContext(context);
            pass->SetProperty(NAME_ID("sceneColor"), sceneColor, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("src_depth"), depthOnlyView, NGIResourceState::PixelShaderShaderResource);
        },
        mPostMat,
        RED,
        "fog_screen_multi_layer",
        true,
        sceneView);

    return true;
}

}   // namespace cross