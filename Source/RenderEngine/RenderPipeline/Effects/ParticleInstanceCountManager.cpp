#include "ParticleInstanceCountManager.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
namespace cross {

ParticleInstanceCountManager::ParticleInstanceCountManager(UInt32 emitterIndex)
{
    mEmitterIndex = emitterIndex;

    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    mRED = rendererSystem->GetRenderingExecutionDescriptor();
    auto instanceCounterBufferSize = mInstanceCounterNum * sizeof(UInt32);
    auto instanceCounterBufferDesc = NGIBufferDesc{instanceCounterBufferSize, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst};
    mInstanceCounterBuffer = mRED->CreateBuffer("instanceCounterBufferUAV_" + std::to_string(emitterIndex), instanceCounterBufferDesc);

    auto indirectArgSize = sizeof(UInt32) * 8;
    auto indirectArgDesc = NGIBufferDesc{indirectArgSize, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::IndirectBuffer | NGIBufferUsage::CopyDst};
    mIndirectArgBuffer = mRED->CreateBuffer("indirectArgBufferUAV", indirectArgDesc);

    auto viewDesc = NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer, 0, instanceCounterBufferSize, GraphicsFormat::Unknown, sizeof(UInt32)};
    mInstanceCounterBufferView = mRED->CreateBufferView(mInstanceCounterBuffer.get(), viewDesc);

    auto argViewDesc = NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer, 0, indirectArgSize, GraphicsFormat::Unknown, sizeof(UInt32)};
    mIndirectArgBufferViewUAV = mRED->CreateBufferView(mIndirectArgBuffer.get(), argViewDesc);
    argViewDesc.Usage = NGIBufferUsage::StructuredBuffer;
    mIndirectArgBufferViewSRV = mRED->CreateBufferView(mIndirectArgBuffer.get(), argViewDesc);

    mRED->AllocatePass("Clear indirectArgBuffer")->ClearBuffer(mIndirectArgBufferViewUAV.get(), 0);
    ClearInstanceCounterBuffer();
}

ParticleInstanceCountManager::~ParticleInstanceCountManager()
{
    while (mStagingBufferPool.size())
    {
        delete mStagingBufferPool.back();
        mStagingBufferPool.pop_back();
    }
}

void ParticleInstanceCountManager::CopyInstanceCount()
{
    const auto instanceCounterBufferSize = mInstanceCounterNum * sizeof(UInt32);
    NGIBufferDesc desc{instanceCounterBufferSize, NGIBufferUsage::CopyDst};
    NGIStagingBuffer* stagingBuffer = nullptr;
    if (mStagingBufferPool.size())
    {
        stagingBuffer = mStagingBufferPool.back();
        mStagingBufferPool.pop_back();
    }
    else
        stagingBuffer = GetNGIDevice().CreateStagingBuffer(desc);
    Assert(stagingBuffer);
    auto stagingBufferRED = mRED->AllocateBuffer("InstanceCountStagingBuffer", stagingBuffer);
    NGICopyBuffer copyRegion{0, 0, instanceCounterBufferSize};
    mRED->AllocatePass("CopyToStagingBuffer")->CopyBufferToBuffer(stagingBufferRED, mInstanceCounterBuffer.get(), 1, &copyRegion);
    stagingBufferRED->SetExternalState(NGIResourceState::HostRead);
    auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
    mPendingCopyBackTasks.emplace(frameCount, stagingBuffer);
}

SInt32 ParticleInstanceCountManager::GetInstanceCount()
{
    const auto instanceCounterBufferSize = mInstanceCounterNum * sizeof(UInt32);
    SInt32 instanceCount = -1;
    while (!mPendingCopyBackTasks.empty())
    {
        auto currentFrameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
        auto& [frameCount, stagingBuffer] = mPendingCopyBackTasks.front();
        if (currentFrameCount - frameCount >= CmdSettings::Inst().gMaxQueuedFrame)
        {
            auto mapped = static_cast<UInt32*>(stagingBuffer->MapRange(NGIBufferUsage::CopySrc, 0, instanceCounterBufferSize));
            instanceCount = mapped[0];
            //LOG_INFO("count {}", instanceCount);
            stagingBuffer->UnmapRange(0, instanceCounterBufferSize);
            mPendingCopyBackTasks.pop();
            mStagingBufferPool.emplace_back(stagingBuffer);
        }
        else
        {
            break;
        }
    }
    return instanceCount;
}

void ParticleInstanceCountManager::CopyToGlobalInstanceCounterBuffer()
{
    Assert(mGlobalInstanceCounterBufferOffset >= 0);
    NGICopyBuffer copyRegion{0, static_cast<SizeType>(mGlobalInstanceCounterBufferOffset) * sizeof(UInt32), sizeof(UInt32)};
    mRED->AllocatePass("CopyToGlobalInstanceCounterBuffer")->CopyBufferToBuffer(mGlobalInstanceCounterBuffer, mInstanceCounterBuffer.get(), 1, &copyRegion);
}

void ParticleInstanceCountManager::ClearInstanceCounterBuffer()
{
   // LOG_INFO("Clear InstanceCount");
    Assert(mInstanceCounterBuffer && mInstanceCounterBufferView);
    // set it to max, so we donot miss judge the zero 
    mRED->AllocatePass("Clear instanceCounterBuffer")->ClearBuffer(mInstanceCounterBufferView.get(), sStaticInvalidValue);
}

}
