#include "DrawRenderTextureToAtlas.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/ImposterBakerSystemR.h"

namespace cross {
void DrawRenderTextureToAtlasPassSettings::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(DrawRenderTextureToAtlasShader);
}

PassDesc DrawRenderTextureToAtlasPass::GetPassDesc()
{
    return PassDesc("DrawRenderTextureToAtlasPass", "Apply DBuffer data to GBuffer.");
}

Float3 RotationFromXVector(Float3 vec)
{
    float yaw, pitch;
    yaw = std::atan2(vec.y, vec.x);
    pitch = std::atan2(-vec.z, std::sqrt(vec.x * vec.x + vec.y * vec.y));
    Float3 rot = Float3(pitch, yaw, 0);
    return rot;
}

void GetAxes(Float3 A, Float3& X, Float3& Y, Float3& Z) 
{
    Quaternion rotation = Quaternion::EulerToQuaternion(A);
    //Float4x4 M = Float4x4::QuaternionToRotationMatrix(rotation);
    //X.x = M.m00;
    //X.y = M.m01;
    //X.z = M.m02;
    //Y.x = M.m10;
    //Y.y = M.m11;
    //Y.z = M.m12;
    //Z.x = M.m20;
    //Z.y = M.m21;
    //Z.z = M.m22;

    // to rotation matrix
    float x, y, z, w;
    x = rotation.x;
    y = rotation.y;
    z = rotation.z;
    w = rotation.w;
    float fTx = x + x;
    float fTy = y + y;
    float fTz = z + z;
    float fTwx = fTx * w;
    float fTwy = fTy * w;
    float fTwz = fTz * w;
    float fTxx = fTx * x;
    float fTxy = fTy * x;
    float fTxz = fTz * x;
    float fTyy = fTy * y;
    float fTyz = fTz * y;
    float fTzz = fTz * z;
    Float4x4 M;

    M.m00 = 1.0f - (fTyy + fTzz);
    M.m01 = fTxy - fTwz;
    M.m02 = fTxz + fTwy;
    M.m10 = fTxy + fTwz;
    M.m11 = 1.0f - (fTxx + fTzz);
    M.m12 = fTyz - fTwx;
    M.m20 = fTxz - fTwy;
    M.m21 = fTyz + fTwx;
    M.m22 = 1.0f - (fTxx + fTyy);

    X.x = M.m00;
    X.y = M.m10;
    X.z = M.m20;

    Y.x = M.m01;
    Y.y = M.m11;
    Y.z = M.m21;

    Z.x = M.m02;
    Z.y = M.m12;
    Z.z = M.m22;
}

void DeriveAxes(Float3 A, Float3& X, Float3& Y, Float3& Z) 
{
    Float3 axesX, axesY, axesZ;
    GetAxes(RotationFromXVector(-A), axesX, axesY, axesZ);
    X = axesY;
    Y = -axesZ;
    Z = axesX;
}

bool DrawRenderTextureToAtlasPass::ExecuteImp(const GameContext& gameContext)
{
    FFSRenderPipeline* ffsPipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    RenderWorld* renderWorld = gameContext.mRenderWorld;
    RenderCamera* renderCamera = gameContext.mRenderCamera;
    ImposterBakerSystemR* imposterBakerSystem = renderWorld->GetRenderSystem<ImposterBakerSystemR>();
    const RenderCamera* captureCamera = imposterBakerSystem->GetRenderCamera();
    if (!imposterBakerSystem->GetBakeTextureEnable() || renderCamera != captureCamera)
    {
        return true;
    }
    RenderingExecutionDescriptor* RED = ffsPipeline->RED();
    auto& gBufferViews = ffsPipeline->GetBuiltInTexture<PassSemanticName::GBufferViews>();
    auto& depthOnlyView = ffsPipeline->GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>();

    ComputeShaderR* drawRenderTextureToAtlasShader = mSetting.DrawRenderTextureToAtlasShaderR;
    RED->BeginRegion("DrawRenderTextureToAtlasPass");

    auto& viewCaptureVec = imposterBakerSystem->GetViewCaptureVec();
    ImposterTextureArray& imposterTexArray = imposterBakerSystem->GetImposterRenderTexture();

    auto outBaseColorTex = RED->AllocateTexture("outBaseColor", imposterTexArray[0], NGIResourceState::ComputeShaderUnorderedAccess);
    auto outNormalTex = RED->AllocateTexture("outNormal", imposterTexArray[1], NGIResourceState::ComputeShaderUnorderedAccess);

    NGITextureViewDesc textureViewDesc{
        NGITextureUsage::UnorderedAccess,
        outBaseColorTex->mDesc.Format,
        NGITextureType::Texture2D,
        {NGITextureAspect::Color, 0, 1, 0, 1},
    };
    auto* outBaseColorView = RED->AllocateTextureView(outBaseColorTex, textureViewDesc);
    auto* outNormalView = RED->AllocateTextureView(outNormalTex, textureViewDesc);

    UInt32 framesInterval = 16;
    auto texWidth = outBaseColorView->mTexture->mDesc.Width;
    auto texHeight = outBaseColorView->mTexture->mDesc.Height;
    auto srcWidth = texWidth / framesInterval;
    auto srcHeight = texHeight / framesInterval;

    UInt3 groupSize;
    drawRenderTextureToAtlasShader->GetThreadGroupSize("RenderToAtlas", groupSize.x, groupSize.y, groupSize.z);
    auto* drawAtlasPass = RED->AllocatePass("DrawRenderTextureToAtlas", true);

    UInt32 currentBakeFrame = imposterBakerSystem->GetCurrentBakeFrame() - 1;
    Float3 axesX, axesY, axesZ;
    Float3 viewVector = viewCaptureVec[currentBakeFrame];
    DeriveAxes(viewVector, axesX, axesY, axesZ);
    Float2 offset;
    offset.x = static_cast<float>((currentBakeFrame % framesInterval) * srcWidth);
    offset.y = static_cast<float>((currentBakeFrame / framesInterval) * srcHeight);
    drawAtlasPass->SetProperty(NAME_ID("center"), imposterBakerSystem->GetCenter());
    drawAtlasPass->SetProperty(NAME_ID("radius"), imposterBakerSystem->GetRadius());
    drawAtlasPass->SetProperty(NAME_ID("deriveAxesX"), axesX);
    drawAtlasPass->SetProperty(NAME_ID("deriveAxesY"), axesY);
    drawAtlasPass->SetProperty(NAME_ID("deriveAxesZ"), axesZ);
    drawAtlasPass->SetProperty(NAME_ID("offset"), offset);
    drawAtlasPass->SetProperty(NAME_ID("targetSize"), srcWidth);
    drawAtlasPass->SetProperty(NAME_ID("_SrcBaseColor"), gBufferViews[0]);
    drawAtlasPass->SetProperty(NAME_ID("_SrcNormal"), gBufferViews[1]);
    drawAtlasPass->SetProperty(NAME_ID("_SrcDepth"), depthOnlyView);
    drawAtlasPass->SetProperty(NAME_ID("_OutBaseColor"), outBaseColorView);
    drawAtlasPass->SetProperty(NAME_ID("_OutNormal"), outNormalView);

    LOG_INFO("RenderToAtlas. currentBakeFrame:{0}, offset:{1}, {2}", currentBakeFrame, offset.x, offset.y);

    drawAtlasPass->Dispatch(drawRenderTextureToAtlasShader, "RenderToAtlas", (srcWidth + groupSize.x - 1) / groupSize.x, (srcHeight + groupSize.y - 1) / groupSize.y, 1);

    RED->EndRegion();

    return true;
}

}   // namespace cross