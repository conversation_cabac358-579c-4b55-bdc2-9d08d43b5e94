#ifdef WIN32
#include "FSR3.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/CloudSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/WindowSystemR.h"
#include "RenderEngine/RenderWindowR.h"
#include "RenderEngine/PresentWindowR.h"
#include "RenderEngine/RendererSystemR.h"

namespace cross {
FSR3SRPass::FSR3SRPass()
{
    ffxQueryDescGetVersions versionQuery{};
    versionQuery.header.type = FFX_API_QUERY_DESC_TYPE_GET_VERSIONS;
    versionQuery.createDescType = FFX_API_CREATE_CONTEXT_DESC_TYPE_UPSCALE;
    // versionQuery.device = Device; // only for DirectX 12 applications
    uint64_t versionCount = 0;
    versionQuery.outputCount = &versionCount;
    ffxQuery(nullptr, &versionQuery.header);
    std::vector<const char*> versionNames;
    m_FsrVersionIds.resize(versionCount);
    versionNames.resize(versionCount);
    versionQuery.versionIds = m_FsrVersionIds.data();
    versionQuery.versionNames = versionNames.data();
    ffxQuery(nullptr, &versionQuery.header);


    //FG
    //auto* windowsSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<WindowSystemR>();
    //auto* window = windowsSystem->GetAppWindow();
    //auto* presentWindow = dynamic_cast<PresentWindowR*>(window);
    //auto* renderSwapchain = presentWindow->GetPresentSwapchain();
    //auto* swapchain = presentWindow->GetSwapchain();

    //VkSwapchainKHR currentSwapchain = dynamic_cast<VulkanSwapchain*>(swapchain)->Get();


    //ffx::CreateContextDescFrameGenerationSwapChainVK createSwapChainDesc{};
    //auto device = dynamic_cast<VulkanDevice*> (GetNGIDevicePtr());
    //createSwapChainDesc.physicalDevice = device->mPhysicalDevice;
    //createSwapChainDesc.device = device->Get();
    //// Pass swapchain to be replaced. Can also be null to only create new swapchain.
    //createSwapChainDesc.swapchain = &currentSwapchain;
    //createSwapChainDesc.createInfo = dynamic_cast<VulkanSwapchain*>(swapchain)->GetCreateInfo();
    //createSwapChainDesc.allocator = nullptr;
    //// Set queues
    //VkQueue gameQueue = VK_NULL_HANDLE;
    //vkGetDeviceQueue(reinterpret_cast<VkDevice>(device->GetNativeDevice()), device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 0, &gameQueue);
    //createSwapChainDesc.gameQueue.queue = gameQueue;
    //createSwapChainDesc.gameQueue.familyIndex = device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex;
    //createSwapChainDesc.gameQueue.submitFunc = nullptr;   // this queue is only used in vkQueuePresentKHR, hence doesn't need a callback

    //VkQueue computeQueue = VK_NULL_HANDLE;
    //vkGetDeviceQueue(reinterpret_cast<VkDevice>(device->GetNativeDevice()), device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 0, &computeQueue);
    //createSwapChainDesc.asyncComputeQueue.queue = computeQueue;
    //createSwapChainDesc.asyncComputeQueue.familyIndex = device->mPhysicalDeviceInfo->mComputeQueueInfo.familyIndex;
    //createSwapChainDesc.asyncComputeQueue.submitFunc = nullptr;

    //VkQueue presentQueue = VK_NULL_HANDLE;
    //vkGetDeviceQueue(reinterpret_cast<VkDevice>(device->GetNativeDevice()), device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 0, &presentQueue);
    //createSwapChainDesc.presentQueue.queue = presentQueue;
    //createSwapChainDesc.presentQueue.familyIndex = device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex;
    //createSwapChainDesc.presentQueue.submitFunc = nullptr;

    //VkQueue transQueue = VK_NULL_HANDLE;
    //vkGetDeviceQueue(reinterpret_cast<VkDevice>(device->GetNativeDevice()), device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 0, &transQueue);
    //createSwapChainDesc.imageAcquireQueue.queue = transQueue;
    //createSwapChainDesc.imageAcquireQueue.familyIndex = device->mPhysicalDeviceInfo->mTransferQueueInfo.familyIndex;
    //createSwapChainDesc.imageAcquireQueue.submitFunc = nullptr;

    //// make sure swapchain is not holding a ref to real swapchain
    //cauldron::GetFramework()->GetSwapChain()->GetImpl()->SetVKSwapChain(VK_NULL_HANDLE);

    //auto convertQueueInfo = [](VkQueueInfoFFXAPI queueInfo) {
    //    VkQueueInfoFFX info;
    //    info.queue = queueInfo.queue;
    //    info.familyIndex = queueInfo.familyIndex;
    //    info.submitFunc = queueInfo.submitFunc;
    //    return info;
    //};

    //VkFrameInterpolationInfoFFX frameInterpolationInfo = {};
    //frameInterpolationInfo.device = createSwapChainDesc.device;
    //frameInterpolationInfo.physicalDevice = createSwapChainDesc.physicalDevice;
    //frameInterpolationInfo.pAllocator = createSwapChainDesc.allocator;
    //frameInterpolationInfo.gameQueue = convertQueueInfo(createSwapChainDesc.gameQueue);
    //frameInterpolationInfo.asyncComputeQueue = convertQueueInfo(createSwapChainDesc.asyncComputeQueue);
    //frameInterpolationInfo.presentQueue = convertQueueInfo(createSwapChainDesc.presentQueue);
    //frameInterpolationInfo.imageAcquireQueue = convertQueueInfo(createSwapChainDesc.imageAcquireQueue);

    //ffx::ReturnCode retCode = ffx::CreateContext(m_SwapChainContext, nullptr, createSwapChainDesc);

    //// Get replacement function pointers
    //ffx::QueryDescSwapchainReplacementFunctionsVK replacementFunctions{};
    //ffx::Query(m_SwapChainContext, replacementFunctions);
    //cauldron::GetDevice()->GetImpl()->SetSwapchainMethodsAndContext(nullptr,
    //                                                                nullptr,
    //                                                                replacementFunctions.pOutGetSwapchainImagesKHR,
    //                                                                replacementFunctions.pOutAcquireNextImageKHR,
    //                                                                replacementFunctions.pOutQueuePresentKHR,
    //                                                                replacementFunctions.pOutSetHdrMetadataEXT,
    //                                                                replacementFunctions.pOutCreateSwapchainFFXAPI,
    //                                                                replacementFunctions.pOutDestroySwapchainFFXAPI,
    //                                                                nullptr,
    //                                                                replacementFunctions.pOutGetLastPresentCountFFXAPI,
    //                                                                m_SwapChainContext,
    //                                                                &frameInterpolationInfo);

    //// Set frameinterpolation swapchain to engine
    //cauldron::GetFramework()->GetSwapChain()->GetImpl()->SetVKSwapChain(currentSwapchain, true);


}

void FSR3SRPass::UpdateFSRContext(bool enabled, const GameContext& gameContext)
{
    if (enabled)
    {
        //auto resInfo = gameContext.mRenderPipeline->GetDisplaySize();

        // static bool s_InvertedDepth = GetConfig()->InvertedDepth;
        s_InvertedDepth = true;
        // Backend creation (for both FFXAPI contexts, FG and Upscale)

        isInit = true;
        auto device = GetNGIDevicePtr();
        // DeviceInternal* device = GetDevice()->GetImpl();
        ffx::CreateBackendVKDesc backendDesc{};
        backendDesc.header.type = FFX_API_CREATE_CONTEXT_DESC_TYPE_BACKEND_VK;
        backendDesc.vkDevice = dynamic_cast<VulkanDevice*>(device)->Get();
        backendDesc.vkPhysicalDevice = dynamic_cast<VulkanDevice*>(device)->mPhysicalDevice;

        backendDesc.vkDeviceProcAddr = dynamic_cast<VulkanDevice*>(device)->vkGetDeviceProcAddrProxy;

        if (m_UpscalerType == UpscalerType::Upscaler_FSRAPI)
        {
            ffx::CreateContextDescUpscale createFsr{};
            //m_UpscalingContextSize[gameContext.mRenderPipeline] = {m_DisplaySize.x, m_DisplaySize.y};
            m_UpscalingContextSize = {m_DisplaySize.x, m_DisplaySize.y};
            createFsr.maxUpscaleSize = {m_DisplaySize.x, m_DisplaySize.y};
            createFsr.maxRenderSize = {m_DisplaySize.x, m_DisplaySize.y};
            createFsr.flags = FFX_UPSCALE_ENABLE_AUTO_EXPOSURE;
            if (s_InvertedDepth)
            {
                createFsr.flags |= FFX_UPSCALE_ENABLE_DEPTH_INVERTED | FFX_UPSCALE_ENABLE_DEPTH_INFINITE;
            }
            createFsr.flags |= FFX_UPSCALE_ENABLE_HIGH_DYNAMIC_RANGE;
            // Create the FSR context
            {
                // createFsr.header.pNext = &backendDesc.header;
                ffx::ReturnCode retCode;
                // lifetime of this must last until after CreateContext call!
                ffx::CreateContextDescOverrideVersion versionOverride{};
                ffx::Context ffxContext = nullptr;
                if (m_FsrVersionIndex < m_FsrVersionIds.size())
                {
                    versionOverride.versionId = m_FsrVersionIds[m_FsrVersionIndex];
                    retCode = ffx::CreateContext(ffxContext, nullptr, createFsr, backendDesc, versionOverride);
                }
                else
                {
                    retCode = ffx::CreateContext(ffxContext, nullptr, createFsr, backendDesc);
                }
                //m_UpscalingContext[gameContext.mRenderPipeline] = ffxContext; 
                m_UpscalingContext = ffxContext; 
                if (retCode != ffx::ReturnCode::Ok)
                {
                    LOG_ERROR("Couldn't create the ffxapi upscaling context: {}", retCode);
                }
            }
            FfxApiEffectMemoryUsage gpuMemoryUsageUpscaler;
            ffx::QueryDescUpscaleGetGPUMemoryUsage upscalerGetGPUMemoryUsage{};
            upscalerGetGPUMemoryUsage.gpuMemoryUsageUpscaler = &gpuMemoryUsageUpscaler;

            //ffx::Query(m_UpscalingContext[gameContext.mRenderPipeline], upscalerGetGPUMemoryUsage);
            ffx::Query(m_UpscalingContext, upscalerGetGPUMemoryUsage);

            LOG_INFO("Upscaler Context VRAM totalUsageInBytes{} MB aliasableUsageInBytes {} MB", gpuMemoryUsageUpscaler.totalUsageInBytes / 1048576.f, gpuMemoryUsageUpscaler.aliasableUsageInBytes / 1048576.f);
        }
    }
}

void FSR3SRPass::AssembleJitterCancel(const GameContext& gameContext, REDTextureView* input, REDTextureView*& output, bool noCulling)
{
    Assert(input);

    if (!output)
    {
        const auto& desc = input->mTexture->mDesc;
        const auto size = gameContext.mRenderPipeline->GetDisplaySize();   // display size by default
        output = gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Jitter Cancel Output", size.x, size.y, desc.Format, desc.Usage);
    }

    REDPass* pass = nullptr;
    NGIClearValue clearValue{{0, 0, 0, 0}};

    auto* RED = gameContext.mRenderPipeline->RED();
    if (FormatHasDepth(output->mTexture->mDesc.Format))
    {
        clearValue.depthStencil = {0.f, 0};
        REDDepthStencilTargetDesc depthStencilTarget{output, NGILoadOp::Clear, NGIStoreOp::Store, NGILoadOp::DontCare, NGIStoreOp::DontCare, clearValue};

        RED->BeginRenderPass("FFX_FSR2_DEPTH_JITTER_CANCEL", 0, nullptr, &depthStencilTarget, noCulling);
        pass = RED->AllocateSubRenderPass("FFX_FSR2_DEPTH_JITTER_CANCEL", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);
        pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_UPSCALE_SPATIAL_DEPTH"), true);
    }
    else
    {
        REDColorTargetDesc renderTarget{output, NGILoadOp::Clear, NGIStoreOp::Store, clearValue};
        NGIRenderPassTargetIndex renderTargetIndex = static_cast<NGIRenderPassTargetIndex>(0);

        RED->BeginRenderPass("FFX_FSR2_COLOR_JITTER_CANCEL", 1, &renderTarget, nullptr, noCulling);
        pass = RED->AllocateSubRenderPass("FFX_FSR2_COLOR_JITTER_CANCEL", 0, nullptr, 1, &renderTargetIndex, REDPassFlagBit{0});
        pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_UPSCALE_SPATIAL_DEPTH"), false);
    }

    pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_JITTER_CANCEL"), true);
    pass->SetProperty(NAME_ID("r_input"), input, NGIResourceState::PixelShaderShaderResource);
    pass->SetProperty(NAME_ID("fUVJitterOffset"), gameContext.mRenderPipeline->GetJitterData()->GetOffsetInUVSpace());

    REDDrawScreenQuad drawInfo{gameContext.mRenderPipeline->GetPostProcessMtl(), "ffx_fsr2_spatial_scale"};
    pass->DrawScreenQuad(drawInfo);
    RED->EndRenderPass();
}


void FSR3SRPass::FillInput(const GameContext& gameContext)
{
    bool bCopyFromFrameDepth = true;
    if (bCopyFromFrameDepth)
    {
        auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        UInt32 DepthStencilWidth = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Width;
        UInt32 DepthStencilHeight = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Height;

        auto format = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Format;
        NGITextureDesc depthStencilDesc{
            format,
            NGITextureType::Texture2D,
            1,
            1,
            DepthStencilWidth,
            DepthStencilHeight,
            1,
            1,
            NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
        };
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() = RED->AllocateTexture("LastFrameDepthStencil", depthStencilDesc);
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>()->ExtendLifetime();

        NGICopyTexture region1 = {0};
        region1.SrcSubresource = NGICalcSubresource(0, 0, 0, 1, 1);
        region1.SrcOffset = NGIOffset3D{0, 0, 0};
        region1.DstSubresource = NGICalcSubresource(0, 0, 0, 1, 1);
        region1.DstOffset = NGIOffset3D{0, 0, 0};
        region1.Extent = NGIExtent3D{DepthStencilWidth, DepthStencilHeight, 1};

        auto* pass = RED->AllocatePass("CopyDepthStencil");
        pass->CopyTextureToTexture(gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>(), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>(), 1, &region1);
    }
    else
    {
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->ExtendLifetime();
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>();
    }
    auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    if (!ffspipeline)
        return;
    mSetting = ffspipeline->GetSetting()->mFSR3Setting;
    
    Init(gameContext);
}

bool FSR3SRPass::NeedsReInit()
{
    return m_NeedReInit;
}

void FSR3SRPass::Init(const GameContext& gameContext)
{
    m_DisplaySize = gameContext.mRenderPipeline->GetDisplaySize();
    //bool needNewContext = m_UpscalingContext.find(gameContext.mRenderPipeline) == m_UpscalingContext.end();
    bool needNewContext = m_UpscalingContext?false:true;
    if (!needNewContext && (m_DisplaySize.x != m_UpscalingContextSize.x||m_DisplaySize.y != m_UpscalingContextSize.y||mSetting.FSR_Mode!=lastMode)) 
        //if (!needNewContext && (m_DisplaySize.x != m_UpscalingContextSize[gameContext.mRenderPipeline].x||m_DisplaySize.y != m_UpscalingContextSize[gameContext.mRenderPipeline].y||mSetting.FSR_Mode!=lastMode))
    {
        lastMode = mSetting.FSR_Mode;
        needNewContext = true;
    }
    //if (!isInit)
    //{
    //    isInit = true;
    //    //Destroy the FSR context
    //    UpdateFSRContext(true, gameContext);
    //}
    if (NeedsReInit() || needNewContext)
    {
        if (needNewContext)
        {
             //if (m_UpscalingContext.find(gameContext.mRenderPipeline) != m_UpscalingContext.end())
             if (m_UpscalingContext)
             {
                 GetNGIDevicePtr()->WaitIdle();
                 //ffx::ReturnCode retCodeForDestroy = ffx::DestroyContext(m_UpscalingContext[gameContext.mRenderPipeline]);
                 ffx::ReturnCode retCodeForDestroy = ffx::DestroyContext(m_UpscalingContext);
                 if (retCodeForDestroy != ffx::ReturnCode::Ok)
                 {
                     LOG_ERROR("Couldn't destroy the ffxapi upscaling context: {}", retCodeForDestroy);
                 }
             }
            //m_UpscalingContext[gameContext.mRenderPipeline] = nullptr;
            m_UpscalingContext = nullptr;
        }
        //UpdateFSRContext(false, gameContext);
        UpdateFSRContext(true, gameContext);
       
    }
    
}

 void FSR3SRPass::ClearTexture(RenderingExecutionDescriptor* RED, REDTextureView* texture, NGIClearValue color)
{
    RED->AllocatePass("Clear " + texture->mTexture->GetName(), true)->ClearTexture(texture, color);
}

 
VkImageType cross::MapTextureType(NGITextureType type)
{
    switch (type)
    {
    case NGITextureType::Texture1D:
    case NGITextureType::Texture1DArray:
        return VK_IMAGE_TYPE_1D;
    case NGITextureType::Texture2D:
    case NGITextureType::Texture2DArray:
    case NGITextureType::TextureCube:
    case NGITextureType::TextureCubeArray:
        return VK_IMAGE_TYPE_2D;
    case NGITextureType::Texture3D:
        return VK_IMAGE_TYPE_3D;
    default:
        AssertMsg(false, "Should not run to this branch");
        return VK_IMAGE_TYPE_MAX_ENUM;
    }
}

VkFormat cross::MapGraphicsFormat(GraphicsFormat format)
{
    switch (format)
    {
    case GraphicsFormat::Unknown:
        return VK_FORMAT_UNDEFINED;
    case GraphicsFormat::R8_SRGB:
        return VK_FORMAT_R8_SRGB;
    case GraphicsFormat::R8G8_SRGB:
        return VK_FORMAT_R8G8_SRGB;
    case GraphicsFormat::R8G8B8_SRGB:
        return VK_FORMAT_R8G8B8_SRGB;
    case GraphicsFormat::R8G8B8A8_SRGB:
        return VK_FORMAT_R8G8B8A8_SRGB;
    case GraphicsFormat::R8_UNorm:
        return VK_FORMAT_R8_UNORM;
    case GraphicsFormat::R8G8_UNorm:
        return VK_FORMAT_R8G8_UNORM;
    case GraphicsFormat::R8G8B8_UNorm:
        return VK_FORMAT_R8G8B8_UNORM;
    case GraphicsFormat::R8G8B8A8_UNorm:
        return VK_FORMAT_R8G8B8A8_UNORM;
    case GraphicsFormat::R8_SNorm:
        return VK_FORMAT_R8_SNORM;
    case GraphicsFormat::R8G8_SNorm:
        return VK_FORMAT_R8G8_SNORM;
    case GraphicsFormat::R8G8B8_SNorm:
        return VK_FORMAT_R8G8B8_SNORM;
    case GraphicsFormat::R8G8B8A8_SNorm:
        return VK_FORMAT_R8G8B8A8_SNORM;
    case GraphicsFormat::R8_UInt:
        return VK_FORMAT_R8_UINT;
    case GraphicsFormat::R8G8_UInt:
        return VK_FORMAT_R8G8_UINT;
    case GraphicsFormat::R8G8B8_UInt:
        return VK_FORMAT_R8G8B8_UINT;
    case GraphicsFormat::R8G8B8A8_UInt:
        return VK_FORMAT_R8G8B8A8_UINT;
    case GraphicsFormat::R8_SInt:
        return VK_FORMAT_R8_SINT;
    case GraphicsFormat::R8G8_SInt:
        return VK_FORMAT_R8G8_SINT;
    case GraphicsFormat::R8G8B8_SInt:
        return VK_FORMAT_R8G8B8_SINT;
    case GraphicsFormat::R8G8B8A8_SInt:
        return VK_FORMAT_R8G8B8A8_SINT;
    case GraphicsFormat::R16_UNorm:
        return VK_FORMAT_R16_UNORM;
    case GraphicsFormat::R16G16_UNorm:
        return VK_FORMAT_R16G16_UNORM;
    case GraphicsFormat::R16G16B16_UNorm:
        return VK_FORMAT_R16G16B16_UNORM;
    case GraphicsFormat::R16G16B16A16_UNorm:
        return VK_FORMAT_R16G16B16A16_UNORM;
    case GraphicsFormat::R16_SNorm:
        return VK_FORMAT_R16_SNORM;
    case GraphicsFormat::R16G16_SNorm:
        return VK_FORMAT_R16G16_SNORM;
    case GraphicsFormat::R16G16B16_SNorm:
        return VK_FORMAT_R16G16B16_SNORM;
    case GraphicsFormat::R16G16B16A16_SNorm:
        return VK_FORMAT_R16G16B16A16_SNORM;
    case GraphicsFormat::R16_UInt:
        return VK_FORMAT_R16_UINT;
    case GraphicsFormat::R16G16_UInt:
        return VK_FORMAT_R16G16_UINT;
    case GraphicsFormat::R16G16B16_UInt:
        return VK_FORMAT_R16G16B16_UINT;
    case GraphicsFormat::R16G16B16A16_UInt:
        return VK_FORMAT_R16G16B16A16_UINT;
    case GraphicsFormat::R16_SInt:
        return VK_FORMAT_R16_SINT;
    case GraphicsFormat::R16G16_SInt:
        return VK_FORMAT_R16G16_SINT;
    case GraphicsFormat::R16G16B16_SInt:
        return VK_FORMAT_R16G16B16_SINT;
    case GraphicsFormat::R16G16B16A16_SInt:
        return VK_FORMAT_R16G16B16A16_SINT;
    case GraphicsFormat::R32_UInt:
        return VK_FORMAT_R32_UINT;
    case GraphicsFormat::R32G32_UInt:
        return VK_FORMAT_R32G32_UINT;
    case GraphicsFormat::R32G32B32_UInt:
        return VK_FORMAT_R32G32B32_UINT;
    case GraphicsFormat::R32G32B32A32_UInt:
        return VK_FORMAT_R32G32B32A32_UINT;
    case GraphicsFormat::R32_SInt:
        return VK_FORMAT_R32_SINT;
    case GraphicsFormat::R32G32_SInt:
        return VK_FORMAT_R32G32_SINT;
    case GraphicsFormat::R32G32B32_SInt:
        return VK_FORMAT_R32G32B32_SINT;
    case GraphicsFormat::R32G32B32A32_SInt:
        return VK_FORMAT_R32G32B32A32_SINT;
    case GraphicsFormat::R16_SFloat:
        return VK_FORMAT_R16_SFLOAT;
    case GraphicsFormat::R16G16_SFloat:
        return VK_FORMAT_R16G16_SFLOAT;
    case GraphicsFormat::R16G16B16_SFloat:
        return VK_FORMAT_R16G16B16_SFLOAT;
    case GraphicsFormat::R16G16B16A16_SFloat:
        return VK_FORMAT_R16G16B16A16_SFLOAT;
    case GraphicsFormat::R32_SFloat:
        return VK_FORMAT_R32_SFLOAT;
    case GraphicsFormat::R32G32_SFloat:
        return VK_FORMAT_R32G32_SFLOAT;
    case GraphicsFormat::R32G32B32_SFloat:
        return VK_FORMAT_R32G32B32_SFLOAT;
    case GraphicsFormat::R32G32B32A32_SFloat:
        return VK_FORMAT_R32G32B32A32_SFLOAT;
    case GraphicsFormat::B8G8R8_SRGB:
        return VK_FORMAT_B8G8R8_SRGB;
    case GraphicsFormat::B8G8R8A8_SRGB:
        return VK_FORMAT_B8G8R8A8_SRGB;
    case GraphicsFormat::B8G8R8_UNorm:
        return VK_FORMAT_B8G8R8_UNORM;
    case GraphicsFormat::B8G8R8A8_UNorm:
        return VK_FORMAT_B8G8R8A8_UNORM;
    case GraphicsFormat::B8G8R8_SNorm:
        return VK_FORMAT_B8G8R8_SNORM;
    case GraphicsFormat::B8G8R8A8_SNorm:
        return VK_FORMAT_B8G8R8A8_SNORM;
    case GraphicsFormat::B8G8R8_UInt:
        return VK_FORMAT_B8G8R8_UINT;
    case GraphicsFormat::B8G8R8A8_UInt:
        return VK_FORMAT_B8G8R8A8_UINT;
    case GraphicsFormat::B8G8R8_SInt:
        return VK_FORMAT_B8G8R8_SINT;
    case GraphicsFormat::B8G8R8A8_SInt:
        return VK_FORMAT_B8G8R8A8_SINT;

    case GraphicsFormat::R4G4B4A4_UNormPack16:
        return VK_FORMAT_R4G4B4A4_UNORM_PACK16;
    case GraphicsFormat::R5G6B5_UNormPack16:
        return VK_FORMAT_R5G6B5_UNORM_PACK16;
    case GraphicsFormat::R5G5B5A1_UNormPack16:
        return VK_FORMAT_R5G5B5A1_UNORM_PACK16;

    case GraphicsFormat::R9G9B9E5_UFloatPack32:
        return VK_FORMAT_E5B9G9R9_UFLOAT_PACK32;
    case GraphicsFormat::R11G11B10_UFloatPack32:
        return VK_FORMAT_B10G11R11_UFLOAT_PACK32;
    case GraphicsFormat::A2B10G10R10_UNormPack32:
        return VK_FORMAT_A2B10G10R10_UNORM_PACK32;
    case GraphicsFormat::A2B10G10R10_UIntPack32:
        return VK_FORMAT_A2B10G10R10_UINT_PACK32;
    case GraphicsFormat::A2B10G10R10_SIntPack32:
        return VK_FORMAT_A2B10G10R10_SINT_PACK32;
    case GraphicsFormat::A2R10G10B10_UNormPack32:
        return VK_FORMAT_A2R10G10B10_UNORM_PACK32;
    case GraphicsFormat::A2R10G10B10_UIntPack32:
        return VK_FORMAT_A2R10G10B10_UINT_PACK32;
    case GraphicsFormat::A2R10G10B10_SIntPack32:
        return VK_FORMAT_A2R10G10B10_SINT_PACK32;

    case GraphicsFormat::A2R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A2R10G10B10_XRUNormPack32:
    case GraphicsFormat::R10G10B10_XRSRGBPack32:
    case GraphicsFormat::R10G10B10_XRUNormPack32:
    case GraphicsFormat::A10R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A10R10G10B10_XRUNormPack32:
        Assert(false);
        return VK_FORMAT_UNDEFINED;

    case GraphicsFormat::D16_UNorm:
        return VK_FORMAT_D16_UNORM;
    case GraphicsFormat::D24_UNorm_X8:
        return VK_FORMAT_X8_D24_UNORM_PACK32;
    case GraphicsFormat::D24_UNorm_S8_UInt:
        return VK_FORMAT_D24_UNORM_S8_UINT;
    case GraphicsFormat::D32_SFloat:
        return VK_FORMAT_D32_SFLOAT;
    case GraphicsFormat::D32_SFloat_S8_UInt:
        return VK_FORMAT_D32_SFLOAT_S8_UINT;
    case GraphicsFormat::S8_UInt:
        return VK_FORMAT_S8_UINT;

    case GraphicsFormat::RGB_BC1_SRGB:
        return VK_FORMAT_BC1_RGB_SRGB_BLOCK;
    case GraphicsFormat::RGB_BC1_UNorm:
        return VK_FORMAT_BC1_RGB_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC1_SRGB:
        return VK_FORMAT_BC1_RGBA_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC1_UNorm:
        return VK_FORMAT_BC1_RGBA_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC2_SRGB:
        return VK_FORMAT_BC2_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC2_UNorm:
        return VK_FORMAT_BC2_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC3_SRGB:
        return VK_FORMAT_BC3_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC3_UNorm:
        return VK_FORMAT_BC3_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_UNorm:
        return VK_FORMAT_BC4_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_SNorm:
        return VK_FORMAT_BC4_SNORM_BLOCK;
    case GraphicsFormat::RG_BC5_UNorm:
        return VK_FORMAT_BC5_UNORM_BLOCK;
    case GraphicsFormat::RG_BC5_SNorm:
        return VK_FORMAT_BC5_SNORM_BLOCK;
    case GraphicsFormat::RGB_BC6H_UFloat:
        return VK_FORMAT_BC6H_UFLOAT_BLOCK;
    case GraphicsFormat::RGB_BC6H_SFloat:
        return VK_FORMAT_BC6H_SFLOAT_BLOCK;
    case GraphicsFormat::RGBA_BC7_SRGB:
        return VK_FORMAT_BC7_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC7_UNorm:
        return VK_FORMAT_BC7_UNORM_BLOCK;

    case GraphicsFormat::RGB_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG;

    case GraphicsFormat::RGB_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK;
    case GraphicsFormat::RGB_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_UNorm:
        return VK_FORMAT_EAC_R11_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_SNorm:
        return VK_FORMAT_EAC_R11_SNORM_BLOCK;
    case GraphicsFormat::RG_EAC_UNorm:
        return VK_FORMAT_EAC_R11G11_UNORM_BLOCK;
    case GraphicsFormat::RG_EAC_SNorm:
        return VK_FORMAT_EAC_R11G11_SNORM_BLOCK;

    case GraphicsFormat::RGBA_ASTC4X4_SRGB:
        return VK_FORMAT_ASTC_4x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UNorm:
        return VK_FORMAT_ASTC_4x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UFloat:
        return VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X4_SRGB:
        return VK_FORMAT_ASTC_5x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UNorm:
        return VK_FORMAT_ASTC_5x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UFloat:
        return VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X5_SRGB:
        return VK_FORMAT_ASTC_5x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UNorm:
        return VK_FORMAT_ASTC_5x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UFloat:
        return VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X5_SRGB:
        return VK_FORMAT_ASTC_6x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UNorm:
        return VK_FORMAT_ASTC_6x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UFloat:
        return VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X6_SRGB:
        return VK_FORMAT_ASTC_6x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UNorm:
        return VK_FORMAT_ASTC_6x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UFloat:
        return VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X5_SRGB:
        return VK_FORMAT_ASTC_8x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UNorm:
        return VK_FORMAT_ASTC_8x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UFloat:
        return VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X6_SRGB:
        return VK_FORMAT_ASTC_8x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UNorm:
        return VK_FORMAT_ASTC_8x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UFloat:
        return VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X8_SRGB:
        return VK_FORMAT_ASTC_8x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UNorm:
        return VK_FORMAT_ASTC_8x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UFloat:
        return VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X5_SRGB:
        return VK_FORMAT_ASTC_10x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UNorm:
        return VK_FORMAT_ASTC_10x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UFloat:
        return VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X6_SRGB:
        return VK_FORMAT_ASTC_10x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UNorm:
        return VK_FORMAT_ASTC_10x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UFloat:
        return VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X8_SRGB:
        return VK_FORMAT_ASTC_10x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UNorm:
        return VK_FORMAT_ASTC_10x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UFloat:
        return VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X10_SRGB:
        return VK_FORMAT_ASTC_10x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UNorm:
        return VK_FORMAT_ASTC_10x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UFloat:
        return VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X10_SRGB:
        return VK_FORMAT_ASTC_12x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UNorm:
        return VK_FORMAT_ASTC_12x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UFloat:
        return VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X12_SRGB:
        return VK_FORMAT_ASTC_12x12_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UNorm:
        return VK_FORMAT_ASTC_12x12_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UFloat:
        return VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT;
    default:
        Assert(false);
        return VK_FORMAT_UNDEFINED;
    }
}

VkSampleCountFlagBits cross::MapSampleCount(UInt32 count)
{
    switch (count)
    {
    case 1:
        return VK_SAMPLE_COUNT_1_BIT;
    case 2:
        return VK_SAMPLE_COUNT_2_BIT;
    case 4:
        return VK_SAMPLE_COUNT_4_BIT;
    case 8:
        return VK_SAMPLE_COUNT_8_BIT;
    case 16:
        return VK_SAMPLE_COUNT_16_BIT;
    case 32:
        return VK_SAMPLE_COUNT_32_BIT;
    case 64:
        return VK_SAMPLE_COUNT_64_BIT;
    default:
        AssertMsg(false, "Invalid sample count");
        return VK_SAMPLE_COUNT_FLAG_BITS_MAX_ENUM;
    }
}


VkImageUsageFlags cross::MapTextureUsage(NGITextureUsage usage)
{
    VkImageUsageFlags vkUsageFlags = 0;
    if (EnumHasAnyFlags(usage, NGITextureUsage::CopySrc))
        vkUsageFlags |= VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::CopyDst))
        vkUsageFlags |= VK_IMAGE_USAGE_TRANSFER_DST_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::RenderTarget))
        vkUsageFlags |= VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::DepthStencil))
        vkUsageFlags |= VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::UnorderedAccess))
        vkUsageFlags |= VK_IMAGE_USAGE_STORAGE_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::ShaderResource))
        vkUsageFlags |= VK_IMAGE_USAGE_SAMPLED_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::SubpassInput))
        vkUsageFlags |= VK_IMAGE_USAGE_INPUT_ATTACHMENT_BIT, vkUsageFlags |= VK_IMAGE_USAGE_SAMPLED_BIT;
    return vkUsageFlags;
}

VkImageCreateInfo CreateImageCreateInfoForFSR(const NGITextureDesc& desc, bool shared=false)
{
    VmaAllocationCreateInfo allocCreateInfo{
        VMA_ALLOCATION_CREATE_USER_DATA_COPY_STRING_BIT,
        VMA_MEMORY_USAGE_GPU_ONLY,
    };
    // use const_cast to pass andrioid;
    // allocCreateInfo.pUserData = const_cast<char*>(pDebugName);
    VkImageCreateInfo createInfo{
        VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO,
        nullptr,
        desc.MutableFormat ? VK_IMAGE_CREATE_MUTABLE_FORMAT_BIT : 0u,
        MapTextureType(desc.Dimension),
        MapGraphicsFormat(desc.Format),
        {
            desc.Width,
            0,
            0,
        },
        desc.MipCount,
        0,
        MapSampleCount(desc.SampleCount),
        allocCreateInfo.usage == VMA_MEMORY_USAGE_GPU_TO_CPU ? VK_IMAGE_TILING_LINEAR : VK_IMAGE_TILING_OPTIMAL,
        MapTextureUsage(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
        VK_IMAGE_LAYOUT_UNDEFINED,
    };
    switch (desc.Dimension)
    {
    case NGITextureType::Texture1D:
        Assert(desc.Height == 1);
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture1DArray:
        Assert(desc.Height == 1);
        Assert(desc.Depth == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture2D:
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture2DArray:
        Assert(desc.Depth == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture3D:
        Assert(desc.ArraySize == 1);
        createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = desc.Depth;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::TextureCube:
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 6);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        // createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 6;
        break;
    case NGITextureType::TextureCubeArray:
        Assert(desc.ArraySize % 6 == 0);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        // createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    default:
        Assert(false);
        break;
    }

    VkExternalMemoryImageCreateInfo extImageCreateInfo = {};
    if (shared)
    {
        extImageCreateInfo.sType = VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO;
#if CROSSENGINE_WIN
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_BIT;
#else
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_FD_BIT_KHR;
#endif
        createInfo.pNext = &extImageCreateInfo;

        // allocCreateInfo.pool = GetDevice<VulkanDevice>()->GetExportableVMAPool();
    }
    return createInfo;
}

FfxApiResource FSR3SRPass::TransferTextureToFSR(REDTextureView* texview, uint32_t state, uint32_t additionalUsages)
{
    if (texview == nullptr)
    {
        return ffxApiGetResourceVK(nullptr, FfxApiResourceDescription(), state);
    }
    REDTexture* tex = texview->GetTexture();
    auto texNative = tex->GetNativeTexture();
    VkImage image = reinterpret_cast<VkImage>(texNative->GetNativeHandle());
    auto createInfo = CreateImageCreateInfoForFSR(tex->GetDesc());
    return ffxApiGetResourceVK((void*)image, ffxApiGetImageResourceDescriptionVK(image, createInfo, additionalUsages), state);
}


void FSR3SRPass::Execute(const GameContext& gameContext)
{
    if (mSetting.enable)
    {
        auto m_DlsplaySize = gameContext.mRenderPipeline->GetDisplaySize();
        //static bool s_InvertedDepth = true;
        auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        auto gbuffer = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::GBufferViews>();
        REDTextureView* motionVectorView = nullptr;
        red->BeginRegion("FSR3SR");
        const UInt2 renderSize = gameContext.mRenderPipeline->GetRenderSize();
        // 1. Prepare motion vector texture for DLSS
        motionVectorView =
            gameContext.mRenderPipeline->CreateTextureView2D("MotionVectorForDLSS", renderSize.x, renderSize.y, GraphicsFormat::R16G16_SFloat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst);
        ClearTexture(red, motionVectorView, {0.f, 0.f, 0.f, 0.f});

        const auto jitterData = gameContext.mRenderPipeline->GetJitterData();
        auto* velocity_pass = red->AllocatePass("MotionVectorPassForDLSS", true);
        velocity_pass->SetProperty(NAME_ID("ViewSize"), renderSize);
        velocity_pass->SetProperty(NAME_ID("ViewInvSize"), Float2(1.0f / renderSize.x, 1.0f / renderSize.y));
        velocity_pass->SetProperty(NAME_ID("fReprojectionMat"), jitterData->GetReprojectionMatrixNoJitter());
        velocity_pass->SetProperty(NAME_ID("InputDepth"), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>(), NGIResourceState::ComputeShaderShaderResource);
        velocity_pass->SetProperty(NAME_ID("InputVelocity"), gbuffer[3]);
        velocity_pass->SetProperty(NAME_ID("OutputTexture"), motionVectorView, NGIResourceState::ComputeShaderUnorderedAccess);
        velocity_pass->Dispatch(mSetting.FSR3ComputeShaderR, "ConvertVelocityCS", (renderSize.x + 8 - 1) / 8, (renderSize.y + 8 - 1) / 8, 1);
        // Original SceneDepth
        REDTextureView* depthView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>();

        // auto transparentReactive = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::FSRTransparentReactiveMaskView>();
        // auto separateTranslucency = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>();
        // auto reactiveview = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::FSRReactiveMaskView>();
        // REDTextureView* reactive = transparentReactive ? transparentReactive : reactiveview;
        // if (reactiveview && transparentReactive)
        // {
        //     auto* pass = red->AllocatePass("FFX_FSR3_MERGE_REACTIVE", true);
        //     pass->SetProperty(NAME_ID("ReactiveTexture"), reactiveview, NGIResourceState::ComputeShaderShaderResource);
        //     pass->SetProperty(NAME_ID("RWReactiveTexture"), transparentReactive, NGIResourceState::ComputeShaderUnorderedAccess);
        //     UInt3 groupSize{8, 8, 1};
        //     pass->Dispatch(mSetting.FSR3ComputeShaderR, "MergeReactiveCS", (renderSize.x + groupSize.x - 1) / groupSize.x, (renderSize.y + groupSize.y - 1) / groupSize.y, groupSize.z);
        // }



        // if (separateTranslucency)
        // {
        //     separateTranslucencyAlpha = gameContext.mRenderPipeline->CreateTextureView2D(
        //         "SeparateTranslucencyAlpha", renderSize.x, renderSize.y, GraphicsFormat::R8_UNorm, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst);
        //     ClearTexture(red, separateTranslucencyAlpha, {0.f, 0.f, 0.f, 0.f});
        //     auto* pass = red->AllocatePass("FFX_FSR3_Separate_Translucency_Alpha",true);
        //     pass->SetProperty(NAME_ID("SeparateTranslucency"), separateTranslucency, NGIResourceState::ComputeShaderShaderResource);
        //     pass->SetProperty(NAME_ID("SeparateTranslucencyAlpha"), separateTranslucencyAlpha, NGIResourceState::ComputeShaderUnorderedAccess);
        //     UInt3 groupSize{8, 8, 1};
        //     pass->Dispatch(mSetting.FSR3ComputeShaderR, "GetSeparateTranslucencyAlpha", (renderSize.x + groupSize.x - 1) / groupSize.x, (renderSize.y + groupSize.y - 1) / groupSize.y, groupSize.z);
        // }

        auto* pass = red->AllocatePass("FFXSR", true);
        REDTextureView* colorInView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
        REDTexture* colorInTex = colorInView->GetTexture();
        const auto& desc = colorInTex->mDesc;
        const auto size = gameContext.mRenderPipeline->GetDisplaySize();

        REDTextureView* colorOutView =
            gameContext.mRenderPipeline->CreateTextureView2D("FSR Out Color", size.x, size.y, desc.Format, desc.Usage | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess);

        pass->AddTextureReference(motionVectorView, REDResourceState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit));
        pass->AddTextureReference(depthView, REDResourceState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit));
        pass->AddTextureReference(colorInView, REDResourceState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit));
        pass->AddTextureReference(colorOutView, REDResourceState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ComputeShaderBit));

        auto lambda = [=](REDPass* pass, NGICommandList* cmdList) {
            ffx::DispatchDescUpscale dispatchUpscale{};
           
            dispatchUpscale.commandList = reinterpret_cast<VkCommandBuffer_T*>(cmdList->GetNativeHandle());
            dispatchUpscale.color = TransferTextureToFSR(colorInView, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            dispatchUpscale.depth = TransferTextureToFSR(depthView, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            dispatchUpscale.motionVectors = TransferTextureToFSR(motionVectorView, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            dispatchUpscale.exposure = TransferTextureToFSR(nullptr, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            dispatchUpscale.output = TransferTextureToFSR(colorOutView, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            dispatchUpscale.reactive = TransferTextureToFSR(nullptr, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            dispatchUpscale.transparencyAndComposition = TransferTextureToFSR(nullptr, FFX_API_RESOURCE_STATE_PIXEL_COMPUTE_READ);
            // Jitter is calculated earlier in the frame using a callback from the camera update
            auto jitter = gameContext.mRenderPipeline->GetJitterData()->jitter;
            dispatchUpscale.jitterOffset.x = jitter.x;
            dispatchUpscale.jitterOffset.y = jitter.y;
            dispatchUpscale.motionVectorScale.x = (float)motionVectorView->GetSize().x;
            dispatchUpscale.motionVectorScale.y = (float)motionVectorView->GetSize().y;
            dispatchUpscale.reset = false;
            dispatchUpscale.enableSharpening = false;
            dispatchUpscale.sharpness = m_Sharpness;

            // Cauldron keeps time in seconds, but FSR expects milliseconds
            //dispatchUpscale.frameTimeDelta = static_cast<float>(deltaTime * 1000.f);

            dispatchUpscale.preExposure = mSetting.AutoExposure ? mSetting.PreExposure : 1.0f;
            dispatchUpscale.renderSize.width = (uint32_t)motionVectorView->GetSize().x;
            dispatchUpscale.renderSize.height = (uint32_t)motionVectorView->GetSize().y;
            dispatchUpscale.upscaleSize.width = m_DlsplaySize.x;
            dispatchUpscale.upscaleSize.height = m_DlsplaySize.y;

            auto camera = gameContext.mRenderPipeline->GetRenderCamera();
            auto cameraAspectRatio = camera->GetAspectRatio();
            auto cameraFOV = camera->GetFOV();
            // Setup camera params as required
            dispatchUpscale.cameraFovAngleVertical = 2.0f * atan(tan(cameraFOV / 2.0f) / cameraAspectRatio);
           
            if (s_InvertedDepth)
            {
                dispatchUpscale.cameraFar = camera->GetNearPlane();
                dispatchUpscale.cameraNear = FLT_MAX;
            }
            else
            {
                dispatchUpscale.cameraFar = camera->GetNearPlane();
                dispatchUpscale.cameraNear = camera->GetNearPlane();
            }

            dispatchUpscale.flags = 0;
            //ffx::ReturnCode retCode = ffx::Dispatch(m_UpscalingContext[gameContext.mRenderPipeline], dispatchUpscale);
            ffx::ReturnCode retCode = ffx::Dispatch(m_UpscalingContext, dispatchUpscale);
            if (retCode != ffx::ReturnCode::Ok)
            {
                LOG_ERROR("Dispatching FSR upscaling failed: {}", (uint32_t)retCode);
            }

        };

        AssembleJitterCancel(gameContext, depthView, gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>(), false);
        pass->OnExecute(lambda);
        red->EndRegion();
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = colorOutView;

        //if (mSetting.FSR_Mode != FSRScalePreset::NativeAA)
        //{
        //    // FFXAPI
        //    // All cauldron resources come into a render module in a generic read state (ResourceState::NonPixelShaderResource | ResourceState::PixelShaderResource)
        //  
        //}


        //m_FrameID += uint64_t(1 + m_SimulatePresentSkip);
        //m_SimulatePresentSkip = false;

        //m_ResetUpscale = false;
        //m_ResetFrameInterpolation = false;

        //// FidelityFX contexts modify the set resource view heaps, so set the cauldron one back
        //SetAllResourceViewHeaps(pCmdList);

        //// We are now done with upscaling
        //GetFramework()->SetUpscalingState(UpscalerState::PostUpscale);
    }
}
}
#endif