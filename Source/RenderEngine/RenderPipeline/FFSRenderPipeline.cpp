#include "EnginePrefix.h"
#include "FFSRenderPipeline.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameCounter.h"
#include "Effects/BlitLowResSceneColor.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/DecalSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/ReflectionProbeSystemR.h"
#include "RenderEngine/ShadowSystemR.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include "RenderEngine/PostProcessVolumeSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/BuiltinShaderParamsNames.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/CastShadowFrustum.h"
#include "RenderEngine/VirtualTexture/VirtualTextureFeedbackBuffer.h"
#include "RenderEngine/VirtualTexture/VirtualTextureSystemR.h"
#include "RenderEngine/TerrainSystemR.h"
#include "RenderEngine/RenderPipeline/Effects/MPCDIPass.h"
#include "RenderEngine/RenderPipeline/Effects/WindowEffectPass.h"
#include "RenderEngine/RenderPipeline/Effects/GizmoPass.h"
#include "RenderEngine/RenderPipeline/Effects/DebugGUI.h"
#include "RenderEngine/RenderPipeline/Effects/UIPass.h"
#include "RenderEngine/RenderPipeline/Effects/FFSOutlineWireFrame.h"
#include "RenderEngine/RenderPipeline/Effects/ParticlePass.h"
#include "RenderEngine/RenderPipeline/Effects/ScreenBlur.h"
#include "RenderEngine/RenderPipeline/Effects/MassiveLightsShadow.h"
#include "RenderEngine/RenderPipeline/Effects/SSAAResolve.h"
#include "RenderEngine/RenderPipeline/Effects/SubSampleShading.h"
#include "RenderEngine/RenderPipeline/Effects/GPass.h"
#include "RenderEngine/RenderPipeline/Effects/GTAO.h"
#include "RenderEngine/RenderPipeline/Effects/DLSS.h"
#include "RenderEngine/InstanceCulling/InstanceCulling.h"

bool cross::FFSRenderPipeline::sTODChanging = false;

bool cross::FFSRenderPipeline::sEnableSkyLightVoxel = true;

void FFSRenderPipeline_SetTODChanging(bool bTODChanging)
{
    cross::FFSRenderPipeline::sTODChanging = bTODChanging;
}

void FFSRenderPipeline_SetEnableSkyLightVoxel(bool bEnableSkyLightVoxel)
{
    cross::FFSRenderPipeline::sEnableSkyLightVoxel = bEnableSkyLightVoxel;
}

cross::FFSRenderPipeline::~FFSRenderPipeline()
{
#ifdef WIN32
    if (SLWrapper::Get().m_sl_initialised && SLWrapper::Get().m_viewports.find(this) != SLWrapper::Get().m_viewports.end())
    {
        SLWrapper::Get().EraseViewport(this);
    }
#endif
}

void cross::FFSRenderPipeline::Initialize(WorldRenderPipeline* worldRenderPipeline, RenderWorld* world, RenderingExecutionDescriptor* RED)
{
    IRenderPipeline::Initialize(worldRenderPipeline, world, RED);
    constexpr NGIResolveType depthResolveType = NGIResolveType::Average;
    constexpr NGIResolveType stencilResolveType = NGIResolveType::DontResolve;
    mIsSupportMSAA = GetNGIDevice().IsSupportMSAA(depthResolveType, stencilResolveType);
    mVTFeedBackManager = std::make_unique<VTFeedbackManager>(VTFeedbackManager());
 
    auto& exposureTex = this->GetBuiltInTexture<PassSemanticName::ExposureTex>();
    NGITextureDesc exposureTexDesc{
        GraphicsFormat::R32G32B32A32_SFloat,
        NGITextureType::Texture2D,
        1, 1, 1, 1, 1, 1,
        NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc,
    };
    exposureTex = RED->CreateTexture("Exposure Texture", exposureTexDesc);
    
    auto& exposureTexView = this->GetBuiltInTexture<PassSemanticName::ExposureTexView>();
    NGITextureViewDesc exposureTexViewDesc{
        NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
        exposureTexDesc.Format, NGITextureType::Texture2D,
        { NGITextureAspect::Color, 0, 1, 0, 1 }
    };
    exposureTexView = RED->CreateTextureView(exposureTex.get(), exposureTexViewDesc);
}

void cross::FFSRenderPipeline::UpdatePipelineSharedContext()
{
    UpdateReflectionProbe();
    mRED->SetProperty(NAME_ID("DISABLE_LOCAL_LIGHT_INVERSE_SQUARED"), !GetSetting()->LocalLightInverseSquared);
}

void cross::FFSRenderPipeline::UpdateSetting(const RenderPipelineSetting* setting)
{
    IRenderPipeline::UpdateSetting(setting);

    mTAA = GetSetting()->mTemporalAntiAliasingSetting.enable;
    mUpscale = GetSetting()->UseFSRorDLSS();

    mUpscaleValue = GetSetting()->GetUpScaleValue(mType);
#ifdef WIN32
    bool pipelineTypeFlag = mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::GameView || mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::SceneView ||
                            mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::MaterialEditorPreview;
    bool worldTypeFlag = static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::ThumbnailWorld && static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::PreviewWorld;
    if (GetSetting()->mDLSSSetting.enable && (!pipelineTypeFlag || !worldTypeFlag))
    {
        mUpscale = false;
        mUpscaleValue = 1.0f;
    }
    else if (GetSetting()->mFSR3Setting.enable && (!pipelineTypeFlag || !worldTypeFlag))
    {
        mUpscale = false;
        mUpscaleValue = 1.0f;
    }
#endif
    /*mUpscale = GetSetting()->mFSR2Setting.enable;
    mUpscaleValue = GetSetting()->mFSR2Setting.GetUpsacleValue();*/

    mPostProcessMtl = GetSetting()->PostProcessMtlR;
    mCombineLightingMtl = GetSetting()->CombineLightingMtlR;
    mOutlineMtl = GetSetting()->OutlineEditorMtlR;
    mWireframeMtl = GetSetting()->WireframeMtlR;
    mViewModeVisualizationMtl = GetSetting()->ViewModeVisualizationMtlR;
    mVolumetricFog.mVolumetricFogComputeShader = GetSetting()->VolumetricFogComputeShaderR;
    mVolumetricFog.mLightInjectionComputeShader = GetSetting()->VFogLightInjectionComputeShaderR;
    mVolumetricFog.mDispatchSizeComputeShader = GetSetting()->VFogDispatchSizeComputeShaderR;
    mCollectShadowMaskShader = GetSetting()->CollectShadowMaskShaderR;
    mSSR.mDeferSSRMtl = GetSetting()->DeferSSRMtlR;
    mSSR.mStochasticReprojectShader = GetSetting()->StochasticReprojectR;
    mSSR.mStochasticPrefilterShader = GetSetting()->StochasticPrefilterR;
    mSSR.mStochasticResolveTemporalShader = GetSetting()->StochasticResolveTemporalR;
    mSSR.mNoiseTexture = GetSetting()->NoiseTextureR;
    mClusterCullingShader = GetSetting()->ClusterCullingShaderR;
    mVegetationCullingShader = GetSetting()->VegetationCullingShaderR;
    mVTFeedbackComputeShader = GetSetting()->VTComputeShaderR;
    mClusterShader = GetSetting()->ClusterShaderR;
}

void cross::FFSRenderPipeline::AssemblePreDepth(REDTextureView*& depthStencilView)
{
    auto* camSys = mWorld->GetRenderSystem<CameraSystemR>();
    bool useTestCullingCamera = false;
    if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
    {
        if (camSys->GetTestCullingCamera())
        {
            useTestCullingCamera = true;
        }
    }

    const RenderCamera* camera = useTestCullingCamera ? GetCullingRenderCamera() : GetRenderCamera();

    NGIClearValue clearValue{{0, 0, 0, 0}};
    clearValue.depthStencil = {0.f, 0};
    REDDepthStencilTargetDesc depthStencilTarget{depthStencilView, NGILoadOp::Clear, NGIStoreOp::Store, NGILoadOp::Clear, NGIStoreOp::Store, clearValue, nullptr, NGIResolveType::DontResolve, NGIResolveType::DontResolve};

    if (GetSetting()->mSubSampleShadingSettings.enable)
    {
        mSubSampleShading.OverrideDepthStencilTarget(depthStencilTarget, depthStencilView);
    }

    // Culling
    auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(camera)});
    auto* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"PreDepth", 0, /*gRenderGroupTransparent - 1*/ gRenderGroupUI - 1});

    mRED->BeginRenderPass("PreDepthPass", 0, nullptr, &depthStencilTarget);
    {
        auto* preDepthPass = mRED->AllocateSubRenderPass("PreDepthPass", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);

        preDepthPass->OnCulling([=](REDPass* pass) {
            if (drawUnitList->GetDefaultObjectIndexBufferView())
                preDepthPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView());
        });

        preDepthPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);
        preDepthPass->RenderDrawUnits({drawUnitList});
    }
    mRED->EndRenderPass();
}

void cross::FFSRenderPipeline::AssembleShadow(REDTextureView* depthView, std::array<REDTextureView*, 4>& gBufferViews, ShadowProperties* shadowProperties, REDTextureView*& shadowMaskView)
{
    // Generate shadow depth pyramid from last frame
    shadowProperties->Reset();
    auto dirShadowMaps = mShadowPass.GetDirectionalShadowMaps();
    if (dirShadowMaps && mRED->Validate(dirShadowMaps))
    {
        auto& shadowDatas = mShadowPass.GetShadowDatas();
        auto* shadowSys = mWorld->GetRenderSystem<ShadowSystemR>();
        auto* lightSys = mWorld->GetRenderSystem<LightSystemR>();
        int depthPyramidIndex = 0;
        auto depthPyramidSize = dirShadowMaps->mDesc.ArraySize;
        auto dirShadowMapsView =
            mRED->AllocateTextureView(dirShadowMaps, NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::D32_SFloat, NGITextureType::Texture2DArray, NGITextureSubRange{NGITextureAspect::Depth, 0, 1, 0, depthPyramidSize}});
        if (shadowProperties->dirShadowDepthPyramidLastFrame.size() < depthPyramidSize)
        {
            shadowProperties->dirShadowDepthPyramidLastFrame.resize(depthPyramidSize, nullptr);
        }
        for (auto& dirLightEntity : shadowProperties->dirLightEntities)
        {
            if (dirShadowMaps && mRED->Validate(dirShadowMaps) && mWorld->IsEntityAlive(dirLightEntity) /*&& shadowProperties->dirShadowMapsView != nullptr*/)
            {
                auto shadowCameraComp = mWorld->GetComponent<ShadowCameraComponentR>(dirLightEntity);
                auto lightComp = mWorld->GetComponent<LightComponentR>(dirLightEntity);
                auto shadowDataIndex = lightSys->GetLightShadowDataIndex(lightComp.Read());
                if (shadowDataIndex >= 0 && shadowDataIndex < shadowDatas.size())
                {
                    auto& shadowCameras = *shadowSys->GetDirectionalShadowCameras(shadowCameraComp.Read(), GetCamera());
                    UInt16 shadowCascadeCount = (UInt16)shadowSys->GetCascadeCount();
                    UInt16 cascadeCount = std::min(static_cast<UInt16>(dirShadowMaps->mDesc.ArraySize), shadowCascadeCount);
                    const auto& shadowData = shadowDatas[shadowDataIndex];
                    auto dirShadowMapIndex = shadowData.shadowMapIndex;

                    lightSys->SetLightDepthPyramidIndex(dirLightEntity, depthPyramidIndex);

                    for (UInt16 index = 0; index < cascadeCount; ++index)
                    {
                        if (shadowSys->IsShadowCascadeNeedUpdate(GetCamera(), index) && shadowProperties->dirShadoMapsDrawLastFrame[dirLightEntity][index])
                        {
                            UInt16 srcIndex = static_cast<UInt16>(dirShadowMapIndex + index);
                            const auto& shadowCamera = shadowCameras[index];
                            auto shadowDepthPyramid = AssembleDepthPyramid(dirShadowMapsView, &shadowCamera, false, true, srcIndex);
                            shadowProperties->dirShadowDepthPyramidLastFrame[depthPyramidIndex + index] = shadowDepthPyramid->mTexture;
                        }
                    }
                    depthPyramidIndex += cascadeCount;
                }
            }
        }
    }

    // Shadow
    mShadowPass.InitLocalLightShadowCacheSettings(GetSetting()->mLocalLightShadowMapSettings, mVisibleLightCullingResult);
    mShadowPass.Initialize(GetSetting()->mVirtualShadowMapSettings);
    mShadowPass.Execute(mGameContext, depthView, gBufferViews, shadowProperties, GetFrameCount(), shadowMaskView);
}

void cross::FFSRenderPipeline::AssembleGPass(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView*& sceneColorView, REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* depthPyramid,
                                             REDTextureView*& manuallyResolvedDepth, REDTextureView*& reactiveMask)
{
    mGPass.Execute(mGameContext,
                   &mSubSampleShading,
                   gBufferViews,
                   sceneColorView,
                   depthStencilView,
                   depthOnlyView,
                   depthPyramid,
                   manuallyResolvedDepth,
                   mNumCulledLightsGrid,
                   mCulledLightDataGrid,
                   mTaaHaltonIndex,
                   IsRenderPipelineSuportMSAA() && GetSetting()->mSubSampleShadingSettings.enable,
                   reactiveMask);
}

void cross::FFSRenderPipeline::UpdateVTContext(REDPass* pass)
{
    // Calculate feedback buffer
    pass->SetProperty(NAME_ID("VTFeedbackBuffer"), mVTFeedbackBufferView);

    if (GetSetting()->EnableVirtualTexture && (GetRenderPipelineType() != ViewType::ReflectionProbe && GetRenderPipelineType() != ViewType::PrefabProxy))
    {
        // Use low discrepancy sequence to run over every pixel in the virtual texture feedback tile.
        float vtFeedbackJitterOffset = static_cast<float>(SampleVirtualTextureFeedbackSequence(GetFrameCount()));
        pass->SetProperty(NAME_ID("VTFeedbackJitterOffset"), vtFeedbackJitterOffset);

        // Offset the selected sample index for each frame.
        auto vtFeedbackScale = GetVirtualTextureFeedbackScale();
        UInt32 numPixelsInTile = static_cast<UInt32>(std::pow(vtFeedbackScale, 2));
        float vtFeedbackSampleOffset = static_cast<float>((GetFrameCount() % numPixelsInTile) + (GetFrameCount() / numPixelsInTile));
        Assert(vtFeedbackScale == 1u << VTMath::FloorLog2(vtFeedbackScale));
        UInt32 vtFeedbackShift = VTMath::FloorLog2(vtFeedbackScale);
        pass->SetProperty(NAME_ID("VTFeedbackSampleOffset"), vtFeedbackSampleOffset);
        pass->SetProperty(NAME_ID("VTFeedbackShift"), vtFeedbackShift);

        // Offset the selected sample index for each frame.
        UInt32 feedbackStride = GetVirtualTextureFeedbackBufferSize(UInt2(mTargetView->mTexture->mDesc.Width, mTargetView->mTexture->mDesc.Height)).x;
        float vtFeedbackStride = static_cast<float>(feedbackStride);
        pass->SetProperty(NAME_ID("VTFeedbackStride"), vtFeedbackStride);
    }
}

void cross::FFSRenderPipeline::AssembleDeferredLighting(const ShadowProperties& shadowProperties, REDTextureView* shadowMaskView, const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthStencilView,
                                                        REDTextureView* depthOnlyView, REDTextureView* contactShadowView, REDTextureView* sceneColorView, bool contactShadowTransmissionEnable)
{
    auto* camSys = mWorld->GetRenderSystem<CameraSystemR>();

    auto RTWidth = mTargetView->mTexture->mDesc.Width;
    auto RTHeight = mTargetView->mTexture->mDesc.Height;
    Float4 screenParams = {static_cast<float>(RTWidth), static_cast<float>(RTHeight), 1.0f / RTWidth, 1.0f / RTHeight};

    // TileBasedDeferred
    mTileBasedDeferredLighting.mlightGridSizeX = mBuildTileCulling.mlightGridSizeX;
    mTileBasedDeferredLighting.mlightGridSizeY = mBuildTileCulling.mlightGridSizeY;
    mTileBasedDeferredLighting.mlightGridSizeZ = mBuildTileCulling.mlightGridSizeZ;
    mTileBasedDeferredLighting.mBOS = mBuildTileCulling.mBOS;
    mTileBasedDeferredLighting.mLightGridPixelSizeShift = mBuildTileCulling.mLightGridPixelSizeShift;
    mTileBasedDeferredLighting.mLocalLightsNum = mBuildTileCulling.mLocalLightsNum;
    mTileBasedDeferredLighting.mDirectionalLightList = mBuildTileCulling.mDirectionalLightList;
    mTileBasedDeferredLighting.mWorld = mWorld;
    mTileBasedDeferredLighting.mVisualize = GetSetting()->viewModeVisualizeType;

    const bool enableMSAA = IsRenderPipelineSuportMSAA() && GetSetting()->mSubSampleShadingSettings.enable;

    mTileBasedDeferredLighting.Initialize(GetSetting()->mTileBasedDefferedLightingSettings);

    mTileBasedDeferredLighting.Execute(mGameContext,
                                       RTWidth,
                                       RTHeight,
                                       &shadowProperties,
                                       shadowMaskView,
                                       enableMSAA,
                                       GetSetting()->mSubSampleShadingSettings,
                                       mSubSampleShading,
                                       gBufferViews,
                                       depthStencilView,
                                       depthOnlyView,
                                       contactShadowView,
                                       sceneColorView,
                                       contactShadowTransmissionEnable,
                                       mGPassDepthPyramidReprojectionView,
                                       mGPassDepthPyramidLastFrameView,
                                       &mSmartGIPass,
                                       mNumCulledLightsGrid,
                                       mCulledLightDataGrid);
}

void cross::FFSRenderPipeline::UpdateMassiveLightsShadowContext(REDPass* lightingPass, REDBufferView* clustersBufferView, REDBufferView* shadowBufferView, REDTextureView* gBuffer2MapView)
{
    cross::NGIBufferView* lightInstanceView = mFoliageGpuDriven.GetLightInstanceView();

    mRayDebugBuffer = mMassiveLightsShadow.UpdateCommonContext(mGameContext, lightingPass, GetFrameCount() % 8);

    lightingPass->SetProperty(NAME_ID("_MassiveLightsClusters"), clustersBufferView, NGIResourceState::PixelShaderShaderResource);
    lightingPass->SetProperty(NAME_ID("_MassiveLightsShadow"), shadowBufferView, NGIResourceState::PixelShaderShaderResource);
    lightingPass->SetProperty(NAME_ID("_LightInstanceData"), lightInstanceView);
    lightingPass->SetProperty(NAME_ID("_GBuffer2Map"), gBuffer2MapView, NGIResourceState::PixelShaderShaderResource);
}

void cross::FFSRenderPipeline::AssembleGPUDrivenLight(const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthStencilView,
                                                      REDTextureView* depthOnlyView, REDTextureView* sceneColorView, REDBufferView* clustersBufferView)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    bool enableGPUDriven = GetSetting()->GPUDrivenLight && GetSetting()->mFoliageGpuDrivenSettings.enable;
    if (!enableGPUDriven)
        return;
    //auto UpdateMassiveLightsShadowContext1 = std::bind(&cross::FFSRenderPipeline::UpdateMassiveLightsShadowContext, this, std::placeholders::_1, clustersBufferView, shadowBufferView, gBufferViews[2]);
    auto AssembleRayDebug = [this]() { mMassiveLightsShadow.AssembleRayDebug(mGameContext, mRayDebugBuffer); };
    mFoliageGpuDriven.AssembleGPUDrivenLight(mGameContext,
                                             gBufferViews,
                                             depthStencilView,
                                             depthOnlyView,
                                             sceneColorView,
                                             mGPassDepthPyramidReprojectionView,
                                             mGPassDepthPyramidLastFrameView,
                                             clustersBufferView,
                                             GetSetting()->viewModeVisualizeType,
                                             nullptr,
                                             AssembleRayDebug);
}

void cross::FFSRenderPipeline::AssembleSkyPass(REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* sceneColorView, REDTextureView* integratedFogView, REDTextureView*& separateTranslucencyView,
                                               REDTextureView*& reactiveView)

{
    AssembleForwardImpl("ForwardSky", gSkyID, {}, nullptr, depthStencilView, depthOnlyView, nullptr, nullptr, nullptr, nullptr, sceneColorView, nullptr, integratedFogView, separateTranslucencyView, reactiveView, false, false, false);

    // Discard these 2 textures
    separateTranslucencyView = nullptr;
    reactiveView = nullptr;
}

void cross::FFSRenderPipeline::AssembleForwardTransparent(const ShadowProperties& shadowProperties, REDTextureView* shadowMaskView, REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* contactShadowView,
                                                          REDTextureView* aoView, REDTextureView* colorLastFrame, REDTextureView* sceneColorReadOnly, REDTextureView* sceneColorView, REDTextureView* ssprView,
                                                          REDTextureView* integratedFogView, REDTextureView*& separateTranslucencyView, REDTextureView*& reactiveView)
{
    AssembleForwardImpl("ForwardTransparent",
                        gForwardID,
                        shadowProperties,
                        shadowMaskView,
                        depthStencilView,
                        depthOnlyView,
                        contactShadowView,
                        aoView,
                        colorLastFrame,
                        sceneColorReadOnly,
                        sceneColorView,
                        ssprView,
                        integratedFogView,
                        separateTranslucencyView,
                        reactiveView,
                        true,
                        true,
                        true);
}

void cross::FFSRenderPipeline::AssembleForwardImpl(std::string_view subPassName, NameID forwardGroupID, const ShadowProperties& shadowProperties, REDTextureView* shadowMaskView, REDTextureView* depthStencilView,
                                                   REDTextureView* depthOnlyView, REDTextureView* contactShadowView, REDTextureView* aoView, REDTextureView* colorLastFrame, REDTextureView* sceneColorReadOnly, REDTextureView* sceneColorView,
                                                   REDTextureView* ssprView, REDTextureView* integratedFogView, REDTextureView*& separateTranslucencyView, REDTextureView*& reactiveView, bool transparent, bool use_msaa, bool gpuCulling)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    mRED->BeginRegion(subPassName);

    auto renderPipelineSetting = static_cast<const FFSRenderPipelineSetting*>(GetSetting());
    bool enableDepthWrite = renderPipelineSetting->EnableTranslucentPassDepthWrite && transparent;
    bool enableMSAA = IsRenderPipelineSuportMSAA() && renderPipelineSetting->mSubSampleShadingSettings.enable && use_msaa;
    UInt16 sampleCount = enableMSAA ? static_cast<UInt16>(renderPipelineSetting->mSubSampleShadingSettings.MSAASampleCount) : 1;

    auto* camSys = mWorld->GetRenderSystem<CameraSystemR>();
    auto* skyLightSys = mWorld->GetRenderSystem<SkyLightSystemR>();
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* rdrPrim = rdrSys->GetRenderPrimitives();
    auto* atmosSystem = mWorld->GetRenderSystem<SkyAtmosphereSystemR>();

    auto RTWidth = sceneColorView->mTexture->mDesc.Width;
    auto RTHeight = sceneColorView->mTexture->mDesc.Height;
    Float4 screenSizeVector = {static_cast<float>(RTWidth), static_cast<float>(RTHeight), 1.0f / RTWidth, 1.0f / RTHeight};

    bool useTestCullingCamera = false;
    if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
    {
        if (camSys->GetTestCullingCamera())
        {
            useTestCullingCamera = true;
        }
    }
    auto* drawCamera = useTestCullingCamera ? GetCullingRenderCamera() : GetRenderCamera();

    std::array<NGICopyTexture, 2> regions{{
        {0, {}, 0, {}, {RTWidth, RTHeight, 1}},
        {1, {}, 1, {}, {RTWidth, RTHeight, 1}},
    }};

    std::vector<REDColorTargetDesc> LightingPassColorTargets{{
        sceneColorView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGIClearValue{0, 0, 0, 0},
    }};

    auto separateTranslucencyTarget = separateTranslucencyView;
    if (transparent)
    {
        // we support write depth in transparent passes.
        // copy a scene depth and use it as the depth/stencil target for transparent object, so any write into this target will not affect other passes.
        if (enableDepthWrite)
        {
            auto sceneDepth = CreateTextureView2D("DepthCopy", RTWidth, RTHeight, depthStencilView->mTexture->mDesc.Format, NGITextureUsage::CopyDst | NGITextureUsage::DepthStencil);

            // when enable msaa, depthStencilView will be override by msaa code
            if (!enableMSAA)
            {
                mRED->AllocatePass("CopySceneDepth")->CopyTextureToTexture(sceneDepth->mTexture, depthStencilView->mTexture, static_cast<UInt32>(regions.size()), regions.data());
            }

            depthStencilView = sceneDepth;
        }

        NGILoadOp separateTranslucencyViewLoadOp = NGILoadOp::Load;

        if (!separateTranslucencyTarget)
        {
            separateTranslucencyTarget =
                CreateTextureView2D("Separate Translucency", RTWidth, RTHeight, sceneColorView->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);
            separateTranslucencyViewLoadOp = NGILoadOp::Clear;
            separateTranslucencyView = CreateTextureView2D(separateTranslucencyTarget->GetTexture(), separateTranslucencyTarget->GetDesc().Format, NGITextureUsage::ShaderResource);
        }

        NGILoadOp reactiveViewLoadOp = NGILoadOp::Load;
        if (GetSetting()->mFSR2Setting.enable)
        {
            if (!reactiveView)
            {
                reactiveView = CreateTextureView2D("Reactive Translucency", RTWidth, RTHeight, GraphicsFormat::R8G8B8A8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);
                reactiveViewLoadOp = NGILoadOp::Clear;
            }
        }

        LightingPassColorTargets.emplace_back(REDColorTargetDesc{
            separateTranslucencyTarget,
            separateTranslucencyViewLoadOp,
            NGIStoreOp::Store,
            NGIClearValue{0, 0, 0, 1.0},
        });

        if (reactiveView)
        {
            LightingPassColorTargets.emplace_back(REDColorTargetDesc{
                reactiveView,
                reactiveViewLoadOp,
                NGIStoreOp::Store,
                NGIClearValue{0, 0, 0, 0},
            });
        }
    }

    REDDepthStencilTargetDesc depthStencilTarget{
        depthStencilView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGILoadOp::Load,
        NGIStoreOp::Store,
    };

    // Override LightingPassColorTargets
    if (enableMSAA)
    {
        auto msaaDepthView = mSubSampleShading.GetMSAAOverideDepthView();
        depthStencilTarget = {nullptr, NGILoadOp::Load, NGIStoreOp::Resolve, NGILoadOp::Load, NGIStoreOp::Resolve, {0, 0, 0, 0}, depthStencilView, NGIResolveType::Max, NGIResolveType::SampleZero};
        // We don't need to copy depthMSAA when depth writing is disabled. Set depthMSAA as a read-only RT for performing depth tests, and set the depth buffer as a shader resource
        // so that we avoid using a single buffer both as an RT and a shader resource.
        if (enableDepthWrite)
        {
            auto sceneDepthmsaa = CreateTextureView2D("DepthCopyMSAA", RTWidth, RTHeight, depthStencilView->mTexture->mDesc.Format, NGITextureUsage::CopyDst | NGITextureUsage::DepthStencil, 1, sampleCount);
            mRED->AllocatePass("CopySceneDepthMSAA")->CopyTextureToTexture(sceneDepthmsaa->mTexture, msaaDepthView->mTexture, static_cast<UInt32>(regions.size()), regions.data());
            depthStencilTarget.Target = sceneDepthmsaa;
        }
        else
        {
            depthStencilTarget.Target = msaaDepthView;
            depthStencilTarget.DepthStoreOp = NGIStoreOp::DontCare;
            depthStencilTarget.StencilStoreOp = NGIStoreOp::DontCare;
            depthStencilTarget.ResolveTarget = nullptr;
        }
        
        auto sceneColorViewMSAA = IRenderPipeline::CreateTextureView2D("SceneColorViewMSAA", RTWidth, RTHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource, 1, sampleCount);
        auto separateTranslucencyViewMSAA =
            CreateTextureView2D("Separate Translucency MSAA", RTWidth, RTHeight, sceneColorView->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess, 1, sampleCount);

        auto Blit = [this](REDTextureView* SrcView, REDTextureView* DstView) {
            PostProcessUtil([&](REDPass* pass) { pass->SetProperty(NAME_ID("_ColorTex"), SrcView, NGIResourceState::PixelShaderShaderResource); }, mPostProcessMtl, mRED, "blit", true, DstView);
        };

        // Don't use CopyTextureToTexture for color texture
        Blit(sceneColorView, sceneColorViewMSAA);
        LightingPassColorTargets[0].Target = sceneColorViewMSAA;
        LightingPassColorTargets[0].StoreOp = NGIStoreOp::Resolve;
        LightingPassColorTargets[0].ResolveTarget = sceneColorView;
        LightingPassColorTargets[1].Target = separateTranslucencyViewMSAA;
        LightingPassColorTargets[1].StoreOp = NGIStoreOp::Resolve;
        LightingPassColorTargets[1].ResolveTarget = separateTranslucencyTarget;
        LightingPassColorTargets[1].ClearValue = NGIClearValue{0, 0, 0, 1};

        if (reactiveView)
        {
            auto reactiveViewMSAA =
                CreateTextureView2D("Reactive Translucency MASA", RTWidth, RTHeight, GraphicsFormat::R8G8B8A8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess, 1, sampleCount);

            LightingPassColorTargets[2].Target = reactiveViewMSAA;
            LightingPassColorTargets[2].StoreOp = NGIStoreOp::Resolve;
            LightingPassColorTargets[2].ResolveTarget = reactiveView;
        }
    }

    // Culling
    cross::REDDrawUnitList* drawUnitList = nullptr;

    auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(drawCamera)});
    drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{forwardGroupID, gRenderGroupTransparent, gRenderGroupUI - 11});


    REDPass* cullingPass = nullptr;
    REDBufferView* objectIndexBufferSRV = nullptr;
    auto& foliageGpuDriven = GetFoliageGpuDriven();
    if (gpuCulling)
    {
        auto [gpuCullingPass, _objectIndexBufferSRV] = InstanceCulling::AssembleInstanceCullingPass(mGameContext, drawUnitList, mGPassDepthPyramidCurrFrameView, "ForwardFoliageCulling", forwardGroupID, true, true);
        cullingPass = gpuCullingPass;
        objectIndexBufferSRV = _objectIndexBufferSRV;

        InstanceCulling::AddReadBackDrawCulling(cullingPass, drawUnitList, mRED->GetFrameId(), renderPipelineSetting->FeedBackDebug);
    }



    mRED->BeginRenderPass(subPassName, static_cast<UInt32>(LightingPassColorTargets.size()), LightingPassColorTargets.data(), &depthStencilTarget);
    {
        std::vector<NGIRenderPassTargetIndex> colorTargets = {NGIRenderPassTargetIndex::Target0};

        if (transparent && separateTranslucencyView)
        {
            colorTargets.emplace_back(NGIRenderPassTargetIndex::Target1);
        }

        if (transparent && reactiveView)
        {
            colorTargets.emplace_back(NGIRenderPassTargetIndex::Target2);
        }

        // ForwardTransparent
        auto passFlag = REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil;
        passFlag |= !enableDepthWrite ? REDPassFlagBit::DepthReadOnly : static_cast<REDPassFlagBit>(0);
        auto* forwardPass = mRED->AllocateSubRenderPass(subPassName, 0, nullptr, static_cast<UInt32>(colorTargets.size()), colorTargets.data(), passFlag);

        // in this case .cullingPas is same as foliage culling pass
        if (gpuCulling && renderPipelineSetting->mFoliageGpuDrivenSettings.enable && renderPipelineSetting->EnableFoliageDrawing)
        {
            foliageGpuDriven.SetRenderPass(cullingPass, forwardPass);
        }

        forwardPass->SetProperty(BuiltInProperty::ce_SceneColor, sceneColorReadOnly);
        forwardPass->SetProperty(BuiltInProperty::ce_SceneDepth, depthOnlyView);
        forwardPass->SetProperty(BuiltInProperty::ce_Scene_Color, sceneColorReadOnly);
        forwardPass->SetProperty(BuiltInProperty::ce_Scene_Depth, depthOnlyView);

        forwardPass->SetProperty(BuiltInProperty::ce_SkyLightIntensity, skyLightSys->GetSkyLightIntensity());

        if (auto type = GetRenderPipelineType(); type == ViewType::GameView && type != ViewType::SceneView)
            UpdateShadowContext(forwardPass, &shadowProperties);

        if (contactShadowView)
        {
            forwardPass->SetProperty(NAME_ID("_ContactShadowMap"), contactShadowView, NGIResourceState::PixelShaderShaderResource);
        }
        else
        {
            forwardPass->SetProperty(NAME_ID("_ContactShadowMap"), rdrPrim->mDefaultUIntTexture2DView.get());
        }

        if (shadowMaskView != nullptr)
        {
            forwardPass->SetProperty(NAME_ID("_ShadowMaskMap"), shadowMaskView, NGIResourceState::PixelShaderShaderResource);
        }

        if (aoView)
        {
            forwardPass->SetProperty(NAME_ID("ao_texture"), aoView, NGIResourceState::PixelShaderShaderResource);
        }

        /*===== Forward Reflection Indirect Lighting =====*/
        forwardPass->SetProperty(NAME_ID("ENABLE_SKY_LIGHT_REALTIME_CAPTURE"), mWorld->GetRenderSystem<SkyLightSystemR>()->IsSkyLightRealtimeCapture());
        forwardPass->SetProperty(NAME_ID("FORWARD_DEAL"), true);
        auto& reflecIndirectSetting = GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting;
        auto& SSRSetting = reflecIndirectSetting.mSSRSetting;
        if (reflecIndirectSetting.ReflectionType == ScreenReflectionType::SSR && SSRSetting.enable && reflecIndirectSetting.enable)
        {
            Float4 forwardSSRParams = Float4(SSRSetting.MaxTraceDistance, static_cast<float>(SSRSetting.NumSteps), static_cast<float>(GetFrameCount() % 8), static_cast<float>(SSRSetting.StartMipLevel));
            Float2 HZBUvFactor(RTWidth / static_cast<float>(2 * mGPassDepthPyramidLastFrameView->GetWidth()), RTHeight / static_cast<float>(2 * mGPassDepthPyramidLastFrameView->GetHeight()));
            Float4 HZBUvFactorAndInvFactor(HZBUvFactor.x, HZBUvFactor.y, 1.0f / HZBUvFactor.x, 1.0f / HZBUvFactor.y);
            forwardPass->SetProperty(NAME_ID("FORWARD_SSR"), true);
            forwardPass->SetProperty(NAME_ID("ce_FurthestHZBTexture"), mGPassDepthPyramidLastFrameView);
            forwardPass->SetProperty(NAME_ID("ce_PrevSceneColorTexture"), colorLastFrame);
            forwardPass->SetProperty(NAME_ID("ce_DepthTexture"), depthOnlyView);
            forwardPass->SetProperty(NAME_ID("ce_ForwardSSRParams"), forwardSSRParams);
            forwardPass->SetProperty(NAME_ID("ce_HZBUvFactorAndInvFactor"), HZBUvFactorAndInvFactor);
        }

        if (GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.enable)
        {
            forwardPass->SetProperty(NAME_ID("_LightGridZParamsB"), mReflectionIndirect.mBOS.x);
            forwardPass->SetProperty(NAME_ID("_LightGridZParamsO"), mReflectionIndirect.mBOS.y);
            forwardPass->SetProperty(NAME_ID("_LightGridZParamsS"), mReflectionIndirect.mBOS.z);
            forwardPass->SetProperty(NAME_ID("_CulledGridSizeX"), mReflectionIndirect.mlightGridSizeX);
            forwardPass->SetProperty(NAME_ID("_CulledGridSizeY"), mReflectionIndirect.mlightGridSizeY);
            forwardPass->SetProperty(NAME_ID("_CulledGridSizeZ"), mReflectionIndirect.mlightGridSizeZ);
            forwardPass->SetProperty(NAME_ID("_LightGridPixelSizeShift"), mReflectionIndirect.mLightGridPixelSizeShift);
            forwardPass->SetProperty(NAME_ID("_NumReflectionCaptures"), mReflectionIndirect.mReflectionProbeNum);
            forwardPass->SetProperty(NAME_ID("_ReflectionCapturesMipNum"), mReflectionIndirect.mReflectionCapturesMipNum);
            if (mReflectionIndirect.mCaptureReflectionView)
            {
                forwardPass->SetProperty(NAME_ID("_CaptureReflectionCubemaps"), mReflectionIndirect.mCaptureReflectionView);
            }
        }
        forwardPass->SetProperty(NAME_ID("_NumCulledLightsGrid"), mNumCulledLightsGrid, NGIResourceState::PixelShaderUnorderedAccess);
        forwardPass->SetProperty(NAME_ID("_CulledLightsDataGrid"), mCulledLightDataGrid, NGIResourceState::PixelShaderUnorderedAccess);
        {
            forwardPass->SetProperty(NAME_ID("ENABLE_TILE_BASED_LIGHTING"), true);
            forwardPass->SetProperty(NAME_ID("_NumLocalLights"), mBuildTileCulling.mLocalLightsNum);
        }

        float manual_exposure = 1.0f;
        if (mPostProcessSetting->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.enable)
        {
            manual_exposure = mPostProcessSetting->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.Exposure;
        }
        forwardPass->SetProperty(NAME_ID("ManualExposure"), manual_exposure);

        auto* cloudSystem = mWorld->GetRenderSystem<CloudSystemR>();
        forwardPass->SetProperty(NAME_ID("useSFog"), ShouldRenderScreenSpaceFog());
        forwardPass->SetProperty(NAME_ID("useVFog"), ShouldRenderVolumetricFog());
        forwardPass->SetProperty(NAME_ID("ENABLE_MULTI_LAYER_FOG"), ShouldUseMultiLayerFog());
        forwardPass->SetProperty(NAME_ID("useCloud"), cloudSystem->EnableVolumetricCloud());

        if (ShouldRenderVolumetricFog())
        {
            mVolumetricFog.UpdateFogApplyContext(forwardPass->GetContext(), this);
        }
        
        if (ShouldUseMultiLayerFog())
            mMultiLayerFog.UpdateFogApplyContext(forwardPass->GetContext());
        else
            mExponentialFog.UpdateFogApplyContext(forwardPass->GetContext());
        if (integratedFogView != nullptr)
        {
            forwardPass->SetProperty(NAME_ID("integrated_texture"), integratedFogView, NGIResourceState::PixelShaderShaderResource);
        }

        //if (cloudSystem->EnableVolumetricCloud() && transparent)
        //{
        //    forwardPass->SetProperty(NAME_ID("View_CloudColor"), GetCloudResources().mCloudColor);
        //    forwardPass->SetProperty(NAME_ID("View_CloudDepth"), GetCloudResources().mCloudDepth);
        //    // Judge nullptr to prevent TOD switching crash
        //    if (GetCloudResources().mUnderCloudStat)
        //    {
        //        forwardPass->SetProperty(NAME_ID("CloudPlanarMask"), GetCloudResources().mUnderCloudStat);
        //    }
        //}

        auto gsettings = cloudSystem->GetGlobalSetting();
        if (gsettings.CloudShadow.CloudCastShadow && transparent)
        {
            UpdateCloudShadowContext(forwardPass);
        }

        //if (gpuCulling && mSetting->InstanceCulling)
        {
            forwardPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), objectIndexBufferSRV);
        }
        //else
        //{
        //    forwardPass->OnCulling([=](REDPass* pass) {
        //        if (drawUnitList->GetDefaultObjectIndexBufferView())
        //            forwardPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView());
        //    });
        //}

        forwardPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);
        forwardPass->RenderDrawUnits({drawUnitList});
    }
    mRED->EndRenderPass();

    if (gpuCulling && renderPipelineSetting->FeedbackDirectDrawOptimization)
    {
        if (GetRenderPipelineType() == ViewType::GameView || GetRenderPipelineType() == ViewType::SceneView)
        {
            InstanceCulling::RequestForReadbackDraw(drawUnitList, mRED->GetFrameId());
        }
    }

    mRED->EndRegion();
}

void cross::FFSRenderPipeline::AssembleSmartGI(const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthView, REDTextureView* hizDepthView, REDTextureView* sceneColorView, REDTextureView* aoView,
                                               const ShadowProperties* shadowProperties)
{
    bool bEnableSmartGI = GetSetting()->mIndirectLightingCompositeSettings.mSmartGISetting.IsEnableGI(mGameContext) && !IsReflectionProbePipeline();
    if (!bEnableSmartGI)
    {
        // Clear smartgi related resources
        mSmartGIDiffuseView = mSmartGISpecularView = nullptr;
        return;
    }
    mSmartGIPass.mVisualize = GetSetting()->viewModeVisualizeType;
    mSmartGIPass.input_depthView = depthView;
    mSmartGIPass.input_targetView = mTargetView;
    mSmartGIPass.output_HiZView = hizDepthView;
    if (mSmartGIPass.NeedClosestHiZDepth())
    {
        mSmartGIPass.output_ClosestHiZView = AssembleDepthPyramid(depthView, nullptr, true, false, 0, true);
    }
    else
    {
        mSmartGIPass.output_ClosestHiZView = hizDepthView;
    }
    mSmartGIPass.Initialize(GetSetting()->mIndirectLightingCompositeSettings.mSmartGISetting);

    mSmartGIPass.input_sceneColorView = sceneColorView;
    mSmartGIPass.input_shadowProperties = shadowProperties;

    std::array<REDTextureView*, 4> gBufferViewsGI;
    gBufferViewsGI[0] = gBufferViews[0];
    gBufferViewsGI[1] = gBufferViews[1];
    gBufferViewsGI[2] = gBufferViews[2];
    gBufferViewsGI[3] = gBufferViews[3];
    mSmartGIPass.input_gBufferViews = gBufferViewsGI;
    mSmartGIPass.input_aoView = aoView;
    mSmartGIPass.mStageStatus = SmartGIPass::StageStatus::SMART_GI_STAGE;

    mSmartGIPass.Execute(mGameContext);
    std::tie(mSmartGIDiffuseView, mSmartGISpecularView) = mSmartGIPass.GetIndirectLighitingViews();
}

void cross::FFSRenderPipeline::AssembleSmartGIFinish(REDTextureView* sceneColorView)
{
    bool bEnableSmartGI = GetSetting()->mIndirectLightingCompositeSettings.mSmartGISetting.IsEnableGI(mGameContext) && !IsReflectionProbePipeline();
    if (!bEnableSmartGI)
        return;

    mSmartGIPass.input_sceneColorView = sceneColorView;
    mSmartGIPass.mStageStatus = SmartGIPass::StageStatus::SMART_GI_COMPLETED_STAGE;
    mSmartGIPass.Execute(mGameContext);
}

void cross::FFSRenderPipeline::AssembleViewModeVisualization(REDTextureView* visOutputView, REDTextureView* sceneColorView)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    mViewModeVisualization.FillInputs(mGameContext);
    mViewModeVisualization.ExecuteImp(mGameContext, visOutputView, sceneColorView);
}

void cross::FFSRenderPipeline::AssembleSeparateTranslucencyBlendPass()
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    mSeparateTranslucencyBlendPass.FillInput(mGameContext);
    mSeparateTranslucencyBlendPass.Execute(mGameContext);
}

#ifdef WIN32
void cross::FFSRenderPipeline::AssembleFSR3()
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    bool pipelineTypeFlag = mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::GameView || mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::SceneView ||
                            mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::MaterialEditorPreview;
    bool worldTypeFlag = static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::ThumbnailWorld && static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::PreviewWorld;
    if (GetSetting()->mFSR3Setting.enable && pipelineTypeFlag && worldTypeFlag)
    {
        mFSR3SRPass.FillInput(mGameContext);
        mFSR3SRPass.Execute(mGameContext);
    }
    
}


 void cross::FFSRenderPipeline::AssembleDLSS()
 {
     QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    bool pipelineTypeFlag = mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::GameView || mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::SceneView ||
                            mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::MaterialEditorPreview;
    bool worldTypeFlag = static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::ThumbnailWorld && static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::PreviewWorld;
    bool dlssEnable = GetSetting()->mDLSSSetting.enable && SLWrapper::Get().GetDLSSAvailable();
    bool isDLSSOpen = dlssEnable && pipelineTypeFlag && worldTypeFlag;
    if (!isDLSSOpen)
     {
         return;
     }
     mDLSSPass.FillInput(mGameContext);
     mDLSSPass.Execute(mGameContext);
 }

void cross::FFSRenderPipeline::AssembleDLSSG(bool isFirst)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    // DLSSG is only available when DLSS is available
    bool pipelineTypeFlag = mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::GameView || mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::SceneView ||
                        mGameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::MaterialEditorPreview;
    bool worldTypeFlag = static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::ThumbnailWorld && static_cast<WorldTypeTag>(mGameContext.mRenderWorld->GetRenderWorldType()) != WorldTypeTag::PreviewWorld;
    bool dlssEnable = GetSetting()->mDLSSSetting.enable && SLWrapper::Get().GetDLSSAvailable();
    bool isDLSSOpen = dlssEnable&& pipelineTypeFlag && worldTypeFlag;
    if (!isDLSSOpen || !SLWrapper::Get().GetDLSSGAvailable())
    {
        return;
    }
    if (isFirst)
        mDLSSGPass.FillInput(mGameContext);
    else
    {
        mDLSSGPass.Execute(mGameContext);
    }
}
    

#endif
void cross::FFSRenderPipeline::AssembleIndirectLightingComposite(const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthView, REDTextureView*& sceneColorViewPrev, REDTextureView* sceneColorViewAfter)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    bool bEnableILC = GetSetting()->mIndirectLightingCompositeSettings.enable && !IsReflectionProbePipeline();
    if (!bEnableILC)
    {
        mAOView = mReflectionIndirectView = mSSPRView = mSSRView = mSmartGIDiffuseView = mSmartGISpecularView = mGIColorView = nullptr;
        return;
    }

    sceneColorViewPrev = CreateTextureView2D(
        "DirectLightingSceneColor", sceneColorViewAfter->GetWidth(), sceneColorViewAfter->GetHeight(), sceneColorViewAfter->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource);
    NGICopyTexture region{0, {}, 0, {}, {sceneColorViewAfter->GetWidth(), sceneColorViewAfter->GetHeight(), 1}};
    mRED->AllocatePass("Copy DirectLightingSceneColor")->CopyTextureToTexture(sceneColorViewPrev->mTexture, sceneColorViewAfter->mTexture, 1, &region);

    mIndirectLightingComposite.mWorld = mWorld;
    // If you don't init setting here, mSetting wouldn't get material infos
    mIndirectLightingComposite.Initialize(GetSetting()->mIndirectLightingCompositeSettings);
    mIndirectLightingComposite.FillInput(mGameContext);
    mIndirectLightingComposite.Execute(mGameContext, gBufferViews, depthView, sceneColorViewPrev, sceneColorViewAfter, mGIColorView);
}

bool cross::FFSRenderPipeline::ShouldRenderSkyAtmosphere()
{
    if (!mWorld->GetRenderSystem<SkyAtmosphereSystemR>()->GetContextReady())
    {
        return false;
    }

    auto* lightSystem = TYPE_CAST(LightSystemR*, mWorld->GetRenderSystem<LightSystemR>());
    return lightSystem->HasAtmosphereLight(0) || lightSystem->HasAtmosphereLight(1);
}

bool cross::FFSRenderPipeline::ShouldRenderScreenSpaceFog() const
{
    bool pipeline = mPostProcessSetting->FogSettings.enable && GetRenderPipelineType() != ViewType::ReflectionProbe;
    bool density = (mPostProcessSetting->FogSettings.FogCommon.Density > 1e-5) || mPostProcessSetting->FogSettings.ffsTempTest.enable;
    return pipeline && density;
}

bool cross::FFSRenderPipeline::ShouldUseMultiLayerFog() const
{
    const auto& dustSettings = mPostProcessSetting->FogSettings.VFog.Dust;
    return mPostProcessSetting->FogSettings.ffsTempTest.enable || (dustSettings.DustOverride && dustSettings.enable);
}

bool cross::FFSRenderPipeline::ShouldRenderVolumetricFog() const
{
    bool pipeline = mPostProcessSetting->FogSettings.enable && mPostProcessSetting->FogSettings.VFog.enable && GetRenderPipelineType() != ViewType::ReflectionProbe;
    bool density = (mPostProcessSetting->FogSettings.FogCommon.Density > 1e-5) || mPostProcessSetting->FogSettings.ffsTempTest.enable;
    return pipeline && density;
}

bool cross::FFSRenderPipeline::ShouldComputeDistantSkyLight()
{
    bool ExistSkyAtmoSphere = ShouldRenderSkyAtmosphere();
    bool ExistVolumetricFog = ShouldRenderVolumetricFog();
    bool ExistSrceenFog = ShouldRenderScreenSpaceFog();

    auto Entities = mWorld->Query<CloudComponentR>();
    bool ExistVolumetricCloud = Entities.GetEntityNum() >= 1;
    return (ExistVolumetricCloud || ExistVolumetricFog || ExistSrceenFog) && ExistSkyAtmoSphere;
}

void cross::FFSRenderPipeline::PrepareAtmosphereSunLight()
{
    auto* lightSystem = TYPE_CAST(LightSystemR*, mWorld->GetRenderSystem<LightSystemR>());
    lightSystem->FillAtmosphereLightConstantBuffer(mRED, mCamera);
}

void cross::FFSRenderPipeline::PreComputeSkyAtmosphere(const RenderCamera* Camera)
{
    mWorld->GetRenderSystem<SkyAtmosphereSystemR>()->PreComputeTextures(Camera);
}

void cross::FFSRenderPipeline::ProcessPerPixelFogTranmittance(ScreenSpaceFog& fogPass, REDTextureView* sceneView)
{
    auto* gameView = mTargetView == nullptr ? sceneView : mTargetView;
    auto gameViewWidth = gameView->mTexture->mDesc.Width;
    auto gameViewHeight = gameView->mTexture->mDesc.Height;
    auto cam = GetRenderCamera();
    Float3 cameraPos = cam->GetCameraOrigin();
#if defined(CE_USE_DOUBLE_TRANSFORM)
    Float3 cameraTile = cam->GetTilePosition();
    cameraPos += cameraTile * LENGTH_PER_TILE_F;
#endif
    Assert(mWorld && cam);
    fogPass.mWorld = mWorld;
    fogPass.mCamera = cam;
    fogPass.mCamPos = cameraPos;
    fogPass.mWidth = gameViewWidth;
    fogPass.mHeight = gameViewHeight;
    
    if (GetSetting()->PerPixelFogTransmittance)
    {
        fogPass.UpdateFogApplyContext(mRED->GetCurrentRegion()->PropertySet);
        mRED->SetProperty(NAME_ID("useSFog"), ShouldRenderScreenSpaceFog());
    }
    mRED->SetProperty(NAME_ID("ENABLE_PER_PIXEL_FOG_TRANSMITTANCE"), GetSetting()->PerPixelFogTransmittance);
}

void cross::FFSRenderPipeline::UpdateSkyAtmosphereAdvancedVars()
{
    SkyAtmoShaderAdvancedVarsParam advancedVars;
    if (auto* setting = GetSetting(); setting != nullptr)
    {
        const SkyAtmosphereAdvancedVars& CVars = setting->mSkyAtmosphereAdvancedVars;
        advancedVars.SkySampleCountMin.mValue = std::max(0.0f, CVars.SkySampleCountMin);
        advancedVars.SkySampleCountMax.mValue = std::max(1.0f, std::max(CVars.SkySampleCountMin, CVars.SkySampleCountMax));
        advancedVars.SkyDistanceToSampleCountMaxInv.mValue = CVars.SkyDistanceToSampleCountMax == 0.0f ? 1.0f / 150.0f : 1.0f / CVars.SkyDistanceToSampleCountMax;
        advancedVars.SkyViewLUTSampleCountMin.mValue = std::max(0.0f, CVars.SkyViewLUTSampleCountMin);
        advancedVars.SkyViewLUTSampleCountMax.mValue = std::max(1.0f, std::max(CVars.SkyViewLUTSampleCountMin, CVars.SkyViewLUTSampleCountMax));
        advancedVars.SkyViewLUTDistanceToSampleCountMaxInv.mValue = CVars.SkyViewLUTDistanceToSampleCountMax == 0.0f ? 1.0f / 150.0f : 1.0f / CVars.SkyViewLUTDistanceToSampleCountMax;
    }
    SetPassParameters(advancedVars, mRED);
}

void cross::FFSRenderPipeline::CompilePassGraph()
{
    FSR2Pass fsr2{};
    RegisterCustomPass(fsr2, false);
    TemporalAntiAliasing taa;
    RegisterCustomPass(taa);
    BlitLowResSceneColor blitlowres;
    RegisterCustomPass(blitlowres);



    //the old particle pass is deprecated, since we no longer need render transparent post frs/supersamling
    //ParticlePass particlepass;
    //RegisterCustomPass(particlepass);
    WindowPass window{};
    RegisterCustomPass(window);
    MotionBlur mbpass;
    RegisterCustomPass(mbpass);

    if (IsViewModeNeedPostProcessing())
    {
        LocalExposure localexposure;
        RegisterCustomPass(localexposure);
    }

    // HistogramExposure autoexposure;
    // RegisterCustomPass(autoexposure);

    ExposurePass exposurePass;
    RegisterCustomPass(exposurePass);

    // DOF
    DepthOfField DOF;
    RegisterCustomPass(DOF);
    KinoBloom bloom;
    RegisterCustomPass(bloom);

    LensEffect lens;
    RegisterCustomPass(lens);

    GizmoBeforeTonemapping gizmobefore;
    RegisterCustomPass(gizmobefore);

    if (GetRenderPipelineType() == ViewType::HDRSceneCapture)
    {
        return;
    }

    if (IsViewModeNeedPostProcessing())
    {
        CombinedLUTTonemapper tonemap;
        RegisterCustomPass(tonemap);
        for (auto& [passName, pPass] : mPassAfterTonemap)
        {
            RegisterCustomPass(pPass);
        }
    }

    MagnifierPass magnifier;
    RegisterCustomPass(magnifier);

    ScreenBlur blur;
    RegisterCustomPass(blur);

    if (GetRenderPipelineType() == ViewType::SceneCapture)
    {
        return;
    }
    // DebugGUI
    DebugGUI debugpass;
    RegisterCustomPass(debugpass);
    // UI
    UIPass UI;
    RegisterCustomPass(UI);

    FFSOutline ffsoutlinewireframe;
    RegisterCustomPass(ffsoutlinewireframe);

    Gizmo gizmopass;
    RegisterCustomPass(gizmopass);

    MPCDIPass mpcdi;
    RegisterCustomPass(mpcdi);

   
}

namespace cross {

struct CopyColor2DisplayPass
{
    constexpr std::string_view GetPassName() { return "CopyColor2DisplayPass"; }

    void FillInput(const GameContext& gameContext) {}

    void Execute(const GameContext& gameContext)
    {
        auto& inputView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
        auto& outputView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>();
        NGICopyTexture nativeWindowRegion{0, {}, 0, {}, {inputView->GetWidth(), inputView->GetHeight(), 1}};
        auto* copyPass = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->AllocatePass("CopyColor");
        copyPass->CopyTextureToTexture(outputView->GetTexture(), inputView->GetTexture(), 1, &nativeWindowRegion);
    }
};

void cross::FFSRenderPipeline::UpdateCloudShadowContext(REDPass* pass) const
{
    auto cloudSystem = mWorld->GetRenderSystem<CloudSystemR>();
    cloudSystem->UpdateCloudShadowContext(pass);
}

}   // namespace cross

void cross::FFSRenderPipeline::CompilePassGraph_MaterialEditorPreview()
{
    FSR2Pass fsr2{};
    RegisterCustomPass(fsr2, false);

    TemporalAntiAliasing taa;
    RegisterCustomPass(taa);

    BlitLowResSceneColor blitlowres;
    RegisterCustomPass(blitlowres);

    //ParticlePass particlepass;
    //RegisterCustomPass(particlepass);

    LocalExposure localexposure;
    RegisterCustomPass(localexposure);

    ExposurePass exposurePass;
    RegisterCustomPass(exposurePass);

    GizmoBeforeTonemapping gizmobefore;
    RegisterCustomPass(gizmobefore);

    CombinedLUTTonemapper tonemap;
    RegisterCustomPass(tonemap);

    Gizmo gizmopass;
    RegisterCustomPass(gizmopass);

    CopyColor2DisplayPass copyColor2Display;
    RegisterCustomPass(copyColor2Display);
}

void cross::FFSRenderPipeline::ExecutePasses()
{
    for (auto& pass : mPassRefs)
    {
        pass->FillInput(mGameContext);
        pass->Execute(mGameContext);
    }
    bool SeparateTranslucencyBlend = true;
    bool FSR2TransSetting =
        !dynamic_cast<FFSRenderPipeline*>(mGameContext.mRenderPipeline)->GetSetting()->mFSR2Setting.enable || !dynamic_cast<FFSRenderPipeline*>(mGameContext.mRenderPipeline)->GetSetting()->mFSR2Setting.SeparateTranslucency;
#ifdef WIN32
    AssembleFSR3();
    SeparateTranslucencyBlend = !dynamic_cast<FFSRenderPipeline*>(mGameContext.mRenderPipeline)->GetSetting()->mDLSSSetting.mSeparateTranslucencyBeforeDLSS;
    if (FSR2TransSetting && !SeparateTranslucencyBlend)
    {
        AssembleSeparateTranslucencyBlendPass();
    }
    AssembleDLSS();
   
#endif
    if (FSR2TransSetting && SeparateTranslucencyBlend)
    {
        AssembleSeparateTranslucencyBlendPass();
    }
    if (GetSetting()->mFSR2Setting.enable && mPassRefsAfterFSR2.size())
    {
        mRED->BeginRegion("After FSR2");
        auto RTWidth = mGameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>()->GetWidth();
        auto RTHeight = mGameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>()->GetHeight();
        Float4 screenSizeVector = {static_cast<float>(RTWidth), static_cast<float>(RTHeight), 1.0f / RTWidth, 1.0f / RTHeight};
        mRED->SetProperty(BuiltInProperty::ce_ScreenParams, screenSizeVector);
    }

    for (auto& pass : mPassRefsAfterFSR2)
    {
        pass->FillInput(mGameContext);
        pass->Execute(mGameContext);
    }
#ifdef WIN32
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeCrossEditor)
        AssembleDLSSG(false);
#endif
    if (GetSetting()->mFSR2Setting.enable && mPassRefsAfterFSR2.size())
        mRED->EndRegion();
    GetBuiltInTexture<PassSemanticName::DisplayColor>() = GetBuiltInTexture<PassSemanticName::LastPassColor>();
    GetBuiltInTexture<PassSemanticName::DisplayColorProxy>() = GetBuiltInTexture<PassSemanticName::LastPassColor>();
    mExtendPasses.clear();
    mPassRefsAfterFSR2.clear();
    mPassRefs.clear();
    mPassAfterTonemap.clear();
}

void cross::FFSRenderPipeline::RegisterPassAfterTonemap(GeneralPass pass) 
{
    if (mExtendPasses.find(HashString(pass.GetPassName().data())) == mExtendPasses.end() && mPassAfterTonemap.find(HashString(pass.GetPassName().data())) == mPassAfterTonemap.end())
    {
        mPassAfterTonemap.emplace(pass.GetPassName().data(), pass);
    }
}

void cross::FFSRenderPipeline::InitializePassSettings()
{
    mSubSampleShading.Initialize(GetSetting()->mSubSampleShadingSettings);
}

void cross::FFSRenderPipeline::AllocateVTResourceAndFeedBackBuffer()
{
    // VTBegin allocate spaces resource and clear feedbackbuffer
    if (GetSetting()->EnableVirtualTexture && !IsReflectionProbePipeline())
    {
        auto* vtSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<VirtualTextureSystemR>();
        vtSystem->AllocateResources();

        UInt32 feedbackFactory = 16;
        UInt32 gameViewWidth = (mTargetView->mTexture->mDesc.Width + feedbackFactory - 1) / feedbackFactory;
        UInt32 gameViewHeight = (mTargetView->mTexture->mDesc.Height + feedbackFactory - 1) / feedbackFactory;
        UInt32 bufferSize = gameViewWidth * gameViewHeight;

        // Allocate buffers
        mVTFeedbackBuffer = mRED->AllocateBuffer("VTFeedbackBuffer", NGIBufferDesc{sizeof(UInt32) * bufferSize, NGIBufferUsage::RWTexelBuffer | NGIBufferUsage::CopySrc});
        mVTFeedbackBufferView = mRED->AllocateBufferView(mVTFeedbackBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32) * bufferSize, GraphicsFormat::R32_UInt, sizeof(UInt32)});

        //---------pass: clear VTFeedbackBuffer----------
        auto* feedbackClearPass = mRED->AllocatePass("VirtualTextureFeedbackClear");
        feedbackClearPass->SetProperty(NAME_ID("VTFeedbackBuffer"), mVTFeedbackBufferView);

        UInt3 groupSize_1;
        mVTFeedbackComputeShader->GetThreadGroupSize("VTFeedbackClear", groupSize_1.x, groupSize_1.y, groupSize_1.z);
        feedbackClearPass->Dispatch(mVTFeedbackComputeShader, "VTFeedbackClear", (gameViewWidth + groupSize_1.x - 1) / groupSize_1.x, (gameViewHeight + groupSize_1.y - 1) / groupSize_1.y, 1);
        SCOPED_CPU_TIMING(GroupRendering, "VTGlobalUpdate");
        vtSystem->Update(mRED, mWorld, mVTFeedbackComputeShader, GetFrameCount(), mVTFeedBackManager.get());
    }
    else
    {
        // empty buffer
        mVTFeedbackBuffer = mRED->AllocateBuffer("VTFeedbackBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer | NGIBufferUsage::CopySrc});
        mVTFeedbackBufferView = mRED->AllocateBufferView(mVTFeedbackBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});
    }
}

void cross::FFSRenderPipeline::Assemble(REDTextureView* targetView)
{
    QUICK_SCOPED_CPU_TIMING("FFSRenderPipeline::Assemble");
#ifdef WIN32
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeCrossEditor)
        AssembleDLSSG();
#endif
    mTargetView = targetView;
    mGameContext.mRenderCamera = const_cast<RenderCamera*>(GetRenderCamera());
    mCullingResult = mRED->Cull(REDCullingDesc{mGameContext.mRenderWorld, mGameContext.mRenderCamera});
    //   set all the light related context
    UpdateLightContext();
    auto* shadowSys = mWorld->GetRenderSystem<ShadowSystemR>();
    shadowSys->SetUpRenderPipelineShadowLights(this, mLightList);

    bool isReflProbePipeline = IsReflectionProbePipeline();

    InitializePassSettings();

    mRED->SetProperty(NAME_ID("ShowComplexPixel"), GetSetting()->mSubSampleShadingSettings.ShowComplex);

    if (ShouldRenderSkyAtmosphere())
    {
        // mRED->BeginRegion("PreComputeSkyAtmosphere");
        // mWorld->GetRenderSystem<SkyAtmosphereSystemR>()->SetupGlobalSkyAtmosphereContext(mGameContext.mRenderPipeline->GetRenderingExecutionDescriptor());

        UpdateSkyAtmosphereAdvancedVars();

        PrepareAtmosphereSunLight();
        PreComputeSkyAtmosphere(mRenderCamera);
        AssembleSkyAtmosphereDistantSkyLightPass();

        auto skyAtmoRenderContextSetFunc = mWorld->GetRenderSystem<SkyAtmosphereSystemR>()->GetSkyAtmoRenderContextFunc();
        auto useDisk = GetRenderPipelineType() != ViewType::ReflectionProbe;
        skyAtmoRenderContextSetFunc(mRED, false, false, useDisk, false);
        // mRED->EndRegion();
    }
    else
    {
        mRED->SetProperty(cross::BuiltInProperty::USE_SKY_ATMOSPHERE, false);

        auto primitive = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderPrimitives();

        // all set to black for default
        SkyRenderingPSParamNGI renderingParam;
        renderingParam.TransmittanceLutTexture.mValue = primitive->mDefaultBlackTexture2DView.get();
        renderingParam.MultiScatTexture.mValue = primitive->mDefaultBlackTexture2DView.get();
        renderingParam.SkyViewLutTexture.mValue = primitive->mDefaultBlackTexture2DView.get();
        renderingParam.DistantSkyLightTexture.mValue = primitive->mDefaultBlackTexture2DView.get();
        renderingParam.AtmosphereCameraScatteringVolume.mValue = primitive->mDefaultBlackTexture3DView.get();
        SetPassParameters(renderingParam, mRED);
    }

    mFoliageGpuDriven.Initialize(GetSetting()->mFoliageGpuDrivenSettings);
    mFoliageGpuDriven.BeginAssemble();

    // Render Target Size View
    NGITextureViewDesc viewDesc{
        NGITextureUsage::ShaderResource | NGITextureUsage::DepthStencil | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
        mDepthStencilFormat,
        NGITextureType::Texture2D,
        {NGITextureAspect::Depth, 0, 1, 0, 1},
    };

    // mDepthStencilTex->ExtendLifetime();
    // Allocate DepthStencilBuffer

    REDTextureView* lastDepthOnlyView = nullptr;
    GetBuiltInTexture<PassSemanticName::LastFrameEyeAdpatationTex>() = nullptr;
    if (GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() && mRED->Validate(GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>()))
    {
        lastDepthOnlyView = mRED->AllocateTextureView(GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>(), viewDesc);
    }
    else
    {
        lastDepthOnlyView = GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>();
    }

    if (mRED->Validate(mGPassDepthPyramidLastFrame))
    {
        mGPassDepthPyramidLastFrameView = mRED->AllocateTextureView(mGPassDepthPyramidLastFrame,
                                                                    {
                                                                        NGITextureUsage::ShaderResource,
                                                                        mGPassDepthPyramidLastFrame->mDesc.Format,
                                                                        NGITextureType::Texture2D,
                                                                        {
                                                                            NGITextureAspect::Color,
                                                                            0,
                                                                            mGPassDepthPyramidLastFrame->mDesc.MipCount,
                                                                            0,
                                                                            1,
                                                                        },
                                                                    });
    }
    else
    {
        mGPassDepthPyramidLastFrameView = nullptr;
    }

    // Reprojection DepthPyramid
    mGPassDepthPyramidReprojectionView = AssembleDepthPyramid(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), mRenderCamera);

    if (GetSetting()->mSubSampleShadingSettings.enable)
    {
        mSubSampleShading.CreateMSAAOverideDepthView(GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>());
    }
    // PreDepth
    if (GetSetting()->EnablePredepth)
    {
        AssemblePreDepth(GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>());
    }

    // VTBegin allocate spaces resource and clear feedbackbuffer
    AllocateVTResourceAndFeedBackBuffer();

    // Cluster lighting culling for many light rendering, output light indices of each cluster
    AssembleBuildTileCulling();

    // GPass
    auto& gBufferViews = GetBuiltInTexture<PassSemanticName::GBufferViews>();
    // Attention, during GPass the sceneColor is only filled with EMISSIVE color!
    REDTextureView* sceneColorView = nullptr;
    GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>() = nullptr;
    GetBuiltInTexture<PassSemanticName::FSRReactiveMaskView>() = nullptr;
    AssembleGPass(gBufferViews,
                  sceneColorView,
                  GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(),
                  GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(),
                  mGPassDepthPyramidLastFrameView,
                  GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>(),
                  GetBuiltInTexture<PassSemanticName::FSRReactiveMaskView>());

    mGPassDepthPyramidCurrFrameView = AssembleDepthPyramid(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), nullptr);

    AssembleDrawRenderTextureToAtlasPass();

    // Thus we copy emissive color for SmartGI here
    CopyEmissiveAfterGPass(mGameContext, sceneColorView);

    if (!GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>())
    {
        GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>() = GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>();
    }

    if (GetRenderPipelineType() == ViewType::PrefabProxy)
    {
        PostProcessUtil([&](REDPass* pass) { pass->SetProperty(NAME_ID("_ColorTex"), gBufferViews[0], NGIResourceState::PixelShaderShaderResource); }, mPostProcessMtl, mRED, "blit", true, mTargetView);
        return;
    }

    // Add decal pass after GPass.
    //
    // Currently we copy gBufferViews to new texture for reading and then
    // modify gBuffer's BaseColor, normal, RMS.
    //
    // Due to the format of GBuffer, we cannot using hardware decal blending.
    // So we manually blend decal with the GBuffer in Decal pass shaders.
    // See LitDecalSurfaceData.hlsl and Decal.hlsl.
    //
    // In the future we can adjust the format of GBuffer to optimize the decal blending

    // TODO(mindalv), the decal sort needs some process for parallelization
    AssembleDecalPass(gBufferViews, sceneColorView, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>());

    // GTAO
    AssembleAmbientOcclusionPass(mWorld, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(), gBufferViews);
    // AssembleGTAO(mWorld, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(), gBufferViews);

    {
        mGPassDepthPyramidLastFrameView = AssembleDepthPyramid(GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>(), nullptr);
        mGPassDepthPyramidLastFrame = mGPassDepthPyramidLastFrameView->mTexture;
        mGPassDepthPyramidLastFrame->ExtendLifetime();
    }

    // Contact Shadow
    REDTextureView* contactShadowView = nullptr;
    bool contactShadowTransmissionEnabled = AssembleContactShadow(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), gBufferViews[1], gBufferViews[2], contactShadowView);

    bool enableGPUDriven = GetSetting()->GPUDrivenLight && GetSetting()->mFoliageGpuDrivenSettings.enable;
    REDBufferView* clustersBufferView = nullptr;
    REDBufferView* shadowBufferView = nullptr;
    if (enableGPUDriven)
    {
        cross::NGIBufferView* lightInstanceView = mFoliageGpuDriven.GetLightInstanceView();
        AssembleMassiveLightsShadow(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), gBufferViews[1], gBufferViews[2], lightInstanceView, clustersBufferView, shadowBufferView);
    }

    // Virtual Shadow Map
    cross::ShadowProperties& shadowProperties = mShadowProperties;
    REDTextureView* shadowMaskView = nullptr;
    if (GetRenderPipelineType() != ViewType::Thumbnail)
        AssembleShadow(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), gBufferViews, &shadowProperties, shadowMaskView);

    UpdateLightShadowDataIndices();
    // todo shadow as global context;

    REDTextureView* voxelFogView = nullptr;
    REDTextureView* integratedFogView = nullptr;
    if (ShouldRenderVolumetricFog())
    {
        AssembleVoxelVolumetricFogPass(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), shadowProperties, integratedFogView, voxelFogView);
    }

    auto width = mTargetView->mTexture->mDesc.Width;
    auto height = mTargetView->mTexture->mDesc.Height;
    GetBuiltInTexture<PassSemanticName::LastFrameSceneColorView>() = AllocateColorViewFromLastFrame(width, height);

    // Reflection Indirect = SSR + reflection probe + skylight
    // Why move AssembleReflectionIndirect after ForwardSky will cause subresource.LastWriter error?
    AssembleReflectionIndirect(gBufferViews, mReflectionIndirectView, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>());

    // MPCDI
    SettingsManager::MPCDI const& mpcdi = EngineGlobal::GetSettingMgr()->GetMPCDI();
    auto displayView = GetBuiltInTexture<PassSemanticName::DisplayColor>();
    if (mpcdi.enabled && mType == ViewType::GameView /*&& !isReflProbePipeline*/)
    {
        auto& desc = displayView->mTexture->mDesc;
        auto newdisplayview = CreateTextureView2D("MPCDI Color Texture", desc.Width, desc.Height, desc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::SubpassInput | NGITextureUsage::ShaderResource);
        GetBuiltInTexture<PassSemanticName::DisplayColorProxy>() = newdisplayview;
    }
    else
    {
        GetBuiltInTexture<PassSemanticName::DisplayColorProxy>() = displayView;
    }

    ScreenSpaceFog* fogPass = ShouldUseMultiLayerFog() ? dynamic_cast<ScreenSpaceFog*>(&mMultiLayerFog) : dynamic_cast<ScreenSpaceFog*>(&mExponentialFog);
    ProcessPerPixelFogTranmittance(*fogPass, sceneColorView);

    //Deferred
    AssembleDeferredLighting(shadowProperties,
                             shadowMaskView,
                             gBufferViews,
                             GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(),
                             GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>(),
                             contactShadowView,
                             sceneColorView,
                             contactShadowTransmissionEnabled);

    //gpu driven light only responsible for foliage light
    if (EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->GPUDrivenLight)
    {
        AssembleGPUDrivenLight(gBufferViews,
                               GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(),
                               GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>(),
                               sceneColorView,
                               clustersBufferView);
    }

    GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>() = nullptr;
    GetBuiltInTexture<PassSemanticName::FSRTransparentReactiveMaskView>() = nullptr;
    // SkyAtmosphere
    if (ShouldRenderSkyAtmosphere())
    {
        AssembleSkyAtmospherePass(&shadowProperties, sceneColorView, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>());
    }

    {
        AssembleSkyPass(GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(),
                        GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(),
                        sceneColorView,
                        integratedFogView,
                        GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>(),
                        GetBuiltInTexture<PassSemanticName::FSRTransparentReactiveMaskView>());
    }
    // ForwardTransparent

    // SmartGI for advanced GI effects
    AssembleSmartGI(gBufferViews, GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), mGPassDepthPyramidLastFrameView, sceneColorView, mAOView, &mShadowProperties);

    // Indirect lighting composite, composite all the indirect lighting
    AssembleIndirectLightingComposite(gBufferViews, GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), GetBuiltInTexture<PassSemanticName::DirectLightingSceneColor>(), sceneColorView);

    if (GetSetting()->VolumetricLight)
    {
        AssembleVolumetricLight(&shadowProperties, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(), sceneColorView);
    }
    {
        // After IndirectLightingComposite, copy sceneColor to SmartGI's finish stage. SmartGI's screen space tracing also use last frame's scene color
        GetBuiltInTexture<PassSemanticName::SceneColorBeforeTransparent>() =
            CreateTextureView2D("SceneColorBeforeTransparent", sceneColorView->GetWidth(), sceneColorView->GetHeight(), sceneColorView->mTexture->mDesc.Format, NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource);
        NGICopyTexture region{0, {}, 0, {}, {sceneColorView->GetWidth(), sceneColorView->GetHeight(), 1}};
        mRED->AllocatePass("Copy Scene Color")->CopyTextureToTexture(GetBuiltInTexture<PassSemanticName::SceneColorBeforeTransparent>()->mTexture, sceneColorView->mTexture, 1, &region);
        AssembleSmartGIFinish(GetBuiltInTexture<PassSemanticName::SceneColorBeforeTransparent>());

        if (ShouldRenderScreenSpaceFog())   // If render sky cloud, cloud apply pass would calculate fog result.
        {
            AssembleScreenSpaceFogPass(*fogPass, sceneColorView, GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), voxelFogView);
        }

        auto cloudTransOrder = GetSetting()->mCloudSetting.CloudTransOrder;
        
        auto renderTransparent = [&]() {
            AssembleForwardTransparent(
                shadowProperties,
                shadowMaskView,
                GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(),
                GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(),
                contactShadowView,
                mAOView,
                GetBuiltInTexture<PassSemanticName::LastFrameSceneColorView>(),
                GetBuiltInTexture<PassSemanticName::SceneColorBeforeTransparent>(),
                sceneColorView,
                mSSPRView,
                integratedFogView,
                GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>(),
                GetBuiltInTexture<PassSemanticName::FSRTransparentReactiveMaskView>());
        };

        auto renderClouds = [&]() {
            AssembleCloudPass(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), CLOUD_PIPE_METHOD::SPATIAL_AND_TEMPORAL);
            AssembleFogAndCloudApply(GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), voxelFogView, sceneColorView, false);
        };

        if (cloudTransOrder == CloudsTransparentOrder::RenderAfterTransparent) {
            renderTransparent();
            renderClouds();
        } else if (cloudTransOrder == CloudsTransparentOrder::RenderBeforeTransparent) {
            renderClouds();
            renderTransparent();
        }
  
        if (isReflProbePipeline)
        {
            PostProcessUtil([&](REDPass* pass) { pass->SetProperty(NAME_ID("_ColorTex"), sceneColorView, NGIResourceState::PixelShaderShaderResource); }, mPostProcessMtl, mRED, "blit", true, mTargetView);
        }
        GetBuiltInTexture<PassSemanticName::LastPassColor>() = sceneColorView;
        if (isReflProbePipeline)
        {
            BlitLowResSceneColor blitlowres;
            blitlowres.FillInput(mGameContext);
            blitlowres.Execute(mGameContext);
            return;
        }

        // Visualization pass here, should before FSR upscaling
        if (true)
        {
            GetBuiltInTexture<PassSemanticName::SceneColorBeforeVisualization>() =
                CreateTextureView2D("ColorBeforeVisualization", sceneColorView->GetWidth(), sceneColorView->GetHeight(), sceneColorView->mTexture->mDesc.Format, NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource);
            mRED->AllocatePass("Copy Scene Color Before Visualization")->CopyTextureToTexture(GetBuiltInTexture<PassSemanticName::SceneColorBeforeVisualization>()->mTexture, sceneColorView->mTexture, 1, &region);
            AssembleViewModeVisualization(GetBuiltInTexture<PassSemanticName::LastPassColor>(), GetBuiltInTexture<PassSemanticName::SceneColorBeforeVisualization>());
        }

        if (GetRenderPipelineType() != ViewType::MaterialEditorPreview)
        {
            CompilePassGraph();
        }
        else
        {
            CompilePassGraph_MaterialEditorPreview();
        }

        ExecutePasses();

        // VTEnd feedback copy GPU to CPU. next frame will use
        if (GetSetting()->EnableVirtualTexture && (GetRenderPipelineType() != ViewType::ReflectionProbe && GetRenderPipelineType() != ViewType::PrefabProxy))
        {
            VTFeedbackBufferDesc feedbackDesc;
            UInt2 vtviewSize = {mTargetView->mTexture->mDesc.Width, mTargetView->mTexture->mDesc.Height};
            UInt32 feedbackFactory = 16;
            feedbackDesc.Init2D(vtviewSize, feedbackFactory);

            SizeType buffSize = static_cast<SizeType>(feedbackDesc.TotalReadSize * sizeof(UInt32));
            NGIBufferDesc desc{
                buffSize,
                NGIBufferUsage::CopyDst,
            };
            auto stagingBuffer = mVTFeedBackManager->GetStagingBuffer(desc);

            NGICopyBuffer region1{0, 0, buffSize};

            // vt feedback copy pass
            auto stagingBufferRED = mRED->AllocateBuffer("FeedbackbufferCopy", stagingBuffer);
            auto* vtFeedbackBufferCopyPass = mRED->AllocatePass("VirtualTextureFeedbackCopy");
            vtFeedbackBufferCopyPass->CopyBufferToBuffer(stagingBufferRED, mVTFeedbackBuffer, 1, &region1);
            stagingBufferRED->SetExternalState(NGIResourceState::CopyDst);

            // copy vtfeedbackbuffer to feedback item for next frame
            auto* vtSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<VirtualTextureSystemR>();
            vtSystem->End(stagingBuffer, feedbackDesc, GetFrameCount(), mVTFeedBackManager.get());
        }
    }
}
bool cross::FFSRenderPipeline::AssembleContactShadow(REDTextureView* depthOnlyView, REDTextureView* gBuffer1MapView, REDTextureView* gBuffer2MapView, REDTextureView*& contactShadowView)
{
    mContactShadow.Initialize(GetSetting()->mContactShadowSettings);
    mContactShadow.Execute(mGameContext, depthOnlyView, gBuffer1MapView, gBuffer2MapView, contactShadowView, GetFrameCount() % 1024, mWorld);
    return mContactShadow.IsContactShadowTransmissionEnabled();
}

void cross::FFSRenderPipeline::AssembleMassiveLightsShadow(REDTextureView* depthOnlyView, REDTextureView* gBuffer1MapView, REDTextureView* gBuffer2MapView, cross::NGIBufferView* lightInstanceView, REDBufferView*& clustersBufferView,
                                                           REDBufferView*& shadowBufferView)
{
    mMassiveLightsShadow.Initialize(GetSetting()->mMassiveLightsShadowSettings);
    mMassiveLightsShadow.Execute(mGameContext, depthOnlyView, gBuffer1MapView, gBuffer2MapView, lightInstanceView, clustersBufferView, shadowBufferView, GetFrameCount(), mWorld);
}

cross::REDTextureView* cross::FFSRenderPipeline::AssembleDepthPyramid(REDTextureView* depthView, const RenderCamera* camera, bool reverseZ /*= true*/, bool extendToNextFrame /* = false*/, UInt16 firstArraySlice /* = 0*/, bool closestDepth)
{
    mDepthPyramidPass.inputDepthView = depthView;
    mDepthPyramidPass.Initialize(GetSetting()->mDepthPyramidSettings);
    mDepthPyramidPass.Execute(mGameContext, camera, reverseZ, firstArraySlice, closestDepth);

    if (extendToNextFrame)
    {
        auto hiZTexture = mDepthPyramidPass.outputHiZView->mTexture;
        hiZTexture->ExtendLifetime();
    }

    return mDepthPyramidPass.outputHiZView;
}

void cross::FFSRenderPipeline::CopyEmissiveAfterGPass(const GameContext& gameContext, REDTextureView* sceneColorView)
{
    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    // Always decoupling sceneColor and emmisiveColor
    {
        auto width = sceneColorView->GetWidth();
        auto height = sceneColorView->GetHeight();
        auto emissiveColorCopy = gameContext.mRenderPipeline->CreateTextureView2D("EmissiveColor", width, height, sceneColorView->GetDesc().Format, NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource);
        NGICopyTexture region{0, {}, 0, {}, {width, height, 1}};
        red->AllocatePass("CopyEmissiveColorForGI")->CopyTextureToTexture(emissiveColorCopy->mTexture, sceneColorView->mTexture, 1, &region);
        mCurEmissiveColorRTView = emissiveColorCopy;
        // Stash and extend this Texture's life time to use in next frame
        mPreEmissiveColorRT = emissiveColorCopy->mTexture;
        if (mPreEmissiveColorRT->mType != REDResourceType::ExtendedToNext)
        {
            mPreEmissiveColorRT->ExtendLifetime();
        }
    }
}

void cross::FFSRenderPipeline::AssembleDrawRenderTextureToAtlasPass()
{
    mDrawRenderTextureToAtlasPass.Initialize(GetSetting()->mDrawRenderTextureToAtlasPassSettings);
    mDrawRenderTextureToAtlasPass.Execute(mGameContext);
}

void cross::FFSRenderPipeline::AssembleScreenSpaceFogPass(REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView * volumetricFogView) 
{
    ScreenSpaceFog* fogPass = ShouldUseMultiLayerFog() ? dynamic_cast<ScreenSpaceFog*>(&mMultiLayerFog) : dynamic_cast<ScreenSpaceFog*>(&mExponentialFog);
    AssembleScreenSpaceFogPass(*fogPass, sceneView, depthOnlyView, volumetricFogView);
}

void cross::FFSRenderPipeline::AssembleScreenSpaceFogPass(ScreenSpaceFog& fogPass, REDTextureView* sceneView, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView)
{
    // Move to ProcessPerPixelFogTranmittance
    /*auto* gameView = mTargetView == nullptr ? sceneView : mTargetView;
    auto gameViewWidth = gameView->mTexture->mDesc.Width;
    auto gameViewHeight = gameView->mTexture->mDesc.Height;
    auto cam = GetRenderCamera();
    Float3 cameraPos = cam->GetCameraOrigin();
#if defined(CE_USE_DOUBLE_TRANSFORM)
    Float3 cameraTile = cam->GetTilePosition();
    cameraPos += cameraTile * LENGTH_PER_TILE_F;
#endif

    fogPass.mWorld = mWorld;
    fogPass.mCamera = cam;
    fogPass.mCamPos = cameraPos;
    fogPass.mWidth = gameViewWidth;
    fogPass.mHeight = gameViewHeight;*/
    Assert(mPostProcessMtl);
    fogPass.mPostMat = mPostProcessMtl;
    fogPass.Initialize(mPostProcessSetting->FogSettings);
    fogPass.Execute(mGameContext, sceneView, depthOnlyView, volumetricFogView);
}

void cross::FFSRenderPipeline::AssembleVoxelVolumetricFogPass(REDTextureView* depthOnlyView, const ShadowProperties& shadowProperties, REDTextureView*& integratedView, REDTextureView*& target)
{
    auto* gameView = mTargetView;
    auto gameViewWidth = gameView->mTexture->mDesc.Width;
    auto gameViewHeight = gameView->mTexture->mDesc.Height;
    mVolumetricFog.mWidth = gameViewWidth;
    mVolumetricFog.mHeight = gameViewHeight;

    auto cam = GetRenderCamera();
    Float3 cameraPos = cam->GetCameraOrigin();
#if defined(CE_USE_DOUBLE_TRANSFORM)
    Float3 cameraTile = cam->GetTilePosition();
    cameraPos += cameraTile * LENGTH_PER_TILE_F;
#endif

    mVolumetricFog.mCamera = cam;
    mVolumetricFog.mCamPos = cameraPos;
    mVolumetricFog.mWidth = gameViewWidth;
    mVolumetricFog.mHeight = gameViewHeight;
    mVolumetricFog.mPostMat = mPostProcessMtl;
    mVolumetricFog.mFrameNum = cross::frame::GetRenderingFrameNumber();
    mVolumetricFog.mWorld = mWorld;
    mVolumetricFog.Initialize(mPostProcessSetting->FogSettings);
    mVolumetricFog.Execute(mGameContext, depthOnlyView, &shadowProperties, integratedView, target);
}

void cross::FFSRenderPipeline::AssembleFogAndCloudApply(REDTextureView* depthOnlyView, REDTextureView* volumetricFogView, REDTextureView*& sceneView, bool isSkyLight)
{

    auto* cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    cloudSys->SetPostProcessMtl(mPostProcessMtl);
    if (ShouldRenderScreenSpaceFog())
    {
        ScreenSpaceFog* fogPass = ShouldUseMultiLayerFog() ? dynamic_cast<ScreenSpaceFog*>(&mMultiLayerFog) : dynamic_cast<ScreenSpaceFog*>(&mExponentialFog);
        cloudSys->SetSFog(fogPass);
    }
    mSkyCloud.mWorld = mWorld;
    mSkyCloud.mRED = mRED;
    mSkyCloud.mPostMtl = mPostProcessMtl;
    mSkyCloud.AssembleFogAndCloudApplyPass(mGameContext, depthOnlyView, volumetricFogView, sceneView, mCloudRes, isSkyLight);
}

void cross::FFSRenderPipeline::UpdateBuffer(NGIBuffer* buffer, void* data, UInt32 size, NGIResourceState resourceState)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    auto* scratchBuffer = rendererSystem->GetScratchBuffer();
    auto stagingBufferWrap = scratchBuffer->AllocateStaging(NGIBufferUsage::CopySrc, size);

    stagingBufferWrap.MemWrite(0, data, size);

    NGICopyBuffer region{
        stagingBufferWrap.GetNGIOffset(),
        0,
        size,
    };

    rendererSystem->UpdateBuffer(buffer, stagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, resourceState);
}

void cross::FFSRenderPipeline::UpdateShadowContext(REDPass* pass, const ShadowProperties* shadowProperties) const
{
    if (shadowProperties->shadowDatasBufferView)
    {
        pass->SetProperty(NAME_ID("_ShadowMatrices"), shadowProperties->shadowMatricesBufferView);
        pass->SetProperty(NAME_ID("_ShadowDatas"), shadowProperties->shadowDatasBufferView);
        pass->SetProperty(NAME_ID("_DirectionalShadowDatas"), shadowProperties->directionalShadowDatasBufferView);
        pass->SetProperty(NAME_ID("_LightDatas"), shadowProperties->lightDataBufferView);
        pass->SetProperty(NAME_ID("_DepthMap"), shadowProperties->depthView);
        pass->SetProperty(NAME_ID("_ScreenSizeAndInvSize"), shadowProperties->screenSizeAndInvSize);
        pass->SetProperty(NAME_ID("_UIntParams0"), shadowProperties->uintParams0);
        pass->SetProperty(NAME_ID("_FloatParams0"), shadowProperties->floatParams0);
        pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), shadowProperties->projectionDataBufferView);
        pass->SetProperty(NAME_ID("_PageTable"), shadowProperties->pageTableBufferView);
        pass->SetProperty(NAME_ID("_PhysicalPagePool"), shadowProperties->physicalPagePoolView);
        pass->SetProperty(NAME_ID("_SpotLightShadowRanges"), shadowProperties->cachedSpotShadowMapRangesView);
        pass->SetProperty(NAME_ID("_PointLightShadowRanges"), shadowProperties->cachedPointShadowMapRangesView);
        Float4 shadowSize{static_cast<float>(GetSetting()->ShadowMapResolution), static_cast<float>(GetSetting()->ShadowMapResolution), 1.f / GetSetting()->ShadowMapResolution, 1.f / GetSetting()->ShadowMapResolution};
        pass->SetProperty(NAME_ID("_DirShadowMapSize"), shadowSize);
        auto* shadowCameraSys = mWorld->GetRenderSystem<ShadowSystemR>();
        pass->SetProperty(NAME_ID("_ShadowCascadeCount"), static_cast<float>(shadowCameraSys->GetCascadeCount()));
    }
    else
    {
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        std::vector<UInt32> Datas(64, 0);
        {
            SizeType shadowDataByteSize = 44;   // shadowData 64 // 64 matrix
            auto* scratchBuffer = rdrSys->GetScratchBuffer();
            auto shadowDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, shadowDataByteSize);
            shadowDataBufferWrap.MemWrite(0, Datas.data(), 44);
            auto shadowDataView = rdrSys->GetTransientResourceManager()->AllocateBufferView(
                NGIBufferViewDesc{
                    NGIBufferUsage::StructuredBuffer,
                    shadowDataBufferWrap.GetNGIOffset(),
                    shadowDataByteSize,
                    GraphicsFormat::Unknown,
                    sizeof(44),
                },
                shadowDataBufferWrap.GetNGIBuffer());
            pass->SetProperty(NAME_ID("_ShadowDatas"), shadowDataView);
        }

        {
            SizeType directionalShadowDataByteSize = 64;
            auto* scratchBuffer = rdrSys->GetScratchBuffer();
            auto directionalShadowDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, directionalShadowDataByteSize);
            directionalShadowDataBufferWrap.MemWrite(0, Datas.data(), 64);
            auto dirShadowDataView = rdrSys->GetTransientResourceManager()->AllocateBufferView(
                NGIBufferViewDesc{
                    NGIBufferUsage::StructuredBuffer,
                    directionalShadowDataBufferWrap.GetNGIOffset(),
                    directionalShadowDataByteSize,
                    GraphicsFormat::Unknown,
                    64,
                },
                directionalShadowDataBufferWrap.GetNGIBuffer());
            pass->SetProperty(NAME_ID("_DirectionalShadowDatas"), dirShadowDataView);
        }

        {
            SizeType shadowMatricesByteSize = 64;
            auto* scratchBuffer = rdrSys->GetScratchBuffer();
            auto shadowMatricesBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, shadowMatricesByteSize);
            shadowMatricesBufferWrap.MemWrite(0, Datas.data(), 64);
            auto shadowMatView = rdrSys->GetTransientResourceManager()->AllocateBufferView(
                NGIBufferViewDesc{
                    NGIBufferUsage::StructuredBuffer,
                    shadowMatricesBufferWrap.GetNGIOffset(),
                    shadowMatricesByteSize,
                    GraphicsFormat::Unknown,
                    sizeof(Float4x4),
                },
                shadowMatricesBufferWrap.GetNGIBuffer());
            pass->SetProperty(NAME_ID("_ShadowMatrices"), shadowMatView);
        }
    }

    if (shadowProperties->dirShadowMapsView)
    {
        pass->SetProperty(NAME_ID("_DirShadowMaps"), shadowProperties->dirShadowMapsView, NGIResourceState::PixelShaderShaderResource);
    }

    if (shadowProperties->cachedSpotShadowMapsView)
    {
        pass->SetProperty(NAME_ID("_LocalLightMultiTargetShadowMap"), shadowProperties->cachedSpotShadowMapsView, NGIResourceState::PixelShaderShaderResource);
    }

    auto* shadowSys = mWorld->GetRenderSystem<ShadowSystemR>();
    const auto& cascadeControl = shadowSys->GetCascadeControl();
    pass->SetProperty(NAME_ID("_CascadeControl"), Float4(cascadeControl[0], cascadeControl[1], cascadeControl[2], cascadeControl[3]));

    const auto& shadowSetting = GetSetting()->mVirtualShadowMapSettings;
    bool isEnableVirtualShadowMap = shadowSetting.EnableVirtualShadowMap();
    bool isEnableLocalLightShadowCache = GetSetting()->mLocalLightShadowMapSettings.enable;

    pass->SetProperty(NAME_ID("ENABLE_VSM"), isEnableVirtualShadowMap);
    pass->SetProperty(NAME_ID("_SMRTRayCount"), shadowSetting.SMRTRayCount);
    pass->SetProperty(NAME_ID("_SMRTSamplesPerRay"), shadowSetting.SMRTSamplesPerRay);

    pass->SetProperty(NAME_ID("USE_CACHED_LOCAL_LIGHT_SHADOW"), isEnableLocalLightShadowCache);
}

void cross::FFSRenderPipeline::AssembleAmbientOcclusionPass(RenderWorld* world, REDTextureView* depthStencilView, std::array<REDTextureView*, 4>& gBufferViews)
{
    bool bEnableAO = GetSetting()->mIndirectLightingCompositeSettings.mAOSetting.enable && !IsReflectionProbePipeline();
    if (!bEnableAO)
    {
        mAOView = nullptr;
        return;
    }
    auto aoType = GetSetting()->mIndirectLightingCompositeSettings.mAOSetting.mAOType;
    switch (aoType)
    {
    case AOType::GTAO:
        if (GetSetting()->mIndirectLightingCompositeSettings.mAOSetting.mGTAOSetting.enable)
        {
            AssembleGTAO(world, GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(), gBufferViews);
        }
        else
        {
            mAOView = nullptr;
        }
        break;
    default:
        mAOView = nullptr;
        break;
    }
}

void cross::FFSRenderPipeline::AssembleGTAO(RenderWorld* world, REDTextureView* depthStencilView, std::array<REDTextureView*, 4>& gBufferViews)
{
    bool enableTemporalNoise = GetSetting()->mFSR2Setting.enable || GetSetting()->mTemporalAntiAliasingSetting.enable;
    mGTAOPass.Initialize(GetSetting()->mIndirectLightingCompositeSettings.mAOSetting.mGTAOSetting);
    mGTAOPass.Execute(mGameContext, enableTemporalNoise, depthStencilView, gBufferViews, mAOView);
}

void cross::FFSRenderPipeline::UpdateReflectionProbe()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* reflectionProbeSystem = TYPE_CAST(ReflectionProbeSystemR*, mWorld->GetRenderSystem<ReflectionProbeSystemR>());
    auto* transformSystem = TYPE_CAST(TransformSystemR*, mWorld->GetRenderSystem<TransformSystemR>());

    const UInt32 MAX_RP = (GetSetting()->EnableDeferredLighting && GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.enable) ? MAX_PIPELINE_REFLECTION_PROBE_NUM : 4;
    auto& reflectionProbes = GetRefleProbeList();

    std::vector<Float4> rpExtentMipmapCounts(MAX_RP, 0);
    std::vector<Float4> rpPosBlendDis(MAX_RP, 0);
    std::vector<Float4> rpBoxMin(MAX_RP, 0);
    std::vector<Float4> rpBoxMax(MAX_RP, 0);
    std::vector<Float4> rpEulerRot(MAX_RP, 0);
#if defined(CE_USE_DOUBLE_TRANSFORM)
    std::vector<Float4> rpTilePos(MAX_RP, 0);
#endif
    for (UInt32 i = 0; i < reflectionProbes.size() && i < MAX_RP; i++)
    {
        auto& rp = reflectionProbes.at(i);
        auto rpComponent = mWorld->GetComponent<ReflectionProbeCameraComponentR>(rp);
        auto transformComp = mWorld->GetComponent<TransformComponentR>(rp);
        float mipmapCount = 1.f;

        auto propName = fmt::format("ce_ReflectionProbeMap{}", i + 1);

        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeType(rpComponent.Read()) == ReflectionProbeType::Realtime)
        {
            mRED->SetProperty(propName, rendererSystem->GetRenderPrimitives()->mDefaultBlackTextureCubeView.get());
            mipmapCount = 0;
        }
        else
        {
            auto rpCubemap = reflectionProbeSystem->GetReflectionProbeCameraReflectionCubeMap(rpComponent.Read());
            if (!rpCubemap)
                continue;
            mRED->SetProperty(propName, rpCubemap->GetNGITextureView());
            mipmapCount = static_cast<float>(rpCubemap->GetNGITextureView()->GetDesc().SubRange.MipLevels);

            auto rpTransformH = mWorld->GetComponent<TransformComponentR>(rp);
            auto quaternionRP = transformSystem->GetWorldRotation(rpTransformH.Read());
            auto eulerRot = Quaternion::QuaternionToEuler(quaternionRP);
            rpEulerRot[i] = Float4(MathUtils::ConvertToDegrees(eulerRot.x), MathUtils::ConvertToDegrees(eulerRot.y), MathUtils::ConvertToDegrees(eulerRot.z), 1.f);
        }
        if (GetSetting()->EnableDeferredLighting && GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.enable)
        {
            mipmapCount = reflectionProbeSystem->GetReflectionProbeCameraIntensity(rpComponent.Read());
        }
        auto& translation = transformSystem->GetWorldTranslation(transformComp.Read());
        float blendDistance = reflectionProbeSystem->GetReflectionProbeCameraBlendDistance(rpComponent.Read());
        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(rpComponent.Read()) == ReflectionProbeShapeType::Sphere)
        {
            float radius = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(rpComponent.Read());
            rpExtentMipmapCounts[i] = Float4(radius, radius, radius, mipmapCount);
            blendDistance = blendDistance > radius ? radius : blendDistance;
            BoundingSphere rpSphere(translation, radius);
            BoundingBox bbox;
            Float3 boxMin;
            Float3 boxMax;
            BoundingBox::CreateFromSphere(bbox, rpSphere);
            bbox.GetMinMax(&boxMin, &boxMax);
            rpBoxMin[i] = Float4(boxMin.x, boxMin.y, boxMin.z, static_cast<float>(reflectionProbeSystem->GetReflectionProbeCameraBoxProjection(rpComponent.Read())));
            rpBoxMax[i] = Float4(boxMax.x, boxMax.y, boxMax.z, 1.0f);
        }
        else
        {
            Float3 extent = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(rpComponent.Read()) / 2.f;
            rpExtentMipmapCounts[i] = Float4(extent.x, extent.y, extent.z, mipmapCount);
            float maxD = extent.x > extent.y ? extent.x : extent.y;
            maxD = maxD > extent.z ? maxD : extent.z;
            blendDistance = blendDistance > maxD ? maxD : blendDistance;
            BoundingBox rpBox(translation, extent);
            Float3 boxMin;
            Float3 boxMax;
            rpBox.GetMinMax(&boxMin, &boxMax);
            rpBoxMin[i] = Float4(boxMin.x, boxMin.y, boxMin.z, static_cast<float>(reflectionProbeSystem->GetReflectionProbeCameraBoxProjection(rpComponent.Read())));
            rpBoxMax[i] = Float4(boxMax.x, boxMax.y, boxMax.z, 1.0f);
        }
        rpPosBlendDis[i] = Float4(translation.x, translation.y, translation.z, blendDistance);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        rpTilePos[i] = Float4(transformSystem->GetTilePosition(mWorld->GetComponent<TilePositionComponentR>(rp).Read()), 0.f);
#endif
    }
    for (UInt32 i = static_cast<UInt32>(reflectionProbes.size()); i < MAX_RP; i++)
    {
        mRED->SetProperty("ce_ReflectionProbeMap" + std::to_string(i + 1), rendererSystem->GetRenderPrimitives()->mDefaultBlackTextureCubeView.get());
    }
    mRED->SetProperty(BuiltInProperty::ce_RPExtentMipmapCount, rpExtentMipmapCounts.data(), rpExtentMipmapCounts.size() * sizeof(Float4));
    mRED->SetProperty(BuiltInProperty::ce_ReflectionProbePosDistance, rpPosBlendDis.data(), rpPosBlendDis.size() * sizeof(Float4));
    mRED->SetProperty(BuiltInProperty::ce_ReflectionProbeBoxMin, rpBoxMin.data(), rpBoxMin.size() * sizeof(Float4));
    mRED->SetProperty(BuiltInProperty::ce_ReflectionProbeBoxMax, rpBoxMax.data(), rpBoxMax.size() * sizeof(Float4));
    mRED->SetProperty(BuiltInProperty::ce_ReflectionProbeEulerRot, rpEulerRot.data(), rpEulerRot.size() * sizeof(Float4));
#if defined(CE_USE_DOUBLE_TRANSFORM)
    mRED->SetProperty(NAME_ID("ce_ReflectionProbeTilePos"), rpTilePos.data(), rpTilePos.size() * sizeof(Float4));
#endif
}

namespace cross {
void FFSRenderPipeline::AssembleVolumetricLight(ShadowProperties* shadowProperties, REDTextureView* sceneDepth, REDTextureView* dstColor)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();

    auto targetViewWidth = dstColor->mTexture->mDesc.Width;
    auto targetViewHeight = dstColor->mTexture->mDesc.Height;

    UInt16 lowResWidth = static_cast<UInt16>(targetViewWidth / 2);
    UInt16 lowResHeight = static_cast<UInt16>(targetViewHeight / 2);

    auto* depthSRV = mRED->AllocateTextureView(sceneDepth->mTexture,
                                               NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                  mDepthStencilFormat,
                                                                  NGITextureType::Texture2D,
                                                                  {
                                                                      NGITextureAspect::Depth,
                                                                      0,
                                                                      1,
                                                                      0,
                                                                      1,
                                                                  }});

    auto* volumeLightRaw = CreateTextureView2D("Volume Light Raw Texture", lowResWidth, lowResHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    auto* volumeLightRawBlurH = CreateTextureView2D("Volume Light Raw Texture", lowResWidth, lowResHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    auto* halfDepthView = CreateTextureView2D("DepthStencil Texture", lowResWidth, lowResHeight, GraphicsFormat::R32_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    RenderContext rayMarch(mRED->GetREDFrameAllocator());
    UpdateContext(rayMarch, mWorld);
    if (shadowProperties->dirShadowMapsView)
    {
        rayMarch.SetProperty(NAME_ID("shadow_textures"), shadowProperties->dirShadowMapsView);
    }
    rayMarch.SetProperty(NAME_ID("src_depth"), depthSRV);
    if (shadowProperties->shadowMatricesBufferView)
        rayMarch.SetProperty(NAME_ID("shadow_matrices"), shadowProperties->shadowMatricesBufferView);
    // rayMarch.SetProperty(NAME_ID("shadow_spheres"), shadowProperties->shadowSplitDatas.data(), shadowProperties->shadowSplitDatas.size() * sizeof(Float4));
    rayMarch.SetProperty(NAME_ID("target_size"), Float4(lowResWidth, lowResHeight, 1.0f / lowResWidth, 1.0f / lowResHeight));
    std::vector<REDTextureView*> rayMarchViews = {volumeLightRaw};
    PostProcess(red, rayMarchViews, mPostProcessMtl, "volumetric_ray_marching", std::move(rayMarch));

    RenderContext downDepth(mRED->GetREDFrameAllocator());
    downDepth.SetProperty(NAME_ID("src_texture"), depthSRV);
    if (NGIPlatform::OpenGLES3 == EngineGlobal::GetSettingMgr()->GetRenderMode())
        downDepth.SetProperty(NAME_ID("src_dimension"), Float4(targetViewWidth * 1.0f, targetViewHeight * 1.0f, 1.0f / targetViewWidth, 1.0f / targetViewHeight));
    std::vector<REDTextureView*> downDepthViews = {halfDepthView};
    PostProcess(red, downDepthViews, mPostProcessMtl, "down_sample_depth", std::move(downDepth));

    RenderContext blurH(mRED->GetREDFrameAllocator());
    blurH.SetProperty(NAME_ID("src_texture"), volumeLightRaw);
    blurH.SetProperty(NAME_ID("ref_depth"), halfDepthView);
    blurH.SetProperty(NAME_ID("src_dimension"), Float4(lowResWidth, lowResHeight, 1.0f / lowResWidth, 1.0f / lowResHeight));
    blurH.SetProperty(NAME_ID("blur_dir"), Float4(1, 0, 0, 0));
    std::vector<REDTextureView*> blurHViews = {volumeLightRawBlurH};
    PostProcess(red, blurHViews, mPostProcessMtl, "bilaterial_blur", std::move(blurH));

    RenderContext blurV(mRED->GetREDFrameAllocator());
    blurV.SetProperty(NAME_ID("src_texture"), volumeLightRawBlurH);
    blurV.SetProperty(NAME_ID("ref_depth"), halfDepthView);
    blurV.SetProperty(NAME_ID("src_dimension"), Float4(lowResWidth, lowResHeight, 1.0f / lowResWidth, 1.0f / lowResHeight));
    blurV.SetProperty(NAME_ID("blur_dir"), Float4(0, 1, 0, 0));
    std::vector<REDTextureView*> blurVViews = {volumeLightRaw};
    PostProcess(red, blurVViews, mPostProcessMtl, "bilaterial_blur", std::move(blurV));

    RenderContext upSample(mRED->GetREDFrameAllocator());
    upSample.SetProperty(NAME_ID("src_texture"), volumeLightRaw);
    upSample.SetProperty(NAME_ID("src_size"), Float4(lowResWidth, lowResHeight, 1.0f / lowResWidth, 1.0f / lowResHeight));
    upSample.SetProperty(NAME_ID("src_depth"), halfDepthView);
    upSample.SetProperty(NAME_ID("dst_depth"), depthSRV);
    std::vector<REDTextureView*> upSampleViews = {dstColor};
    PostProcess(red, upSampleViews, mPostProcessMtl, "bilaterial_upsample", std::move(upSample), false);
}

void FFSRenderPipeline::AssembleSkyAtmosphereDistantSkyLightPass()
{
    mWorld->GetRenderSystem<SkyAtmosphereSystemR>()->PreComputeDistantSkyLightTextures();
}

void FFSRenderPipeline::AssembleDecalPass(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* color, REDTextureView*& sceneDepthStencilView)
{
    QUICK_SCOPED_CPU_TIMING("FFSRenderPipeline::AssembleDecalPass");

    auto* DecalSystem = mWorld->GetRenderSystem<DecalSystemR>();
    if (DecalSystem && DecalSystem->ShouldRenderDecal())
    {
        mRED->BeginRegion("Decal Pass");
        {
            auto gameViewWidth = static_cast<UInt16>(gBufferViews[0]->mTexture->mDesc.Width);
            auto gameViewHeight = static_cast<UInt16>(gBufferViews[0]->mTexture->mDesc.Height);

            auto Blit = [this](REDTextureView* SrcView, REDTextureView* DstView) {
                PostProcessUtil([&](REDPass* pass) { pass->SetProperty(NAME_ID("_ColorTex"), SrcView, NGIResourceState::PixelShaderShaderResource); }, mPostProcessMtl, mRED, "blit", true, DstView);
            };

            // Allocate DBuffer 
            auto& DBufferViews = GetBuiltInTexture<PassSemanticName::DBufferViews>();
            DBufferViews[0] = CreateTextureView2D("DBuffer 0", gameViewWidth, gameViewHeight, gBufferViews[0]->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            DBufferViews[1] = CreateTextureView2D("DBuffer 1", gameViewWidth, gameViewHeight, gBufferViews[1]->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            DBufferViews[2] = CreateTextureView2D("DBuffer 2", gameViewWidth, gameViewHeight, gBufferViews[2]->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

            // Fill DBuffer Views
            DecalSystem->RenderDecalBuffers(sceneDepthStencilView, GetRenderCamera(), DBufferViews, color);

            // Allocate GBuffer Views
            std::array<REDTextureView*, 3> CopiedGBufferViews;
            CopiedGBufferViews[0] = CreateTextureView2D("Copied GBuffer 0", gameViewWidth, gameViewHeight, gBufferViews[0]->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            CopiedGBufferViews[1] = CreateTextureView2D("Copied GBuffer 1", gameViewWidth, gameViewHeight, gBufferViews[1]->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            CopiedGBufferViews[2] = CreateTextureView2D("Copied GBuffer 2", gameViewWidth, gameViewHeight, gBufferViews[2]->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

            // Copy GBuffer views for reading
            mRED->BeginRegion("Copy GBuffer");
            {
                Blit(gBufferViews[0], CopiedGBufferViews[0]);
                Blit(gBufferViews[1], CopiedGBufferViews[1]);
                Blit(gBufferViews[2], CopiedGBufferViews[2]);
            }
            mRED->EndRegion();

            // Apply DBuffer data to gbuffer0, 1, 2
            mApplyDBufferDataPass.Initialize(GetSetting()->mApplyDBufferDataPassSettings);
            mApplyDBufferDataPass.ExecuteImp(mGameContext, sceneDepthStencilView, DBufferViews, CopiedGBufferViews, gBufferViews);
        };
        mRED->EndRegion();
    }
}

void FFSRenderPipeline::AssembleSkyAtmospherePass(ShadowProperties* shadowData, REDTextureView* color, REDTextureView*& sceneDepthStencilView)
{
    QUICK_SCOPED_CPU_TIMING("AssembleSkyAtmospherePass");
    auto* sceneDepthView = mRED->AllocateTextureView(sceneDepthStencilView->mTexture,
                                                     NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                        sceneDepthStencilView->mDesc.Format,
                                                                        NGITextureType::Texture2D,
                                                                        NGITextureSubRange{
                                                                            NGITextureAspect::Depth,
                                                                            0,
                                                                            sceneDepthStencilView->mDesc.SubRange.MipLevels,
                                                                            0,
                                                                            1,
                                                                        }});

    auto gameViewWidth = static_cast<UInt16>(color->mTexture->mDesc.Width);
    auto gameViewHeight = static_cast<UInt16>(color->mTexture->mDesc.Height);

    auto skyAtmoRenderContextSetFunc = mWorld->GetRenderSystem<SkyAtmosphereSystemR>()->GetSkyAtmoRenderContextFunc();
    PostProcessUtil(
        [&](REDPass* pass) {
            // auto& context = pass->GetContext();
            // UpdateContext(context, mWorld);
            // auto useDisk = GetRenderPipelineType() != ViewType::ReflectionProbe;
            auto skyAtmoSys = mWorld->GetRenderSystem<SkyAtmosphereSystemR>();
            skyAtmoSys->SetRenderCameraCaptureViewContext(pass, mRenderCamera);
            auto useVSM = GetRenderPipelineType() != ViewType::ReflectionProbe && GetRenderPipelineType() != ViewType::PrefabProxy;
            // skyAtmoRenderContextSetFunc(pass, false, false, useDisk, useVSM);
            pass->SetProperty(NAME_ID("_ViewDepthTexture"), sceneDepthView);
            if (useVSM)
            {
                UpdateShadowContext(pass, &mShadowProperties);
            }
        },
        mPostProcessMtl,
        mRED,
        "ueatmo",
        false,
        color);
}

void FFSRenderPipeline::AssembleCloudPass(REDTextureView*& depth, CLOUD_PIPE_METHOD method, UInt2 overrideScreenSize)
{
    mCloudRes.Reset();
    auto Entities = mWorld->Query<CloudComponentR>();
    if (Entities.GetEntityNum() < 1)
    {
        return;
    }
    auto* cloudSys = TYPE_CAST(CloudSystemR*, mWorld->GetRenderSystem<CloudSystemR>());

    if (!cloudSys->EnableVolumetricCloud())
    {
        return;
    }

    auto* gameView = mTargetView;
    auto [gameViewWidth, gameViewHeight] = overrideScreenSize;
    if (gameView && (gameViewWidth == 0 || gameViewHeight == 0))
    {
        gameViewWidth = gameView->mTexture->mDesc.Width;
        gameViewHeight = gameView->mTexture->mDesc.Height;
    }

    mSkyCloud.mWorld = mWorld;
    mSkyCloud.mRED = mRED;
    mSkyCloud.mPostMtl = mPostProcessMtl;
    mSkyCloud.Initialize(GetSetting()->mCloudSetting);
    mSkyCloud.SetScreenSize(gameViewWidth, gameViewHeight);
    mSkyCloud.ExecuteImp(mGameContext, depth, mCloudRes, method);
}

// Need fine tuning to work well
void FFSRenderPipeline::AssembleSSPR(REDTextureView*& sceneColorView, REDTextureView*& sceneDepthStencilView)
{
    auto* gameView = mTargetView;
    auto gameViewWidth = gameView->mTexture->mDesc.Width;
    auto gameViewHeight = gameView->mTexture->mDesc.Height;

    mScreenSpacePlaneReflection.mWidth = gameViewWidth;
    mScreenSpacePlaneReflection.mHeight = gameViewHeight;
    mScreenSpacePlaneReflection.mPostMat = mPostProcessMtl;
    mScreenSpacePlaneReflection.mCamera = GetRenderCamera();
    auto ssprSetting = GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.mSSPRSetting;
    mScreenSpacePlaneReflection.Initialize(ssprSetting);
    mScreenSpacePlaneReflection.Execute(mGameContext, sceneColorView, sceneDepthStencilView, mSSPRView);
}

// Ref. [SIGGRAPH 2015]Stochastic Screen Space Reflection
void FFSRenderPipeline::AssembleSSR(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* sceneViewLastFrame, REDTextureView* sceneDepthStencilView, REDTextureView* depthPyramid)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    mSSR.mPostMtl = mPostProcessMtl;
    mSSR.mBlackTexture = rendererSystem->GetRenderPrimitives()->mDefaultBlackTexture2DView.get();
    mSSR.mBlackCubeTexture = rendererSystem->GetRenderPrimitives()->mDefaultBlackTextureCubeView.get();
    mSSR.mCamera = GetRenderCamera();
    mSSR.Initialize(GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.mSSRSetting);
    mSSR.Execute(mGameContext, gBufferViews, sceneViewLastFrame, sceneDepthStencilView, depthPyramid, mSSRView);
}

void FFSRenderPipeline::AssembleBuildTileCulling()
{
    auto* numCullBuffer = mRED->AllocateBuffer("GridCullNumBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer});
    mNumCulledLightsGrid = mRED->AllocateBufferView(numCullBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});
    auto* cullDataBuffer = mRED->AllocateBuffer("GridCullDataBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer});
    mCulledLightDataGrid = mRED->AllocateBufferView(cullDataBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});

    mBuildTileCulling.mWidth = static_cast<UInt16>(mTargetView->mTexture->mDesc.Width);
    mBuildTileCulling.mHeight = static_cast<UInt16>(mTargetView->mTexture->mDesc.Height);
    mBuildTileCulling.mCamera = GetRenderCamera();

    // Reflection Probe Info
    auto* reflectionProbeSystem = TYPE_CAST(ReflectionProbeSystemR*, mWorld->GetRenderSystem<ReflectionProbeSystemR>());
    auto* transformSystem = TYPE_CAST(TransformSystemR*, mWorld->GetRenderSystem<TransformSystemR>());
    const UInt32 MAX_RP = MAX_PIPELINE_REFLECTION_PROBE_NUM;
    auto& reflectionProbes = GetRefleProbeList();
    memset(mBuildTileCulling.mReflProbePosRadius.data(), 0, sizeof(mBuildTileCulling.mReflProbePosRadius));
#if defined(CE_USE_DOUBLE_TRANSFORM)
    memset(mBuildTileCulling.mReflProbeTilePos.data(), 0, sizeof(mBuildTileCulling.mReflProbeTilePos));
#endif
    for (int i = 0; i < reflectionProbes.size() && i < MAX_RP; i++)
    {
        auto& rp = reflectionProbes.at(i);
        auto rpComponent = mWorld->GetComponent<ReflectionProbeCameraComponentR>(rp);
        auto transformComp = mWorld->GetComponent<TransformComponentR>(rp);
        auto& translation = transformSystem->GetWorldTranslation(transformComp.Read());
        float range;
        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(rpComponent.Read()) == ReflectionProbeShapeType::Sphere)
        {
            range = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(rpComponent.Read());
        }
        else
        {
            Float3 extent = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(rpComponent.Read()) / 2.f;
            float maxD = extent.x > extent.y ? extent.x : extent.y;
            range = maxD > extent.z ? maxD : extent.z;
        }
        mBuildTileCulling.mReflProbePosRadius[i] = Float4(translation.x, translation.y, translation.z, range);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        mBuildTileCulling.mReflProbeTilePos[i] = Float4(transformSystem->GetTilePosition(mWorld->GetComponent<TilePositionComponentR>(rp).Read()), 0.f);
#endif
    }
    UInt32 rpSize = static_cast<UInt32>(reflectionProbes.size());
    mBuildTileCulling.mReflectionProbeNum = rpSize > MAX_RP ? MAX_RP : rpSize;

    mBuildTileCulling.mEnableBuildLocalLights = true;
    mBuildTileCulling.mUseLightBound = GetSetting()->UseLightBoundCulling;
    mBuildTileCulling.mUseLink = GetSetting()->LinkMode;
    if (mBuildTileCulling.mEnableBuildLocalLights)
    {
        auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* lightSystem = TYPE_CAST(const LightSystemR*, mWorld->GetRenderSystem<LightSystemR>());
        auto camTile = GetRenderCamera()->GetTilePosition();
        UInt32 dirLightsNum = 0;
        mBuildTileCulling.mDirectionalLightList.clear();

        auto lightIndices = GetLightIndexList();
        UInt32 lightSize = static_cast<UInt32>(lightIndices.size());
        std::vector<Float4> lightPosAndRadiusData(lightSize);
        std::vector<Float4> lightDirAndTanOuterAngleData(lightSize);
        mBuildTileCulling.mLightsNum = lightSize;
        for (UInt32 i = 0; i < lightSize; i++)
        {
            auto lightIdx = lightIndices[i];
            auto lightEntity = lightSystem->GetLightEntityID(lightIdx);
            auto lightTrans = lightSystem->GetLightTransform(lightIdx);
            switch (lightSystem->GetLightType(lightIdx))
            {
            case LightType::Directional:
            {
                lightPosAndRadiusData[i] = Float4(0.f, 0.f, 0.f, -1.f);
                mBuildTileCulling.mDirectionalLightList.push_back({lightEntity, i});
                dirLightsNum++;
                break;
            }
            case LightType::Rect:
            case LightType::Point:
            {
                auto range = lightSystem->GetLightRange(lightIdx);
                auto translation = lightTrans.translate;
#if defined(CE_USE_DOUBLE_TRANSFORM)
                translation += (lightTrans.tilePos - camTile) * LENGTH_PER_TILE_F;
#endif
                lightPosAndRadiusData[i] = Float4(translation.x, translation.y, translation.z, range);
                break;
            }
            case LightType::Spot:
            {
                auto range = lightSystem->GetLightRange(lightIdx);

                auto spotAngle = std::acos(lightSystem->GetLightOuterCosAngle(lightIdx));
                auto spotTanAngle = std::tan(spotAngle);
                auto spotDir = lightTrans.rotation.Float4Rotate(Float4(0.f, 0.f, 1.f, 0.f));
                auto translation = lightTrans.translate;
#if defined(CE_USE_DOUBLE_TRANSFORM)
                translation += (lightTrans.tilePos - camTile) * LENGTH_PER_TILE_F;
#endif
                lightPosAndRadiusData[i] = Float4(translation.x, translation.y, translation.z, range);
                lightDirAndTanOuterAngleData[i] = Float4(spotDir.x, spotDir.y, spotDir.z, spotTanAngle);
                break;
            }
            default:
                break;
            }
            mBuildTileCulling.mLocalLightsNum = mBuildTileCulling.mLightsNum - dirLightsNum;
        }   // Light loop
        SizeType dataByteSize = std::max<SizeType>(1u, sizeof(Float4) * lightSize);
        {
            auto* scratchBuffer = rendererSys->GetScratchBuffer();
            auto dataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataByteSize);
            if (lightSize)
            {
                dataBufferWrap.MemWrite(0, lightPosAndRadiusData.data(), dataByteSize);
            }

            mBuildTileCulling.mLightPosAndRadiusBufferView = rendererSys->GetTransientResourceManager()->AllocateBufferView(
                NGIBufferViewDesc{
                    NGIBufferUsage::StructuredBuffer,
                    dataBufferWrap.GetNGIOffset(),
                    dataByteSize,
                    GraphicsFormat::Unknown,
                    sizeof(Float4),
                },
                dataBufferWrap.GetNGIBuffer());
        }
        {
            auto* scratchBuffer = rendererSys->GetScratchBuffer();
            auto dataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataByteSize);
            if (lightSize)
            {
                dataBufferWrap.MemWrite(0, lightDirAndTanOuterAngleData.data(), dataByteSize);
            }

            mBuildTileCulling.mLightDirAndTanOuterAngleBufferView = rendererSys->GetTransientResourceManager()->AllocateBufferView(
                NGIBufferViewDesc{
                    NGIBufferUsage::StructuredBuffer,
                    dataBufferWrap.GetNGIOffset(),
                    dataByteSize,
                    GraphicsFormat::Unknown,
                    sizeof(Float4),
                },
                dataBufferWrap.GetNGIBuffer());
        }
    }

    mBuildTileCulling.Initialize(GetSetting()->mBuildTileCullingSettings);
    mBuildTileCulling.Execute(mGameContext, mNumCulledLightsGrid, mCulledLightDataGrid);
}

void FFSRenderPipeline::AssembleReflectionIndirect(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView*& reflecIndirectViewOut, REDTextureView* sceneDepthStencilView)
{
    bool bEnableReflectionIndirect = GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.enable;
    if (!bEnableReflectionIndirect)
    {
        // Clear reflection indirect related textures
        mSSRView = mSSPRView = mReflectionIndirectView = nullptr;
        return;
    }
    auto reflecType = GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.ReflectionType;
    // Using last frame scene color is reasonable, since this frame's final scene color is not ready
    switch (reflecType)
    {
    case ScreenReflectionType::SSPR:
        AssembleSSPR(GetBuiltInTexture<PassSemanticName::LastFrameSceneColorView>(), GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>());
        mSSRView = nullptr;
        break;
    case ScreenReflectionType::SSR:
        AssembleSSR(gBufferViews, GetBuiltInTexture<PassSemanticName::LastFrameSceneColorView>(), GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(), mGPassDepthPyramidLastFrameView);
        mSSPRView = nullptr;
        break;
    default:
        mSSRView = mSSPRView = nullptr;
        break;
    }
    // Reflection probe can be turned off by delete component, skylight can be turned off by disable/delete component
    mReflectionIndirect.mlightGridSizeX = mBuildTileCulling.mlightGridSizeX;
    mReflectionIndirect.mlightGridSizeY = mBuildTileCulling.mlightGridSizeY;
    mReflectionIndirect.mlightGridSizeZ = mBuildTileCulling.mlightGridSizeZ;
    mReflectionIndirect.mBOS = mBuildTileCulling.mBOS;
    mReflectionIndirect.mLightGridPixelSizeShift = mBuildTileCulling.mLightGridPixelSizeShift;
    auto& reflectionProbes = GetRefleProbeList();
    mReflectionIndirect.mReflectionProbeNum = mBuildTileCulling.mReflectionProbeNum;
    mReflectionIndirect.mRefleProbeList = &reflectionProbes;
    mReflectionIndirect.mIsTileCulling = GetSetting()->mBuildTileCullingSettings.enable;
    mReflectionIndirect.mWorld = mWorld;
    mReflectionIndirect.mPostMat = GetSetting()->PostProcessMtlR;

    mReflectionIndirect.Initialize(GetSetting()->mIndirectLightingCompositeSettings.mReflectionIndirectSetting);
    // ReflectionIndirect use aoView/gtaoBentNormal for specularOcclusion(Will cause artifacts, should remove them)
    mReflectionIndirect.Execute(mGameContext, gBufferViews, reflecIndirectViewOut, mSSRView, mNumCulledLightsGrid, mCulledLightDataGrid, sceneDepthStencilView, mSSPRView);
}

REDTextureView* FFSRenderPipeline::AllocateColorViewFromLastFrame(UInt32 viewWidth, UInt32 viewHeight)
{
    REDTextureView* colorView{};
    const NGITextureViewDesc viewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst,
                                      GraphicsFormat::R16G16B16A16_SFloat,
                                      NGITextureType::Texture2D,
                                      {
                                          NGITextureAspect::Color,
                                          0U,
                                          1U,
                                          0U,
                                          1U,
                                      }};
    NGITextureDesc desc{
        GraphicsFormat::R16G16B16A16_SFloat,
        NGITextureType::Texture2D,
        1,
        1,
        viewWidth,
        viewHeight,
        1,
        1,
        NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst,
    };
    if (!mRED->Validate(mSceneColorTex) || mSceneColorTex->mDesc.Width != viewWidth || mSceneColorTex->mDesc.Height != viewHeight)
    {
        if (!mRED->Validate(mSceneColorTex))
        {
            auto* sceneColorTex = mRED->AllocateTexture("SceneColorPreserved Texture", desc);
            colorView = mRED->AllocateTextureView(sceneColorTex, viewDesc);
            auto* pass = mRED->AllocatePass("Clear Color Texture");
            NGIClearValue clearValue{{0, 0, 0, 0}};
            pass->ClearTexture(colorView, clearValue);
        }
        else
        {
            // blit
            REDTextureView* oldView = mRED->AllocateTextureView(mSceneColorTex, viewDesc);
            REDTextureView* tempLastView = CreateTextureView2D("Temp Blit LastFrame Texture", viewWidth, viewHeight, desc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);
            RenderContext blitCtx(mRED->GetREDFrameAllocator());
            blitCtx.SetProperty(NAME_ID("_ColorTex"), oldView);
            std::vector<REDTextureView*> blitViews = {tempLastView};
            PostProcess(mRED, blitViews, mPostProcessMtl, "blit", std::move(blitCtx));

            auto* sceneColorTex = mRED->AllocateTexture("SceneColorPreserved Texture", desc);
            colorView = mRED->AllocateTextureView(sceneColorTex, viewDesc);
            RenderContext blit2Ctx(mRED->GetREDFrameAllocator());
            blit2Ctx.SetProperty(NAME_ID("_ColorTex"), tempLastView);
            std::vector<REDTextureView*> blit2Views = {colorView};
            PostProcess(mRED, blit2Views, mPostProcessMtl, "blit", std::move(blit2Ctx));
        }
    }
    else
    {
        colorView = mRED->AllocateTextureView(mSceneColorTex, viewDesc);
    }
    mSceneColorTex = mRED->AllocateTexture("SceneColorPreserved Texture", desc);
    mSceneColorTex->ExtendLifetime();

    return colorView;
}

void FFSRenderPipeline::PrepareRenderData()
{
    IRenderPipeline::PrepareRenderData();

    if ((GetSetting()->mFSR2Setting.enable && GetSetting()->mFSR2Setting.MiscFactors.DebugClearJitter) || (GetSetting()->viewModeVisualizeType == ViewModeVisualizeType::MotionVector))
    {
        mJitterData.ClearJitter();
    }
}

GPass& FFSRenderPipeline::GetGPass()
{
    return mGPass;
}


}   // namespace cross
