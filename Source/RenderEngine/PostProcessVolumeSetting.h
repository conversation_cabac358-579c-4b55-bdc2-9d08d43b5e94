#pragma once

#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossBase/CEMetaMacros.h"
#include "RenderEngine/Texture/GPUTexture.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngineForward.h"
#include "RenderEngine/RenderPipeline/Effects/CombinedLUTTonemapper.h"
#include "RenderEngine/RenderPipeline/Effects/HistogramExposure.h"
#include "RenderEngine/RenderPipeline/Effects/LensEffect.h"
#include "RenderEngine/RenderPipeline/Effects/KinoBloom.h"
#include "RenderEngine/RenderPipeline/Effects/ScreenSpaceFog.h"


#include "RenderEngine/RenderPipeline/Effects/VolumetricFog.h"
#include "RenderEngine/RenderPipeline/Effects/ScreenSpacePlaneReflection.h"
#include "RenderEngine/RenderPipeline/Effects/ScreenSpaceReflection.h"
#include "RenderEngine/RenderPipeline/Effects/DepthOfField.h"
#include "RenderEngine/RenderPipeline/Effects/BuildTileCulling.h"
#include "RenderEngine/RenderPipeline/Effects/ReflectionIndirect.h"
#include "RenderEngine/RenderPipeline/Effects/MotionBlur.h"
#include "RenderEngine/RenderPipeline/Effects/SmartGIPass.h"
#include "RenderEngine/RenderPipeline/Effects/ScreenBlur.h"
#include "RenderEngine/RenderPipeline/Effects/WeatherEffects.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/PostProcessVolumeSetting.generated.h"
namespace cross {

struct CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API PostProcessChromaticAberrationSetting
{
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enalbe len effect"))
    bool enable{false};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Intensity of chromatic aberration effect", bKeyFrame = true))
    float Intensity{0.1f};
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Sample range", bKeyFrame = true))
    float Range{1.f};

    CE_Serialize_Deserialize;
};

struct CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API PostProcessVignetteSetting
{
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enalbe chromatic aberration")) bool enable{false};    
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Intensity of vignette", ValueMin = "0.0", ValueMax = "1.0", bKeyFrame = true))
    float VignetteIntensity{0.4f};
    CE_Serialize_Deserialize;
};

struct CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API PostProcessBlendSetting
{
    CEMeta(Serialize, Editor, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Blend Priority"))
    CEProperty(ScriptReadWrite)
    int32_t Priority{0};

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Weight should be set in range 0..1"))
    CEProperty(ScriptReadWrite)
    float Weight{1.f};

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    CEProperty(ScriptReadWrite)
    float Radius{100.f};

    CE_Serialize_Deserialize;
};

struct CEMeta(Editor, Reflect, Puerts) RENDER_ENGINE_API PostProcessVolumeSetting
{
    CEGeneratedCode(PostProcessVolumeSetting)
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Global-  all RenderPipelines; Local-influence RenderPipelines with camera inside volume bounder"))
    PostProcessVolumeType mType{PostProcessVolumeType::Global};

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of PPV blending"))

    PostProcessBlendSetting BlendSettings;

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of len effect"))

    PostProcessChromaticAberrationSetting ChromaticAberration;

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of len effect"))

    PostProcessVignetteSetting Vignette;

    //CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of SSPR effect")) ScreenSpacePlaneReflectionSetting SSPlaneReflection;

    //CEMeta(Serialize, Editor, Script, Reflect, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of SSR effect")) ScreenSpaceReflectionSetting SSR;

    //CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Settings of SmartGI effect")) SmartGIPostProcessSetting GISetting;

    CE_Serialize_Deserialize;
};

enum struct CEMeta(Editor, Reflect) PostProcessOverrideState : UInt32
{
    None = 0u,
    ManualExposure      = 1u,
    Exposure            = ManualExposure << 1u,
    LocalExposure       = Exposure << 1u,
    Bloom               = LocalExposure << 1u,
    ChromaticAberration = Bloom << 1u,
    Vignette            = ChromaticAberration << 1u,
    TonemapSetting      = Vignette << 1u,
    LensFlare           = TonemapSetting << 1u,
    //SSPlaneReflection   = LensFlare << 1u,
    //SSR                 = SSPlaneReflection << 1u,
    DOF                 = LensFlare << 1u,
    MotionBlur          = DOF << 1u,
    ScreenBlur          = MotionBlur << 1u,
    Rain                = ScreenBlur << 1u,
    Wind                = Rain << 1u,
    Snow                = Wind << 1u,
    All                 = 0xFFFFFFFF
};
ENUM_CLASS_FLAGS(PostProcessOverrideState)

struct RENDER_ENGINE_API PostProcessGameOverride : public PostProcessVolumeSetting
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "", bHide = true))
    PostProcessOverrideState OverrideState = PostProcessOverrideState::None;
};

struct PostProcessVolumeTrans
{
    Float4x4 BoundingToWorld;
    Float3 Extend;
    Float3 TilePos;
};

struct CEMeta(Editor) RENDER_ENGINE_API PostProcessVolumeSettingR
{
    PostProcessVolumeSetting BaseSettings;
    FogSetting FogSettings;
    PostProcessVolumeTrans BoundingTrans;
    GPUTexture* lutGpuTex{nullptr};
    GPUTexture* vignetteGpuTex{nullptr};
    bool EnableForEditorEyeIcon{true};
    bool isSelected{false};
    BoundingOrientedBox obb;
    PrimitiveData framePrimitive;
};
}   // namespace cross