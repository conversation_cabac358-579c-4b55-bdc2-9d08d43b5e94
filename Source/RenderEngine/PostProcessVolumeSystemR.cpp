#include "EnginePrefix.h"
#include "RenderEngine/ReflectionProbeSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/SettingsManager.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"

#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/PostProcessVolumeSystemR.h"
#include "RenderEngine/FogSystemR.h"
#include "RenderEngine/RenderEngine.h"

namespace cross {

ecs::ComponentDesc* PostProcessVolumeComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::PostProcessVolumeComponentR>(false);
}

void PostProcessVolumeSystemR::SetBoundingWireFrameShow(ecs::EntityID entity, bool isShow)
{
    auto compW = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity).Write();
    if (isShow)
    {
        auto it = mShowingBoundingBoxWireFramePrimitives.find(entity);
        if (it == mShowingBoundingBoxWireFramePrimitives.end())
        {
            mShowingBoundingBoxWireFramePrimitives.emplace(std::pair<ecs::EntityID, PrimitiveData>(entity, {}));
        }
    }
    else
    {
        mShowingBoundingBoxWireFramePrimitives.erase(entity);
    }
}

void PostProcessVolumeSystemR::SetPostProcessVolumeBounding(ecs::EntityID entity, const Float4x4& boundingToWorld, const Float3& extend, const Float3& tilePos)
{
    auto comp = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity);
    auto ppvCompH = comp.Write();

    ppvCompH->mSetting.BoundingTrans.BoundingToWorld = boundingToWorld;
    ppvCompH->mSetting.BoundingTrans.Extend = extend;
    ppvCompH->mSetting.BoundingTrans.TilePos = tilePos;
    if (ppvCompH->mSetting.EnableForEditorEyeIcon && ppvCompH->mSetting.BaseSettings.mType != PostProcessVolumeType::Global)
    {
        GenerateObb(entity);
    }
}

void PostProcessVolumeSystemR::GenerateObb(ecs::EntityID entity)
{
    auto comp = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity);
    auto ppvCompH = comp.Write();
    if (!ppvCompH->mSetting.EnableForEditorEyeIcon || ppvCompH->mSetting.BaseSettings.mType == PostProcessVolumeType::Global)
        return;
    Float4x4 AbsoluteMatrix = ppvCompH->mSetting.BoundingTrans.BoundingToWorld;

#ifdef CE_USE_DOUBLE_TRANSFORM
    AbsoluteMatrix = static_cast<Float4x4>(RelativeMatrixToAbsoluteMatrix(ppvCompH->mSetting.BoundingTrans.TilePos, AbsoluteMatrix));
#endif
    BoundingOrientedBox obb;
    obb.Transform(ppvCompH->mSetting.obb, AbsoluteMatrix);
}

void PostProcessVolumeSystemR::DrawBoundingBoxWireFrame()
{
    auto primitiveSystem = mRenderWorld->GetRenderSystem<PrimitiveRenderSystemR>();

    BoundingOrientedBox obb = {};
    for (auto& [ppvEntity, primitive] : mShowingBoundingBoxWireFramePrimitives)
    {
        if (!mRenderWorld->IsEntityAlive(ppvEntity))
        {
            continue;
        }

        auto ppvCompH = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(ppvEntity).Write();
        if (!ppvCompH->mSetting.EnableForEditorEyeIcon || ppvCompH->mSetting.BaseSettings.mType == PostProcessVolumeType::Global)
            continue;

        UInt32 frameColor = 0x00FF0000;
        if (ppvCompH->mSetting.isSelected)
            frameColor = 0x0000FF00;

        Float4x4 AbsoluteMatrix = ppvCompH->mSetting.BoundingTrans.BoundingToWorld;
#ifdef CE_USE_DOUBLE_TRANSFORM
        AbsoluteMatrix = static_cast<Float4x4>(RelativeMatrixToAbsoluteMatrix(ppvCompH->mSetting.BoundingTrans.TilePos, AbsoluteMatrix));
#endif

        PrimitiveGenerator::GenerateOrientedBoxFrame(&primitive, obb, &AbsoluteMatrix, frameColor);
        primitiveSystem->BatchPrimitive(&primitive, primitiveSystem->GetPresetMaterial(true, false, false), false);
    }
}

#define LINEAR_LERP_PP(NAME) ppvLerp.NAME = lerp(ppvLerp.NAME, p.first->BaseSettings.NAME, weight)

#define SQUARE_LERP_PP(NAME) ppvLerp.NAME = lerp(ppvLerp.NAME, p.first->BaseSettings.NAME, weight * weight)

std::shared_ptr<PostProcessVolumeSettingR> PostProcessVolumeSystemR::BlendPostProcessVolumeSettings(std::vector<std::pair<PostProcessVolumeSettingR*, float>>& blendList, PostProcessVolumeSettingR* lead)
{
    // params which won't be blended should be set the same with leader ppv
    std::shared_ptr<PostProcessVolumeSettingR> ret = std::make_shared<PostProcessVolumeSettingR>(*lead);
    auto& ppvLerp = ret->BaseSettings;

    auto lerp = [](float a, float b, float w) { return a * (1.f - w) + b * w; };

    // initialize parameters which will be blended
    ppvLerp.mPostProcessExposureSetting.mHistogramExposureSettings.AutoExposureBias = 1.f;
    ppvLerp.mPostProcessExposureSetting.mManualExposureSettings.Exposure = 1.f;
    ppvLerp.mPostProcessLocalExposureSetting.HighlightContrast = 1.f;
    ppvLerp.mPostProcessLocalExposureSetting.ShadowContrast = 1.f;
    ppvLerp.mPostProcessLocalExposureSetting.HighlightThreshold = 0.f;
    ppvLerp.mPostProcessLocalExposureSetting.ShadowThreshold = 0.f;
    ppvLerp.mPostProcessLocalExposureSetting.DetailStrength = 1.f;
    ppvLerp.mPostProcessLocalExposureSetting.BlurredLuminanceBlend = 0.6f;
    ppvLerp.mPostProcessLocalExposureSetting.MiddleGreyBias = 0.f;

    for (auto& p : blendList)
    {
        float weight = std::min(std::max(p.second, 0.f), 1.f);

        const auto& settings = p.first->BaseSettings;

        if (settings.mType != PostProcessVolumeType::LocalLerpOnly || settings.mPostProcessExposureSetting.mManualExposureSettings.enable)
        {
            LINEAR_LERP_PP(mPostProcessExposureSetting.mManualExposureSettings.Exposure);
        }
        if (settings.mType != PostProcessVolumeType::LocalLerpOnly || settings.mPostProcessExposureSetting.mHistogramExposureSettings.enable)
        {
            LINEAR_LERP_PP(mPostProcessExposureSetting.mHistogramExposureSettings.AutoExposureBias);
        }
        if (settings.mType != PostProcessVolumeType::LocalLerpOnly || settings.mPostProcessLocalExposureSetting.enable)
        {
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.HighlightContrast);
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.ShadowContrast);
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.HighlightThreshold);
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.ShadowThreshold);
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.DetailStrength);
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.BlurredLuminanceBlend);
            LINEAR_LERP_PP(mPostProcessLocalExposureSetting.MiddleGreyBias);
        }
    }

    return ret;
}

PostProcessVolumeSystemR::PostProcessVolumeSystemR()
{
    defaultPostProcessVolumeSetting.FogSettings.enable = false;
    defaultPostProcessVolumeSetting.FogSettings.VFog.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessExposureSetting.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessBloomSetting.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.ChromaticAberration.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.Vignette.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mTonemapSettings.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessLensFlareSetting.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessLocalExposureSetting.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessWindSetting.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessSnowSetting.enable = false;
    defaultPostProcessVolumeSetting.BaseSettings.mPostProcessRainSetting.enable = false;
}

#define SET_GAME_OVERRIDE_SETTING(SETTING, FIELD)                                                                                                                                                                                                     \
    if ((state & PostProcessOverrideState::SETTING) != PostProcessOverrideState::None)                                                                                                                                                         \
        mGameOverrideSettings.FIELD = ppSetting.FIELD;

void PostProcessVolumeSystemR::SetGameOverrideSetting(const PostProcessGameOverride& ppSetting, PostProcessOverrideState overrideState)
{
    if (overrideState == PostProcessOverrideState::None)
    {
        return;
    }

    auto& state = overrideState;
    SET_GAME_OVERRIDE_SETTING(Exposure, mPostProcessExposureSetting)
    SET_GAME_OVERRIDE_SETTING(LocalExposure, mPostProcessLocalExposureSetting)
    SET_GAME_OVERRIDE_SETTING(Bloom, mPostProcessBloomSetting)
    SET_GAME_OVERRIDE_SETTING(ChromaticAberration, ChromaticAberration)
    SET_GAME_OVERRIDE_SETTING(Vignette, Vignette)
    SET_GAME_OVERRIDE_SETTING(TonemapSetting, mTonemapSettings)
    SET_GAME_OVERRIDE_SETTING(LensFlare, mPostProcessLensFlareSetting)
    //SET_GAME_OVERRIDE_SETTING(SSPlaneReflection, SSPlaneReflection)
    //SET_GAME_OVERRIDE_SETTING(SSR, SSR)
    SET_GAME_OVERRIDE_SETTING(DOF, mDepthOfFieldSetting)
    SET_GAME_OVERRIDE_SETTING(MotionBlur, mMotionBlurSetting)
    SET_GAME_OVERRIDE_SETTING(ScreenBlur, mScreenBlurSetting)
    SET_GAME_OVERRIDE_SETTING(Rain, mPostProcessRainSetting)
    SET_GAME_OVERRIDE_SETTING(Wind, mPostProcessWindSetting)
    SET_GAME_OVERRIDE_SETTING(Snow, mPostProcessSnowSetting)

    mGameOverrideSettings.OverrideState |= overrideState;
}

void PostProcessVolumeSystemR::ClearGameOverrideSetting(PostProcessOverrideState clearState)
{
    mGameOverrideSettings.OverrideState = (mGameOverrideSettings.OverrideState | clearState) ^ clearState;
}

void PostProcessVolumeSystemR::SetEnableGameOverride(bool bEnable)
{
    bGameOverride = bEnable;
}

#define PPV_GAME_OVERRIDE_SETTING(SETTING, FIELD)                                                                                                                                                                                                     \
    if ((state & PostProcessOverrideState::SETTING) != PostProcessOverrideState::None)                                                                                                                                                         \
        ppv->BaseSettings.FIELD = mGameOverrideSettings.FIELD;

void PostProcessVolumeSystemR::PerformGameOverrideSetting(PostProcessVolumeSettingR* ppv)
{
    SCOPED_CPU_TIMING(GroupRendering, "PpvUpdate_OverrideState");
    auto& state = mGameOverrideSettings.OverrideState;
    /*if ((state & PostProcessOverrideState::Fog) != PostProcessOverrideState::None)
    {
        LOG_INFO("FFS::Visibility PostProcessVolumeSystemR::PerformGameOverrideSetting");
        LOG_INFO("fogEnable: {}", mGameOverrideSettings.Fog.enable);
        LOG_INFO("sFogEnable: {}", mGameOverrideSettings.Fog.SFog.enable);
        LOG_INFO("vFogEnable: {}", mGameOverrideSettings.Fog.VFog.enable);
        LOG_INFO("ffsTempEnable: {}", mGameOverrideSettings.Fog.ffsTempTest.enable);
        LOG_INFO("distributionType: {}", mGameOverrideSettings.Fog.ffsTempTest.FogDistribution);
        LOG_INFO("RVR: {}", mGameOverrideSettings.Fog.ffsTempTest.RVR);
        LOG_INFO("rvrMin: {}", mGameOverrideSettings.Fog.ffsTempTest.RVR_Min);
        LOG_INFO("rvrMax: {}", mGameOverrideSettings.Fog.ffsTempTest.RVR_Max);
        LOG_INFO("baseHeight: {}", mGameOverrideSettings.Fog.ffsTempTest.BaseHeight);
    }*/
    PPV_GAME_OVERRIDE_SETTING(Exposure, mPostProcessExposureSetting)
    PPV_GAME_OVERRIDE_SETTING(LocalExposure, mPostProcessLocalExposureSetting)
    PPV_GAME_OVERRIDE_SETTING(Bloom, mPostProcessBloomSetting)
    PPV_GAME_OVERRIDE_SETTING(ChromaticAberration, ChromaticAberration)
    PPV_GAME_OVERRIDE_SETTING(Vignette, Vignette)
    PPV_GAME_OVERRIDE_SETTING(TonemapSetting, mTonemapSettings)
    PPV_GAME_OVERRIDE_SETTING(LensFlare, mPostProcessLensFlareSetting)
    //PPV_GAME_OVERRIDE_SETTING(SSPlaneReflection, SSPlaneReflection)
    //PPV_GAME_OVERRIDE_SETTING(SSR, SSR)
    PPV_GAME_OVERRIDE_SETTING(DOF, mDepthOfFieldSetting)
    PPV_GAME_OVERRIDE_SETTING(MotionBlur, mMotionBlurSetting)
    PPV_GAME_OVERRIDE_SETTING(ScreenBlur, mScreenBlurSetting)
    PPV_GAME_OVERRIDE_SETTING(Rain, mPostProcessRainSetting)
    PPV_GAME_OVERRIDE_SETTING(Wind, mPostProcessWindSetting)
    PPV_GAME_OVERRIDE_SETTING(Snow, mPostProcessSnowSetting)
}

int32_t PostProcessVolumeSystemR::IsEnabledVolumeInThisPipeline(const PostProcessVolumeHandle& ppv, const IRenderPipeline& rp) const
{
    if (ppv.Read()->mSetting.EnableForEditorEyeIcon == false)
        return -1;
    if (ppv.Read()->mSetting.BaseSettings.mType == PostProcessVolumeType::Global)
    {
        return ppv.Read()->mSetting.BaseSettings.BlendSettings.Priority;
    }
    else
    {
        auto camera = rp.GetRenderCamera();
        if (camera == nullptr)
            return -1;
        auto camPos = camera->GetCameraOrigin();
#if defined(CE_USE_DOUBLE_TRANSFORM)
        camPos += (camera->GetTilePosition() - ppv.Read()->mSetting.BoundingTrans.TilePos) * LENGTH_PER_TILE;
#endif
        if (!ppv.Read()->mSetting.obb.Contains(camPos))
            return -1;
        else
        {
            return ppv.Read()->mSetting.BaseSettings.BlendSettings.Priority;
        }
    }
}

float PostProcessVolumeSystemR::GetVolumeWeight(const PostProcessVolumeHandle& ppv, const ecs::ComponentHandle<TransformComponentR>& trans, const IRenderPipeline& rp)
{
    if (ppv.Read()->mSetting.BaseSettings.mType == PostProcessVolumeType::Global)
        return 1.f;
    auto camera = rp.GetRenderCamera();
    if (camera == nullptr)
        return 0.f;
    auto camPos = camera->GetCameraOrigin();
#if defined(CE_USE_DOUBLE_TRANSFORM)
    camPos += (camera->GetTilePosition() - ppv.Read()->mSetting.BoundingTrans.TilePos) * LENGTH_PER_TILE;
#endif
    if (ppv.Read()->mSetting.obb.Contains(camPos))
        return 1.f;
    Float3 ext = ppv.Read()->mSetting.BoundingTrans.Extend;
    Float4 relCamPosf4 = Float4(camPos, 1.f) * ppv.Read()->mSetting.BoundingTrans.BoundingToWorld.Inverted();
    Float3 relCamPos = Float3(std::abs(relCamPosf4.x) * ext.x, std::abs(relCamPosf4.y) * ext.y, std::abs(relCamPosf4.z) * ext.z);
    Float3 relCorner = relCamPos - ext;
    float distSqr = 0.f;
    if (relCorner.x > 0.f)
        distSqr += relCorner.x * relCorner.x;
    if (relCorner.y > 0.f)
        distSqr += relCorner.y * relCorner.y;
    if (relCorner.z > 0.f)
        distSqr += relCorner.z * relCorner.z;
    if (distSqr < 1.f)
        return 1.f;
    float dist = std::sqrt(distSqr);

    float weight = std::max(0.f, 1.f - dist / std::max(ppv.Read()->mSetting.BaseSettings.BlendSettings.Radius, 1.f));

    return weight;
}

void PostProcessVolumeSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "PostProcessVolumeRUpdate");
        auto* pipelineSystem = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>();
        const auto& rps = pipelineSystem->GetAllRenderPipelines();

        auto ppvEntities = mRenderWorld->Query<PostProcessVolumeComponentR, TransformComponentR>();
        ppvSettings.clear();

        const ColorRGBAf color = {.6f, 1.f, .6f, 1.f};
        const float width = 18.0f;
        float start = 40.0f;

        FogSetting fogSetting{};
        auto fogEntities = mRenderWorld->Query<FogComponentR>();
        for (auto fogH : fogEntities)
        {
            fogSetting = fogH.Read()->mSetting;
            // use first FogComponentR 
            break;
        }

        // for all Camera pipelines:
        for (auto i = rps.lower_bound(ViewType::PrefabProxy), Last = rps.upper_bound(ViewType::ScenePreview); i != Last; i++)
        {
            if (i->first < ViewType::PrefabProxy)
                continue;
            auto* renderPipeline = (i->second).get();
            int32_t resultPriority = -1;
            for (auto [ppvH, ppvTransH] : ppvEntities)
            {
                int32_t tempPriority = IsEnabledVolumeInThisPipeline(ppvH, *renderPipeline);
                if (tempPriority > resultPriority && ppvH.Read()->mSetting.BaseSettings.mType != PostProcessVolumeType::LocalLerpOnly)
                {
                    resultPriority = tempPriority;
                }
            }

            std::vector<std::pair<PostProcessVolumeSettingR*, float>> blendList(0);
            PostProcessVolumeSettingR* leader = nullptr;
            for (auto [ppvH, ppvTransH] : ppvEntities)
            {
                const auto& blendSetting = ppvH.Read()->mSetting.BaseSettings.BlendSettings;
                if (blendSetting.Priority < resultPriority)
                    continue;
                float weight = GetVolumeWeight(ppvH, ppvTransH, *renderPipeline);
                if (weight > 1e-3f)
                {
                    if (weight > 0.999f && blendSetting.Priority >= resultPriority && ppvH.Read()->mSetting.BaseSettings.mType != PostProcessVolumeType::LocalLerpOnly)
                    {
                        leader = &ppvH.Write()->mSetting;
                    }
                    blendList.push_back(std::make_pair(&ppvH.Write()->mSetting, weight * blendSetting.Weight));
                }
            }

            if (leader == nullptr)
            {
                leader = &defaultPostProcessVolumeSetting;
                blendList.insert(blendList.begin(), std::make_pair(&defaultPostProcessVolumeSetting, 1.f));
            }

            std::stable_sort(blendList.begin(), blendList.end(), [](const std::pair<PostProcessVolumeSettingR*, float>& A, const std::pair<PostProcessVolumeSettingR*, float>& B) {
                return A.first->BaseSettings.BlendSettings.Priority < B.first->BaseSettings.BlendSettings.Priority;
            });
            std::shared_ptr<PostProcessVolumeSettingR> ppvSetting = BlendPostProcessVolumeSettings(blendList, leader);
            ppvSetting->FogSettings = fogSetting;
            ppvSettings.push_back(ppvSetting);
            if (mGameOverrideSettings.OverrideState != PostProcessOverrideState::None)
                PerformGameOverrideSetting(ppvSettings.back().get());
            pipelineSystem->UpdateRenderPipelinePostProcessVolumeSetting(renderPipeline, ppvSettings.back().get());

            if (bShowAverageSceneLuminance && i->first >= ViewType::GameView && i->second->GetRenderPipelineType() == ViewType::SceneView)
            {
                auto primRenderSystem = mRenderWorld->GetRenderSystem<PrimitiveRenderSystemR>();
                auto exposureTexResult = i->second->GetBuiltInValue<PassSemanticName::ExposureTexResult>();

                primRenderSystem->DrawScreenText({40.f, start}, "AverageSceneLuminance: " + std::to_string(exposureTexResult.z), color, 13);
                start += width;
            }
        }
        DrawBoundingBoxWireFrame();
    });
}

void PostProcessVolumeSystemR::SetPropertyPostProcessVolumeSettings(ecs::EntityID entity, const PostProcessVolumeSetting& val)
{
    auto comp = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity);
    comp.Write()->mSetting.BaseSettings = val;
}
void PostProcessVolumeSystemR::SetSelected(ecs::EntityID entity, bool isSelected)
{
    auto comp = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity);
    comp.Write()->mSetting.isSelected = isSelected;
}

void PostProcessVolumeSystemR::SetPostProcessVolumeLUTTexture(ecs::EntityID entity, GPUTexture* val)
{
    auto comp = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity);
    comp.Write()->mSetting.lutGpuTex = val;
}

void PostProcessVolumeSystemR::SetEnable(ecs::EntityID entity, bool bEnable)
{
    auto comp = mRenderWorld->GetComponent<PostProcessVolumeComponentR>(entity);
    comp.Write()->mSetting.EnableForEditorEyeIcon = bEnable;
}
}   // namespace cross