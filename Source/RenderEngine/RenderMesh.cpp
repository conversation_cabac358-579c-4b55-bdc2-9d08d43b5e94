#include "RenderMesh.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"

namespace cross
{
    MeshR::MeshR(MeshAssetData* meshAssetData)
    {
        mMeshAssetData = meshAssetData;
    }

    void MeshR::BuildStaticMesh(IVertexStreamLayoutParameter* meshParameter)
    {
        Assert(mMeshAssetData);

        bool enableStreaming = gResourceMgr.mStreamingMgr->IsStreamingEnabled();
        SetReadyForStreaming(meshParameter != nullptr);
        if (enableStreaming && mMeshAssetData->IsMeshStreamable() && IsReadyForStreaming())
        {
            SetState(State::Initializing);

            // Create vertex and index buffer of meshes with a specific LOD level
            VertexStreamLayoutStaticLODStreamingParameter* meshParam = dynamic_cast<VertexStreamLayoutStaticLODStreamingParameter*>(meshParameter);
            IVertexStreamLayoutPolicy* streamLayout = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC_LOD_STREAMING);
            streamLayout->AssembleGpuResource(this, mMeshAssetData, meshParam);
            // LOG_DEBUG("Streaming: Stream in mesh {} with LOD {}", mMeshAssetData->GetName(), meshParam->SelectedLOD);

            SetState(State::Initialized);
        }
        else if (enableStreaming && mMeshAssetData->IsMeshStreamable() && !IsReadyForStreaming())
        {
             SetState(State::Initializing);

             // Collect vertex and index data info of meshes for each LOD level
             VertexStreamLayoutStaticLODStreamingParameter meshParam;
             meshParam.EnableStreaming = false;
             IVertexStreamLayoutPolicy* streamLayout = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC_LOD_STREAMING);
             streamLayout->AssembleGpuResource(this, mMeshAssetData, &meshParam);
             SetReadyForStreaming(true);

             SetState(State::Initialized);
        }
        else
        {
            IVertexStreamLayoutPolicy* streamLayout = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC);
            SetState(State::Initializing);
            streamLayout->AssembleGpuResource(this, mMeshAssetData, meshParameter);
            SetState(State::Initialized);
        }
    }

    const std::vector<RenderGeometry>& MeshR::GetRenderGeometries() const
    {
        return mGeometries;
    }

    RenderGeometry& MeshR::GetRenderGeometry(UInt32 index)
    {
        return mGeometries[index];
    }

    UInt32 MeshR::GetGeometryCount() const
    {
        return static_cast<UInt32>(mGeometries.size());
    }

    bool MeshR::IsGeometryEmpty() const
    {
        return mGeometries.empty();
    }

    std::vector<LODMeshR>* MeshR::GetLODMeshes()
    {
        return &mLODMeshes;
    }

    LODMeshR* MeshR::GetLODMesh(UInt8 lodLevel)
    {
        if (lodLevel >= 0 && lodLevel < mLODMeshes.size())
        {
            return &mLODMeshes[lodLevel];
        }
        else
        {
            return nullptr;
        }
    }

    UInt32 MeshR::GetLODMeshCount() const
    {
        return static_cast<UInt32>(mLODMeshes.size());
    }

    void MeshR::ClearAndResizeLODMeshes(UInt32 lodCount)
    {
        mLODMeshes.clear();
        mLODMeshes.resize(lodCount);
    }

    UInt32 MeshR::GetLODMeshBufferSize(UInt8 lodLevel) const
    {
        if (lodLevel >= 0 && lodLevel < mLODMeshes.size())
        {
            return mLODMeshes[lodLevel].GetBufferSize();
        }
        else
        {
            Assert(false);
            return 0;
        }
    }

    // TODO: Not sure if the condition it is enough
    bool MeshR::IsReadyForStreaming() const
    {
        return mReadyForStreaming;
    }

    void MeshR::SetReadyForStreaming(bool ready)
    {
        mReadyForStreaming = ready;
    }

    void MeshR::SetNameHash(UInt32 index, StringHash32 hash)
    {
        mGeometryNameHashes[index] = hash;
    }

    void MeshR::ClearAndResize(UInt32 geometryCount)
    {
        mGeometries.clear();
        mGeometries.resize(geometryCount);
        mGeometryNameHashes.clear();
        mGeometryNameHashes.resize(geometryCount);
    }

    void MeshR::SetState(State state)
    {
        mState.store(state, std::memory_order_relaxed);
    }

    MeshR::State MeshR::GetState() const
    {
        return mState.load(std::memory_order_relaxed);
    }

    bool MeshR::TryUpdate(UInt32 curFrameId)
    {
        return mVersion.exchange(curFrameId) != curFrameId;
    }

    std::mutex& MeshR::GetBuildMutex()
    {
        return mBuildMutex;
    }
    
}