#include "EnginePrefix.h"
#include "SkyLightSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"

#include "Resource/IResourceInterface.h"

#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"

static UInt32 SKY_VIEW_REALTIME_CAPTURE_CUBEMAP_WIDTH = 128;
namespace cross {

cross::ecs::ComponentDesc* cross::SkyLightComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::SkyLightComponentR>(false);
}
const cross::NGIBufferView* cross::SkyLightSystemR::GetDiffuseProbe(const RenderSkyLightComponentReader& lightHandle) const
{
    return mSHbufferView;
}

const SkyLightComponentR* SkyLightSystemR::GetSkyLight() const
{
    auto skyLights = mRenderWorld->Query<SkyLightComponentR>();

    // only the first skylight is used as skylight
    if (skyLights.GetEntityNum() == 0)
    {
        return &GetZeroSkyLight();
    }

    // a dangerous (maybe) hack to support default
    // auto reader = skyLights[0].Read();
    return (skyLights[0].mComponent);
}

void cross::SkyLightSystemR::PrepareSkyViewRealTimeCaptureTexture()
{
    const UInt32 cubeWidth = SKY_VIEW_REALTIME_CAPTURE_CUBEMAP_WIDTH;
    Assert(MathUtils::IsPowerOfTwo(cubeWidth));
    const UInt16 cubeMipCount = static_cast<UInt16>(std::log2(cubeWidth)) + 1;

    auto sys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = sys->GetRenderingExecutionDescriptor();

    auto CreateCubemapAndSHBuffer = [&, this](UInt32 index) -> void {
        // Create cube
        auto* texture = GetNGIDevice().CreateTexture(NGITextureDesc{GraphicsFormat::R16G16B16A16_SFloat,
                                                                    GPUTextureCube,
                                                                    cubeMipCount,
                                                                    1,
                                                                    cubeWidth,
                                                                    cubeWidth,
                                                                    1,
                                                                    static_cast<UInt16>(REFLECTION_PROBE_CAMERA_NUM),
                                                                    NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                                                                    false,
                                                                    true},
                                                     "SkyViewRealTimeCapture");
        // sys->UpdateTexture(texture, nullptr, NGICopyBufferTexture{}, NGIResourceState::Undefined, NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        sys->InitializeTexture(texture, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        auto* viewReadOnly = GetNGIDevice().CreateTextureView(texture,
                                                              NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                 GraphicsFormat::R16G16B16A16_SFloat,
                                                                                 GPUTextureCube,
                                                                                 {
                                                                                     NGITextureAspect::Color,
                                                                                     0,
                                                                                     cubeMipCount,
                                                                                     0,
                                                                                     REFLECTION_PROBE_CAMERA_NUM,
                                                                                 }});

        mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapTexture.reset(texture);
        mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapNGIView.reset(viewReadOnly);

        // Create buffer
        auto* buffer = GetNGIDevice().CreateBuffer(NGIBufferDesc{sizeof(Float4) * 7, NGIBufferUsage::RWStructuredBuffer}, "OutIrradianceEnvMapSH");
        // sys->UpdateBuffer(buffer, nullptr, NGICopyBuffer{}, NGIResourceState::Undefined, NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        auto* bufferViewReadOnly = GetNGIDevice().CreateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, sizeof(Float4) * 7, GraphicsFormat::Unknown, sizeof(Float4)});

        mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHBuffer.reset(buffer);
        mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHBufferNGIView.reset(bufferViewReadOnly);
    };

    auto AllocateCubeMapViewsAndBufferView = [&, this](UInt32 index) -> void {
        // Allocate texture
        mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapREDTexture = RED->AllocateTexture(fmt::format("SkyViewCubeMapREDTexture{}", index), mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapTexture.get());
        REDTextureView* sourceCubeMip = RED->AllocateTextureView(mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapREDTexture,
                                                                 NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                                                                                    GraphicsFormat::R16G16B16A16_SFloat,
                                                                                    NGITextureType::TextureCube,
                                                                                    {
                                                                                        NGITextureAspect::Color,
                                                                                        0,
                                                                                        cubeMipCount,
                                                                                        0,
                                                                                        6,
                                                                                    }});
        sourceCubeMip->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        // Allocate buffer
        mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHREDBuffer = RED->AllocateBuffer("IrradianceEnvMapSHREDBuffer", mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHBuffer.get());
        mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHREDBuffer->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
    };

    auto CreateDepthTexture = [&, this]() -> void {
        // Create cube
        auto* texture = GetNGIDevice().CreateTexture(
            NGITextureDesc{GraphicsFormat::D16_UNorm, NGITextureType::Texture2D, 1, 1, cubeWidth, cubeWidth, 1, 1, NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst, false, false},
            "DefaultDepth_SkyViewRealTimeCapture");
        auto* textureView = GetNGIDevice().CreateTextureView(texture,
                                                             {NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
                                                              GraphicsFormat::D16_UNorm,
                                                              NGITextureType::Texture2D,
                                                              {
                                                                  NGITextureAspect::Depth,
                                                                  0,
                                                                  1,
                                                                  0,
                                                                  1,
                                                              }});

        NGIClearValue clearValue{};
        clearValue.depthStencil = {
            0,
            0,
        };
        sys->InitializeTexture(textureView, clearValue, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
        mDefaultDepthTextures.DepthTexture.reset(texture);
        mDefaultDepthTextures.DepthTextureNGIView.reset(textureView);
    };

    auto AllocateDepthTextureView = [&, this]() -> void {
        REDTexture* DefaultDepthTextureRED = RED->AllocateTexture("DefaultSceneDepth_SkyViewRealTimeCapture", mDefaultDepthTextures.DepthTexture.get());
        REDTextureView* DefaultDepthTextureView = RED->AllocateTextureView(DefaultDepthTextureRED,
                                                                           NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                              DefaultDepthTextureRED->mDesc.Format,
                                                                                              NGITextureType::Texture2D,
                                                                                              NGITextureSubRange{
                                                                                                  NGITextureAspect::Depth,
                                                                                                  0,
                                                                                                  1,
                                                                                                  0,
                                                                                                  1,
                                                                                              }});

        DefaultDepthTextureView->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        mDefaultDepthTextures.DepthTextureREDTexture = DefaultDepthTextureRED;
        mDefaultDepthTextures.DepthTextureREDTextureView = DefaultDepthTextureView;

    };
    
    if (mSkyViewRealTimeCaptureTexturesAndBuffer[0].CubeMapTexture == nullptr)
    {
        // create NGI texture
        CreateCubemapAndSHBuffer(0);
        CreateCubemapAndSHBuffer(1);
        CreateCubemapAndSHBuffer(2);
        CreateDepthTexture();
    }

    AllocateCubeMapViewsAndBufferView(0);
    AllocateCubeMapViewsAndBufferView(1);
    AllocateCubeMapViewsAndBufferView(2);
    AllocateDepthTextureView();
}

void cross::SkyLightSystemR::Release()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto ReleaseTexturesAndBuffer = [&, this](UInt32 index) -> void {
        if (mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapTexture != nullptr)
        {
            rendererSystem->DestroyNGIObject(std::move(mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapTexture));
            rendererSystem->DestroyNGIObject(std::move(mSkyViewRealTimeCaptureTexturesAndBuffer[index].CubeMapNGIView));
            rendererSystem->DestroyNGIObject(std::move(mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHBuffer));
            rendererSystem->DestroyNGIObject(std::move(mSkyViewRealTimeCaptureTexturesAndBuffer[index].DiffuseSHBufferNGIView));
        }
    };
    ReleaseTexturesAndBuffer(0);
    ReleaseTexturesAndBuffer(1);
    ReleaseTexturesAndBuffer(2);

    if (mDefaultDiffuseSHBuffer != nullptr)
    {
        rendererSystem->DestroyNGIObject(std::move(mDefaultDiffuseSHBuffer));
        rendererSystem->DestroyNGIObject(std::move(mDefaultDiffuseSHBufferNGIView));
        rendererSystem->DestroyNGIObject(std::move(mDefaultDepthTextures.DepthTexture));
        rendererSystem->DestroyNGIObject(std::move(mDefaultDepthTextures.DepthTextureNGIView));
    }
    delete this;
}

void cross::SkyLightSystemR::OnFirstUpdate(FrameParam* frameParam)
{
    // Create buffer
    auto* buffer = GetNGIDevice().CreateBuffer(NGIBufferDesc{sizeof(Float4) * 7, NGIBufferUsage::CopyDst | NGIBufferUsage::RWStructuredBuffer}, "DefaultIrradianceEnvMapSH");
    auto* bufferViewReadOnly = GetNGIDevice().CreateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, sizeof(Float4) * 7, GraphicsFormat::Unknown, sizeof(Float4)});
    auto sys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    sys->InitializeBuffer(bufferViewReadOnly, 0, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

    mDefaultDiffuseSHBuffer.reset(buffer);
    mDefaultDiffuseSHBufferNGIView.reset(bufferViewReadOnly);
}

// For different time slice configure, using different frame task for each state
// The number in this function means the task_id of each frame
// each work for all task_id are defined in ProcessStateTask
static const std::vector<UInt32>& GetSliceStateSetup(UInt32 InSliceCount, SInt32 InCurrentFrameState)
{
    static const std::vector<std::vector<std::vector<UInt32>>> sSliceFrameSetup = {
        // {firstFrame, OtherFrames}
        {{0}},                                                           // dummy
        {{0}, {0}},                                                      // 1
        {{0}, {1, 2, 3, 4, 5}, {6, 7, 8, 9, 10, 11}},                    // 2
        {{0}, {1, 2, 3}, {4, 5, 6}, {7, 8, 9, 10, 11}},                  // 3
        {{0}, {1, 2}, {3, 4}, {5, 6, 7}, {8, 9, 10, 11}},                // 4
        {{0}, {1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10, 11}},              // 5
        {{0}, {1}, {2}, {3, 4}, {5, 6}, {7, 8}, {9, 10, 11}},            // 6
        {{0}, {1}, {2}, {3}, {4}, {5, 6}, {7, 8}, {9, 10, 11}},          // 7
        {{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7, 8}, {9, 10, 11}},        // 8
        {{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9, 10, 11}},      // 9
        {{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10, 11}},    // 10
        {{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11}}   // 11
    };
    
    Assert(InSliceCount == sSliceFrameSetup[InSliceCount].size() - 1);

    return sSliceFrameSetup[InSliceCount][InCurrentFrameState];
}

// Get valid statecount to prevent overflow or underflow
static SInt32 GetSliceCount(SInt32 InSliceCount) {
    if (InSliceCount <= 0 || InSliceCount > REALTIME_CAPTURE_MAX_SLICE)
    {
        InSliceCount = REALTIME_CAPTURE_MAX_SLICE;
    }
    return InSliceCount;
}

// Go to the next state or reset the state
static SInt32 GetNextState(SInt32 CurrentState, SInt32 NumStates) {
    if (CurrentState >= NumStates)
    {
        // reset
        return 1;
    }
    else
    {
        // forward
        return CurrentState + 1;
    }
}

cross::IGPUTexture* cross::SkyLightSystemR::GetSpecularProbe(const RenderSkyLightComponentReader& lightHandle)
{
    return lightHandle->mSpecularProbe;
}

void cross::SkyLightSystemR::ResetState()
{
    mRealTimeSlicedReflectionCaptureState = -1;
}

void cross::SkyLightSystemR::SetEnable(ecs::EntityID entity, bool enable)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mEnable = enable;
}

void cross::SkyLightSystemR::SetRealTimeCapture(ecs::EntityID entity, bool enable)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mRealTimeCapture = enable;
    if (mRealTimeCapture != enable)
    {
        mRealTimeCapture = enable;
        mRealTimeSlicedReflectionCaptureState = 0;
    }
}

void cross::SkyLightSystemR::SetRealTimeCaptureSliceCount(ecs::EntityID entity, SInt32 SliceNum)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mRealTimeCaptureSliceCount = SliceNum;
    if (mRealTimeSlicedReflectionCaptureSliceCount != SliceNum)
    {
        mRealTimeSlicedReflectionCaptureSliceCount = SliceNum;
        mRealTimeSlicedReflectionCaptureState = 0;
    }
}

void cross::SkyLightSystemR::SetDiffuseProbe(ecs::EntityID entity, const SkyLightDiffuseProbe&& diffuseProbe)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mDiffuseProbe = diffuseProbe;
}

void cross::SkyLightSystemR::SetSpecularProbe(ecs::EntityID entity, IGPUTexture* specularProbe)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mSpecularProbe = specularProbe;

    // mSkyLightCubeTexture = specularProbe;
}

void cross::SkyLightSystemR::SetLightColor(ecs::EntityID entity, Float3&& lightColor)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mLightColor = std::move(lightColor);

    // mSkyLightColor = lightColor;
}

void cross::SkyLightSystemR::SetLightMapIntensityDebug(ecs::EntityID entity, float lightMapIntensity)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mLightMapIntensityDebug = std::move(lightMapIntensity);

    // mLightMapIntensityDebug = lightMapIntensity;
}

void cross::SkyLightSystemR::SetSkyLightIntensity(ecs::EntityID entity, float prtIntensity)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->mSkyLightIntensity = std::move(prtIntensity);

}

void cross::SkyLightSystemR::SetIsLowerHemisphereColor(ecs::EntityID entity, bool bLowerHemiSolid)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->bLowerHemiSolidColor = bLowerHemiSolid;
}

void cross::SkyLightSystemR::SetTODLowerHemisphereColor(ecs::EntityID entity, bool bTODLowerHemiSolid)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->TODLowerHemiSolidColor = bTODLowerHemiSolid;
}

void cross::SkyLightSystemR::SetLowerHemisphereColor(ecs::EntityID entity, Float4 LowerHemiColor)
{
    auto compH = mRenderWorld->GetComponent<SkyLightComponentR>(entity);
    auto writer = compH.Write();
    writer->LowerHemisphereColor = LowerHemiColor;
}

cross::NGITextureView* SkyLightSystemR::GetSkyLightTexture() const
{
    if (GetHasReadyCubeMap())
    {
        return mSkyViewRealTimeCaptureTexturesAndBuffer[GetReadyCubeMapIndex()].CubeMapNGIView.get();
    }
    else
    {
        if (GetSkyLight()->mSpecularProbe)
        {
            return GetSkyLight()->mSpecularProbe->GetNGITextureView();
        }
        else
        {
            return nullptr;
        }
    }
}

void SkyLightSystemR::RenderSkylightTextures()
{
    SCOPED_CPU_TIMING(GroupRendering, "SkyLightRUpdate");
    auto queryResult = mRenderWorld->Query<SkyLightComponentR>();

    auto* renderPipelineSystem = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>();
    auto* skysys = mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    auto* lightSystem = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* renderSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = renderSystem->GetRenderingExecutionDescriptor();

    auto UpdateStaticSHBuffer = [&, this]() -> void {
        const SkyLightDiffuseProbe& sh = GetSkyLight()->mDiffuseProbe;
        auto* scratchBuffer = renderSystem->GetScratchBuffer();
        auto shDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, sizeof(SkyLightDiffuseProbe));
        shDataBufferWrap.MemWrite(0, sh.data(), sizeof(SkyLightDiffuseProbe));
        mSHbufferView = renderSystem->GetTransientResourceManager()->AllocateBufferView(
            NGIBufferViewDesc{
                NGIBufferUsage::StructuredBuffer,
                shDataBufferWrap.GetNGIOffset(),
                sizeof(SkyLightDiffuseProbe),
                GraphicsFormat::Unknown,
                sizeof(Float4),
            },
            shDataBufferWrap.GetNGIBuffer());
    };
    ResetDiffuseSHBuffer();

    // find main camera pipeline
    IRenderPipeline* mainPipeline = nullptr;
    for (auto& [type, renderPipeline] : renderPipelineSystem->GetAllRenderPipelines())
    {
        if (type != ViewType::ReflectionProbe && renderPipeline->IsMainCamera())
        {
            mainPipeline = renderPipeline.get();
        }
    }

    if (mainPipeline == nullptr)
    {
        return;
    }

    // detect sky
    auto ShouldRenderSky = [&, this]() mutable -> bool {
        if (!skysys->GetContextReady())
        {
            return false;
        }

        auto& lights = mainPipeline->GetLightList();
        if (lights.empty())
        {
            return false;
        }
        else
        {
            auto reader = mRenderWorld->GetComponent<LightComponentR>(lights[0]).Read();
            if (lightSystem->GetLightType(reader) != LightType::Directional)
            {
                return false;
            }
        }
        return true;
    };

    bool SkyEnabled = ShouldRenderSky();

    // process the first entity
    for (const auto& skLightComp : queryResult)
    {
        // if real time reflection
        if (mRealTimeCapture && mComputeShader != nullptr)
        {
            // 0. find main render pipeline and prepare global context. i.e, light, camera
            auto skyLightEntity = skLightComp.GetEntityID();
            auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
            auto* cameraSys = mRenderWorld->GetRenderSystem<CameraSystemR>();
            auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(skyLightEntity);
            Float3 cameraPos = transformSys->GetWorldTranslation(transformComp.Read());
            Float3 cameraForward = transformSys->GetWorldRotation(transformComp.Read()).Float3Rotate({0, 0, -1});
#ifdef CE_USE_DOUBLE_TRANSFORM
            auto transformTileComp = mRenderWorld->GetComponent<TilePositionComponentR>(skyLightEntity);
            Float3 cameraTilePos = transformSys->GetTilePosition(transformTileComp.Read());
#endif

            // Prepare 6 view matrix for 6 cube faces and 1 project matrix since all capture cameras have the same inner parameters
            std::array<Float4x4, REFLECTION_PROBE_CAMERA_NUM> viewMat;
            Float4x4A projMat;
            float nearClip = 1.0f;
            float farClip = 10000.0f;
            // Set near far plane as the main Camera near far plane
            // So that fog capture can be the same as the main scene
            auto mainCameraComp = mRenderWorld->GetComponent<CameraComponentR>(cameraSys->GetMainCamera());
            if (mainCameraComp.IsValid())
            {
                auto* renderMainCamera = cameraSys->GetRenderCamera(mainCameraComp.Read());
                if (renderMainCamera != nullptr)
                {
                    nearClip = renderMainCamera->GetNearPlane();
                    farClip = renderMainCamera->GetFarPlane();
                }
            }
            renderSystem->CalCubeCaptureMatrixInUECoordinates(cameraPos, nearClip, farClip, viewMat, projMat);

            const UInt32 cubeWidth = SKY_VIEW_REALTIME_CAPTURE_CUBEMAP_WIDTH;
            Assert(MathUtils::IsPowerOfTwo(cubeWidth));
            const UInt16 cubeMipCount = static_cast<UInt16>(std::log2(cubeWidth)) + 1;

            // Define lambda functions
            auto RenderSkyFunc = [&, this]() -> void {
                REDTexture* targetCube = mSkyViewRealTimeCaptureTexturesAndBuffer[GetSkyRTCubeMapIndex()].CubeMapREDTexture;

                for (UInt16 faceIndex = 0; faceIndex < REFLECTION_PROBE_CAMERA_NUM; faceIndex++)
                {
                    RED->BeginRegion(fmt::format("Render Sky For Face: {}", faceIndex));
                    RED->SetProperty(BuiltInProperty::ce_View, viewMat[faceIndex]);
                    RED->SetProperty(BuiltInProperty::ce_Projection, projMat);
                    RED->SetProperty(BuiltInProperty::ce_InvView, viewMat[faceIndex].Inverted());
                    RED->SetProperty(BuiltInProperty::ce_InvProjection, projMat.Inverted());

                    REDTextureView* targetCubeFaceView = RED->AllocateTextureView(targetCube,
                                                                                  NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                                                     GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                     NGITextureType::Texture2D,
                                                                                                     {
                                                                                                         NGITextureAspect::Color,
                                                                                                         0,
                                                                                                         1,
                                                                                                         faceIndex,
                                                                                                         1,
                                                                                                     }});
                    skysys->RenderSkyWithComputeShader(targetCubeFaceView);

                    auto DefaultDepthTextureView = mDefaultDepthTextures.DepthTextureREDTextureView;

                    targetCubeFaceView = RED->AllocateTextureView(targetCube,
                                                                  NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                                                                     GraphicsFormat::R16G16B16A16_SFloat,
                                                                                     NGITextureType::Texture2D,
                                                                                     {
                                                                                         NGITextureAspect::Color,
                                                                                         0,
                                                                                         1,
                                                                                         faceIndex,
                                                                                         1,
                                                                                     }});

                    FFSRenderPipeline* ffsRenderPipeline = TYPE_CAST(FFSRenderPipeline*, mainPipeline);
                    if (ffsRenderPipeline && ffsRenderPipeline->ShouldRenderScreenSpaceFog())
                    {
                        RED->BeginRegion("Render ExpFog");
                        {
                            ffsRenderPipeline->AssembleScreenSpaceFogPass(targetCubeFaceView, DefaultDepthTextureView, nullptr);
                        }
                        RED->EndRegion();
                    }

                    CloudSystemR* cloudSys = mRenderWorld->GetRenderSystem<CloudSystemR>();
                    if (ffsRenderPipeline && cloudSys->EnableVolumetricCloud())
                    {
                        RED->BeginRegion("Render Cloud");
                        {
                            const UInt2 rtSize = {SKY_VIEW_REALTIME_CAPTURE_CUBEMAP_WIDTH, SKY_VIEW_REALTIME_CAPTURE_CUBEMAP_WIDTH};
                            CloudResources cloudRes;
                            skysys->SetupGlobalSkyAtmosphereContext(RED);
                            ffsRenderPipeline->UpdateLightContext();
                            ffsRenderPipeline->AssembleCloudPass(DefaultDepthTextureView, CLOUD_PIPE_METHOD::SIMPLE, rtSize);
                            ffsRenderPipeline->AssembleFogAndCloudApply(DefaultDepthTextureView, nullptr, targetCubeFaceView, false);
                        }
                        RED->EndRegion();
                    }

                    // Currently we only set solid color for none wgs84 mode
                    if (skLightComp.Read()->bLowerHemiSolidColor)
                    {
                        RED->BeginRegion("Fill HemiSphere Solid Color");

                        // Set SkyAtmosphere Parameters
                        skysys->SetupGlobalSkyAtmosphereContext(RED);
                        mRenderWorld->GetRenderSystem<LightSystemR>()->FillAtmosphereLightConstantBuffer(RED, cameraSys->GetMainCamera());
                        UInt3 GroupSize = math::DivideAndRoundUp(UInt3{cubeWidth, cubeWidth, 1}, UInt3{8, 8, 1});

                        REDTextureView* targetCubeAllFaceView = RED->AllocateTextureView(targetCube,
                                                                                         NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                                                            GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                            NGITextureType::Texture2D,
                                                                                                            {
                                                                                                                NGITextureAspect::Color,
                                                                                                                0,
                                                                                                                1,
                                                                                                                faceIndex,
                                                                                                                1,
                                                                                                            }});

                        SkyRealTimeCaptureLowerHemisphereColorCSParam LowerHemiParams;
                        LowerHemiParams.OutTextureMipColor.mValue = targetCubeAllFaceView;
                        LowerHemiParams.LowerHemisphereSolidColor.mValue = skLightComp.Read()->LowerHemisphereColor;
                        LowerHemiParams.TODLowerHemisphereSolidColor.mValue = skLightComp.Read()->TODLowerHemiSolidColor;
                        LowerHemiParams.CurrentMip.mValue = faceIndex;
                        LowerHemiParams.FaceThreadGroupSize.mValue = GroupSize.x * 8;
                        LowerHemiParams.CubeFace.mValue = faceIndex;

                        auto computepass = RED->AllocatePass(fmt::format("Fill HemiSphere Solid Color"));
                        SetPassParameters(LowerHemiParams, computepass);

                        auto skyAtmoSys = mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();
                        skyAtmoSys->SetSkyCameraCaptureViewContext(computepass);

                        const NameID Kernel = skysys->GetPlanetTopAtWorldOrigin() ? "ApplyLowerHemisphereColorCSSimple" : "ApplyLowerHemisphereColorCS";

                        computepass->Dispatch(mComputeShader, Kernel, GroupSize.x, GroupSize.y, GroupSize.z);
                        RED->EndRegion();
                    }
                    RED->EndRegion();
                }
            };

            auto ClearSkyFunc = [&, this]() -> void {
                REDTexture* targetCube = mSkyViewRealTimeCaptureTexturesAndBuffer[GetSkyRTCubeMapIndex()].CubeMapREDTexture;

                for (UInt16 faceIndex = 0; faceIndex < REFLECTION_PROBE_CAMERA_NUM; faceIndex++)
                {
                    REDTextureView* targetCubeFaceView = RED->AllocateTextureView(targetCube,
                                                                                  NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                                                     GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                     NGITextureType::Texture2D,
                                                                                                     {
                                                                                                         NGITextureAspect::Color,
                                                                                                         0,
                                                                                                         1,
                                                                                                         faceIndex,
                                                                                                         1,
                                                                                                     }});
                    skysys->ClearSkyWithComputeShader(targetCubeFaceView);
                }
            };

            auto GenCubeMips = [&, this](UInt16 MipStart, UInt16 MipEnd, UInt16 FaceStart, UInt16 FaceEnd) -> void {
                SkyRealTimeCaptureGenMipCSParam genMipParam;
                genMipParam.ReverseZ.mValue = true;
                genMipParam.Mip0Size.mValue = cubeWidth;
                genMipParam.NumSamples.mValue = 1024;
                genMipParam.NumMips.mValue = cubeMipCount;
                REDTexture* targetCube = mSkyViewRealTimeCaptureTexturesAndBuffer[GetSkyRTCubeMapIndex()].CubeMapREDTexture;
                for (UInt16 mipIndex = MipStart; mipIndex < MipEnd; mipIndex++)
                {
                    REDTextureView* sourceCubeMip = RED->AllocateTextureView(targetCube,
                                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                NGITextureType::TextureCube,
                                                                                                {
                                                                                                    NGITextureAspect::Color,
                                                                                                    mipIndex,
                                                                                                    1,
                                                                                                    0,
                                                                                                    6,
                                                                                                }});

                    genMipParam.SourceColorMapTexture.mValue = sourceCubeMip;
                    genMipParam.CurrentMip.mValue = mipIndex;
                    for (UInt16 faceIndex = FaceStart; faceIndex < FaceEnd; faceIndex++)
                    {
                        REDTextureView* nextMipRenderTarget = RED->AllocateTextureView(targetCube,
                                                                                       NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                                                          GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                          NGITextureType::Texture2D,
                                                                                                          {
                                                                                                              NGITextureAspect::Color,
                                                                                                              static_cast<UInt16>(mipIndex + 1),
                                                                                                              1,
                                                                                                              faceIndex,
                                                                                                              1,
                                                                                                          }});
                        genMipParam.GenNextMipRenderTargetTexture.mValue = nextMipRenderTarget;
                        genMipParam.CurrentCubeFace.mValue = faceIndex;

                        std::string kernel = "DownsampleCS";

                        auto computepass = RED->AllocatePass(fmt::format("{}_Mip{}_ForFace{}", kernel, mipIndex + 1, faceIndex), true);
                        SetPassParameters(genMipParam, computepass);

                        UInt32 nextFaceSize = cubeWidth >> (mipIndex + 1);
                        // max: 128 / 8, min: 1
                        UInt32 groupSize = (nextFaceSize + 7) / 8;
                        computepass->Dispatch(mComputeShader, kernel, groupSize, groupSize, 1);
                    }
                }
            };

            auto FilterFaces = [&, this](UInt16 MipStart, UInt16 MipEnd, UInt16 FaceStart, UInt16 FaceEnd) -> void {
                SkyRealTimeCaptureGenMipCSParam filterParam;
                filterParam.ReverseZ.mValue = true;
                filterParam.Mip0Size.mValue = cubeWidth;
                filterParam.NumSamples.mValue = 1024;
                filterParam.NumMips.mValue = cubeMipCount;
                REDTexture* targetCube = mSkyViewRealTimeCaptureTexturesAndBuffer[GetWorkingCubeMapIndex()].CubeMapREDTexture;
                REDTexture* sourceCube = mSkyViewRealTimeCaptureTexturesAndBuffer[GetSkyRTCubeMapIndex()].CubeMapREDTexture;
                for (UInt16 mipIndex = MipStart; mipIndex < MipEnd; mipIndex++)
                {
                    REDTextureView* sourceCubeMip = RED->AllocateTextureView(sourceCube,
                                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                NGITextureType::TextureCube,
                                                                                                {
                                                                                                    NGITextureAspect::Color,
                                                                                                    mipIndex,
                                                                                                    1,
                                                                                                    0,
                                                                                                    6,
                                                                                                }});

                    filterParam.SourceColorMapTexture.mValue = sourceCubeMip;
                    filterParam.CurrentMip.mValue = mipIndex;
                    for (UInt16 faceIndex = FaceStart; faceIndex < FaceEnd; faceIndex++)
                    {
                        REDTextureView* filterRenderTarget = RED->AllocateTextureView(targetCube,
                                                                                      NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                                                         GraphicsFormat::R16G16B16A16_SFloat,
                                                                                                         NGITextureType::Texture2D,
                                                                                                         {
                                                                                                             NGITextureAspect::Color,
                                                                                                             static_cast<UInt16>(mipIndex),
                                                                                                             1,
                                                                                                             faceIndex,
                                                                                                             1,
                                                                                                         }});
                        filterParam.GenNextMipRenderTargetTexture.mValue = filterRenderTarget;
                        filterParam.CurrentCubeFace.mValue = faceIndex;

                        std::string kernel = "FilterCS";

                        auto computepass = RED->AllocatePass(fmt::format("{}_Mip{}_ForFace{}", kernel, mipIndex, faceIndex), true);
                        SetPassParameters(filterParam, computepass);

                        UInt32 FaceSize = cubeWidth >> mipIndex;
                        // max: 128 / 8, min: 1
                        UInt32 groupSize = (FaceSize + 7) / 8;
                        computepass->Dispatch(mComputeShader, kernel, groupSize, groupSize, 1);
                    }
                }
            };

            auto ComputeEnvMapDiffuseIrradiance = [&, this]() -> void {
                REDTexture* targetCube = mSkyViewRealTimeCaptureTexturesAndBuffer[GetWorkingCubeMapIndex()].CubeMapREDTexture;
                REDTextureView* sourceCubeMip = RED->AllocateTextureView(targetCube,
                                                                         NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                            GraphicsFormat::R16G16B16A16_SFloat,
                                                                                            NGITextureType::TextureCube,
                                                                                            {
                                                                                                NGITextureAspect::Color,
                                                                                                0,
                                                                                                cubeMipCount,
                                                                                                0,
                                                                                                6,
                                                                                            }});

                REDBuffer* targetDiffuseBuffer = mSkyViewRealTimeCaptureTexturesAndBuffer[GetWorkingCubeMapIndex()].DiffuseSHREDBuffer;
                REDBufferView* bufferView = RED->AllocateBufferView(targetDiffuseBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, sizeof(Float4) * 7, GraphicsFormat::Unknown, sizeof(Float4)});

                SkyRealTimeCaptureComputeEnvMapDiffuseIrradianceParam genSHParam;
                genSHParam.SourceColorMapTexture.mValue = sourceCubeMip;
                // sample on a 16*16 cube map face
                genSHParam.MipIndex.mValue = cubeMipCount - 5;
                genSHParam.UniformSampleSolidAngle.mValue = 4.0f * MathUtils::MathPi / 64.0f;
                genSHParam.OutIrradianceEnvMapSH.mValue = bufferView;

                auto computepass = RED->AllocatePass("GenDiffuseSH", true);
                SetPassParameters(genSHParam, computepass);
                computepass->Dispatch(mComputeShader, "ComputeSkyEnvMapDiffuseIrradianceCS", 1, 1, 1);
            };

            auto ProcessStateTask = [&, this](UInt32 stateNum) {
                // first frame
                if (stateNum == 0)
                {
                    if (SkyEnabled)
                    {
                        // 1. precompute sky view
                        skysys->PrecomputeSkyViewReflectionRealTimeCaptureLutTexture();
                        skysys->PreComputeDistantSkyLightTextures();

                        // 2. render cubemap
                        RenderSkyFunc();
                    }
                    else
                    {
                        ClearSkyFunc();
                    }

                    // 3. gen mipmap & filter
                    GenCubeMips(0, cubeMipCount - 1, 0, 6);
                    FilterFaces(0, cubeMipCount, 0, 6);

                    // 4. gen SH
                    ComputeEnvMapDiffuseIrradiance();
                    mReadyRealtimeCaptureIndex = GetWorkingCubeMapIndex();
                }
                else if (stateNum == 1)
                {
                    if (SkyEnabled)
                    {
                        // 1. precompute sky view
                        skysys->PrecomputeSkyViewReflectionRealTimeCaptureLutTexture();

                        // 2. render cubemap
                        RenderSkyFunc();
                    }
                    else
                    {
                        ClearSkyFunc();
                    }
                }
                // 3. gen mipmap & filter
                else if (stateNum == 2 && cubeMipCount >= 1)
                {
                    // Gen all mips
                    GenCubeMips(0, cubeMipCount - 1, 0, 6);
                }
                // 3. gen mipmap & filter
                else if (stateNum == 3 && cubeMipCount >= 1)
                {
                    // filter mip 0's face 0 & face 1
                    FilterFaces(0, 1, 0, 2);
                }
                else if (stateNum == 4 && cubeMipCount >= 1)
                {
                    // filter mip 0's face 2 & face 3
                    FilterFaces(0, 1, 2, 4);
                }
                else if (stateNum == 5 && cubeMipCount >= 1)
                {
                    // filter mip 0's face 4 & face 5
                    FilterFaces(0, 1, 4, 6);
                }
                else if (stateNum == 6 && cubeMipCount >= 2)
                {
                    // filter mip 1
                    FilterFaces(1, 2, 0, 6);
                }
                else if (stateNum == 7 && cubeMipCount >= 3)
                {
                    // filter mip 2
                    FilterFaces(2, 3, 0, 6);
                }
                else if (stateNum == 8 && cubeMipCount >= 4)
                {
                    // filter mip 3
                    FilterFaces(3, 4, 0, 6);
                }
                else if (stateNum == 9 && cubeMipCount >= 5)
                {
                    if (cubeMipCount >= 6)
                    {
                        // filter mip 4 5
                        FilterFaces(4, 6, 0, 6);
                    }
                    else
                    {
                        // filter mip 4
                        FilterFaces(4, 5, 0, 6);
                    }
                }
                else if (stateNum == 10 && cubeMipCount >= 7)
                {
                    // filter mip 6, 7, etc.
                    FilterFaces(6, cubeMipCount, 0, 6);
                }
                else if (stateNum == 11)
                {
                    // 4. gen SH
                    ComputeEnvMapDiffuseIrradiance();
                    mReadyRealtimeCaptureIndex = GetWorkingCubeMapIndex();
                }
            };

            RED->BeginRegion(fmt::format("SkyLight RealTimeCapture for view in world: {}", mRenderWorld->GetName().GetCString()));

            RED->SetProperty(BuiltInProperty::ce_CameraPos, cameraPos);
            RED->SetProperty(BuiltInProperty::ce_CameraForward, cameraForward);
#if defined(CE_USE_DOUBLE_TRANSFORM)
            RED->SetProperty(BuiltInProperty::ce_CameraTilePosition, cameraTilePos);
#endif
            if (auto* ffsPipeline = TYPE_CAST(FFSRenderPipeline*, mainPipeline); ffsPipeline != nullptr)
            {
                ffsPipeline->UpdateSkyAtmosphereAdvancedVars();
            }

            lightSystem->FillAtmosphereLightConstantBuffer(RED);

            PrepareSkyViewRealTimeCaptureTexture();

            // SliceCount is the number of frames to complete one capture task
            // Note that slice count will always be 1 in the first frame
            SInt32 SliceCount = GetSliceCount(mRealTimeSlicedReflectionCaptureSliceCount);

            // CurrentSliceStatesSetup contains the tasks to do in this frame
            const auto& CurrentSliceStatesSetup = GetSliceStateSetup(SliceCount, std::max(0, mRealTimeSlicedReflectionCaptureState));
            for (UInt32 frameNum : CurrentSliceStatesSetup)
            {
                ProcessStateTask(frameNum);
            }

            // state transition
            mRealTimeSlicedReflectionCaptureState = GetNextState(mRealTimeSlicedReflectionCaptureState, SliceCount);

            mSHbufferView = mSkyViewRealTimeCaptureTexturesAndBuffer[GetReadyCubeMapIndex()].DiffuseSHBufferNGIView.get();

            RED->EndRegion();
        }
        else
        {
            UpdateStaticSHBuffer();
        }

        break;
    }
}

}   // namespace cross