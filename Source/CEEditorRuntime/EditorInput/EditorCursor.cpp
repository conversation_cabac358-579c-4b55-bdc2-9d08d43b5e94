#include "EnginePrefix.h"
#include "EditorInput/EditorCursor.h"

namespace cross {

Float2 EditorCursor::GetPosition() const
{
    Assert(mGetMousePosition);

    float mouseX = 0.f, mouseY = 0.f;
    mGetMousePosition(&mouseX, &mouseY);

    return {mouseX, mouseY};
}

void EditorCursor::SetPosition(const SInt32 x, const SInt32 y)
{
}

void EditorCursor::Show(bool bShow)
{
    Assert(mShowMouseCursor);
    mShowMouseCursor(bShow);
}

void EditorCursor::Lock(Float2 const inOrigin, Float2 const inSize)
{
    Assert(mLockMouseCursor);
    mLockMouseCursor(inOrigin.x, inOrigin.y, inSize.x, inSize.y);
}

void EditorCursor::UnLock()
{
    Assert(mUnLockMouseCursor);
    mUnLockMouseCursor();
}

void EditorCursor::GetSize(SInt32& outWidth, SInt32& outHeight) const
{
}

void EditorCursor::SetFetchMousePosCallBack(EditorFetchMousePosCallBack callBack)
{
    mGetMousePosition = callBack;
}

void EditorCursor::SetShowMouseCursorCallBack(EditorShowMouseCursorCallBack callBack)
{
    mShowMouseCursor = callBack;
}

void EditorCursor::SetLockMouseCursorCallBack(EditorLockMouseCursorCallBack callBack)
{
    mLockMouseCursor = callBack;
}

void EditorCursor::SetUnLockMouseCursorCallBack(EditorGeneralCallBack callBack)
{
    mUnLockMouseCursor = callBack;
}

}

