#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionPixelDepth : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "PixelDepth";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};

class CEMeta(Cli) MaterialExpressionPixelLinearDepth : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "PixelLinearDepth";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};


class CEMeta(Cli) MaterialExpressionSceneDepth : public MaterialExpression
{
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "SceneDepth";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};


class CEMeta(Cli) MaterialExpressionSceneLinearDepth : public MaterialExpression
{
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "SceneLinearDepth";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};



}   // namespace cross