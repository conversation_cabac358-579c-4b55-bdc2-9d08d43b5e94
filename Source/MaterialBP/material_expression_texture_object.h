#pragma once

#include "material_expression_parameter.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"

namespace cross {
class CEMeta(Cli, Reflect) Material_API MaterialExpressionTextureObject : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Texture Object";
    }

    virtual void DoHandlePropertyChange(IMaterialEditor * editor) override;

    bool IsVirtualTexture() const
    {
        return m_TextureType == MaterialValueType::MCT_TextureVirtual || m_TextureType == MaterialValueType::MCT_TextureVirtualNormal;
    }

public:
    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    MaterialValueType m_TextureType = MaterialValueType::MCT_Texture2D;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    std::string m_TextureObjectName;

    CEProperty(Reflect,
               EditorPropertyInfo(
                   PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Texture, ObjectClassID2 = ClassIDType.CLASS_TextureUDIM, ObjectClassID3 = ClassIDType.CLASS_Texture2DArray))
    std::string m_TextureString;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross