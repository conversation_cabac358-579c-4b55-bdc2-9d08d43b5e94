#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionSkyAtmosphereLightDiskLuminance : public MaterialExpression
{
public:
    int32_t Compile(MaterialCompiler& compiler, ExpressionOutput* output) override;

    std::string GetCaption() const override { return fmt::format("SkyAtmosphereLightDiskLuminance[{}]", m_LightIndex); }

    CEProperty(Reflect, EditorPropertyInfo(DisplayName = "Light Index", ValueMin = "0", ValueMax = "1"))
    uint32_t m_LightIndex = 0;

    CEProperty(Reflect)
    ExpressionInput m_DiskAngularDiameterOverride;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross