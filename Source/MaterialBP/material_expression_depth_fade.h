#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionDepthFade : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "DepthFade";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Opacity;

    CEProperty(Reflect)
    ExpressionInput m_FadeDistance;

    CEProperty(Reflect, meta(OverrideInputProperty = m_Opacity))
    float m_OpacityDefault = 1.0f;

    CEProperty(Reflect, meta(OverrideInputProperty = m_FadeDistance))
    float m_FadeDistanceDefault = 100.0f;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross