#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionPower : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Power";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Base;

    CEProperty(Reflect)
    ExpressionInput m_Exponent;

    CEProperty(Reflect, meta(OverrideInputProperty = m_Exponent))
    float m_ConstExponent = 1;
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross