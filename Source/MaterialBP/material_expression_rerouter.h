#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionRerouter : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "";
    }

    virtual std::string GetMenuName() const override
    {
        return "Reroute";
    }

public:
    CEProperty(Reflect, meta(DisplayName = ""))
    ExpressionInput m_Input;

    CEMeta(Reflect, meta(DisplayName = ""))
    ExpressionOutput m_Result;
};
}   // namespace cross