#pragma once

#include "material_compiler.h"

namespace cross {
class SerializeNode;
class DeserializeNode;
}   // namespace cross

namespace cross {
class MaterialGraphNode;
class MaterialGraphPin;
class MaterialExpression;
class ExpressionOutput;
class MaterialEditor;
class MaterialExpressionParameter;
class MaterialExpressionShaderConst;
class CEMeta(Cli) ExpressionPin
{
public:
    CE_Serialize_Deserialize;

    bool m_Enable = true;
    std::string m_Name;
    MaterialGraphPin* m_GraphPin;

    std::string m_BindedPropertyName;
    std::function<gbf::reflection::Value(void)> m_GetBindedPropertyValue;
};

class CEMeta(Cli) ExpressionInput : public ExpressionPin
{
public:
    CE_Serialize_Deserialize;

    int32_t Compile(MaterialCompiler & compiler);

    // PConstant is the override material constant value, if nullptr, use hard coded default value else use a modifiable value
    int32_t CompileAttributes(MaterialCompiler & compiler, EMaterialProperty attribute, const float* PConstant = nullptr);

    CEFunction(AdditionalSerialize)
    void AdditionalSerialize(cross::SerializeNode & node, SerializeContext & context) const;

public:
    ExpressionOutput* m_LinkedExpressionOutput = nullptr;
};

class CEMeta(Cli) ExpressionAttributesInput : public ExpressionInput
{
public:
    CE_Serialize_Deserialize;
    int32_t CompileWithDefault(MaterialCompiler & compiler, EMaterialProperty attribute);
};

class CEMeta(Cli) ExpressionOutput : public ExpressionPin
{
public:
    int32_t Compile(MaterialCompiler & compiler);

public:
    MaterialExpression* m_ParentExpression;
    ExpressionOutput* m_ExpressionOutputRedirectTo = nullptr;
    ColorMask m_ColorMask = ColorMask::All;
};
class CEMeta(Cli) Material_API IMaterialEditor
{
public:
    virtual void UpdateExpressionsState(MaterialExpression* expressionChanged = nullptr) = 0;
    virtual void UpdateUITextureAndExpressionTexture(MaterialExpression* expressionChanged) = 0;
    virtual void ClearInvalidLinks() = 0;
    virtual void UpdateExpressionAppearance(MaterialExpression* expression) = 0;
    virtual std::vector<ExpressionInput*> GetLinkedInputPins(ExpressionOutput* output) = 0;
    virtual void OnParameterChange(MaterialExpressionParameter* expression) = 0;
    virtual void OnParameterChange(MaterialParameter* parameter) = 0;
    virtual void InitializeNamedReroutes() = 0;
    virtual void MakeNamedRerouteNameUnique(MaterialExpression* epxression) = 0;
    virtual void OnShaderConstChange(MaterialExpressionShaderConst* expressionShaderConst) = 0;
    virtual void SetResourceChanged(bool value) = 0;
    virtual void AddParameter(MaterialExpressionParameter* paramter) = 0;
    virtual void RemoveParameter(MaterialExpressionParameter * paramter) = 0;
};
class CEMeta(Cli) Material_API MaterialExpression : public gbf::reflection::RttiBase
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual ~MaterialExpression() {}

    void InitializePinsData();

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    virtual std::string GetCaption() const = 0;

    virtual std::string GetMenuName() const
    {
        return GetCaption();
    }

    // called by CrossEditor
    virtual void OnPropertyChange(IMaterialEditor * editor);
    static  void InitPassState(resource::Fx * fx, const std::string& passName);
    virtual void DoHandlePropertyChange(IMaterialEditor * editor) {}
    static std::vector<std::shared_ptr<MaterialExpression>> DeserializeExpressions(const cross::DeserializeNode& node);
    auto& GetInputPins()
    {
        return m_Inputs;
    };

    auto& GetOutputPins()
    {
        return m_Outputs;
    }

    CEFunction(AdditionalSerialize)
    void AdditionalSerialize(cross::SerializeNode & node, SerializeContext & context) const;

    virtual void Deserialize(const cross::DeserializeNode& node);

    virtual ImColor GetTitleColor() const
    {
        cross::HashString name(GetMenuName());
        auto color = ImColor(name.GetHash32());
        color.Value.w = 1;
        return color;
    }

    virtual Float2 GetNodeSize()
    {
        return Float2(100, 100);
    }

    virtual void SetCustomAttrInt2(int x, int y, IMaterialEditor* editor) {}

public:
    static void RegisterExpressions();

public:
    int32_t GetExpressionInputIndex(ExpressionInput * input) const;
    int32_t GetExpressionOutputIndex(ExpressionOutput * output) const;

public:
    CEProperty(EditorPropertyInfo(bHide = true))
    int32_t m_Id;
    CEProperty(EditorPropertyInfo(bHide = true))
    int32_t m_EditorPositionX;
    CEProperty(EditorPropertyInfo(bHide = true))
    int32_t m_EditorPositionY;

    MaterialGraphNode* m_GraphNode;
    std::string m_ErrorMessage;

    CEProperty(Reflect)
    std::string m_Description;

    std::vector<ExpressionInput*> m_Inputs;
    std::vector<ExpressionOutput*> m_Outputs;
};
}   // namespace cross