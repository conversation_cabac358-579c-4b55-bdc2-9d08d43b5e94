#pragma once

namespace ClangenCli
{
	//-------------------------------------------------------------------------------------------
	[System::AttributeUsageAttribute(System::AttributeTargets::Class | System::AttributeTargets::Struct)]
	public ref class ClassCategory : System::Attribute
	{
	public:
		property System::String^ CategoryPath
		{
		public:
			System::String^ get();
		private:
			void set(System::String^ value);
		}

		ClassCategory(System::String^ category_)
		{
			CategoryPath = category_;
		}
		System::String^ mCategoryPath;
	};
	//-------------------------------------------------------------------------------------------
	[System::AttributeUsageAttribute(System::AttributeTargets::Class | System::AttributeTargets::Struct)]
	public ref class ClassDisplayName : System::Attribute
	{
	public:
		property System::String^ DisplayName
		{
		public:
			System::String^ get();
		private:
			void set(System::String^ value);
		}

		ClassDisplayName(System::String^ display_)
		{
			DisplayName = display_;
		}
		System::String^ mDisplayName;
	};
	//-------------------------------------------------------------------------------------------
	[System::AttributeUsageAttribute(System::AttributeTargets::Class | System::AttributeTargets::Struct)]
	public ref class ClassVisible : System::Attribute
	{
	public:
		property bool IsVisible
		{
		public:
			bool get();
		private:
			void set(bool value);
		}

		ClassVisible(bool visible_)
		{
			IsVisible = visible_;
		}
		bool mIsVisible;
	};
	//-------------------------------------------------------------------------------------------
}

