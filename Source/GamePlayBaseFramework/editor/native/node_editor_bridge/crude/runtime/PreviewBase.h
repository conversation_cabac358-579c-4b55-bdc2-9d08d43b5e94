#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "node_editor_bridge_fwd.hpp"
#include "CrossBase/Math/CrossMath.h"
#include "EditorKey.h"

namespace cross {
class EditorUIRenderInterface;
struct IRenderWindow;
class GameWorld;
}   // namespace cross

namespace cross {
class MaterialEditor;
class MaterialInstanceEditor;

class NODE_EDITOR_BRIDGE_API CEMeta(Cli) PreviewBase : public SystemEventReceiver
{
public:
    virtual ~PreviewBase();

    virtual void Tick();

    CEMeta(Cli)
    virtual void OnResize(const Float2& size);

    CEMeta(Cli)
    virtual void OnMouseMoveEvent(int mouseX, int mouseY);

    CEMeta(Cli)
    virtual void OnMouseWheelEvent(int mouseDeltaZ);

    CEMeta(Cli)
    virtual void OnKeyEvent(EditorKey btn, bool isDown);

    CEMeta(Cli)
    virtual void OnActivate(bool active);

    CEMeta(Cli)
    int GetTexture()
    {
        return m_RenderTexture;
    }

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag);


    virtual void Init();

    void UpdateMainCamera();
    std::optional<cross::ecs::EntityID> m_MainCamera;
    cross::GameWorld* m_World;

    int m_RenderTexture;
    int m_Width, m_Height;
    float m_CameraDistance = 200.0f;
    float m_CameraThetaAngle = 0.0f, m_CameraPhiAngle = 1.308f;

    int m_MouseX = 0, m_MouseY = 0;
    bool m_LeftButtonDown = false, m_RightButtonDown = false, m_MiddleButtonDown = false;
};
}   // namespace cross
