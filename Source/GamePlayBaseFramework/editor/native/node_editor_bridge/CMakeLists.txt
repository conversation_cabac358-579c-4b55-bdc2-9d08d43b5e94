cmake_minimum_required(VERSION 3.12)

project(imgui-node-editor)

# Define IMGUI_NODE_EDITOR_ROOT_DIR pointing to project root directory
get_filename_component(IMGUI_NODE_EDITOR_ROOT_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../../third_party/IMGUINodeEditor" ABSOLUTE)

message(STATUS "node editor path: ${IMGUI_NODE_EDITOR_ROOT_DIR}")
set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}")
set(EXECUTABLE_OUTPUT_PATH "${CMAKE_BINARY_DIR}")
# Enable solution folders in Visual Studio and Folders in Xcode
# set_property(GLOBAL PROPERTY USE_FOLDERS ON)

# Point CMake where to look for module files.
list(APPEND CMAKE_MODULE_PATH ${IMGUI_NODE_EDITOR_ROOT_DIR}/misc/cmake-modules)

# Node editor use C++14
#set(CMAKE_CXX_STANDARD            14)
#set(CMAKE_CXX_STANDARD_REQUIRED   YES)

add_definitions(-DNODE_EDITOR_BRIDGE_EXPORTS=1)
add_definitions(-DIMGUI_DEFINE_MATH_OPERATORS=1)

set(PROJECT_NAME "node_editor_bridge")

include_directories(
    "${PROJECT_SOURCE_DIR}/../../../externals/include"
)

set(all_project_src "")

file(GLOB top_src "*.*")
source_group(\\ FILES ${top_src})
list(APPEND all_project_src ${top_src})

file(GLOB application_src "application/*.*")
source_group(\\application FILES ${application_src})
list(APPEND all_project_src ${application_src})

file(GLOB external_dxerr_src "external/dxerr/*.*")
source_group(\\external\\dxerr FILES ${external_dxerr_src})
list(APPEND all_project_src ${external_dxerr_src})

file(GLOB external_nonstd_src "external/nonstd/*.*")
source_group(\\external\\nonstd FILES ${external_nonstd_src})
list(APPEND all_project_src ${external_nonstd_src})

file(GLOB external_tinyfiledialogs_src "external/tinyfiledialogs/*.*")
source_group(external\\tinyfiledialogs FILES ${external_tinyfiledialogs_src})
list(APPEND all_project_src ${external_tinyfiledialogs_src})


file(GLOB crude_src "crude/*.*")
source_group(\\crude FILES ${crude_src})
list(APPEND all_project_src ${crude_src})

file(GLOB crude_misc_src "crude/misc/*.*")
source_group(\\crude\\misc FILES ${crude_misc_src})
list(APPEND all_project_src ${crude_misc_src})

file(GLOB crude_runtime_src "crude/runtime/*.*")
source_group(\\crude\\runtime FILES ${crude_runtime_src})
list(APPEND all_project_src ${crude_runtime_src})

file(GLOB crude_runtime_crude_src "crude/runtime/crude/*.*")
source_group(\\crude\\runtime\\crude FILES ${crude_runtime_crude_src})
list(APPEND all_project_src ${crude_runtime_crude_src})

file(GLOB crude_runtime_material_src "crude/runtime/material/*.*")
source_group(\\crude\\runtime\\material FILES ${crude_runtime_material_src})
list(APPEND all_project_src ${crude_runtime_material_src})

file(GLOB crude_runtime_workflow_src "crude/runtime/workflow/*.*")
source_group(\\crude\\runtime\\workflow FILES ${crude_runtime_workflow_src})
list(APPEND all_project_src ${crude_runtime_workflow_src})

file(GLOB editor_src "editor/*.*")
source_group(\\editor FILES ${editor_src})
list(APPEND all_project_src ${editor_src})

file(GLOB editor_utilities_src "editor/utilities/*.*")
source_group(\\editor\\utilities FILES ${editor_utilities_src})
list(APPEND all_project_src ${editor_utilities_src})


set(GENERATED_CODE)
set(CurrentGeneratedCodeRoot "${CMAKE_CURRENT_SOURCE_DIR}/GeneratedCode")
file(GLOB_RECURSE GENERATED_CODE ${CurrentGeneratedCodeRoot}/*.cpp ${CurrentGeneratedCodeRoot}/*.h)
source_group(TREE ${CurrentGeneratedCodeRoot} PREFIX "GeneratedCode" FILES ${GENERATED_CODE})


list(APPEND all_project_src ${GENERATED_CODE})


# add_executable(${name} ${_Example_Type} ${_Example_Sources} ${_Example_Resources} ${_Example_CommonResources})

add_library(${PROJECT_NAME} SHARED
    ${all_project_src}
)
#target_compile_options(${PROJECT_NAME} PRIVATE "/std:c++17")

#direct3d11
#find package libraries
#find_package(imgui REQUIRED)
#find_package(imgui_node_editor REQUIRED)
#find_package(stb_image REQUIRED)
#find_package(ScopeGuard REQUIRED)

target_include_directories(${PROJECT_NAME} 
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../third_party> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/third_party>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../base> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/base>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../meta> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/meta>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../logic> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/logic>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../async> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/async>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../imodules> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/imodules>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/../../../editor/native/node_editor_bridge>  $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/editor/native/node_editor_bridge>
)

if(GBF_COMPILE_WITH_CE)
    set_target_properties(${PROJECT_NAME} PROPERTIES NEED_EXPORT_COMPILE_INFO true)
    set(dependItems CrossEngine CrossBase CECommon ECS CrossPhysics CEResource CEEditorRuntime MaterialBP)
	get_property(m3ddirs GLOBAL PROPERTY "M3RDDirs")
	get_managed_thirdparty_dir(MANAGED_THIRDPARTY_DIR)
	foreach(target ${m3ddirs})
		include_directories(SYSTEM "${target}")
	endforeach()
    link_managed_thirdparty_dir(${PROJECT_NAME})
	## definitions
	get_property(definitions DIRECTORY ${CROSSENGINE_SRC_DIR}/Source PROPERTY COMPILE_DEFINITIONS) 
	foreach(def ${definitions})
	 target_compile_definitions(${PROJECT_NAME} PUBLIC "${def}")
	endforeach()
	## CXX flags
	get_property(cppflags GLOBAL PROPERTY "CPPFLAG")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${cppflags}")
	include_directories("${CROSSENGINE_SRC_DIR}/Source/Projects/PrecompiledHeaders")
    include_directories(${CROSSENGINE_SRC_DIR}/BuildingBlocks/EditorRuntime)
	include_directories("${CROSSENGINE_SRC_DIR}/Source")
	include_directories("${CROSSENGINE_SRC_DIR}/Source/External")
	include_directories("${CROSSENGINE_SRC_DIR}/Source/Reflection/include")
    include_directories("${CROSSENGINE_SRC_DIR}/Source/CEGameFramework")
    include_directories("${CROSSENGINE_SRC_DIR}/Source/Scripts")
    include_directories("${CROSSENGINE_SRC_DIR}/Source/GamePlayBaseFramework/logic/visual")
    include_directories("${CROSSENGINE_SRC_DIR}/Source/MaterialBP")
	include_directories(${ENGINE_GENERATED_DIR})
	foreach(item ${dependItems})
		include_directories("${CROSSENGINE_SRC_DIR}/Source/${item}")
		add_dependencies(${PROJECT_NAME} ${item})
		target_link_libraries(${PROJECT_NAME} PUBLIC ${item})
	endforeach()

 foreach(target ${LINK_DIRS})
    message(${target})
	link_directories(${target})
endforeach()
    add_dependencies(${PROJECT_NAME}
        gbf_core
        reflection
        
        package_system
        imod_shared
        blueprint
        virtual_machine
        runtime_resource
        AssetPipeline
        )
    include_directories(${IMGUI_NODE_EDITOR_ROOT_DIR})
    target_link_libraries(${PROJECT_NAME} PUBLIC 
    imguinodeeditor

    gbf_core
    reflection
    imod_shared
    GameFramework
    blueprint
    virtual_machine
    runtime_resource
    physfs-static.lib
    AssetPipeline
    )
else()
add_dependencies(${PROJECT_NAME}
    gbf_core
    reflection
    package_system
    imod_shared
    blueprint
    virtual_machine
    runtime_resource
)
include_directories(${IMGUI_NODE_EDITOR_ROOT_DIR})

target_link_libraries(${PROJECT_NAME} PUBLIC 
    imguinodeeditor
    gbf_core
    reflection
    imod_shared

    blueprint
    virtual_machine
    runtime_resource
)
endif()

add_custom_command(
    TARGET ${PROJECT_NAME}
    PRE_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ARGS ${_ExampleBinDir}/data
)
if(GBF_COMPILE_WITH_CE)
set(CROSSEDITOR_BIN_DIR "${CROSSENGINE_BIN_DIR}")
add_custom_command(
				TARGET ${PROJECT_NAME}
				POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/data/*.png\" \"${CROSSENGINE_BIN_DIR}/$<CONFIG>/Data/\"	
                POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/data/*.ttf\" \"${CROSSENGINE_BIN_DIR}/$<CONFIG>/Data/\"	
                POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/data/*.txt\" \"${CROSSENGINE_BIN_DIR}/$<CONFIG>/Data/\"	
				)
# add_custom_command(
# 				TARGET ${PROJECT_NAME}
# 				POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/data/*.png\" \"${CROSSEDITOR_BIN_DIR}/$<CONFIG>/data/\"	
#                 POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/data/*.ttf\" \"${CROSSEDITOR_BIN_DIR}/$<CONFIG>/data/\"	
#                 POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/data/*.txt\" \"${CROSSEDITOR_BIN_DIR}/$<CONFIG>/data/\"	
# 				)
endif()
SET_PROPERTY(TARGET ${PROJECT_NAME} PROPERTY FOLDER "editor/native/blueprints")
SET_PROPERTY(TARGET ${PROJECT_NAME} PROPERTY MODULE_API_STR "NODE_EDITOR_BRIDGE_API")
