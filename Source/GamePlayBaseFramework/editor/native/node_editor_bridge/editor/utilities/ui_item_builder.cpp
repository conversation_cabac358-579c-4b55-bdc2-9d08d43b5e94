#include "editor/utilities/ui_item_builder.h"

namespace node_editor::utility {

ItemBuilder::ItemBuilder() : m_InCreate(ax::NodeEditor::BeginCreate(ImGui::GetStyleColorVec4(ImGuiCol_NavHighlight))) {}

ItemBuilder::~ItemBuilder() { ax::NodeEditor::EndCreate(); }

ItemBuilder::operator bool() const { return m_InCreate; }

ItemBuilder::NodeBuilder* ItemBuilder::QueryNewNode() {
  if (m_InCreate && ax::NodeEditor::QueryNewNode(&m_NodeBuilder.m_PinId))
    return &m_NodeBuilder;
  else
    return nullptr;
}

ItemBuilder::LinkBuilder* ItemBuilder::QueryNewLink() {
  if (m_InCreate && ax::NodeEditor::QueryNewLink(&m_LinkBuilder.m_StartPinId, &m_LinkBuilder.m_EndPinId))
    return &m_LinkBuilder;
  else
    return nullptr;
}

bool ItemBuilder::LinkBuilder::Accept() { return ax::NodeEditor::AcceptNewItem(ImVec4(0.34f, 1.0f, 0.34f, 1.0f), 3.0f); }

void ItemBuilder::LinkBuilder::Reject() { ax::NodeEditor::RejectNewItem(ImVec4(1.0f, 0.0f, 0.0f, 1.0f)); }

bool ItemBuilder::NodeBuilder::Accept() { return ax::NodeEditor::AcceptNewItem(ImVec4(0.34f, 1.0f, 0.34f, 1.0f), 3.0f); }

void ItemBuilder::NodeBuilder::Reject() { ax::NodeEditor::RejectNewItem(ImVec4(1.0f, 0.0f, 0.0f, 1.0f)); }

}
