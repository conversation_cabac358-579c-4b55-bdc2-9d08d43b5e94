using Clangen.Model;
using DotLiquid;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Clangen
{
    class SerializationGenerator
    {
        private Template fileTemplate;
        private CrossEngineOption CEConfig;
        private HashSet<string> generatedFiles = new HashSet<string>();

        public SerializationGenerator(CrossEngineOption config)
        {
            fileTemplate = Template.Parse("{% include 'Serialization/serialization' %}");
            CEConfig = config;
        }


        public string GenerateRenderContent(IList<ClassModel> classes)
        {
            return fileTemplate.Render(Hash.FromAnonymousObject(new
            {
                includes = new string[] { classes.First().SourceFile },
                classes = classes
            }));
        }
        public static string GetOutputPath(string filePath, ModuleInfo module, CrossEngineOption CEConfig, bool? isCoreModule = null)
        {
            string outputDir;

            bool isCore = module.is_core;
            var baseName = Path.GetFileNameWithoutExtension(filePath);
            var dirName = Path.GetDirectoryName(filePath);
            if (isCore)
            {
                var relativeDir = Path.GetRelativePath(CEConfig.CrossEngineSourceRoot, dirName);
                //outputDir = CEConfig.GeneratedCodeDir + "/Source/" + relativeDir;
                outputDir = CEConfig.GeneratedCodeDir + "/" + relativeDir;
            }
            else
            {
                var relativeDir = Path.GetRelativePath(module.source_dir, dirName);
                //outputDir = CEConfig.GeneratedCodeDir + $"/BuildingBlocks/{module.name}/GeneratedCode/" + relativeDir;
                outputDir = $"{module.source_dir}/GeneratedCode/" + relativeDir;
            }
            return Path.GetFullPath($"{outputDir}/{baseName}.serialization.cpp").Replace('\\', '/');
        }

        public HashSet<string> Generate(AstContent astContent)
        {
            generatedFiles.Clear();
            var groupAst = astContent.GroupByModule(CEConfig.ModuleNeedToBeGenerated);
            foreach (var module in CEConfig.ModuleNeedToBeGenerated)
            {
                if (groupAst.TryGetValue(module.name, out var moduleAst))
                {
                    foreach (var (filePath, ast) in moduleAst.GroupByFile())
                    {
                        var classes = ast.Classes.Where(c => c.NeedSerialization).ToList();
                        if (classes.Count > 0)
                        {
                            var content = GenerateRenderContent(classes);
                            var path = GetOutputPath(filePath, module, CEConfig, module.is_core);

                            FileUtils.WriteTextAfterCompare(path, content);
                            generatedFiles.Add(path);
                        }
                    }
                }
            }
            return generatedFiles;
        }
    }
}