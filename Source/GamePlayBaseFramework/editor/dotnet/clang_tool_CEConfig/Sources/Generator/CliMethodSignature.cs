using Clangen.Model;
using CppAst.General;
using DotLiquid;
using System.Collections.Generic;

namespace Clangen
{
    public enum CallConverterType : int
    {
        Bridge2Native = 0,
        Native2Bridge,
    }

    class CliMethodSignature : Drop
    {
        private static DotLiquid.Template msBridgeHeaderLT;
        private static DotLiquid.Template msBridgeBodyLT;
        private static DotLiquid.Template msBridgeCallExprLT;

        private static DotLiquid.Template msOverrideProxyBodyLT;
        private static DotLiquid.Template msOverrideProxyConstructorBodyLT;
        private static DotLiquid.Template msOverrideProxyCallExprLT;
        public string BridgeCppReturnTypeName
        {
            get
            {
                return CliGenerator.TypeCarryToolCli.ToTypeName(TypeCarryItem.TypeConverterType.BridgeCliReturn, ReturnType); ;
            }
        }
        public bool IsStatic { get; set; }
        public bool IsConstructor { get; set; }
        public string LeafClassName { get; set; }
        public string TargetName { get; set; }
        public string Name { get; set; }
        public string ClassName { get; set; }
        public string NamespaceCook { get; set; }
        public bool IsOverride { get; set; }
        public bool IsComponent { get; set; }
        public bool IsConst { get; set; }
        public bool IsVirtual { get; set; }
        public string ReturnTypeName { get; set; }
        public TypeContentOffer ReturnType;
        public ClassModel ParentDrop { get; set; }
        private List<ParameterModel> Paramlist;
        static CliMethodSignature()
        {
            Template.Parse("{% include 'Cli/cli_class_header' %}");
            msBridgeHeaderLT = Template.Parse("{% include 'Cli/cli_method_header' %}");
            msBridgeBodyLT = Template.Parse("{% include 'Cli/cli_method_body'%}");
            msBridgeCallExprLT = Template.Parse("{% include 'Cli/cli_method_call_expr'%}");

            msOverrideProxyBodyLT = Template.Parse("{% include 'Cli/cli_override_proxy_method_body'%}");
            msOverrideProxyCallExprLT = Template.Parse("{% include 'Cli/cli_override_proxy_method_call_expr'%}");
            msOverrideProxyConstructorBodyLT = Template.Parse("{% include 'Cli/cli_override_proxy_constructor_body'%}");

        }
        public string OverrideDelegateName
        {
            get
            {
                return string.Format("{0}_Native_Proxy", TargetName);
            }
        }
        public CliMethodSignature(FunctionModel model, ClassModel parentDrop, string cppclassname, string namespacecook)
        {
            ParentDrop = parentDrop;
            IsStatic = model.IsStatic;
            IsConstructor = model.IsConstructor;
            LeafClassName = parentDrop.Name;
            ClassName = cppclassname;
            NamespaceCook = namespacecook;
            TargetName = model.Name;
            IsConst = model.IsConst;
            IsComponent = false;
            ReturnType = NativeHelper.NativeType2ContentOffer(model.ReturnType);
            IsOverride = model.IsCliOverride;
            IsVirtual = model.IsVirtual;
            foreach (ParameterModel param in model.Parameters)
            {
                if (NativeHelper.NativeType2ContentOffer(param.Type).LinkListTargetType.IsComponentHandle)
                {
                    IsComponent = true;
                }
            }
            Paramlist = model.Parameters;
            ReturnTypeName = model.ReturnType.FullName;
            Name = model.Name;
        }
        public CliMethodSignature(ConstructorModel model, ClassModel parentDrop, string cppclassname, string namespacecook)
        {
            ParentDrop = parentDrop;
            IsStatic = model.IsStatic;
            IsConstructor = model.IsConstructor;
            LeafClassName = parentDrop.Name;
            ClassName = cppclassname;
            NamespaceCook = namespacecook;
            TargetName = model.Name;
            IsConst = model.IsConst;
            ReturnType = NativeHelper.NativeType2ContentOffer(model.ReturnType);
            Paramlist = model.Parameters;
            Name = model.Name;
        }
        public string BridgeCppParamListString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                int idx = 1;
                foreach (ParameterModel param in Paramlist)
                {
                    if (param.Name == "" || NativeHelper.NativeType2ContentOffer(param.Type).LinkListTargetType.IsDelegate)
                    {
                        param.Name = $"_param_{idx++}";
                    }
                    paramStrList.Add(param.BridgeCppInputTypeName + " " + param.Name);
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }
        public string BridgeCppParamListStringNoVarName
        {
            get
            {
                List<string> paramStrList = new List<string>();
                int idx = 1;
                foreach (ParameterModel param in Paramlist)
                {
                    if (param.Name == "" || NativeHelper.NativeType2ContentOffer(param.Type).LinkListTargetType.IsDelegate)
                    {
                        param.Name = $"_param_{idx++}";
                    }
                    paramStrList.Add(param.BridgeCppInputTypeName + " ");
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }
        public string NativeParamListString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in Paramlist)
                {
                    paramStrList.Add(string.Format("{0} {1}", param.Type.Name, param.Name));
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }
        public string NativeToBridgeParamListNoTypeString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in Paramlist)
                {
                    paramStrList.Add(CliGenerator.TypeCarryToolCli.DoCallCovert(TypeCarryItem.CallConverterType.Native2Bridge, param.Name, NativeHelper.NativeType2ContentOffer(param.Type)));
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string BridgeToNativeParamListNoTypeStringOverrideProxy
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (ParameterModel param in Paramlist)
                {
                    paramStrList.Add(CliGenerator.TypeCarryToolCli.DoCallCovert(TypeCarryItem.CallConverterType.Bridge2Native, param.Name, NativeHelper.NativeType2ContentOffer(param.Type)));
                }
                paramStrList.Add("this");
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }
        public string BridgeToNativeParamListNoTypeString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (ParameterModel param in Paramlist)
                {
                    paramStrList.Add(CliGenerator.TypeCarryToolCli.DoCallCovert(TypeCarryItem.CallConverterType.Bridge2Native, param.Name, NativeHelper.NativeType2ContentOffer(param.Type)));
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }
        public string RenderBridgeHeader
        {
            get
            {
                string result = msBridgeHeaderLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));
                return result;
            }
        }

        public string RenderBridgeBody
        {
            get
            {
                string result = msBridgeBodyLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));
                return result;
            }
        }

        public string RenderOverrideProxyBody
        {
            get
            {
                string result = msOverrideProxyBodyLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));
                return result;
            }
        }

        public string RenderOverrideProxyConstructorBody
        {
            get
            {
                string result = msOverrideProxyConstructorBodyLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));
                return result;
            }
        }

        public string RenderBridgeCallBody
        {
            get
            {
                string callbody = msBridgeCallExprLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));

                if (!ReturnType.IsVoid)
                {
                    callbody = string.Format("return {0}", CliGenerator.TypeCarryToolCli.DoCallCovert(TypeCarryItem.CallConverterType.Native2Bridge, callbody, ReturnType));
                }
                return callbody;
            }
        }

        public string RenderOverrideProxyCallBody
        {
            get
            {
                string callbody = msOverrideProxyCallExprLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));

                if (!ReturnType.IsVoid)
                {
                    callbody = string.Format("return {0}", CliGenerator.TypeCarryToolCli.DoCallCovert(TypeCarryItem.CallConverterType.Native2Bridge, callbody, ReturnType));
                }
                return callbody;
            }
        }

        public string ConstFlag
        {
            get
            {
                if (IsConst)
                {
                    return "const";
                }
                else
                {
                    return "";
                }
            }
        }

    }
}