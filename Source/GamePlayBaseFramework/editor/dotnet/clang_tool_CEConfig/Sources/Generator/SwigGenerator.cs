using Clangen.Model;
using DotLiquid;
using DotLiquid.Util;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;

namespace Clangen
{
    class SwigGenerator
    {
        static int MaxProcessCount = 6;

        private Template fileTemplate;
        private CrossEngineOption CEConfig;
        private Dictionary<string, HashSet<TypeModel>> modelTemplateTypesDict;
        private HashSet<string> generatedModuleSet;
        private HashSet<string> generatedFiles = new HashSet<string>();

        public SwigGenerator(CrossEngineOption config)
        {
            fileTemplate = Template.Parse("{% include 'Swig/swig' %}");
            CEConfig = config;
        }

        public string GenerateRenderContent(ModuleInfo module, AstContent ast)
        {
            //var templateTypeSet = new SortedSet<TypeModel>();
            var typeSet = new HashSet<TypeModel>();
            var includeSet = new HashSet<string>();
            var imports = new List<string>();
            var includes = new List<string>();
            includes.AddRange(ast.Enums.Select(v => v.SourceFile));
            includes.AddRange(ast.Classes.Select(v => v.SourceFile));
            includes.AddRange(ast.Functions.Select(v => v.SourceFile));
            includes.AddRange(ast.Variables.Select(v => v.SourceFile));
            includes.AddRange(ast.Classes.Where(v => v.IsSystem).Select(v => InterfaceGenerator.GetInterfaceHeaderPath(v.SourceFile, module, CEConfig)));
            includes = includes.Distinct().ToList();
            includes.Sort();


            foreach (var dep in module.dependencies)
            {
                if (CEConfig.SwigConfig.TryGetValue(dep, out var filepath))
                {
                    imports.Add(filepath);
                }
            }

            foreach (var dep in module.dependencies)
            {
                if (generatedModuleSet.Contains(dep))
                {
                    var depModule = CEConfig.ModuleInfoDict[dep];
                    imports.Add(GetSwigConfigPath(depModule, CEConfig));
                }
            }
            imports.Sort();

            foreach (var c in ast.Classes)
            {
                typeSet.UnionWith(c.Fields.Where(f => f.NeedBindingEditor).Select(f => f.Type));
                foreach (var f in c.Functions)
                {
                    if (f.NeedBindingEditor)
                    {
                        typeSet.Add(f.ReturnType);
                        typeSet.UnionWith(f.Parameters.Select(p => p.Type));
                    }
                }
                foreach (var ctor in c.Constructors)
                {
                    if (ctor.NeedBindingEditor)
                    {
                        typeSet.UnionWith(ctor.Parameters.Select(p => p.Type));
                    }
                }
            }

            typeSet.UnionWith(ast.Variables.Select(v => v.Type));

            foreach (var f in ast.Functions)
            {
                typeSet.Add(f.ReturnType);
                typeSet.UnionWith(f.Parameters.Select(p => p.Type));
            }

            var delegateTypes = typeSet.Where(t => t.IsDelegate);
            var pointerTypes = typeSet.Where(t => t.IsPointer && !t.IsDelegate && t.WithoutConstRefPtr.FullName != "char");
            var templateTypes = typeSet.Select(t => t.WithoutConstRef).Where(t => t.IsSpecializedTemplate && !t.IsScopedComponent).ToHashSet();

            //var declaredTempalteTypes = new HashSet<TypeModel>();
            //foreach (var dep in module.dependencies)
            //{
            //    if (modelTemplateTypesDict.ContainsKey(dep))
            //        declaredTempalteTypes.UnionWith(modelTemplateTypesDict[dep]);
            //}
            //modelTemplateTypesDict[module.name] = templateTypes.Union(declaredTempalteTypes).ToHashSet();

            //templateTypes.ExceptWith(declaredTempalteTypes);

            return fileTemplate.Render(Hash.FromAnonymousObject(new
            {
                module_name = module.csharp_name,
                imports = imports,
                includes = includes,
                enums = ast.Enums,
                classes = ast.Classes,
                variables = ast.Variables,
                functions = ast.Functions,
                pointer_types = pointerTypes,
                delegate_types = delegateTypes,
                template_types = templateTypes
            }));
        }

        static string GetSwigConfigPath(ModuleInfo module, CrossEngineOption CEConfig)
        {
            string outputDir;
            if (module.is_core || module.is_engine_module)
            {
                //var relativeDir = Path.GetRelativePath(CEConfig.CrossEngineSourceRoot, module.source_dir);
                //outputDir = CEConfig.GeneratedCodeDir + "/" + relativeDir;
                outputDir = CEConfig.CSharpGeneratedDir + "/swig_config";
            }
            else
            {
                outputDir = $"{module.source_dir}/GeneratedCode/";
            }
            return Path.GetFullPath($"{outputDir}/{module.name}.swig").Replace('\\', '/');
        }

        static string GetCppPath(ModuleInfo module, CrossEngineOption CEConfig)
        {
            string outputDir;
            if (module.is_core)
            {
                var relativeDir = Path.GetRelativePath(CEConfig.CrossEngineSourceRoot, module.source_dir);
                outputDir = CEConfig.GeneratedCodeDir + "/" + relativeDir + $"/PInvoke/{module.name}PInvoke.cpp";
            }
            else
            {
                outputDir = $"{module.source_dir}/GeneratedCode/" + $"/PInvoke/{module.name}PInvoke.cpp";
            }
            return Path.GetFullPath(outputDir).Replace('\\', '/');
        }

        static string GetCsharpOutputDir(ModuleInfo module, CrossEngineOption CEConfig)
        {
            string csharpOutputDir;
            if (NeedGenerateToEngine(module, CEConfig))
            {
                csharpOutputDir = CEConfig.CSharpGeneratedDir + "/" + module.name;
            }
            else
            {
                csharpOutputDir = $"{module.source_dir}/ManagedCode/GeneratedCode";
            }
            return Path.GetFullPath(csharpOutputDir).Replace('\\', '/');
        }

        static public bool NeedGenerateToEngine(ModuleInfo module, CrossEngineOption CEConfig)
        {
            if (!CEConfig.WithEngineSource)
                return false;

            if (module.is_core)
                return true;

            // in other case. need module have managed code to general a c# plugin
            var allcsharp = Directory.GetFiles(module.source_dir, "*.cs", SearchOption.AllDirectories);
            int csharpCount = allcsharp.Where(v => !v.Contains("GeneratedCode")).Count();
            if (csharpCount > 0)
                return false;

            return true;
        }

        public HashSet<string> Generate(AstContent astContent)
        {
            generatedFiles.Clear();
            modelTemplateTypesDict = new();
            generatedModuleSet = new();
            var groupAst = astContent.Where(e => e.NeedBindingEditor).GroupByModule(CEConfig.ModuleNeedToBeGenerated);
            var commandList = new List<SwigCsharpCommand>();
            foreach (var module in CEConfig.ModuleNeedToBeGenerated)
            {
                if (groupAst.TryGetValue(module.name, out var ast))
                {
                    bool needExport = ast.Enums.Count + ast.Classes.Count + ast.Variables.Count + ast.Functions.Count > 0;
                    if (needExport)
                    {
                        generatedModuleSet.Add(module.name);

                        string swigConfigPath = GetSwigConfigPath(module, CEConfig);
                        string csharpOutputDir = GetCsharpOutputDir(module, CEConfig);
                        var cppFile = GetCppPath(module, CEConfig);

                        var conent = GenerateRenderContent(module, ast);
                        FileUtils.WriteTextAfterCompare(swigConfigPath, conent);
                        generatedFiles.Add(swigConfigPath);

                        csharpOutputDir += ".temp";
                        if (Directory.Exists(csharpOutputDir))
                            Directory.Delete(csharpOutputDir, true);

                        cppFile = cppFile + ".temp";

                        var cmd = new SwigCsharpCommand(module.name, cppFile, csharpOutputDir, swigConfigPath, CEConfig.SwigToolPath);
                        commandList.Add(cmd);
                    }
                }
            }

            RunSwigCommands(commandList);

            foreach (var moduleName in generatedModuleSet)
            {
                var module = CEConfig.ModuleInfoDict[moduleName];
                var cppFile = GetCppPath(module, CEConfig);
                string csharpOutputDir = GetCsharpOutputDir(module, CEConfig);

                var tmpCppFile = cppFile + ".temp";
                var tmpCSharpOutputDir = csharpOutputDir + ".temp";

                if (!Directory.Exists(csharpOutputDir))
                    Directory.CreateDirectory(csharpOutputDir);

                FileUtils.CopyAfterCompare(tmpCppFile, cppFile);
                generatedFiles.Add(cppFile);
                foreach (string file in Directory.GetFiles(tmpCSharpOutputDir))
                {
                    string dstFile = csharpOutputDir + "/" + Path.GetFileName(file);
                    FileUtils.CopyAfterCompare(file, dstFile);
                    generatedFiles.Add(dstFile);
                }
                File.Delete(tmpCppFile);
                Directory.Delete(tmpCSharpOutputDir, true);
            }

            return generatedFiles;
        }

        bool CheckCppOutputFileValid(string cppOutputFile)
        {
            if (File.Exists(cppOutputFile))
            {
                var lines = File.ReadLines(cppOutputFile).TakeLast(10);
                return lines.Any(line => line.IndexOf("=== cpp end ===") > 0);
            }
            return false;
        }

        void RunSwigCommands(List<SwigCsharpCommand> readyList)
        {
            var runningList = new List<SwigCsharpCommand>();
            var errors = new List<string>();

            int timeout = 30000;
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            bool hasError = false;
            while (runningList.Count > 0 || readyList.Count > 0)
            {
                while (runningList.Count < MaxProcessCount && readyList.Count > 0)
                {
                    var p = readyList.Pop();
                    p.Start();
                    runningList.Add(p);
                }

                Thread.Sleep(100);

                var processList = runningList.Where(p => p.HasExited);
                runningList = runningList.Except(processList).ToList();

                foreach (var p in processList)
                {
                    if (p.ExitCode == 0)
                    {
                        if (!CheckCppOutputFileValid(p.CppOutput))
                        {
                            hasError = true;
                            Log.Error($"[swig] \"{p.CppOutput}\" is not correctly generated.");
                        }
                    }
                    else
                    {
                        errors.AddRange(p.StandardError.Split('\n').Select(v => v.Trim()));
                    }
                }

                if (stopwatch.ElapsedMilliseconds > timeout)
                {
                    foreach (var p in runningList)
                        p.Kill();
                    errors.Insert(0, "swig command timeout");
                    break;
                }
            }
            if (errors.Count() > 0)
            {
                hasError = true;
                errors = errors.Where(e => e != "").Distinct().Take(10).ToList();
                foreach (var e in errors)
                {
                    Log.Error($"[swig] {e}");
                }
            }
            if (hasError)
            {
                throw new Exception("SwigGenerator failed");
            }
        }
    }
}