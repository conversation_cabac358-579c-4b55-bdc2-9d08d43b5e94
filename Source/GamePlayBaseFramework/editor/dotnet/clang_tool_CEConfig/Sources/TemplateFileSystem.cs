using DotLiquid;
using DotLiquid.FileSystems;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace Clangen
{
    internal class CachedTemplateFileSystem : ITemplateFileSystem
    {
        private IDictionary<string, Template> _templateCache = new Dictionary<string, Template>();
        private string _rootDir;
        bool _useEmbeddedTemplates = false;
        public CachedTemplateFileSystem(string rootDir, bool useEmbeddedTemplates)
        {
            _rootDir = rootDir;
            _useEmbeddedTemplates = useEmbeddedTemplates;
        }

        public string ReadTemplateFile(Context context, string templateName)
        {
            string fileContext;
            string templatePath = (string)context[templateName];
            string path = Path.Combine(_rootDir, templatePath) + ".liquid";
            var AppAssembly = Assembly.GetExecutingAssembly();

            if (_useEmbeddedTemplates)
            {
                string embeddedPath = StringUtil.ParseToEmbededPath(path);
                var stream = AppAssembly.GetManifestResourceStream(embeddedPath);
                if (stream == null)
                {
                    throw new FileNotFoundException($"Cannot find embedded template file \"{embeddedPath}\"");
                }
                StreamReader sr = new StreamReader(stream);
                fileContext = sr.ReadToEnd();
            }
            else
            {
                if (!File.Exists(path))
                {
                    throw new FileNotFoundException($"Cannot find template file \"{path}\"");
                }
                fileContext = File.ReadAllText(path);
            }
            return fileContext;
        }

        public Template GetTemplate(Context context, string templateName)
        {
            Template template;
            if (_templateCache.TryGetValue(templateName, out template))
            {
                return template;
            }
            var result = ReadTemplateFile(context, templateName);
            template = Template.Parse(result);
            _templateCache[templateName] = template;
            return template;
        }
    }
}