/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#pragma once
#include "Runtime/Input/Core/InputKeys.h"
#include "{{header_name}}"

namespace gbf { namespace logic {
    
{%- for class in classes -%}{%- for function in class.functions -%}
{%- if function.is_workflow_export == true -%}
// {{function.declaration }}  
{% capture cur_class_name %}UBlueprintNode_{{class.name}}_{{function.name}}{% endcapture %}

void {{cur_class_name}}::InitializeSlotsImpl()
{
{%- if function.is_workflow_executable == true -%}
    // ExecSlot
    AddExecSlot(BlueprintLinkDirection::In, 0, "");
    AddExecSlot(BlueprintLinkDirection::Out, 0, "");{%- endif -%}
    // In data slot
    int _data_slot_index = 0;
    {%- if function.is_static == false -%}
    target_slot_id = _data_slot_index++;
    AddDataSlot(BlueprintLinkDirection::In, target_slot_id, machine::VValueKind::kUser, "Target({{class.name}})", reflection::query_meta_class<{{class.full_name}}>()->id());
    {%- endif -%}
    {%- for param in function.parameters -%}
    {%- capture cur_para_name -%}{{param.type.full_name}} {{param.name}}{%- endcapture -%}
    {%- if param.type.is_string -%}
    {%- capture value_kind -%}machine::VValueKind::kString{%- endcapture -%}
    {%- elsif param.type.is_pointer or param.type.is_reference or param.type.is_class -%}
    {%- capture value_kind -%}machine::VValueKind::kUser{%- endcapture -%}
    {%- elsif param.type.IsBuiltinType -%}
    {%- capture value_kind -%}reflection::CastToValueKind<{{param.type.name}}>(){%- endcapture -%}
    {%- else -%}
    {%- capture value_kind -%}machine::VValueKind::kNone{%- endcapture -%}
    {%- endif -%}
    {%- if param.type.is_string -%}{%- capture meta_class -%} {%- endcapture -%}
    {%- elsif param.type.is_pointer or param.type.is_reference or param.type.is_class -%}
    {%- capture meta_class -%}, reflection::query_meta_class<std::remove_cv_t<std::remove_pointer_t<std::remove_reference_t<{{param.type.full_name}}>>>>()->id(){%- endcapture -%}{%- else -%}
    {%- capture meta_class -%} {%- endcapture -%}
    {%- endif -%}
    
    {{param.name}}_slot_id = _data_slot_index++;
    // {{param.type.full_name}} {{param.name}}  
    AddDataSlot(BlueprintLinkDirection::In, {{param.name}}_slot_id, {{value_kind}}, "{{cur_para_name}}"{{meta_class}});
    {%- endfor -%}
    {%- if function.return_type.is_void == false -%}
    // out data slot
    {%- capture cur_ret_name -%}{{function.return_type.full_name}}{%- endcapture -%}
    {%- if function.return_type.is_string -%}
    {%- capture value_kind -%}machine::VValueKind::kString{%- endcapture -%}
    {%- elsif function.return_type.is_pointer or function.return_type.is_class -%}
    {%- capture value_kind -%}machine::VValueKind::kUser{%- endcapture -%}
    {%- elsif function.return_type.IsBuiltinType -%}
    {%- capture value_kind -%}reflection::CastToValueKind<{{function.return_type.name}}>(){%- endcapture -%}
    {%- else -%}
    {%- capture value_kind -%}machine::VValueKind::kNone{%- endcapture -%}
    {%- endif -%}
    {%- if function.return_type.is_string -%}{%- capture meta_class -%} {%- endcapture -%}
    {%- elsif function.return_type.is_pointer or function.return_type.is_class -%}
    {%- capture meta_class -%}, reflection::query_meta_class<{{function.return_type.full_name | remove: "const " | remove: "*"}}>()->id(){%- endcapture -%}{%- else -%}
    {%- capture meta_class -%} {%- endcapture -%}
    {%- endif -%}
    
    // {{function.return_type.full_name}}
    AddDataSlot(BlueprintLinkDirection::Out, 0, {{value_kind}}, "{{cur_ret_name}}"{{meta_class}});
    {%- endif -%}
}

{%- if function.is_workflow_executable == true -%}
UBlueprintActionNode::ProcessingInfo {{cur_class_name}}::RtActivateLogicImpl(machine::VCoroutine * coroutine_, UBlueprintExecSlot * slot)
{
    machine::MemoryScope* local = coroutine_->GetCurrentMemoryScope();
    machine::NamedMemoryScope* global = coroutine_->GetGlobalMemoryScope();
    UBlueprintActionNode::ProcessingInfo info;
{%-elsif function.is_workflow_pure == true -%}
machine::VValuePtr {{cur_class_name}}::RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global)
{
{%- endif -%}
    // prepare param
    const auto& defaul_value = {%- if class.is_game_object == true -%} global->QueryValue("ThisGameObjectPtr")->Ref<reflection::UserObject>();{%- else -%}reflection::UserObject{};{%- endif -%}
    
    const reflection::UserObject& p = RtTryGetDataSlotValue(BlueprintLinkDirection::In, 0, local, global, defaul_value);
    {{class.full_name}}* Target = {%- if class.is_workflow_struct == true -%}(reflection::get_raw_pointer_user_object<{{class.full_name}}>(p));{%-else -%} dynamic_cast<{{class.full_name}}*>(reflection::get_raw_pointer_user_object<reflection::RttiBase>(p));{%- endif -%}
    
    if (Target == nullptr)
    {
{%- if function.is_workflow_executable == true -%}
        info.State = UBlueprintActionNode::LogicState::Warning;
        info.ErrorMessage = "{{function.name}}: target is not {{class.name}}";
        return info;
{%-elsif function.is_workflow_pure == true -%}        
        auto value_id = slot->GetFullSlotId();
        auto out = local->QueryValue(value_id);
        if (!out)
            out = local->CreateValue(value_id, slot->GetDefaultValue());
        return out;
{%- endif -%}
    }

{%- for param in function.parameters -%}
   using {{param.name}}_type_remove_cv = std::remove_cv_t<{{param.type.full_name}}>;
   using {{param.name}}_type_remove_reference = std::remove_reference_t<{{param.type.full_name}}>;
   using {{param.name}}_type_remove_cv_reference = std::remove_reference_t<std::remove_cv_t<{{param.type.full_name}}>>;
   using {{param.name}}_type_remove_pointer = std::remove_pointer_t<{{param.type.full_name}}>;
   using {{param.name}}_type_remove_cv_pointer = std::remove_cv_t<std::remove_pointer_t<{{param.type.full_name}}>>;
{%- if param.type.is_builtin_type == true -%}
    {{param.type.full_name}} {{param.name}}_default_value = {};
{%- elsif param.type.is_pointer == true-%}
    reflection::UserObject {{param.name}}_default_value = reflection::make_raw_pointer_user_object(nullptr);
{%- else -%}
    reflection::UserObject {{param.name}}_default_value = {};
{%- endif -%}

    auto {{param.name}}_slot_value = RtTryGetDataSlotValue(BlueprintLinkDirection::In, {{param.name}}_slot_id, local, global, {{param.name}}_default_value);
    auto {{param.name}} = 
{%- if param.type.is_pointer -%}
        reflection::get_raw_pointer_user_object<{{param.name}}_type_remove_cv_pointer>({{param.name}}_slot_value);
{%- elsif param.type.is_string -%}
        {{param.name}}_slot_value;
{%- elsif param.type.is_class or param.type.is_reference -%}
        (reflection::get_raw_pointer_user_object<{{param.name}}_type_remove_cv_reference>({{param.name}}_slot_value)) ? 
            *(reflection::get_raw_pointer_user_object<{{param.name}}_type_remove_cv_reference>({{param.name}}_slot_value)) 
            : {{param.name}}_type_remove_cv_reference{};
{%- else -%}
        {{param.name}}_slot_value;
{%- endif -%}
{%- endfor -%}
            
    // call 
    // {{function.declaration }}
    {% if function.return_type.is_void == false %}auto ret = {%- endif -%} Target->{{function.name}}( {%- for param in function.parameters -%} {{param.name}}{%-if param != function.parameters.last -%},{%- endif -%} {%- endfor -%});
{%- if function.return_type.is_void == false -%} 
    auto slot_out = GetDataSlotOut(0);
    auto out_slot_id = slot_out->GetFullSlotId();
    auto out = local->QueryValue(out_slot_id);

    if (!out)
        out = local->CreateValue(out_slot_id, slot_out->GetDefaultValue());

    {%- if function.return_type.is_pointer -%}
    *out = reflection::make_raw_pointer_user_object(ret);
    {%-  elsif function.return_type.is_string -%}
    *out = ret;    
    {%- elsif function.return_type.is_class -%}
    
    auto holder_obj = reflection::__box(std::make_shared<{{function.return_type.full_name}}>(ret));
    const auto& wrapper_obj = reflection::make_raw_pointer_user_object(holder_obj.GetPointer(), std::move(holder_obj));

    *out = wrapper_obj;
    {%- else -%}
    *out = ret;
    {%- endif -%}
{%- endif -%}
    
{%- if function.is_workflow_executable == true -%}
    info.State = UBlueprintActionNode::LogicState::Ok;
    RtActiveOuputLink(coroutine_, 0);
    return info;
{%-elsif function.is_workflow_pure == true -%}
    return out;
{%- endif -%}
}    
{%- endif -%}{%- endfor -%}{%- if class.is_workflow_struct == true -%}{%- if class.is_rtti_class == false -%}
{% capture cur_class_name %}UBlueprintMakeStruct_{{class.full_name | replace: ":", "_"}}{% endcapture %}

{% capture add_member_slots %}
    {%- for field in class.fields -%}
    {%- if field.is_public == true and field.is_readable == true -%}
#if defined(STRUCT_READMEMBER) {%- if field.is_writable == true -%} || defined(STRUCT_WRITEMEMBER) {% else%} && !defined(STRUCT_WRITEMEMBER){% endif %}

    {%- capture cur_para_name -%}{{field.type.full_name}} {{field.name}}{%- endcapture -%}
    {%- if field.type.is_string -%}
    {%- capture value_kind -%}machine::VValueKind::kString{%- endcapture -%}
    {%- elsif field.type.is_pointer or field.type.is_class -%}
    {%- capture value_kind -%}machine::VValueKind::kUser{%- endcapture -%}
    {%- elsif field.type.IsBuiltinType -%}
    {%- capture value_kind -%}reflection::CastToValueKind<{{field.type.name}}>(){%- endcapture -%}
    {%- else -%}
    {%- capture value_kind -%}machine::VValueKind::kNone{%- endcapture -%}
    {%- endif -%}
    {%- if field.type.is_string -%}{%- capture meta_class -%} {%- endcapture -%}
    {%- elsif field.type.is_pointer or field.type.is_class -%}
    {%- capture meta_class -%}, reflection::query_meta_class<{{field.type.full_name | remove: "const " | remove: "*"}}>()->id(){%- endcapture -%}{%- else -%}
    {%- capture meta_class -%} {%- endcapture -%}
    {%- endif -%}
    
    {{field.name}}_slot_id = _data_slot_index++;
    // {{field.type.full_name}} {{field.name}}  
    AddDataSlot(member_slots_dir, {{field.name}}_slot_id, {{value_kind}}, "{{cur_para_name}}"{{meta_class}});
#endif
    {%- endif -%}{%- endfor -%}
{% endcapture %}


{% capture set_member_for_struct %}
    {%- for field in class.fields -%}
    {%- if field.is_public == true and field.is_writable == true -%}
    {%- if field.type.is_builtin_type == true -%}
    {{field.type.full_name}} {{field.name}}_default_value = {};
{%- elsif field.type.is_pointer == true-%}
    reflection::UserObject {{field.name}}_default_value = reflection::make_raw_pointer_user_object(({{field.type.full_name | remove: "const "}})nullptr);
{%- else -%}
    reflection::UserObject {{field.name}}_default_value = {};
{%- endif -%}
    auto {{field.name}}_slot_value = RtTryGetDataSlotValue(BlueprintLinkDirection::In, {{field.name}}_slot_id, local, global, {{field.name}}_default_value);
    val-> {{field.name}} = 
{%- if field.type.is_pointer -%}
        reflection::get_raw_pointer_user_object<{{field.type.full_name | remove: "*"}}>({{field.name}}_slot_value);
{%- elsif field.type.is_string -%}
        {{field.name}}_slot_value;
{%- elsif field.type.is_class -%}
        (reflection::get_raw_pointer_user_object<{{field.type.full_name}}>({{field.name}}_slot_value)) ? 
            *(reflection::get_raw_pointer_user_object<{{field.type.full_name}}>({{field.name}}_slot_value)) 
            : {{field.type.full_name}}{};
{%- else -%}
        {{field.name}}_slot_value;
{%- endif -%}
    {%- endif -%}{%- endfor -%}
{% endcapture %}

//make struct
void {{cur_class_name}}::InitializeSlotsImpl()
{
    // In data slot
    int _data_slot_index = 0;
    const BlueprintLinkDirection member_slots_dir = BlueprintLinkDirection::In;
#ifndef STRUCT_WRITEMEMBER 
#define STRUCT_WRITEMEMBER
    {{add_member_slots}}
#undef STRUCT_WRITEMEMBER
#endif

    AddDataSlot(BlueprintLinkDirection::Out, 0, machine::VValueKind::kUser, "Out Struct", reflection::query_meta_class<{{class.full_name}}>()->id());
}
//make struct
machine::VValuePtr {{cur_class_name}}::RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global)
{
    auto slot_out = GetDataSlotOut(0);
    auto out_slot_id = slot_out->GetFullSlotId();
    auto out = local->QueryValue(out_slot_id);

    if (!out)
        out = local->CreateValue(out_slot_id, slot_out->GetDefaultValue());
    
    auto val = std::make_shared<{{class.full_name}}>();
    {{set_member_for_struct}}

    auto holder_obj = reflection::__box(val);
    const auto& wrapper_obj = reflection::make_raw_pointer_user_object(holder_obj.GetPointer(), std::move(holder_obj));

    *out = wrapper_obj;
    return out;
}

{% capture cur_class_name %}UBlueprintBreakStruct_{{class.full_name | replace: ":", "_"}}{% endcapture %}
void {{cur_class_name}}::InitializeSlotsImpl()
{
    // In data slot
    // in struct ref
    AddDataSlot(BlueprintLinkDirection::In, 0, machine::VValueKind::kUser, "In Struct Ref", reflection::query_meta_class<{{class.full_name}}>()->id());
    int _data_slot_index = 0;
    const BlueprintLinkDirection member_slots_dir = BlueprintLinkDirection::Out;
#ifndef STRUCT_READMEMBER 
#define STRUCT_READMEMBER
    {{add_member_slots}}
#undef STRUCT_READMEMBER
#endif
}
machine::VValuePtr {{cur_class_name}}::RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global)
{
    reflection::UserObject in_struct_default_value = {};
    const reflection::UserObject& in_struct_obj = RtTryGetDataSlotValue(BlueprintLinkDirection::In, 0, local, global, in_struct_default_value);
    {{class.full_name}}* in_struct_ptr = reflection::get_raw_pointer_user_object<{{class.full_name}}>(in_struct_obj);

    {{class.full_name}} default_struct_value = {};
    if (in_struct_ptr == nullptr)
    {
        in_struct_ptr = &default_struct_value;
    }

    auto target_full_slot_id = slot->GetFullSlotId();

    {%- for field in class.fields -%}
    {%- if field.is_public == true and field.is_readable == true -%}
    {
        auto slot_out = GetDataSlotOut({{field.name}}_slot_id);
        auto out_slot_id = slot_out->GetFullSlotId();
        if(target_full_slot_id == out_slot_id)
        {
            auto out = local->QueryValue(out_slot_id);

            if (!out)
                out = local->CreateValue(out_slot_id, slot_out->GetDefaultValue());
                
            {%- if field.type.is_pointer -%}
            *out = reflection::make_raw_pointer_user_object(in_struct_ptr->{{field.name}});
            {%-  elsif field.type.is_string -%}
            *out = in_struct_ptr->{{field.name}};    
            {%- elsif field.type.is_class -%}

            auto holder_obj = reflection::__box(std::make_shared<{{field.type.full_name}}>(in_struct_ptr->{{field.name}}));
            const auto& wrapper_obj = reflection::make_raw_pointer_user_object(holder_obj.GetPointer(), std::move(holder_obj));

            *out = wrapper_obj;
            {%- else -%}
            *out = in_struct_ptr->{{field.name}};
            {%- endif -%}
            return out;
        }
    }
    {%- endif -%}{%- endfor -%}

    return nullptr;
}

{% capture cur_class_name %}UBlueprintSetMemberIn_{{class.full_name | replace: ":", "_"}}{% endcapture %}
void {{cur_class_name}}::InitializeSlotsImpl()
{
    // ExecSlot
    AddExecSlot(BlueprintLinkDirection::In, 0, "");
    AddExecSlot(BlueprintLinkDirection::Out, 0, "");

    // In data slot
    int _data_slot_index = 0;
    const BlueprintLinkDirection member_slots_dir = BlueprintLinkDirection::In;
    
    // in struct ref
    AddDataSlot(BlueprintLinkDirection::In, _data_slot_index++, machine::VValueKind::kUser, "In Struct Ref", reflection::query_meta_class<{{class.full_name}}>()->id());
#ifndef STRUCT_WRITEMEMBER 
#define STRUCT_WRITEMEMBER
    {{add_member_slots}}
#undef STRUCT_WRITEMEMBER
#endif
    // out
    AddDataSlot(BlueprintLinkDirection::Out, 0, machine::VValueKind::kUser, "Out Struct", reflection::query_meta_class<{{class.full_name}}>()->id());
}

gbf::logic::UBlueprintActionNode::ProcessingInfo {{cur_class_name}}::RtActivateLogicImpl(gbf::machine::VCoroutine* coroutine_, gbf::logic::UBlueprintExecSlot* slot)
{
    machine::MemoryScope* local = coroutine_->GetCurrentMemoryScope();
    machine::NamedMemoryScope* global = coroutine_->GetGlobalMemoryScope();
    UBlueprintActionNode::ProcessingInfo info;

    reflection::UserObject in_struct_default_value = {};
    const reflection::UserObject& in_struct_obj = RtTryGetDataSlotValue(BlueprintLinkDirection::In, 0, local, global, in_struct_default_value);
    {{class.full_name}}* in_struct_ptr = reflection::get_raw_pointer_user_object<{{class.full_name}}>(in_struct_obj);

    if (in_struct_ptr == nullptr)
    {
        info.State = UBlueprintActionNode::LogicState::Warning;
        info.ErrorMessage = "SetMemberIn {{class.name}} : struct ref is not {{class.name}}";
        return info;
    }

    auto val = in_struct_ptr;
    {{set_member_for_struct}}
    
    auto slot_out = GetDataSlotOut(0);
    auto out_slot_id = slot_out->GetFullSlotId();
    auto out = local->QueryValue(out_slot_id);

    if (!out)
        out = local->CreateValue(out_slot_id, slot_out->GetDefaultValue());

    auto holder_obj = reflection::__box(std::make_shared<{{class.full_name}}>(*in_struct_ptr));
    const auto& wrapper_obj = reflection::make_raw_pointer_user_object(holder_obj.GetPointer(), std::move(holder_obj));
    
    *out = wrapper_obj;

    info.State = UBlueprintActionNode::LogicState::Ok;
    RtActiveOuputLink(coroutine_, 0);
    return info;

}
{%- endif -%}
{%- endif -%}

{%- endfor -%}
}}