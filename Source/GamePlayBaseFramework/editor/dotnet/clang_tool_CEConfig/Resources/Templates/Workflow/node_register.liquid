/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/

#include "EnginePrefix.h"

{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

namespace cegf
{

void {{module_name}}_RegisterNode()
{
    if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpTypeCrossEditor)
    {
{%- for class in classes -%}{%- for function in class.functions -%}
{%- if function.is_workflow_export == true -%}
            cross::WorkflowNodeRegistry::GetInstance().RegisterNode<gbf::logic::UBlueprintNode_{{class.name}}_{{function.name}}>("{{class.name}}::{{function.name}}", "{{class.name}}");
{%- endif -%}
{%- endfor -%}
            cross::WorkflowNodeRegistry::GetInstance().RegisterNode<gbf::logic::UBlueprintNode_CastTo{{class.name}}>("Cast To {{class.name}}", "Cast");
            cross::WorkflowNodeRegistry::GetInstance().RegisterNode<gbf::logic::UBlueprintNode_GetClass_{{class.name}}>("Get Class of {{class.name}}", "Class");
{%- if class.is_workflow_struct == true -%}
{%- if class.is_rtti_class == false -%}
            cross::WorkflowNodeRegistry::GetInstance().RegisterNode<gbf::logic::UBlueprintMakeStruct_{{class.full_name | replace: ":", "_"}}>("Make Struct Of {{class.name}}", "Struct");
            cross::WorkflowNodeRegistry::GetInstance().RegisterNode<gbf::logic::UBlueprintBreakStruct_{{class.full_name | replace: ":", "_"}}>("Break Struct Of {{class.name}}", "Struct");
            cross::WorkflowNodeRegistry::GetInstance().RegisterNode<gbf::logic::UBlueprintSetMemberIn_{{class.full_name | replace: ":", "_"}}>("Set Member In {{class.name}}", "Struct");
{%- endif -%}
{%- endif -%}
{%- endfor -%}
    }
}

}