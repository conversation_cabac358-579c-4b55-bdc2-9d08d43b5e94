using Irony.Parsing;
using System;
using System.Collections.Generic;

namespace CppAst
{

    public class NamedParameterParser
    {
        #region Embeded Types
        public static class TerminalNames
        {
            public const string Identifier = "identifier",
            Number = "number",
            String = "string",
            Boolean = "boolean",
            Comma = ",",
            Equal = "=",
            Expression = "expression",
            Assignment = "assignment",
            LoopPair = "loop_pair",
            NamedArguments = "named_arguments";
        }

        [Language("NamedParameter.CppAst", "0.1", "Grammer for named parameter")]
        public class NamedParameterGrammer : Irony.Parsing.Grammar
        {
            static NamedParameterGrammer sGrammer;
            public static NamedParameterGrammer Instance
            {
                get
                {
                    if (sGrammer == null)
                    {
                        sGrammer = new NamedParameterGrammer();
                    }
                    return sGrammer;
                }
            }

            private NamedParameterGrammer() :
                base(true)
            {
                #region Declare Terminals Here
                NumberLiteral NUMBER = CreateNumberLiteral(TerminalNames.Number);
                StringLiteral STRING_LITERAL = new StringLiteral(TerminalNames.String, "\"", StringOptions.AllowsAllEscapes);
                IdentifierTerminal Name = new IdentifierTerminal(TerminalNames.Identifier);


                //  Regular Operators
                var COMMA = ToTerm(TerminalNames.Comma);
                var EQUAL = ToTerm(TerminalNames.Equal);

                #region Keywords

                var TRUE_KEYWORD = Keyword("true");
                var FALSE_KEYWORD = Keyword("false");



                //var CLASS = Keyword("class");
                //var NEW = Keyword("new");
                #endregion



                #endregion

                #region Declare NonTerminals Here
                NonTerminal BOOLEAN = new NonTerminal(TerminalNames.Boolean);
                NonTerminal EXPRESSION = new NonTerminal(TerminalNames.Expression);
                NonTerminal ASSIGNMENT = new NonTerminal(TerminalNames.Assignment);
                NonTerminal NAMED_ARGUMENTS = new NonTerminal(TerminalNames.NamedArguments);
                NonTerminal LOOP_PAIR = new NonTerminal(TerminalNames.LoopPair);
                #endregion

                #region Place Rules Here
                ////NORMAL_RECORD.Rule = Name + FIELD_FETCH;

                BOOLEAN.Rule = TRUE_KEYWORD | FALSE_KEYWORD;
                EXPRESSION.Rule = BOOLEAN | NUMBER | STRING_LITERAL;
                ASSIGNMENT.Rule = Name + EQUAL + EXPRESSION;
                LOOP_PAIR.Rule = MakeStarRule(COMMA + ASSIGNMENT);
                NAMED_ARGUMENTS.Rule = ASSIGNMENT + LOOP_PAIR;

                this.Root = NAMED_ARGUMENTS;
                #endregion

                #region Define Keywords and Register Symbols
                ////this.RegisterBracePair("[", "]");

                ////this.MarkPunctuation(",", ";");
                #endregion
            }

            //Must create new overrides here in order to support the "Operator" token color
            public new void RegisterOperators(int precedence, params string[] opSymbols)
            {
                RegisterOperators(precedence, Associativity.Left, opSymbols);
            }

            //Must create new overrides here in order to support the "Operator" token color
            public new void RegisterOperators(int precedence, Associativity associativity, params string[] opSymbols)
            {
                foreach (string op in opSymbols)
                {
                    KeyTerm opSymbol = Operator(op);
                    opSymbol.Precedence = precedence;
                    opSymbol.Associativity = associativity;
                }
            }

            BnfExpression MakeStarRule(BnfTerm term)
            {
                return MakeStarRule(new NonTerminal(term.Name + "*"), term);
            }

            public KeyTerm Keyword(string keyword)
            {
                var term = ToTerm(keyword);
                // term.SetOption(TermOptions.IsKeyword, true);
                // term.SetOption(TermOptions.IsReservedWord, true);

                this.MarkReservedWords(keyword);
                term.EditorInfo = new TokenEditorInfo(TokenType.Keyword, TokenColor.Keyword, TokenTriggers.None);

                return term;
            }

            public KeyTerm Operator(string op)
            {
                string opCased = this.CaseSensitive ? op : op.ToLower();
                var term = new KeyTerm(opCased, op);
                //term.SetOption(TermOptions.IsOperator, true);

                term.EditorInfo = new TokenEditorInfo(TokenType.Operator, TokenColor.Keyword, TokenTriggers.None);

                return term;
            }

            protected static NumberLiteral CreateNumberLiteral(string name)
            {

                NumberLiteral term = new NumberLiteral(name, NumberOptions.IntOnly);
                //default int types are Integer (32bit) -> LongInteger (BigInt); Try Int64 before BigInt: Better performance?
                term.DefaultIntTypes = new TypeCode[] { TypeCode.Int32 };
                term.DefaultFloatType = TypeCode.Double; // it is default
                                                         ////term.AddPrefix("0x", NumberOptions.Hex);

                return term;
            }
        }

        #endregion


        public static bool ParseNamedParameters(string content, Dictionary<string, object> outNamedParameterDic, out string errorMessage)
        {
            errorMessage = "";

            if (string.IsNullOrWhiteSpace(content))
            {
                return true;
            }

            Irony.Parsing.Parser parser = new Irony.Parsing.Parser(NamedParameterGrammer.Instance);
            var ast = parser.Parse(content);

            if (!ast.HasErrors())
            {
                ParseAssignment(ast.Root.ChildNodes[0], outNamedParameterDic);

                if (ast.Root.ChildNodes.Count >= 2 && ast.Root.ChildNodes[1].ChildNodes.Count > 0)
                {
                    ParseLoopItem(ast.Root.ChildNodes[1].ChildNodes[0], outNamedParameterDic);
                }

                return true;
            }
            else
            {
                errorMessage = ast.ParserMessages.ToString();
            }

            return false;
        }


        private static object ParseExpressionValue(ParseTreeNode node)
        {
            if (node.Term.Name == TerminalNames.String)
            {
                return node.Token.ValueString;
            }
            else if (node.Term.Name == TerminalNames.Boolean)
            {
                if (node.ChildNodes[0].Term.Name == "false")
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else if (node.Term.Name == TerminalNames.Number)
            {
                return node.Token.Value;
            }
            else
            {
                throw new Exception("Can not run to here!");
            }
        }

        private static void ParseAssignment(ParseTreeNode node, Dictionary<string, object> outNamedParameterDic)
        {
            string varName = node.ChildNodes[0].Token.ValueString;
            if (!outNamedParameterDic.ContainsKey(varName))
            {
                outNamedParameterDic.Add(varName, ParseExpressionValue(node.ChildNodes[2].ChildNodes[0]));
            }
        }

        private static void ParseLoopItem(Irony.Parsing.ParseTreeNode loopNode, Dictionary<string, object> outNamedParameterDic)
        {
            ParseAssignment(loopNode.ChildNodes[1], outNamedParameterDic);

            for (int i = 2; i < loopNode.ChildNodes.Count; i++)
            {
                var newLoopNode = loopNode.ChildNodes[i];
                ParseAssignment(newLoopNode.ChildNodes[1], outNamedParameterDic);
            }
        }
    }
}
