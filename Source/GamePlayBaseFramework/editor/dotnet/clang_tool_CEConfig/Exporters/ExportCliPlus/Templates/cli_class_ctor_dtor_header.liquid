//constructor export here
public:
{%- if this_class.is_override -%}
    {%- if this_class.has_constructor -%}
        {%- for sig in this_class.constructor.signatures -%}
{{ sig.render_bridge_header }}
        {%- endfor -%}
    {%- elsif this_class.has_default_constructor -%}
    {{ this_class.unscoped_name }}();
    {%- endif -%}
{%- else -%}
    {%- unless this_class.is_abstract -%}
        {%- if this_class.has_constructor -%}
            {%- for sig in this_class.constructor.signatures -%}
{{ sig.render_bridge_header }}
            {%- endfor -%}
        {%- elsif this_class.has_default_constructor -%}
    {{ this_class.unscoped_name }}();
        {%- endif -%}
    {%- endunless -%}
{%- endif -%}
