using System.Collections.Generic;

namespace CppAst.CliPlus
{
    public class CliProjectInfo : DotLiquid.Drop
    {
        public string ManagedTargetName { get; set; }
        public string ExtIncludeFile { get; set; }
        public bool EnableGcObject { get; set; }
        public bool GenerateCMakeFiles { get; set; }
        public bool GenerateCommonFiles { get; set; }
        public List<string> CmakeIncludePaths { get; private set; } = new List<string>();
        public List<string> CmakeLibPaths { get; private set; } = new List<string>();
        public List<string> CmakeLinkLibNames { get; private set; } = new List<string>();
        public List<string> CmakeDependencies { get; private set; } = new List<string>();
    }

    public class Config : General.Config
    {
        public CliProjectInfo CliProject { get; private set; } = new CliProjectInfo();

        public Config() :
            base()
        {
            mDefaultRetargetMethodNameMap.Add("operator=", "__assign");
            mDefaultRetargetMethodNameMap.Add("operator+", "__add");
            mDefaultRetargetMethodNameMap.Add("operator-", "__sub");
            mDefaultRetargetMethodNameMap.Add("operator*", "__mul");
            mDefaultRetargetMethodNameMap.Add("operator/", "__div");
            mDefaultRetargetMethodNameMap.Add("operator<", "__lt");
            mDefaultRetargetMethodNameMap.Add("operator<=", "__le");
            mDefaultRetargetMethodNameMap.Add("operator==", "__eq");
            mDefaultRetargetMethodNameMap.Add("operator>", "__gt");
            mDefaultRetargetMethodNameMap.Add("operator*=", "__mul_eq");
            mDefaultRetargetMethodNameMap.Add("operator/=", "__div_eq");
            mDefaultRetargetMethodNameMap.Add("operator+=", "__add_eq");
            mDefaultRetargetMethodNameMap.Add("operator-=", "__sub_eq");
            // TODO ... To complete all META-method mapping

            mDefaultIgnoreMethodList.Add("operator[]");
            mDefaultIgnoreMethodList.Add("operator!=");

            DefaultNamespace = "";
        }

    }
}
