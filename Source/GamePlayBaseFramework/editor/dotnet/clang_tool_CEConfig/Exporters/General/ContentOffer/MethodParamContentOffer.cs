using System.Collections.Generic;

namespace CppAst.General
{
    public class MethodParamContentOffer
    {
        public string ParamName
        {
            get;
            private set;
        }

        public string CookFullTypeName
        {
            get;
            private set;
        }

        public string FullTypeName
        {
            get;
            private set;
        }

        public TypeContentOffer ParamType
        {
            get;
            private set;
        }

        public MethodParamContentOffer(CppCompilation builder, CppType paramType, string paramName, Dictionary<string, CppTemplateArgument> allTempTypeArgMap)
        {

            if (allTempTypeArgMap == null)
            {
                ParamType = NativeHelper.NativeType2ContentOffer(builder, paramType);
            }
            else
            {
                ParamType = NativeHelper.DeduceTemplateType(builder, paramType, allTempTypeArgMap);
            }

            ParamName = paramName;

            FullTypeName = ParamType.FullCppName;
            CookFullTypeName = Clangen.NamespaceTool.CppNamespaceToCookNamespace(FullTypeName);
        }
    }
}
