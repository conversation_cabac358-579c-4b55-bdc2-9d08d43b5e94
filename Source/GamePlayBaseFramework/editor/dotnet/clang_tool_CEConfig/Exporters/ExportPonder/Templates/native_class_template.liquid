// tmplate class "{{ this_class.unscoped_name }}" export start
template<{{this_class.template_param_list_string}}>
static void ponder_register_template_class_{{ this_class.identify_name }}(const char* exportName)
{
{%- if this_class.is_base_class_template -%}  
    std::string subClassName = exportName;
    subClassName.append("_");
    subClassName.append("{{this_class.base_class_name}}");
    ponder_register_template_class_{{this_class.base_class_identify_name}}(subClassName.c_str());
{%- endif -%}

    __register_type<{{ this_class.name_with_template_arg }}>(exportName)
{% comment %} class export start {% endcomment %}
    {%- if this_class.has_base_class -%}
        .base<{{this_class.base_class_name}}>()
    {%- endif -%}
        //member fields export here
    {%- for field in this_class.fields -%}
        {{ field.render }}
    {%- endfor -%}
        //member properties export here
    {%- for prop in this_class.properties -%}
        {{ prop.render }}
    {%- endfor -%}
    {% comment %}
        //embeded enum values export here
    {%- for enum in this_class.enums -%}
{{ enum.render }}
    {%- endfor -%}
    {% endcomment %}
        //constructor export here
    {%- unless this_class.is_abstract -%}
        {%- if this_class.has_constructor -%}
{{ this_class.constructor.render_constructor }}
        {%- elsif this_class.has_default_constructor -%}
        .constructor()
        {%- endif -%}
    {%- endunless -%}
        //methods export here
    {%- for method in this_class.methods -%}
        {{ method.render }}
    {%- endfor -%}
    ;
}
