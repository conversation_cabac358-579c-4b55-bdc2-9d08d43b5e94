//code generate from clangen.mono

#include "{{ out_file_name }}.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************
{%- for class in override_classes -%}
{%- unless class.need_ignore -%}
{%- if class.is_override -%}
{{ class.render_override_proxy_body }}
{%- endif -%}
{%- endunless -%}
{%- endfor -%}

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
{%- for class in classes -%}
{%- unless class.need_ignore -%}
{{ class.render_bridge_body }}
{%- endunless -%}
{%- endfor -%}

//***************************************************************
//**********All class template instances export here*************
//***************************************************************
{%- for class in template_instance_list -%}
{{ class.render_bridge_body }}
{%- endfor -%}

//***************************************************************
//**********All stl container export here************************
//***************************************************************
{%- for class in stl_containers -%}
{{ class.render_body }}
{%- endfor -%}

//***************************************************************
//**********All override class export here***********************
//***************************************************************
{%- for override_class in override_class_list -%}
{{ override_class.render_bridge_body }};
{%- endfor -%}


