namespace CppAst.Pyra
{
    public class ClassTemplateInstanceDrop : General.ClassTemplateInstanceDrop
    {
        private static DotLiquid.Template msBridgeBodyLT;

        static ClassTemplateInstanceDrop()
        {
            msBridgeBodyLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportPyra/Templates/native_class_template_instance.liquid");
        }

        public ClassTemplateInstanceDrop(CppClass specializationNode, Exporter parentExporter) :
            base(specializationNode, parentExporter)
        {
        }

        public string Render
        {
            get
            {
                string result = msBridgeBodyLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    this_class = this,
                }));
                return result;
            }
        }
    }
}
