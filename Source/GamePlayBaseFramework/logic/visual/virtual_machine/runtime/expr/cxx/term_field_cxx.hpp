#pragma once

#include "visual/virtual_machine/runtime/expr/iterm_field.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/string/conststring.h"
#include "reflection/meta/meta_class.hpp"

namespace gbf {
namespace machine {

class VIRTUAL_MACHINE_API TermFieldCxx : public ITermField {
 public:
  TermFieldCxx(){
    term_type_ = VMTermType::Field;
  };
  ~TermFieldCxx();
  void Init();

  std::string_view class_name() const noexcept { return class_name_; }
  void set_class_name(std::string_view c_name) noexcept { class_name_ = c_name; }

  std::string_view field_name() const noexcept { return field_name_; }
  void set_field_name(std::string_view f_name) noexcept { field_name_ = f_name; }

  VValue Evaluate(const VObject& obj) override;
  void Assign(const VObject& obj, const VValue& val) override;

  void SerializeToJson(IVMStreamWriter& writer) override;
  void DeserializeFromJson(IVMStreamReadNode& node) override;

  static void __meta_auto_register();
 protected:
   ////const reflection::MetaClass*   meta_class_ = nullptr;
   std::string                    class_name_ = "unknown";
   std::string                    field_name_ = "unknown";
   bool                           is_static_ = false;

   const reflection::Property* meta_property_ = nullptr;
   ////const reflection::Function* static_property_func_ = nullptr;
};

}  // namespace machine
}  // namespace gbf