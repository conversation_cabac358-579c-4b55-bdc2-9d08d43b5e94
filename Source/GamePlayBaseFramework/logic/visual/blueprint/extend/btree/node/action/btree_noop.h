#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/extend/btree/node/action/btree_action_base.h"

#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf {
namespace logic {
class BLUEPRINT_API UBtreeNoop : public UBtreeActionBase {
  using base = UBtreeActionBase;

 public:
  UBtreeNoop();
  ~UBtreeNoop();

  machine::VMRunStepStatus UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) override;

 protected:
 public:  
  static void __meta_auto_register() {
    __register_cxx_type<UBtreeNoop>()
        .base<UBtreeActionBase>()
        .constructor<>();
  }              
};
}  // namespace logic
}  // namespace gbf
