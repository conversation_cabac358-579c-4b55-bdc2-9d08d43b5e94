#include "visual/blueprint/extend/btree/node/condition/btree_condition.h"
#include "visual/virtual_machine/runtime/expr/evaluate_unit.hpp"
#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
// #include "visual/virtual_machine/extend/vmvalue.h"
#include "visual/virtual_machine/runtime/vscheduler.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {
namespace logic {
UBtreeCondition::UBtreeCondition() { m_title = "Condition"; }
UBtreeCondition::~UBtreeCondition() {}

machine::VMRunStepStatus UBtreeCondition::UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) {;
  auto evl_group = machine::EvaluateUnit::GetEvaluateGroup(m_operator_type);
  // compute
  if (evl_group == machine::VMEvalGroupType::MathCompare || evl_group == machine::VMEvalGroupType::NormalComapre) {
    if (m_operator_left  && m_operator_right ) {
      auto lval = m_operator_left.Evaluate(coroutine_, obj);
      auto rval = m_operator_right.Evaluate(coroutine_, obj);
      auto ret_val = machine::EvaluateUnit::DoOperation(m_operator_type, lval, rval);
      bool result = reflection::value_cast<bool>(ret_val);
      return result ? machine::VMRunStepStatus::Succeed : machine::VMRunStepStatus::Failed;
    }
  }

  assert(false);
  return machine::VMRunStepStatus::Failed;
}

void UBtreeCondition::SerializeToJson(machine::IVMStreamWriter& writer) {
  base::SerializeToJson(writer);
  writer.AddIntProperty("operator_type", (int)m_operator_type);

  if (m_operator_left) {
    writer.StartObject("operator_left");
    m_operator_left.SerializeToJson(writer);
    writer.EndObject();
  }

  if (m_operator_right) {
    writer.StartObject("m_operator_right");
    m_operator_right.SerializeToJson(writer);
    writer.EndObject();
  }
}
void UBtreeCondition::DeserializeFromJson(machine::IVMStreamReadNode& node) {
  base::DeserializeFromJson(node);
  m_operator_type = (machine::VMOperatorType)node.GetIntProperty("operator_type");

  auto* value = node.GetNode("operator_left");
  if (value != nullptr && value->IsObject()) {
    m_operator_left.DeserializeFromJson(*value);
  }

  value = node.GetNode("m_operator_right");
  if (value != nullptr && value->IsObject()) {
    m_operator_right.DeserializeFromJson(*value);
  }
}
}  // namespace logic
}  // namespace gbf
