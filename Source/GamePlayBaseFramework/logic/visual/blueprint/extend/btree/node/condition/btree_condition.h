#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/extend/btree/btree_fwd.h"
#include "visual/blueprint/extend/btree/node/condition/btree_condition_base.h"
#include "visual/blueprint/extend/btree/node/action/btree_action_base.h"

// #include "visual/virtual_machine/gc/gc.h"
#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace logic {
////class BTOperator;
class BLUEPRINT_API UBtreeCondition : public UBtreeConditionBase {
  using base = UBtreeConditionBase;

 public:
  UBtreeCondition();
  ~UBtreeCondition();

  void set_operator_left(const UBtreeValue& term) noexcept { m_operator_left = term; }
  const UBtreeValue& get_operator_left() const noexcept { return m_operator_left; }

  void set_operator_right(const UBtreeValue& term) noexcept { m_operator_right = term; }
  const UBtreeValue& get_operator_right() const noexcept { return m_operator_right; }

  void set_operator_type(machine::VMOperatorType type) noexcept { m_operator_type = type; }
  machine::VMOperatorType get_operator_type(machine::VMOperatorType type) const noexcept { return m_operator_type; }



  machine::VMRunStepStatus UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) override;
 protected:
  void SerializeToJson(machine::IVMStreamWriter& writer) override;
  void DeserializeFromJson(machine::IVMStreamReadNode& node) override;

 protected:
  machine::VMOperatorType m_operator_type = machine::VMOperatorType::Invalid;

  UBtreeValue m_operator_left;
  UBtreeValue m_operator_right;
 public:
  static void __meta_auto_register() {
    __register_cxx_type<UBtreeCondition>()
        .base<UBtreeConditionBase>()
        .constructor<>();
  }  
};

}  // namespace logic
}  // namespace gbf
