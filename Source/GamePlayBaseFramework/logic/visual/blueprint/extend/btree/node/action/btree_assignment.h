#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/extend/btree/node/action/btree_action_base.h"

#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "visual/virtual_machine/runtime/expr/iterm_field.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace logic {
class BLUEPRINT_API UBtreeAssignment : public UBtreeActionBase {
  using base = UBtreeActionBase;

 public:
  UBtreeAssignment();
  ~UBtreeAssignment();

  bool is_use_cast() const noexcept { return m_use_cast; }

  void set_use_cast(bool use_cast) noexcept { m_use_cast = use_cast; }

  void set_operator_left(const UBtreeValue& new_arg) noexcept { m_operator_left = new_arg; }

  const UBtreeValue& get_operator_left() const noexcept { return m_operator_left; }

  void set_operator_right(const UBtreeValue& new_arg) noexcept { m_operator_right = new_arg; }

  const UBtreeValue& get_operator_right() const noexcept { return m_operator_right; }



  machine::VMRunStepStatus UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) override;
 protected:
  void SerializeToJson(machine::IVMStreamWriter& writer) override;

  void DeserializeFromJson(machine::IVMStreamReadNode& node) override;
 protected:
  bool m_use_cast = false;
  UBtreeValue m_operator_left;
  UBtreeValue m_operator_right;
 public:  
  static void __meta_auto_register() {
    __register_cxx_type<UBtreeAssignment>()
        .base<UBtreeActionBase>()
        .constructor<>();
  }                    
};


}  // namespace logic
}  // namespace gbf
