#include "visual/blueprint/details/node/blueprint_event_node.h"
#include "visual/blueprint/details/blueprint_instruction_impl.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "reflection/objects/make_user_object.hpp"

namespace gbf { namespace logic {
    UBlueprintEventNode::UBlueprintEventNode(std::string_view event_name, std::string_view title)
        : UBlueprintNode(BlueprintNodeType::Event, BlueprintSlotAvailableFlag::NodeOutVarOut, title)
        , m_event_name(event_name)
    {}

    void UBlueprintEventNode::RtTriggered(machine::VCoroutine* coroutine, size_t out_slot_index, const BlueprintEventParamList& param)
    {
        RtTriggerImpl(param, coroutine);
        RtActiveOuputLink(coroutine, out_slot_index);
        // coroutine->resume_yield(false);
    }

    void UBlueprintEventNode::RtPushNodeAsInstruction(machine::VCoroutine* coroutine, size_t out_slot_index, const BlueprintEventParamList& param_list)
    {
        auto ins = reflection::make_shared_with_rtti<NGEventInstruction>(coroutine, this, param_list, out_slot_index);
        coroutine->PushInstruction(ins);
        coroutine->ResumeYield(false);
    }
    void UBlueprintEventNode::SerializeToJson(machine::IVMStreamWriter & writer)
    {
        UBlueprintNode::SerializeToJson(writer);
        writer.AddStringProperty("event_name", m_event_name);
    }

    void UBlueprintEventNode::DeserializeFields(machine::IVMStreamReadNode & node)
    {
        UBlueprintNode::DeserializeFields(node);
        auto cur_event = node.GetStringProperty("event_name");
        set_event_name(cur_event);
    }
}}   // namespace gbf::logic
