#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_action_node.h"
// #include "visual/virtual_machine/extend/vmvalue.h"
#include "visual/virtual_machine/runtime/memory_scope.hpp"
#include "visual/virtual_machine/runtime/named_memory_scope.hpp"

namespace gbf { namespace logic {

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintMetaCastNode : public UBlueprintActionNode
    {
    public:
    public:
        enum CustomSlotId : uint32_t
        {
            In = NVM_GEN_C_SLOT_ID(BlueprintSlotType::Exec, BlueprintLinkDirection::In, 0),
            OutTrue = NVM_GEN_C_SLOT_ID(BlueprintSlotType::Exec, BlueprintLinkDirection::Out, 0),
            OutFalse = NVM_GEN_C_SLOT_ID(BlueprintSlotType::Exec, BlueprintLinkDirection::Out, 1),
            InVar = NVM_GEN_C_SLOT_ID(BlueprintSlotType::Data, BlueprintLinkDirection::In, 0),
            OutVar = NVM_GEN_C_SLOT_ID(BlueprintSlotType::Data, BlueprintLinkDirection::Out, 0),
        };
        CEMeta(Reflect)
        UBlueprintMetaCastNode();
        UBlueprintMetaCastNode(const std::string& to_type_name);

        const std::string& get_to_type() const;
        void SerializeToJson(machine::IVMStreamWriter & writer) override;

    protected:
        bool Init(const std::string& to_type_name);
        ProcessingInfo RtActivateLogicImpl(machine::VCoroutine * coroutine_, UBlueprintExecSlot * slot) override;
        void DeserializeFields(machine::IVMStreamReadNode & node) override;
    private:
        std::string m_ToType;
        const reflection::MetaClass* m_ToClass = nullptr;
    };

}}   // namespace gbf::logic
