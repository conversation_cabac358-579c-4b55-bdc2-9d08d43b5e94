#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/blueprint/details/node/blueprint_node.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf { namespace logic {

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintEventNode : public UBlueprintNode
    {
    public:
        CEMeta(Reflect) 
        explicit UBlueprintEventNode(std::string_view _event_name = {}, std::string_view _title = {});

        const std::string& event_name() const noexcept
        {
            return m_event_name;
        }
        virtual void set_event_name(const std::string& event_name)
        {
            m_event_name = event_name;
            m_title = std::string("OnEvent::") + event_name;
        }

        void RtTriggered(machine::VCoroutine * coroutine_, size_t out_slot_index, const BlueprintEventParamList& param);
        void RtPushNodeAsInstruction(machine::VCoroutine * coroutine, size_t out_slot_index, const BlueprintEventParamList& param);

    protected:
        virtual void RtTriggerImpl(const BlueprintEventParamList& param_list, machine::VCoroutine* coroutine){};
        void SerializeToJson(machine::IVMStreamWriter & writer) override;
        void DeserializeFields(machine::IVMStreamReadNode & node) override;

    protected:
        std::string m_event_name;
    };
}}   // namespace gbf::logic
