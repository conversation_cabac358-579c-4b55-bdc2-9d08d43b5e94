#include "visual/blueprint/details/node/event/blueprint_event_test_started.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
// #include "visual/virtual_machine/extend/vmvalue.h"

namespace gbf
{
	namespace logic
	{
		const char* UBlueprintEventTestStarted::EventName = "EventNodeTestStarted";

		UBlueprintEventTestStarted::UBlueprintEventTestStarted() :
			UBlueprintEventNode(EventName, "Test Started Event1")
		{
			InitializeSlotsImpl();
		}

		void UBlueprintEventTestStarted::InitializeSlotsImpl()
		{
			AddExecSlot(BlueprintLinkDirection::Out, 0, "Started");
			AddDataSlot(BlueprintLinkDirection::Out, 0, machine::VValueKind::kString, "Task name");
		}

		void UBlueprintEventTestStarted::RtTriggerImpl(const BlueprintEventParamList& param_list, machine::VCoroutine* coroutine)
		{
			assert(param_list.size() == size_t{ 1 });

			auto param = param_list.front();
			//do something here
			RtTrySetDataSlotValue(
				BlueprintLinkDirection::Out, 0,
				coroutine->GetCurrentMemoryScope(),
				coroutine->GetGlobalMemoryScope(),
				param);
			////get_slot_vm_value(NodeSlotId::DataOutStart, coroutine_->get_current_memory_frame())->assign(param_);
		}

		////SeqNode* EventNodeTestStarted::copy_impl()
		////{
		////	return __gc_new(EventNodeTestStarted)();
		////}

		//-------------------------------------------------------------------------------------------
	}
}
