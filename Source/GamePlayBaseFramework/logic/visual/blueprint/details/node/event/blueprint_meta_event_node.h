#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_event_node.h"

#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf { namespace logic {
    class UBlueprintEvent;

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintMetaEventNode : public UBlueprintEventNode
    {
    public:
        CEMeta(Reflect)
        UBlueprintMetaEventNode();
        void set_event_name(const std::string& event_name) override;
        

    protected:
        void refresh_event();
        void InitializeSlotsImpl();

        virtual void update_title()
        {
            m_title = m_event_name;
        }

    protected:
        void RtTriggerImpl(const BlueprintEventParamList& param_list, machine::VCoroutine* coroutine) override;
    };
}}   // namespace gbf::logic
