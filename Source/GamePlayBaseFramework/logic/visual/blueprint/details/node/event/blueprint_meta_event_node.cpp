#include "visual/blueprint/details/node/event/blueprint_meta_event_node.h"

#include "visual/blueprint/details/blueprint_event.h"
#include "visual/blueprint/details/blueprint_graph.h"
#include "visual/blueprint/details/blueprint_graph_group.h"
#include "visual/blueprint/details/blueprint_workspace.h"

#include "visual/blueprint/details/node/blueprint_data_slot.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf { namespace logic {
    UBlueprintMetaEventNode::UBlueprintMetaEventNode()
        : UBlueprintEventNode()
    {

    }

    void UBlueprintMetaEventNode::set_event_name(const std::string& event_name)
    {
        UBlueprintEventNode::set_event_name(event_name);
        m_title = std::string("OnEvent::") + event_name;
        refresh_event();
    }

    void UBlueprintMetaEventNode::refresh_event()
    {
        m_in_instruction_slots.clear();
        m_out_instruction_slots.clear();
        m_in_variable_slots.clear();
        m_out_variable_slots.clear();

        InitializeSlotsImpl();
    }

    void UBlueprintMetaEventNode::InitializeSlotsImpl()
    {
        // init instruction slot
        AddExecSlot(BlueprintLinkDirection::Out, 0, "");

        auto* graph_group = GetParentGraph()->GetGroup();
        auto* the_event = graph_group->GetEvent(m_event_name);

        update_title();

        auto param_count = the_event->GetParamCount();
        for (auto loop = 0u; loop < param_count; ++loop)
        {
            auto* param = the_event->GetParamByIndex(loop);
            assert(param);

            reflection::TypeId type_id = param->var_class ? param->var_class->id() : UINT64_MAX;
            auto variableSlot = AddDataSlot(BlueprintLinkDirection::Out, loop, param->var_type, param->name, type_id);

            assert(variableSlot);

            ////// default value;
            ////if (param->has_default_value()) variableSlot->SetDefaultValue(param->get_default_value());
        }
    }

    void UBlueprintMetaEventNode::RtTriggerImpl(const BlueprintEventParamList& param_list, machine::VCoroutine* coroutine)
    {
        auto event_param_count = GetDataSlotOutCount();
        assert(event_param_count == param_list.size());
        for (auto loop = size_t{0}; loop < event_param_count; ++loop)
        {
            RtTrySetDataSlotValue(BlueprintLinkDirection::Out, loop, coroutine->GetCurrentMemoryScope(), coroutine->GetGlobalMemoryScope(), param_list[loop]);
        }
    }
}}   // namespace gbf::logic
