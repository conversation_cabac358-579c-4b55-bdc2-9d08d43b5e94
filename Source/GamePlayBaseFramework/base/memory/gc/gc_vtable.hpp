#pragma once

#include <string>
#include <type_traits>


#include "memory/memory_export.hpp"

#include "memory/gc/gc_fwd.hpp"
#include "memory/gc/gc_info.hpp"
#include "memory/gc/object_scope.hpp"

#include "core/utils/meta_type_hash.hpp"
#include "core/utils/static_const.hpp"

////#include "reflection/type.hpp"
////#include "reflection/traits/data_type_traits.hpp"
////#include "reflection/traits/type_traits.hpp"#include "memory/gc/gc_util.hpp"
#include "memory/gc/detail/enable_gc_from_this_base.hpp"
////#include "reflection/utils/typeid.hpp"

////#include "reflection/type.hpp"

namespace gbf {
namespace gc {

struct gc_vtable {
  ////void (* copy_ctor_)(void* obj_pointer, const void* other_obj);
  void (* dtor_)(void* obj_pointer) = nullptr;
  void (* free_)(gc_info* obj, gc_manager* manager) = nullptr;
  void (* mark_self_)(gc_info* obj, gc_manager* manager) = nullptr;
  void (* mark_childs_)(gc_info* obj, gc_manager* manager) = nullptr;
  void (* post_ctor_action)(gc_info* obj) = nullptr;
  

  //not need here~
  ////reflection::ValueKind       type_kind_;
  std::string_view  name_;
  uint64_t     name_id_ = 0;

  size_t            data_size_ = 0;

  bool              can_copy_ctor_ = false;
  bool              can_move_ctor_ = false;
  bool              can_default_ctor_ = false;
  bool              has_child_ = false;

  bool              is_support_gc_from_this_ = false;
};

namespace detail{

//destroyer for type
template <typename T>
inline static void destroy(std::false_type, T* ptr) {
  reinterpret_cast<T*>(ptr)->~T();
}

template <typename T>
inline static void destroy(std::true_type, T*) {}

template <typename T>
inline static void destroy(T* ptr) {
  if (ptr) destroy(std::is_trivially_destructible<T>{}, ptr);
}


template<class T>
struct gc_vtable_for{
//!defined(CLANG_GENERATOR) && 
////#if !defined(CLANG_GENERATOR) && GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
////  static_assert(std::is_same_v<T, std::remove_cvref_t<T>>, "must a data type here!");
////#endif
  static_assert(std::is_same_v<T, std::remove_reference_t<std::remove_cv_t<T>> >, "must a data type here!");

  ////using DataType = typename reflection::detail::TDataType<T>::Type;
  using DataType = T;
  ////using TypeTraits = typename reflection::detail::TTypeTraits<DataType>;
  constexpr gc_vtable_for(){
    vtbl_.can_copy_ctor_ = std::is_copy_constructible_v<T>;
    vtbl_.can_move_ctor_ = std::is_nothrow_move_constructible_v<T>;
    vtbl_.can_default_ctor_ = std::is_default_constructible_v<T>;

    vtbl_.dtor_ = +[](void* obj_pointer){
      destroy<DataType>((DataType*)obj_pointer);
    };
    vtbl_.free_ = +[](gc_info* info, gc_manager* manager){
      gc_util::do_gc_free(info, manager);
    };
    vtbl_.mark_self_ = +[](gc_info* info, gc_manager* manager) {
      gc_util::do_gc_mark(info, manager);
    };

    ////constexpr bool is_support_gc_from_this = std::is_base_of_v<detail::__enable_gc_from_this_base, T>;
    vtbl_.is_support_gc_from_this_ = std::is_base_of_v<detail::__enable_gc_from_this_base, T>;

    if constexpr(std::is_base_of_v<object_scope, T>) {
      //object scope
      vtbl_.mark_childs_ = +[](gc_info* info, gc_manager* manager) {
        auto* obj_scope = (object_scope*)info->get_obj_ptr();
        obj_scope->do_scope_mark(manager);
      };
      vtbl_.post_ctor_action = +[](gc_info* info) {
        auto* obj = (object_scope*)(info->get_obj_ptr());
        if constexpr(std::is_base_of_v<detail::__enable_gc_from_this_base, T>) {
          auto* tmp_base = (detail::__enable_gc_from_this_base*)obj;
          tmp_base->embeded_gc_info_ = info;
        }

        gc_util::do_object_scope_ctor_post_action(obj);
      };
      vtbl_.has_child_ = true;
    }
    else {
      vtbl_.mark_childs_ = +[](gc_info* info, gc_manager* manager) { };
      vtbl_.post_ctor_action = +[](gc_info* info) { 
        if constexpr(std::is_base_of_v<detail::__enable_gc_from_this_base, T>) {
          auto* obj = (object_scope*)(info->get_obj_ptr());
          auto* tmp_base = (detail::__enable_gc_from_this_base*)obj;
          tmp_base->embeded_gc_info_ = info;
        }
      };
      vtbl_.has_child_ = false;
    }



    ////constexpr reflection::ValueKind kind = reflection::MapType<DataType>();
    ////vtbl_.type_kind_ = kind;
    vtbl_.data_size_ = sizeof(DataType);
    
    vtbl_.name_ = MetatypeHash::NamePretty<DataType>();
    vtbl_.name_id_ = MetatypeHash::Hash<DataType>();
  }
  ~gc_vtable_for() = default;

  gc_vtable vtbl_;
};

template <class T>
constexpr gc_vtable_for<T> const* select_vtable_for() noexcept {
  return &StaticConst<gc_vtable_for<T>>::value;
}


template <class T>
constexpr gc_vtable const* query_vtable_by_type() noexcept {
  return &(select_vtable_for<T>()->vtbl_);
}

}

}  // namespace gc
}  // namespace gbf
