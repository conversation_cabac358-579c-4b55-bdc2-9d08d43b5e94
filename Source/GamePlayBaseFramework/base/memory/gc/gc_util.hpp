#pragma once

#include "memory/gc/gc_fwd.hpp"
#include "memory/memory_export.hpp"

namespace gbf {
namespace gc {

//-------------------------------------------------------------------------------------
GBF_MEMORY_API extern gc_manager* g_gc;
//-------------------------------------------------------------------------------------

class GBF_MEMORY_API gc_util {
 public:
  static void do_gc_free(gc_info* info, gc_manager* manager);

  static void do_gc_mark(gc_info* info, gc_manager* manager);

  static gc_info** _allocate_local_slot();

  static void _free_local_slot(gc_info** obj_slot);

  static gc_info** _allocate_global_slot();

  static void _free_global_slot(gc_info** obj_slot);

  static gc_info** _allocate_weak_slot();

  static void _free_weak_slot(gc_info** obj_slot);

  static gc_info* _try_get_object_from_weak(gc_info** weak_slot);

  static std::pair<gc_info*, void*> _allocate_gc_object(size_t size, size_t align = 0, int flags = MAY_TRIGGER_GC);

  static void push_alone_object(gc_info* alone_one);

  static void do_object_scope_ctor_post_action(object_scope* obj);

  static void add_info_to_run_scope(run_scope_ptr& run_scope, gc_info* info);
  ////void DisposeObject(GcInfo* info);
};

}  // namespace gc
}  // namespace gbf



