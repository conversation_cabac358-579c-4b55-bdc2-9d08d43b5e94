#pragma once

#include <set>

#include "memory/gc/gcobject.hpp"
#include "memory/memory_define.hpp"

namespace gbf {
namespace reflection {
// only support value as gc object
template <typename V>
class TGCSet : public GcObject, public std::set<V> {
 public:
  void gc_mark_child(gc_manager* gc) override {
    for (auto iter = this->begin(); iter != this->end(); iter++) {
      gc->mark_one_obj(*iter);
    }
  }

  bool has_members() const noexcept override { return !(this->empty()); }
};
}  // namespace reflection
}  // namespace gbf
