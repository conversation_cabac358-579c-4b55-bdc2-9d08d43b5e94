#include "core/utils/data_stream.h"
#include "core/utils/string_util.h"

namespace gbf {
//-----------------------------------------------------------------------
std::string DataStream::GetLine(bool trimAfter) {
  char tmpBuf[kDataStreamTempSize];
  std::string retString;
  size_t readCount;
  // Keep looping while not hitting delimiter
  while ((readCount = Read(tmpBuf, kDataStreamTempSize - 1)) != 0) {
    // Terminate string
    tmpBuf[readCount] = '\0';

    char* p = strchr(tmpBuf, '\n');
    if (p != nullptr) {
      // Reposition backwards
      Skip((long)(p + 1 - tmpBuf - readCount));
      *p = '\0';
    }

    retString += tmpBuf;

    if (p != nullptr) {
      // Trim off trailing CR if this was a CR/LF entry
      if (retString.length() && retString[retString.length() - 1] == '\r') {
        retString.erase(retString.length() - 1, 1);
      }

      // Found terminator, break out
      break;
    }
  }

  if (trimAfter) {
    StringUtil::Trim(retString);
  }

  return retString;
}
//-----------------------------------------------------------------------
size_t DataStream::ReadLine(char* buf, size_t maxCount, std::string_view delim) {
  // Deal with both Unix & Windows LFs
  bool trimCR = false;
  if (delim.find_first_of('\n') != std::string::npos) {
    trimCR = true;
  }

  char tmpBuf[kDataStreamTempSize];
  size_t chunkSize = std::min(maxCount, (size_t)kDataStreamTempSize - 1);
  size_t totalCount = 0;
  size_t readCount;
  while (chunkSize && (readCount = Read(tmpBuf, chunkSize)) != 0) {
    // Terminate
    tmpBuf[readCount] = '\0';

    // Find first delimiter
    size_t pos = strcspn(tmpBuf, delim.data());

    if (pos < readCount) {
      // Found terminator, reposition backwards
      Skip((long)(pos + 1 - readCount));
    }

    // Are we genuinely copying?
    if (buf) {
      memcpy(buf + totalCount, tmpBuf, pos);
    }
    totalCount += pos;

    if (pos < readCount) {
      // Trim off trailing CR if this was a CR/LF entry
      if (trimCR && totalCount && buf && buf[totalCount - 1] == '\r') {
        --totalCount;
      }

      // Found terminator, break out
      break;
    }

    // Adjust chunkSize for next time
    chunkSize = std::min(maxCount - totalCount, (size_t)kDataStreamTempSize - 1);
  }

  // Terminate
  if (buf) buf[totalCount] = '\0';

  return totalCount;
}
//-----------------------------------------------------------------------
size_t DataStream::SkipLine(std::string_view delim) {
  char tmpBuf[kDataStreamTempSize];
  size_t total = 0;
  size_t readCount;
  // Keep looping while not hitting delimiter
  while ((readCount = Read(tmpBuf, kDataStreamTempSize - 1)) != 0) {
    // Terminate string
    tmpBuf[readCount] = '\0';

    // Find first delimiter
    size_t pos = strcspn(tmpBuf, delim.data());

    if (pos < readCount) {
      // Found terminator, reposition backwards
      Skip((long)(pos + 1 - readCount));

      total += pos + 1;

      // break out
      break;
    }

    total += readCount;
  }

  return total;
}
//-----------------------------------------------------------------------
std::string DataStream::GetAsString(void) {
  std::string result;

  size_t bufSize = size_;
  Seek(0);
  if (bufSize > 0) {
    result.resize(bufSize);
    Read(&result[0], bufSize);
  } else {
    constexpr size_t pageSize = 4096;
    char buffer[pageSize];
    while (!IsEof()) {
      auto nr = Read(buffer, bufSize);
      result.append(buffer, nr);
    }
  }

  // RVO
  return result;
}

void DataStream::ReadToByteBuffer(ByteBuffer& outByteBuffer) {
  // Read the entire buffer - ideally in one read, but if the size of
  // the buffer is unknown, do multiple fixed size reads.
  size_t bufSize = 4096;
  char* pBuf = new char[bufSize];
  // Ensure read from begin of stream
  Seek(0);
  while (!IsEof()) {
    size_t nr = Read(pBuf, bufSize);
    outByteBuffer.Append((uint8*)pBuf, nr);
  }
  delete[] pBuf;
}
//-----------------------------------------------------------------------
FileStreamDataStream::FileStreamDataStream(std::ifstream* s, bool freeOnClose)
    : DataStream(), in_stream_(s), fstream_ro_(s), fstream_(nullptr), free_on_close_(freeOnClose) {
  // calculate the size
  in_stream_->seekg(0, std::ios_base::end);
  size_ = (size_t)in_stream_->tellg();
  in_stream_->seekg(0, std::ios_base::beg);
  DetermineAccess();
}
//-----------------------------------------------------------------------
FileStreamDataStream::FileStreamDataStream(std::string_view name, std::ifstream* s, bool freeOnClose)
    : DataStream(name), in_stream_(s), fstream_ro_(s), fstream_(nullptr), free_on_close_(freeOnClose) {
  // calculate the size
  in_stream_->seekg(0, std::ios_base::end);
  size_ = (size_t)in_stream_->tellg();
  in_stream_->seekg(0, std::ios_base::beg);
  DetermineAccess();
}
//-----------------------------------------------------------------------
FileStreamDataStream::FileStreamDataStream(std::string_view name, std::ifstream* s, size_t inSize, bool freeOnClose)
    : DataStream(name), in_stream_(s), fstream_ro_(s), fstream_(nullptr), free_on_close_(freeOnClose) {
  // Size is passed in
  size_ = inSize;
  DetermineAccess();
}
//---------------------------------------------------------------------
FileStreamDataStream::FileStreamDataStream(std::fstream* s, bool freeOnClose)
    : DataStream(false), in_stream_(s), fstream_ro_(nullptr), fstream_(s), free_on_close_(freeOnClose) {
  // writable!
  // calculate the size
  in_stream_->seekg(0, std::ios_base::end);
  size_ = (size_t)in_stream_->tellg();
  in_stream_->seekg(0, std::ios_base::beg);
  DetermineAccess();
}
//-----------------------------------------------------------------------
FileStreamDataStream::FileStreamDataStream(std::string_view name, std::fstream* s, bool freeOnClose)
    : DataStream(name, false), in_stream_(s), fstream_ro_(nullptr), fstream_(s), free_on_close_(freeOnClose) {
  // writable!
  // calculate the size
  in_stream_->seekg(0, std::ios_base::end);
  size_ = (size_t)in_stream_->tellg();
  in_stream_->seekg(0, std::ios_base::beg);
  DetermineAccess();
}
//-----------------------------------------------------------------------
FileStreamDataStream::FileStreamDataStream(std::string_view name, std::fstream* s, size_t inSize, bool freeOnClose)
    : DataStream(name, false), in_stream_(s), fstream_ro_(nullptr), fstream_(s), free_on_close_(freeOnClose) {
  // writable!
  // Size is passed in
  size_ = inSize;
  DetermineAccess();
}
//---------------------------------------------------------------------
void FileStreamDataStream::DetermineAccess() {
  access_mode_ = 0;
  if (in_stream_) access_mode_ |= READ;
  if (fstream_) access_mode_ |= WRITE;
}
//-----------------------------------------------------------------------
FileStreamDataStream::~FileStreamDataStream() { Close(); }
//-----------------------------------------------------------------------
size_t FileStreamDataStream::Read(void* buf, size_t count) {
  in_stream_->read(static_cast<char*>(buf), static_cast<std::streamsize>(count));
  return (size_t)in_stream_->gcount();
}
//-----------------------------------------------------------------------
size_t FileStreamDataStream::Write(const void* buf, size_t count) {
  size_t written = 0;
  if (IsWriteable() && fstream_) {
    fstream_->write(static_cast<const char*>(buf), static_cast<std::streamsize>(count));
    written = count;
  }
  return written;
}
//-----------------------------------------------------------------------
size_t FileStreamDataStream::ReadLine(char* buf, size_t maxCount, std::string_view delim) {
  if (delim.empty()) {
    return 0;
  }
  // Deal with both Unix & Windows LFs
  bool trimCR = false;
  if (delim.at(0) == '\n') {
    trimCR = true;
  }
  // maxCount + 1 since count excludes terminator in getline
  in_stream_->getline(buf, static_cast<std::streamsize>(maxCount + 1), delim.at(0));
  size_t ret = (size_t)in_stream_->gcount();
  // three options
  // 1) we had an eof before we read a whole line
  // 2) we ran out of buffer space
  // 3) we read a whole line - in this case the delim character is taken from the stream but not written in the buffer so the read data is of length
  // ret-1 and thus ends at index ret-2 in all cases the buffer will be null terminated for us

  if (in_stream_->eof()) {
    // no problem
  } else if (in_stream_->fail()) {
    // Did we fail because of maxCount hit? No - no terminating character
    // in included in the count in this case
    if (ret == maxCount) {
      // clear failbit for next time
      in_stream_->clear();
    } else {
      return 0;
    }
  } else {
    // we need to adjust ret because we want to use it as a
    // pointer to the terminating null character and it is
    // currently the length of the data read from the stream
    // i.e. 1 more than the length of the data in the buffer and
    // hence 1 more than the _index_ of the NULL character
    --ret;
  }

  // trim off CR if we found CR/LF
  if (trimCR && buf[ret - 1] == '\r') {
    --ret;
    buf[ret] = '\0';
  }
  return ret;
}
//-----------------------------------------------------------------------
void FileStreamDataStream::Skip(long count) {
  in_stream_->clear();  // Clear fail status in case eof was set
  in_stream_->seekg(static_cast<std::ifstream::pos_type>(count), std::ios::cur);
}
//-----------------------------------------------------------------------
void FileStreamDataStream::Seek(size_t pos) {
  in_stream_->clear();  // Clear fail status in case eof was set
  in_stream_->seekg(static_cast<std::streamoff>(pos), std::ios::beg);
}
//-----------------------------------------------------------------------
size_t FileStreamDataStream::Tell(void) const {
  in_stream_->clear();  // Clear fail status in case eof was set
  return (size_t)in_stream_->tellg();
}
//-----------------------------------------------------------------------
bool FileStreamDataStream::IsEof(void) const { return in_stream_->eof(); }
//-----------------------------------------------------------------------
void FileStreamDataStream::Close(void) {
  if (in_stream_) {
    if (!closed_) {
      closed_ = true;
      // Unfortunately, there is no file-specific shared class hierarchy between fstream and ifstream (!!)
      if (fstream_ro_) {
        fstream_ro_->close();
        if (free_on_close_) delete fstream_ro_;
        fstream_ro_ = nullptr;
      }
      if (fstream_) {
        fstream_->flush();
        fstream_->close();
        if (free_on_close_) delete fstream_;
        fstream_ = nullptr;
      }
    }

    if (free_on_close_) {
      // delete the stream too
      delete fstream_ro_;
      delete fstream_;

      in_stream_ = nullptr;
      fstream_ro_ = nullptr;
      fstream_ = nullptr;
    }
  }
}
//-----------------------------------------------------------------------
//-----------------------------------------------------------------------
FileHandleDataStream::FileHandleDataStream(FILE* handle, uint16_t accessMode) : DataStream(accessMode), file_handle_(handle) {
  // Determine size
  fseek(file_handle_, 0, SEEK_END);
  size_ = ftell(file_handle_);
  fseek(file_handle_, 0, SEEK_SET);
}
//-----------------------------------------------------------------------
FileHandleDataStream::FileHandleDataStream(std::string_view name, FILE* handle, uint16_t accessMode) : DataStream(name, accessMode), file_handle_(handle) {
  // Determine size
  fseek(file_handle_, 0, SEEK_END);
  size_ = ftell(file_handle_);
  fseek(file_handle_, 0, SEEK_SET);
}
//-----------------------------------------------------------------------
FileHandleDataStream::~FileHandleDataStream() { Close(); }
//-----------------------------------------------------------------------
size_t FileHandleDataStream::Read(void* buf, size_t count) { return fread(buf, 1, count, file_handle_); }
//-----------------------------------------------------------------------
size_t FileHandleDataStream::Write(const void* buf, size_t count) {
  if (!IsWriteable())
    return 0;
  else
    return fwrite(buf, 1, count, file_handle_);
}
//---------------------------------------------------------------------
//-----------------------------------------------------------------------
void FileHandleDataStream::Skip(long count) { fseek(file_handle_, count, SEEK_CUR); }
//-----------------------------------------------------------------------
void FileHandleDataStream::Seek(size_t pos) { fseek(file_handle_, static_cast<long>(pos), SEEK_SET); }
//-----------------------------------------------------------------------
size_t FileHandleDataStream::Tell(void) const { return ftell(file_handle_); }
//-----------------------------------------------------------------------
bool FileHandleDataStream::IsEof(void) const { return feof(file_handle_) != 0; }
//-----------------------------------------------------------------------
void FileHandleDataStream::Close(void) {
  if (file_handle_ != nullptr) {
    fclose(file_handle_);
    file_handle_ = nullptr;
  }
}
//-----------------------------------------------------------------------

}  // namespace gbf
