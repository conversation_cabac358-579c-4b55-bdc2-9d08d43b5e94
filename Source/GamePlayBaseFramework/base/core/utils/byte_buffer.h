#pragma once

#include <memory.h>
#include <cassert>

#include <list>
#include <map>
#include <string>
#include <vector>

#include <algorithm>
#include <memory>

#include "core/config.hpp"
#include "core/utils/byte_conv.h"
////#include "byte_buffer_error.h"

#include "core/utils/byte_buffer_storage.h"
#include "core/utils/byte_buffer_view.h"
#include "core/utils/byte_buffer_writer.h"

namespace gbf {
////static const unsigned char ZIP_HEADER[] = { '@', 'Z', 'I', 'P' };

class GBF_CORE_API ByteBuffer {
 public:
  // constructor
  ByteBuffer() {}

  ByteBuffer(std::string_view sv) { Append(sv); }

  // constructor
  ByteBuffer(size_t res) { m_storage.Reserve(res); }

  // copy constructor
  ByteBuffer(const ByteBuffer& buf) : m_write_size(buf.m_write_size), m_read_position(buf.m_read_position), m_storage(buf.m_storage) {}

  // right reference
  ByteBuffer(ByteBuffer&& buf) noexcept { *this = std::move(buf); }

  ~ByteBuffer() { free_buffer(); }

  ByteBuffer& operator=(const ByteBuffer&) = default;

  ByteBuffer& operator=(ByteBuffer&& buf) noexcept {
    std::swap(m_write_size, buf.m_write_size);
    std::swap(m_read_position, buf.m_read_position);
    m_storage = std::move(buf.m_storage);

    return *this;
  }

  uint8_t operator[](size_t pos) const { return m_storage[pos]; }

  size_t ReadPosition() const { return m_read_position; }

  size_t ReadPosition(size_t rpos_) {
    m_read_position = rpos_;
    return m_read_position;
  }

  size_t WritePosition() const { return m_write_size; }

  size_t WritePosition(size_t wpos_) {
    m_write_size = wpos_;
    return m_write_size;
  }

  uint8_t* ReadPtr() const { return (uint8_t*)(m_storage.ReadContents(m_read_position)); }

  uint8_t* WritePtr() { return (uint8_t*)m_storage.ModifyContents(m_write_size); }

  size_t Space() const {
    if (m_write_size >= m_storage.Capacity()) {
      return 0;
    } else {
      return m_storage.Capacity() - m_write_size;
    }
  }

  // remove already read data from buffer
  bool Crunch() {
    if (GBF_UNLIKELY(m_storage.Crunch(m_read_position, m_write_size))) {
      return false;
    }

    m_write_size = m_storage.Capacity();
    m_read_position = 0;
    return true;
  }

  void ResetAsBuffer() {
    m_storage.ResetAsBuffer(m_write_size);
    m_write_size = m_read_position = 0;
  }

  size_t NotReadSize() const {
    if (m_write_size > m_read_position) {
      return m_write_size - m_read_position;
    } else {
      return 0;
    }
  }

  const uint8_t* Contents() const { return (const uint8_t*)m_storage.Contents(); }

  uint8_t* WritableContents() { return (uint8_t*)m_storage.WritableContents(); }

  size_t Size() const { return m_write_size; }
  bool Empty() const { return m_write_size == 0; }

  void Reserve(size_t ressize) { m_storage.Reserve(ressize); }

  void Append(const char* src, size_t cnt) { return Append((const uint8_t*)src, cnt); }

  void Append(const uint8_t* src, size_t cnt) { m_write_size = m_storage.AppendStraight(m_write_size, src, cnt); }

  void Append(const ByteBuffer& buffer) {
    if (buffer.WritePosition()) {
      Append(buffer.Contents(), buffer.WritePosition());
    }
  }

  void Append(std::string_view sv) { Append(sv.data(), sv.length()); }

  void Put(size_t pos, const uint8_t* src, size_t cnt) { m_storage.PutStraight(pos, m_write_size, src, cnt); }

  bool ReadSkip(size_t skip) {
    if (m_read_position + skip > Size()) {
      return false;
    }
    m_read_position += skip;
    return true;
  }

  bool ReadToMemory(void* targt_data, size_t content_size) {
    if (m_read_position + content_size > Size()) {
      return false;
    }
    memcpy(targt_data, ReadPtr(), content_size);
    m_read_position += content_size;
    return true;
  }

  void WriteToMemory(const void* src_data, size_t content_size) { Append((const uint8_t*)src_data, content_size); }

  bool WriteDataToFile(const std::string& file_name);

  bool ReadDataFromFile(const std::string& file_name);

  std::string GetAsHexString() const { return m_storage.GetAsHexString(Size()); }

  void SetAsHexString(const std::string_view hex_str) {
    m_write_size = m_storage.SetAsHexString(hex_str);
    m_read_position = 0;
  }

  ByteBufferView CreateView() { return {(const char*)Contents(), Size()}; }

  ByteBufferWriter::TBytePackStream<true> GetPackLittleEndianStream() { return ByteBufferWriter::TBytePackStream<true>(m_storage, m_write_size); }

  ByteBufferWriter::TBytePackStream<false> GetPackBigEndianStream() { return ByteBufferWriter::TBytePackStream<false>(m_storage, m_write_size); }

  ByteBufferView::TByteUnpackStream<true> GetUnpackLittleEndianStream() {
    return ByteBufferView::TByteUnpackStream<true>(m_read_position, m_storage.Contents(), m_write_size);
  }

  ByteBufferView::TByteUnpackStream<false> GetUnpackBigEndianStream() {
    return ByteBufferView::TByteUnpackStream<false>(m_read_position, m_storage.Contents(), m_write_size);
  }

  // for compatible with old code
  template <class T>
  ByteBuffer& operator>>(T& value) {
    GetUnpackBigEndianStream() >> value;
    return *this;
  }

  // for compatible with old code
  template <class T>
  ByteBuffer& operator<<(T value) {
    GetPackBigEndianStream() << value;
    return *this;
  }

 protected:
  void free_buffer() {
    m_write_size = m_read_position = 0;
    m_storage.Clear();
  }

 protected:
  ByteBufferStorage m_storage;
  size_t m_write_size = 0;
  size_t m_read_position = 0;
};

template <typename T>
inline ByteBuffer& operator<<(ByteBuffer& b, std::vector<T> const& v) {
  b << (unsigned int)v.size();
  // memcpy()
  // b.append((const UINT8*)&v[0],sizeof(T)*v.size());
  for (typename std::vector<T>::const_iterator i = v.begin(); i != v.end(); ++i) {
    b << *i;
  }
  return b;
}

template <typename T>
inline ByteBuffer& operator>>(ByteBuffer& b, std::vector<T>& v) {
  unsigned int vsize;
  b >> vsize;
  v.clear();
  v.reserve(vsize);
  while (vsize--) {
    T t;
    b >> t;
    v.push_back(t);
  }
  return b;
}

template <typename T>
inline ByteBuffer& operator<<(ByteBuffer& b, std::list<T> const& v) {
  b << (unsigned int)v.size();
  for (typename std::list<T>::iterator i = v.begin(); i != v.end(); ++i) {
    b << *i;
  }
  return b;
}

template <typename T>
inline ByteBuffer& operator>>(ByteBuffer& b, std::list<T>& v) {
  unsigned int vsize;
  b >> vsize;
  v.clear();
  while (vsize--) {
    T t;
    b >> t;
    v.push_back(t);
  }
  return b;
}

template <typename K, typename V>
inline ByteBuffer& operator<<(ByteBuffer& b, std::map<K, V>& m) {
  b << (unsigned int)m.size();
  for (typename std::map<K, V>::iterator i = m.begin(); i != m.end(); ++i) {
    b << i->first << i->second;
  }
  return b;
}

template <typename K, typename V>
inline ByteBuffer& operator>>(ByteBuffer& b, std::map<K, V>& m) {
  unsigned int msize;
  b >> msize;
  m.clear();
  while (msize--) {
    K k;
    V v;
    b >> k >> v;
    m.insert(make_pair(k, v));
  }
  return b;
}

//-----------------------------------------------------------------------------------------------
using ByteBufferPtr = std::shared_ptr<ByteBuffer>;

}  // namespace gbf
