#include "core/thread/impl/thread_timer_impl.h"
#include <algorithm>

namespace gbf {
namespace threads {

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
//-------------------------------------------------------------------------
ThreadTimerImpl::ThreadTimerImpl() { Reset(0); }

//-------------------------------------------------------------------------
ThreadTimerImpl::~ThreadTimerImpl() {}

//-------------------------------------------------------------------------
void ThreadTimerImpl::Reset(int offsetSeconds) {
  m_offset_seconds = offsetSeconds;
  // Get the constant frequency
  QueryPerformanceFrequency(&m_frequency);

  // Query the timer
  QueryPerformanceCounter(&m_start_time);
  m_start_tick = GetTickCount64();
  m_start_tick += offsetSeconds * 1000;

  m_last_time = 0;
  m_zero_clock = clock();

  time(&m_start_sys_time);
}
//-------------------------------------------------------------------------
void ThreadTimerImpl::SyncWith(const ThreadTimerImpl& sync_timer) {
  m_frequency = sync_timer.m_frequency;
  m_start_time = sync_timer.m_start_time;
  m_start_tick = sync_timer.m_start_tick;
  m_zero_clock = sync_timer.m_zero_clock;

  m_last_time = 0;
}
//-------------------------------------------------------------------------
unsigned long long ThreadTimerImpl::GetMilliseconds() {
  LARGE_INTEGER curTime;

  // Query the timer
  QueryPerformanceCounter(&curTime);

  LONGLONG newTime = curTime.QuadPart - m_start_time.QuadPart;

  // scale by 1000 for milliseconds
  unsigned long long newTicks = (unsigned long long)(1000 * newTime / m_frequency.QuadPart);

  // detect and compensate for performance counter leaps
  // (surprisingly common, see Microsoft KB: Q274323)
  unsigned long check = GetTickCount64() - m_start_tick;
  signed long long msecOff = (signed long long)(newTicks - check);
  if (msecOff < -100 || msecOff > 100) {
    // We must keep the timer running forward :)
    LONGLONG adjust = (std::min)(msecOff * m_frequency.QuadPart / 1000, newTime - m_last_time);
    m_start_time.QuadPart += adjust;
    newTime -= adjust;

    // Re-calculate milliseconds
    newTicks = (unsigned long long)(1000 * newTime / m_frequency.QuadPart);
  }

  // Record last time for adjust
  m_last_time = newTime;

  return newTicks;
}

//-------------------------------------------------------------------------
unsigned long long ThreadTimerImpl::GetMicroseconds() {
  LARGE_INTEGER curTime;

  // Query the timer
  QueryPerformanceCounter(&curTime);

  LONGLONG newTime = curTime.QuadPart - m_start_time.QuadPart;

  // get milliseconds to check against GetTickCount
  unsigned long long newTicks = (unsigned long long)(1000 * newTime / m_frequency.QuadPart);

  // detect and compensate for performance counter leaps
  // (surprisingly common, see Microsoft KB: Q274323)
  unsigned long long check = GetTickCount64() - m_start_tick;
  signed long long msecOff = (signed long long)(newTicks - check);
  if (msecOff < -100 || msecOff > 100) {
    // We must keep the timer running forward :)
    LONGLONG adjust = (std::min)(msecOff * m_frequency.QuadPart / 1000, newTime - m_last_time);
    m_start_time.QuadPart += adjust;
    newTime -= adjust;
  }

  // Record last time for adjust
  m_last_time = newTime;

  // scale by 1000000 for microseconds
  unsigned long long newMicro = (unsigned long long)(1000000 * newTime / m_frequency.QuadPart);

  return newMicro;
}

#else
// better: use clock_gettime( CLOCK_MONOTONIC, &ts ) as gettimeofday might drift
// for instance in case of leap days, travel across TZ etc.
// even better: let std::chrono::steady_clock handle this cross platform
//--------------------------------------------------------------------------------//
ThreadTimerImpl::ThreadTimerImpl() { Reset(0); }

//--------------------------------------------------------------------------------//
ThreadTimerImpl::~ThreadTimerImpl() {}

//--------------------------------------------------------------------------------//
void ThreadTimerImpl::Reset(int offsetSeconds) {
  m_offset_seconds = offsetSeconds;

  m_zero_clock = clock();
  gettimeofday(&m_start_tod, NULL);

  // m_start_tod.tv_sec += offsetSeconds;

  time(&m_start_sys_time);
}
//----------------------------------------------------------------------------------
void ThreadTimerImpl::SyncWith(const ThreadTimerImpl& sync_timer) {
  m_zero_clock = sync_timer.m_zero_clock;
  m_start_tod = sync_timer.m_start_tod;
}
//--------------------------------------------------------------------------------//
unsigned long long ThreadTimerImpl::GetMilliseconds() {
  struct timeval now;
  gettimeofday(&now, NULL);
  return (now.tv_sec - m_start_tod.tv_sec + m_offset_seconds) * (unsigned long long)1000 +
         (now.tv_usec - m_start_tod.tv_usec) / 1000;
}

//--------------------------------------------------------------------------------//
unsigned long long ThreadTimerImpl::GetMicroseconds() {
  struct timeval now;
  gettimeofday(&now, NULL);
  return (now.tv_sec - m_start_tod.tv_sec + m_offset_seconds) * (unsigned long long)1000000 +
         (now.tv_usec - m_start_tod.tv_usec);
}

#endif
//-------------------------------------------------------------------------
unsigned long long ThreadTimerImpl::GetMillisecondsCpu() {
  clock_t newClock = clock();
  return (unsigned long long)((double)(newClock - m_zero_clock) / ((double)CLOCKS_PER_SEC / 1000.0));
}

//-------------------------------------------------------------------------
unsigned long long ThreadTimerImpl::GetMicrosecondsCpu() {
  clock_t newClock = clock();
  return (unsigned long long)((double)(newClock - m_zero_clock) / ((double)CLOCKS_PER_SEC / 1000000.0));
}
}  // namespace threads
}  // namespace gbf
