#pragma once

namespace gbf {
namespace threads {

// Helper class to lock and unlock a mutex automatically.
template <typename MutexImplSys>
class scoped_lock {
 private:
  scoped_lock(const scoped_lock&) = delete;
  scoped_lock& operator=(const scoped_lock&) = delete;

 public:
  // Tag type used to distinguish constructors.
  enum adopt_lock_t { adopt_lock };

  // Constructor adopts a lock that is already held.
  scoped_lock(MutexImplSys& m, adopt_lock_t) : mutex_(m), locked_(true) {}

  // Constructor acquires the lock.
  explicit scoped_lock(MutexImplSys& m) : mutex_(m) {
    mutex_.lock();
    locked_ = true;
  }

  // Destructor releases the lock.
  ~scoped_lock() {
    if (locked_) mutex_.unlock();
  }

  // Explicitly acquire the lock.
  void lock() {
    if (!locked_) {
      mutex_.lock();
      locked_ = true;
    }
  }

  // Explicitly release the lock.
  void unlock() {
    if (locked_) {
      mutex_.unlock();
      locked_ = false;
    }
  }

  // Test whether the lock is held.
  bool locked() const { return locked_; }

  // Get the underlying mutex.
  MutexImplSys& mutex() { return mutex_; }

 private:
  // The underlying mutex.
  MutexImplSys& mutex_;

  // Whether the mutex is currently locked or unlocked.
  bool locked_;
};

}  // namespace threads
}  // namespace gbf
