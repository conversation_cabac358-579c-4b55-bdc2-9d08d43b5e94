#include "core/plugin/dynlib_manager.h"
#include "core/plugin/dynlib.h"

namespace gbf {
//-----------------------------------------------------------------------
DynLibManager::DynLibManager() {}
//-----------------------------------------------------------------------
DynLib* DynLibManager::Load(const std::string& filename) {
  DynLibMap::iterator i = library_map_.find(filename);
  if (i != library_map_.end()) {
    return i->second;
  } else {
    DynLib* pLib = new DynLib(filename);
    pLib->Load();
    library_map_[filename] = pLib;
    return pLib;
  }
}

gbf::DynLib* DynLibManager::Query(const std::string& filename) {
  DynLibMap::iterator i = library_map_.find(filename);
  if (i != library_map_.end()) {
    return i->second;
  } else {
    return nullptr;
  }
}

//-----------------------------------------------------------------------
void DynLibManager::Unload(DynLib* lib) {
  DynLibMap::iterator i = library_map_.find(lib->GetName());
  if (i != library_map_.end()) {
    library_map_.erase(i);
  }
  lib->Unload();
  delete lib;
}
//-----------------------------------------------------------------------
DynLibManager::~DynLibManager() {
  // Unload & Delete resources in turn
  for (DynLibMap::iterator it = library_map_.begin(); it != library_map_.end(); ++it) {
    it->second->Unload();
    delete it->second;
  }

  // Empty the list
  library_map_.clear();
}
}  // namespace gbf
