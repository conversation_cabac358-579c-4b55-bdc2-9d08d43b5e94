#include "core/original/tcircularbuffer.h"
#include <cassert>
#include <cstdlib>
#include <cstring>

namespace rstudio_original {

TCircularBuffer::TCircularBuffer() {
  m_dwsize = 0;
  m_dwhead = 0;
  m_dwtail = 0;
  m_dataptr = nullptr;
}

TCircularBuffer::~TCircularBuffer() {}

int TCircularBuffer::Create(unsigned int a_Buffersize) {
  void* pDataptr;

  pDataptr = calloc(1, a_Buffersize);
  if (!pDataptr) {
    return -1;
  }

  m_dwsize = a_Buffersize;
  m_dwhead = 0;
  m_dwtail = 0;
  m_dataptr = (char*)pDataptr;

  return 0;
}

void TCircularBuffer::Destroy() {
  if (m_dataptr) {
    free(m_dataptr);
    m_dataptr = nullptr;
  }

  m_dwsize = 0;
  m_dwhead = 0;
  m_dwtail = 0;

  return;
}

int TCircularBuffer::Write(unsigned int a_dwdatalen, const char* a_pszdata) {
  unsigned int dwFreeRoom = 0;
  unsigned int dwPkgLen = 0;
  unsigned int dwTempTail = 0;
  unsigned int dwHeadLen = 0;
  unsigned int dwBodyLen = 0;
  unsigned int dwTailRoom = 0;

  assert(a_pszdata != nullptr);
  assert(a_dwdatalen > 0);

  dwFreeRoom = GetFreeRoom();

  dwHeadLen = sizeof(unsigned int);
  dwBodyLen = a_dwdatalen;
  dwPkgLen = dwHeadLen + dwBodyLen;

  if (dwFreeRoom < dwPkgLen) {
    return -1;
  }

  dwTempTail = m_dwtail;

  if ((m_dwtail + dwHeadLen) <= m_dwsize) {
    memcpy((void*)(m_dataptr + dwTempTail), (void*)&dwBodyLen, dwHeadLen);
    dwTempTail = (m_dwtail + dwHeadLen) % m_dwsize;
  } else {
    dwTailRoom = m_dwsize - dwTempTail;
    memcpy((void*)(m_dataptr + dwTempTail), (void*)&dwBodyLen, dwTailRoom);
    memcpy((void*)m_dataptr, ((char*)&dwBodyLen + dwTailRoom), dwHeadLen - dwTailRoom);
    dwTempTail = dwHeadLen - dwTailRoom;
  }

  if (dwTempTail + dwBodyLen <= m_dwsize) {
    memcpy((void*)(m_dataptr + dwTempTail), a_pszdata, dwBodyLen);
    dwTempTail = (dwTempTail + dwBodyLen) % m_dwsize;
  } else {
    dwTailRoom = m_dwsize - dwTempTail;
    memcpy((void*)(m_dataptr + dwTempTail), a_pszdata, dwTailRoom);
    memcpy((void*)m_dataptr, (void*)(a_pszdata + dwTailRoom), dwBodyLen - dwTailRoom);
    dwTempTail = dwBodyLen - dwTailRoom;
  }

  m_dwtail = dwTempTail;

  return 0;
}

int TCircularBuffer::Read(unsigned int* a_dwBufflen, char* a_pszbuf) {
  unsigned int dwDataSize = 0;
  unsigned int dwHeadLen = 0;
  unsigned int dwBodyLen = 0;
  unsigned int dwTempHead = 0;
  unsigned int dwTailRoom = 0;

  if (m_dwhead == m_dwtail) {
    /*empty*/
    return 0;
  }

  dwDataSize = GetDataSize();

  dwHeadLen = sizeof(unsigned int);

  if (dwDataSize <= dwHeadLen) {
    ClearBuffer();
    return -1;
  }

  dwTempHead = m_dwhead;

  /*msg head*/
  if ((m_dwhead + dwHeadLen) <= m_dwsize) {
    memcpy((void*)&dwBodyLen, (void*)(m_dataptr + dwTempHead), dwHeadLen);
    dwTempHead = (m_dwhead + dwHeadLen) % m_dwsize;
  } else {
    dwTailRoom = m_dwsize - m_dwhead;
    memcpy((void*)&dwBodyLen, (void*)(m_dataptr + dwTempHead), dwTailRoom);
    memcpy((void*)((char*)(&dwBodyLen) + dwTailRoom), (void*)(m_dataptr), dwHeadLen - dwTailRoom);
    dwTempHead = dwHeadLen - dwTailRoom;
  }

  /*insufficent body buffer */
  if (*a_dwBufflen < dwBodyLen) {
    return -2;
  }

  /*invalid:package length exceeds data size*/
  if (dwDataSize < dwHeadLen + dwBodyLen) {
    ClearBuffer();
    return -3;
  }

  /*msg body*/
  if ((dwTempHead + dwBodyLen) <= m_dwsize) {
    memcpy((void*)a_pszbuf, (void*)(m_dataptr + dwTempHead), dwBodyLen);
    dwTempHead = (dwTempHead + dwBodyLen) % m_dwsize;
  } else {
    dwTailRoom = m_dwsize - dwTempHead;
    memcpy(a_pszbuf, (void*)(m_dataptr + dwTempHead), dwTailRoom);
    memcpy(a_pszbuf + dwTailRoom, (void*)m_dataptr, dwBodyLen - dwTailRoom);
    dwTempHead = dwBodyLen - dwTailRoom;
  }

  m_dwhead = dwTempHead;
  *a_dwBufflen = dwBodyLen;

  return dwBodyLen;
}

}  // namespace gbf_original
