
#pragma once

#include "reflection/config.hpp"
#include "reflection/type.hpp"

namespace gbf {
namespace reflection {
namespace detail {
// for basic type pointer handle~~~
class BuildInValueRef {
 public:
  template <typename T>
  static BuildInValueRef Make(T* p) {
    return BuildInValueRef(p, Type<std::remove_const_t<T>>::Info());
  }

  template <typename T>
  static BuildInValueRef Make(T& p) {
    return BuildInValueRef(&p, Type<std::remove_const_t<T>>::Info());
  }

  BuildInValueRef(const BuildInValueRef&) = default;

  ////ValueRef& operator= (const ValueRef&) = delete;
  BuildInValueRef& operator=(const BuildInValueRef& other) {
    ptr_ = other.ptr_;
    type_ = other.type_;
    return *this;
  }

  bool operator<(const BuildInValueRef& other) const { return type_->Less(ptr_, other.ptr_); }

  bool operator==(const BuildInValueRef& other) const { return type_->Equal(ptr_, other.ptr_); }

  template <typename T>
  T* GetRef() {
    const IType* target_type = Type<std::remove_const_t<T>>::Info();
    if (target_type != type_) [[unlikely]] {
        PONDER_ERROR(ClassUnrelated(type_->Name(), target_type->Name()));
    }
    return static_cast<T*>(const_cast<void*>(ptr_));
  }

  template <typename T>
  const T* GetRef() const {
    const IType* target_type = Type<std::remove_const_t<T>>::Info();
    if (target_type != type_) [[unlikely]] {
        PONDER_ERROR(ClassUnrelated(type_->Name(), target_type->Name()));
    }
    return static_cast<const T*>(ptr_);
  }
 const void* GetPtr() const { return ptr_; }
 private:
  struct IType {
    virtual ~IType() = default;
    virtual bool Less(const void* a, const void* b) const = 0;
    virtual bool Equal(const void* a, const void* b) const = 0;
    virtual IdRef Name() const = 0;
  };

  template <typename T>
  struct Type final : public IType {
    static const IType* Info() {
      static const Type<T> i;
      return &i;
    }

    bool Less(const void* a, const void* b) const final {
      return *static_cast<const T*>(a) < *static_cast<const T*>(b);
    }
    bool Equal(const void* a, const void* b) const final {
      return *static_cast<const T*>(a) == *static_cast<const T*>(b);
    }
    IdRef Name() const final {
      return MetatypeHash::NamePretty<T>();
    }
  };

  BuildInValueRef(const void* p, const IType* t) : ptr_(p), type_(t) {}

  const void* ptr_;
  // std::type_index m_type;
  const IType* type_;
};

template <>
struct BuildInValueRef::Type<void> : public BuildInValueRef::IType {
  static const BuildInValueRef::IType* Info() {
    static const Type<void> i;
    return &i;
  }
  bool Less(const void* a, const void* b) const final { return a < b; }
  bool Equal(const void* a, const void* b) const final { return a == b; }
  IdRef Name() const final { return "void"; }
};

}  // namespace detail
}  // namespace reflection
}  // namespace gbf
