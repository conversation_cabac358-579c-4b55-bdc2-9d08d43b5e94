

namespace gbf {
namespace reflection {


////template <typename T>
////Value::Value(const T& val)
////    : kind_(reflection::detail::TValueMapper<T>::kind)  // mapType<T> NOT used so get same kind as to()
////                                                    /*, m_value(reflection::detail::ValueMapper<T>::to(val))*/
////{
////  value_ = reflection::detail::TValueMapper<T>::to(val);
////}
////
////template <typename T>
////T Value::to() const {
////  try {
////    return detail::TValueTo<T>::Convert(*this);
////  } catch (detail::bad_conversion&) {
////    PONDER_ERROR(BadType(kind(), MapType<T>()));
////  }
////}
////
////template <typename T>
////typename std::enable_if_t<detail::TIsUserType<T>::value, T&> Value::Ref() {
////  try {
////    // Support for user type here
////    return std::get<UserObject>(value_).Ref<T>();
////  } catch (std::bad_variant_access&) {
////    PONDER_ERROR(BadType(kind(), MapType<T>()));
////  }
////}
////
////template <typename T>
////typename std::enable_if_t<!detail::TIsUserType<T>::value, T&> Value::Ref() {
////  try {
////    return std::get<T>(value_);
////  } catch (std::bad_variant_access&) {
////    PONDER_ERROR(BadType(kind(), MapType<T>()));
////  }
////}
////
////template <typename T>
////typename std::enable_if_t<detail::TIsUserType<T>::value, const T&> Value::ConstRef() const {
////  try {
////    // Support for user type here
////    return std::get<UserObject>(value_).ConstRef<T>();
////  } catch (std::bad_variant_access&) {
////    PONDER_ERROR(BadType(kind(), MapType<T>()));
////  }
////}
////
////template <typename T>
////typename std::enable_if_t<!detail::TIsUserType<T>::value, const T&> Value::ConstRef() const {
////  try {
////    return std::get<T>(value_);
////  } catch (std::bad_variant_access&) {
////    PONDER_ERROR(BadType(kind(), MapType<T>()));
////  }
////}
////
////template <typename T>
////bool Value::IsCompatible() const {
////  try {
////    to<T>();
////    return true;
////  } catch (std::exception&) {
////    return false;
////  }
////}

}  // namespace reflection
}  // namespace gbf
