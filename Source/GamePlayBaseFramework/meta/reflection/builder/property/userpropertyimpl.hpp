
#pragma once

#include "reflection/meta/user_property.hpp"
#include "reflection/objects/make_user_object.hpp"

namespace gbf {
namespace reflection {
namespace detail {

template <typename A>
class UserPropertyImpl : public UserProperty {
 public:
  UserPropertyImpl(IdRef name, A&& accessor);

 protected:
  bool IsReadable() const final;
  bool IsWritable() const final;

  Value GetValue(const UserObject& object) const final;
  void SetValue(const UserObject& object, const Value& value) const final;

 private:
  A accessor_;  // Object used to access the actual C++ property
};

}  // namespace detail
}  // namespace reflection
}  // namespace gbf

#include "reflection/builder/property/userpropertyimpl.inl"
