
namespace gbf {
namespace reflection {
namespace detail {

template <typename A>
UserPropertyImpl<A>::UserPropertyImpl(IdRef name, A&& accessor)
    : UserProperty(name, *query_meta_class<typename A::DataType>()), accessor_(accessor) {
  implement_type_ = PropertyImplementType::kCppUserProperty;
}

template <typename A>
Value UserPropertyImpl<A>::GetValue(const UserObject& object) const {
  // We copy the returned object based on the return type of the getter: (copy) T, const T&, (ref) T&.
  return accessor_.interface_.GetValue(user_object_ref_as<typename A::ClassType>(object));
}

template <typename A>
void UserPropertyImpl<A>::SetValue(const UserObject& object, const Value& value) const {
  if (!accessor_.interface_.Setter(user_object_ref_as<typename A::ClassType>(object), value)) PONDER_ERROR(ForbiddenWrite(name()));
}

template <typename A>
bool UserPropertyImpl<A>::IsReadable() const {
  return A::kCanRead;
}

template <typename A>
bool UserPropertyImpl<A>::IsWritable() const {
  return A::kCanWrite;
}

}  // namespace detail
}  // namespace reflection
}  // namespace gbf
