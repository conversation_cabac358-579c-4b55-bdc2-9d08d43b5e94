#include "reflection/meta/func_caller.hpp"
#include "reflection/meta/function.hpp"

namespace gbf {
namespace reflection {

gbf::reflection::IdReturn IFuncCaller::name() const {
  assert(parent_func_);
  return parent_func_->name();
}

gbf::reflection::IdReturn IFuncCaller::class_name() const { 
  assert(parent_func_);
  return parent_func_->class_name();
}

gbf::reflection::IdReturn IFuncCaller::full_name() const {
  ////assert(parent_func_);
  static const std::string empty_name;
  return parent_func_ ? parent_func_->full_name() : empty_name;
}

}  // namespace reflection
}  // namespace gbf
