#include "reflection/meta/constructor.hpp"
#include "reflection/meta/ctor_caller.hpp"

namespace gbf {
namespace reflection {

Constructor::~Constructor() {
  Clear();
}

CtorCallerArray Constructor::GetMatchedArgNumberCaller(FuncLanguageType caller_type, int arg_number) const {
  CtorCallerArray tmparray;
  for (auto& caller : reg_ctor_callers_[(int)caller_type]) {
    if (caller->GetArgNumber() == arg_number) {
      tmparray.emplace_back(caller);
    }
  }
  return tmparray;
}

void Constructor::FireBuilderChanged() {
  // Check argument number for constructor
  for (int i = 1; i < (int)FuncLanguageType::Total; i++) {
    std::map<int, int> arg_number_counts;
    for (auto& ctor : reg_ctor_callers_[i]) {
      int arg_num = ctor->GetArgNumber();
      auto iter = arg_number_counts.find(arg_num);
      if (iter != arg_number_counts.end()) {
        iter->second++;
      } else {
        arg_number_counts[arg_num] = 1;
      }
    }

    for (auto& ctor : reg_ctor_callers_[i]) {
      int arg_num = ctor->GetArgNumber();
      if (arg_number_counts[arg_num] == 1) {
        ctor->set_is_just_one(true);
      }
      else {
        ctor->set_is_just_one(false);
      }
    }
  }
}

void Constructor::Clear() {
  for (int i = 1; i < (int)FuncLanguageType::Total; i++) {
    for (auto& caller : reg_ctor_callers_[i]) {
      delete caller;
    }
    reg_ctor_callers_[i].clear();
  }
}

void Constructor::AddCaller(FuncLanguageType call_type, ICtorCaller* caller) {
  reg_ctor_callers_[(int)call_type].push_back(caller);
}

}  // namespace reflection
}  // namespace gbf

