
#pragma once

#include "reflection/meta/property.hpp"

namespace gbf {
namespace reflection {
/**
 * \brief Base class for all simple types of properties
 *
 * This class actually does nothing more than its base, it's just a way to separate
 * simple properties from other types.
 *
 * \sa ArrayProperty, EnumProperty, ObjectProperty
 */
class GBF_REFLECTION_API SimpleProperty : public Property {
 public:
  /**
   * \brief Construct the property from its description
   *
   * \param name Name of the property
   * \param type Type of the property
   */
  SimpleProperty(IdRef name, ValueKind type, TypeId typeIndex);

  /**
   * \brief Destructor
   */
  virtual ~SimpleProperty();
};
}  // namespace reflection
}  // namespace gbf
