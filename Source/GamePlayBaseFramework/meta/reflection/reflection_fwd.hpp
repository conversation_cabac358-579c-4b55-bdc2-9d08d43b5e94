#pragma once

#include <functional>
#include <memory>
#include <string>
#include <string_view>
#include <vector>

#include "reflection/reflection_export.hpp"
#include "reflection/config.hpp"
#include "reflection/traits/id_traits.hpp"
#include "memory/gc/smart_ptr.hpp"

//-------------------------------------------------------------------------------------
//----------------------------------Configs-------------------------------------
#define RSTUDIO_ENABLE_REFLECTION_META_EDITOR_INFO 0

#define RSTUDIO_ENABLE_XML_ARCHIVE_SUPPORT 1

#define RTTI_OBJECT_ID_BITS_COUNT 60
#define RTTI_OBJECT_STORAGE_TYPE_BITS 4

static_assert(RTTI_OBJECT_ID_BITS_COUNT + RTTI_OBJECT_STORAGE_TYPE_BITS == 64, "RttiObject use bits must be 64!");

#define ENABLE_GBF_REFLECTION_FUNCTIONS_PROFILER 0

//-------------------------------------------------------------------------------------
namespace gbf {
namespace reflection {

// Declaration
class MetaClass;
class MetaEnum;
class MetaArray;
class MetaMap;
struct UsedMetaInfo;

class Property;
class UserProperty;
class Value;
class Args;
class UserObject;
class RttiBase;

class ArrayObject;
class EnumObject;
struct NoType;

struct DataHolder;
struct ObjectVtable;
struct ArrayVtable;
////class ObjectStorage;

////class PbClassBuilder;

template <typename T>
class ClassBuilder;

template <typename T>
class BaseBuilder;

template <typename T>
class FunctionBuilder;

template <typename T>
class LuaFunctionBuilder;

template <typename T>
class PropertyBuilder;


class Constructor;
class ClassVisitor;

class Function;
class IFuncCaller;
using FuncCallerArray = std::vector<IFuncCaller*>;
class ICtorCaller;
using CtorCallerArray = std::vector<ICtorCaller*>;

namespace detail {
class BuildInValueRef;
class ClassManager;

template <typename T, typename C = void>
struct TValueMapper;

}

// Enum
enum class StorageType: int {
  StorageNone = 0,
  StorageRemote,
  StorageGc,
  StorageRemoteShared,
  StorageLocal,
  StorageTotal,
};

enum class FuncLanguageType: int {
  Unknown = 0,
  AsCxx,
  AsLua,
  ////AsTypeScript,
  Total,
};

enum class FuncCallType : int {
  Unknown = 0,
  ObjectCall,
  OverloadObjectCall,
  StaticCall,
  ReadonlyStaticProperty,
};

// slot policy for scoped_ptr
struct storage_policy {};
struct gc_storage_policy : storage_policy {};                   // Use gc to control object lifetime.
struct remote_storage_policy : storage_policy {};               // No lifetime control mode, just keep external object as a pointer.
struct remote_shared_storage_policy : storage_policy {};        // Use shared_ptr<> to control object lifetime.
struct local_storage_policy : storage_policy {};                // Just use DataHolder to save object, the fast mode, but it's a value type


//Change the default storage policy to change default settings
////using default_storage_policy = remote_shared_storage_policy;

//use for copy or reflection create object
#ifdef GBF_ENABLE_GC_ON_REFLECTION
using owned_storage_policy = gc_storage_policy;
const StorageType kOwnedStorageType = StorageType::StorageGc;
#else
using owned_storage_policy = remote_shared_storage_policy;
const StorageType kOwnedStorageType = StorageType::StorageRemoteShared;
#endif

//use for not owned object(just a reference)
using external_storage_policy = remote_storage_policy;

//make it can save a Vector3~~ 
const size_t kObjectStorageBuffSize = sizeof(std::int64_t) * 2;


// some functions here
template <typename T, typename StoragePolicy = owned_storage_policy>
inline UserObject make_user_object(T& object, StoragePolicy policy = StoragePolicy{});

template <typename T, typename StoragePolicy = owned_storage_policy>
inline UserObject make_user_object(T* object, StoragePolicy policy = StoragePolicy{});

template <typename T, typename StoragePolicy = owned_storage_policy>
inline UserObject make_user_object(T&& object, StoragePolicy policy = StoragePolicy{});

template <typename T>
inline T& user_object_ref_as(const UserObject& obj);

template <typename T>
inline UserObject __box(const std::shared_ptr<T>& obj);

template <typename T>
inline UserObject __box(const gc::global_ptr<T>& obj);

inline UserObject __box_rtti_object(RttiBase* obj);

template <typename T>
inline bool __unbox(const UserObject& obj, std::shared_ptr<T>& out_smart);

template <typename T>
inline bool __unbox(const UserObject& obj, gc::global_ptr<T>& out_smart);

template <typename T, typename StoragePolicy = owned_storage_policy>
inline Value make_value(const T& val, StoragePolicy policy = StoragePolicy{});

////template <typename T, typename StoragePolicy = gc_storage_policy>
////inline Value make_value(T* val, StoragePolicy policy = StoragePolicy{});

template <typename T>
inline T& value_ref_as(const Value& val);

template <typename T>
inline bool value_can_convert_to(const Value& val);


template <typename T>
inline ArrayObject make_array_object_owned_copy(const T* other);

template <typename T>
inline ArrayObject make_array_object_remote(const T* other);

template <typename T>
inline ArrayObject make_array_object_owned_default();

////inline ArrayObject make_array_object_by_name(const std::string& n);

////inline ArrayObject make_array_object_by_id(uint64_t aid);

//Some utils here




}  // namespace reflection
}  // namespace gbf
