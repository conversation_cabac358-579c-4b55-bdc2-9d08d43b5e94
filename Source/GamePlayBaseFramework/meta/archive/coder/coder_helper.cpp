#include "archive/coder/coder_helper.h"
#include "core/error/errors.hpp"
#include "core/utils/string_util.h"
#include "reflection/runtime/cxx_runtime.hpp"
#include "archive/protobuf/builder/pb_property_builder.hpp"

namespace gbf {
namespace reflection {

void CoderHelper::FilterFields(const MetaClass& metaClass, const UserObject& object,
                               std::vector<const Property*>& outFields) {
  auto propArray = metaClass.GetPropertiesOrderByTag();
  for (const auto* property : propArray) {
    // If the property has the exclude tag, ignore it
    //                if ((exclude != Value::nothing) && property.hasTag(exclude))
    //                    continue;

    // Special handle for pb property, test if not need(only optional & oneof property need test)
    if (property->implement_type() == reflection::PropertyImplementType::kPbOptionalProperty) {
      const auto* pbProp = (reflection::detail::PbOptionalPropertyImpl*)(property);
      if (!pbProp->HasThis(object)) continue;
    } else if (property->implement_type() == reflection::PropertyImplementType::kPbOneofProperty) {
      const auto* pbProp = (reflection::detail::PbOneofPropertyImpl*)(property);
      if (!pbProp->HasThis(object)) continue;
    }

    outFields.push_back(property);
  }
}

void CoderHelper::GetAllFields(const MetaClass& metaClass, const UserObject& object,
                               std::vector<const Property*>& outFields) {
  auto propArray = metaClass.GetPropertiesOrderByTag();
  for (const auto* property : propArray) {
    outFields.push_back(property);
  }
}

}  // namespace reflection
}  // namespace gbf
