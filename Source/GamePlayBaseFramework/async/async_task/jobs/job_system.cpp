#include "async_task/jobs/job_system.hpp"

#include <cassert>

#include "async_task/jobs/job_fence.hpp"
#include "async_task/jobs/job_notify.hpp"
#include "async_task/jobs/job_system_slot.hpp"
#include "async_task/jobs/job_ticket.hpp"
#include "async_task/jobs/job_waiter.hpp"
#include "async_task/jobs/timer_quest_helper.hpp"
#include "async_task/tasks/coro_service_manager.hpp"
#include "core/error/errors.hpp"
#include "core/imodules/iprofiler_module.h"
#include "core/utils/string_util.h"

namespace gbf::jobs {

static size_t s_main_thread_id = gbf::threads::Thread::CurrentThreadId();

////const bool kIsOpenSeperateRenderThread = true;

////PROFILER_METHOD_INFO(gThreadModuleUpdate, "ThreadModule_Update", RStudio::ProfilerGroupType::kOther);

job_system::job_system() {
  ////ThreadModuleHelper::Instance();
  timer_quest_helper_ = new timer_quest_helper(*this);
  coro_service_manager_ = new coro::coro_service_manager(*this);
}

job_system::~job_system() {
  if (GBF_LIKELY(timer_quest_helper_)) {
    delete timer_quest_helper_;
  }
}

void job_system::init_in_main_thread() {
  assert(job_system_slot_array_.empty() && "job_system::init_in_main_thread() can only call one time!");

  job_system_slot_ptr job_slot = std::make_shared<job_system_slot>(JobType::kLogicJob, *this);
  job_slot->init(0);

  s_main_thread_id = threads::Thread::CurrentThreadId();
  ////job_system_slot::set_thread_policy_and_priority(-1, ThreadJobSystemType::LogicJob);
  ////const auto& bigCoreIndics = ThreadModuleHelper::Instance()->GetBigCoreIndics();
  ////job_system_slot::bind_thread_to_cpu_core(bigCoreIndics);

  // init main thread info
  job_system_slot::s_this_thread_info = new job_system_slot::slot_thread_info(JobType::kLogicJob, "Main Thread", 0);

  job_system_slot_array_.emplace_back(job_slot);

  timer_quest_helper_->init();
}

bool job_system::add_new_slot(JobType job_type, int work_thread_num) {
  int job_index = (int)job_type;
  if (GBF_UNLIKELY(job_index >= (int)JobType::kTotalJobTypes || job_index < 0)) {
    GBF_ERROR(JobThreadJobTypeNotValid{});
  }

  if (GBF_LIKELY(job_index >= (int)job_system_slot_array_.size())) {
    job_system_slot_array_.resize(job_index + 1);
  }

  job_system_slot_ptr job_slot = std::make_shared<job_system_slot>(job_type, *this);
  job_slot->init(work_thread_num);
  job_system_slot_array_[job_index] = std::move(job_slot);
  return check_slot_array_no_empty_item();
}

bool job_system::check_slot_array_no_empty_item() const {
  for (auto& slot : job_system_slot_array_) {
    if (!slot) return false;
  }
  return true;
}

void job_system::update() {
  assert(this_thread_is_logic_slot() && "job_system::update() must call in main thread!");
  ////for (auto& slot : m_job_system_slot_array)
  for (gbf::jobs::job_system_slot_ptr& slot : job_system_slot_array_) {
    if (slot->is_run_in_main_thread()) {
      slot->manual_run();
    }
  }
}

void job_system::destroy() {
  timer_quest_helper_->stop();
  delete timer_quest_helper_;
  timer_quest_helper_ = nullptr;

  ////for (auto& slot : m_job_system_slot_array)
  for (gbf::jobs::job_system_slot_ptr& slot : job_system_slot_array_) {
    slot->stop();
    slot.reset();
  }

  ////GBF_PROFILER_CLEANUP_THREAD();

  job_system_slot_array_.clear();
}

void job_system::post(thread_job_function&& jobFunc, JobType slot_id) {
  assert((int)slot_id < job_system_slot_array_.size() && "job_system::post() slot_id not valid!");
  job_system_slot_array_[(int)slot_id]->post(std::move(jobFunc));
}

void job_system::dispatch(thread_job_function&& jobFunc, JobType job_type /*= JobSystemType::kLogicJob*/) {
  assert((int)job_type < job_system_slot_array_.size() && "job_system::post() slot_id not valid!");
  job_system_slot_array_[(int)job_type]->dispatch(std::move(jobFunc));
}

void job_system::dispatch_async_task(coro::async_task_ptr&& atask, JobType job_type /*= JobSystemType::kLogicJob*/) {
  assert((int)job_type < job_system_slot_array_.size() && "job_system::post() slot_id not valid!");
  job_system_slot_array_[(int)job_type]->dispatch_async_task(std::move(atask));
}

job_strand_ptr job_system::request_strand(JobType slot_id) { return job_system_slot_array_[(int)slot_id]->request_strand(); }

job_waiter_ptr job_system::request_waiter() { return std::make_unique<job_waiter>(); }

job_notify_ptr job_system::request_notify() { return std::make_unique<job_notify>(); }

job_ticket_ptr job_system::request_ticket() { return std::make_unique<job_ticket>(); }

job_fence_ptr job_system::request_fence() { return std::make_unique<job_fence>(this); }

int job_system::hardware_cores() { return gbf::threads::Thread::HardwareConcurrency(); }

bool job_system::this_thread_is_logic_slot() { return threads::Thread::CurrentThreadId() == s_main_thread_id; }

JobType job_system::this_thread_job_type() {
  auto* tInfo = job_system_slot::s_this_thread_info.get();
  if (tInfo) {
    return tInfo->job_system_slot_id;
  } else {
    // this thread is created outside, not in threadmodule
    return JobType::kInvalidJob;
  }
}

int job_system::this_thread_work_index() {
  auto* tInfo = job_system_slot::s_this_thread_info.get();
  if (tInfo) {
    return tInfo->thread_work_index;
  } else {
    return 0;
  }
}

bool job_system::this_thread_slot_type_is(JobType slot_id) {
  return (job_system_slot::s_this_thread_info && job_system_slot::s_this_thread_info.get()->job_system_slot_id == slot_id);
  ////if (job_system_slot::ms_this_thread_info->job_system_slot_id != jobType)
  ////{
  ////	CAP_EXCEPT_INVALID("ThreadModule::EnsureThreadJobType() not match job type get here!");
  ////	////throw std::exception();
  ////}
}

void job_system::register_external_thread(const char* threadName) {
  if (job_system_slot::s_this_thread_info) {
    return;
  }

  job_system_slot::s_this_thread_info = new job_system_slot::slot_thread_info(JobType::kExternalJob, threadName, 0);
  ////PROFILER_INITIALIZE(threadName, true);

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  job_system_slot::set_thread_name_to_system(threadName);
#endif
}

void job_system::reset_main_thread() {
  s_main_thread_id = threads::Thread::CurrentThreadId();
  if (job_system_slot::s_this_thread_info) {
    delete job_system_slot::s_this_thread_info;
    job_system_slot::s_this_thread_info = nullptr;
  }

  job_system_slot::s_this_thread_info = new job_system_slot::slot_thread_info(JobType::kLogicJob, "Main Thread", 0);
}

uint64_t job_system::add_always_run_job(JobType slot_id, thread_job_function&& period_job, unsigned long period_time_ms) {
  // no need this way.
  // return timer_quest_helper_->CreateNewTask(TaskRunType::kAlwaysCanIgnore, period_time_ms, std::move(period_job),
  //                                             slot_id);

  return timer_quest_helper_->create_new_timer(TaskRunType::kAlways, period_time_ms, std::move(period_job), slot_id);
}

uint64_t job_system::add_always_run_job_no_skip(JobType slot_id, thread_job_function&& period_job, unsigned long period_time_ms) {
  return timer_quest_helper_->create_new_timer(TaskRunType::kAlways, period_time_ms, std::move(period_job), slot_id);
}

uint64_t job_system::add_times_run_job(JobType slot_id, thread_job_function&& period_job, unsigned long period_time_ms, unsigned int run_count) {
  return timer_quest_helper_->create_new_timer(TaskRunType::kByRunCount, period_time_ms, std::move(period_job), slot_id, (int)run_count);
}

uint64_t job_system::add_delay_run_job(JobType slot_id, thread_job_function&& period_job, unsigned long delay_time_ms) {
  return timer_quest_helper_->create_new_timer(TaskRunType::kOnlyOne, delay_time_ms, std::move(period_job), slot_id);
}

void job_system::kill_timer_job(uint64_t tid) { timer_quest_helper_->delete_timer(tid); }

std::string job_system::get_thread_name(JobType slot_id, int thread_index) {
  return StringUtil::Format("Thread-%s[%d]", get_job_type_name(slot_id).data(), thread_index);
}

const char* job_system::this_thread_job_type_name() {
  return get_job_type_name(this_thread_job_type()).data();
}

std::string_view get_job_type_name(JobType job_type) {
  switch (job_type) {
    case gbf::JobType::kLogicJob:
      return "LogicJob";
    case gbf::JobType::kWorkJob:
      return "WorkJob";
    case gbf::JobType::kSlowJob:
      return "SlowJob";
    case gbf::JobType::kNetworkJob:
      return "NetworkJob";
    case gbf::JobType::kNetworkConnectJob:
      return "NetworkConnectJob";
    case gbf::JobType::kLogJob:
      return "LogJob";
    case gbf::JobType::kExtra1LogJob:
      return "Extra1LogJob";
    case gbf::JobType::kExtra2LogJob:
      return "Extra2LogJob";
    case gbf::JobType::kNotifyExternalJob:
      return "NotifyExternalJob";
    case gbf::JobType::kCustomJob0:
      return "CustomJob0";
    case gbf::JobType::kCustomJob1:
      return "CustomJob1";
    case gbf::JobType::kCustomJob2:
      return "CustomJob2";
    case gbf::JobType::kCustomJob3:
      return "CustomJob3";
    case gbf::JobType::kInvalidJob:
      return "InvalidJob";
    case gbf::JobType::kExternalJob:
      return "ExternalJob";
    default:
      return "UnknownJob";
  }
}

}  // namespace gbf::jobs
