#pragma once

#include "async_task/async_task_export.hpp"
#include "async_task/jobs/job_notify.hpp"
#include "core/thread/thread_event.h"

namespace gbf::jobs {

class ASYNC_TASKS_API job_waiter {
 public:
  job_waiter();
  ~job_waiter();

  void one_start();

  void one_finish();

  void wait();

  void reset();

 protected:
  threads::ThreadEvent event_;
  job_notify notify_;
};
}  // namespace gbf::jobs
