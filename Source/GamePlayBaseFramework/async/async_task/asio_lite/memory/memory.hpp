#pragma once

#include <memory>
#include <cstdlib>
#include "async_task/asio_lite/error/throw_exception.hpp"
#include "async_task/async_task_config.hpp"

namespace asio {
namespace detail {

using std::shared_ptr;
using std::weak_ptr;

using std::addressof;

} // namespace detail

#if defined(ASIO_HAS_CXX11_ALLOCATORS)
using std::allocator_arg_t;
# define ASIO_USES_ALLOCATOR(t) \
  namespace std { \
    template <typename Allocator> \
    struct uses_allocator<t, Allocator> : true_type {}; \
  } \
  /**/
# define ASIO_REBIND_ALLOC(alloc, t) \
  typename std::allocator_traits<alloc>::template rebind_alloc<t>
  /**/
#else // defined(ASIO_HAS_CXX11_ALLOCATORS)
struct allocator_arg_t {};
# define ASIO_USES_ALLOCATOR(t)
# define ASIO_REBIND_ALLOC(alloc, t) \
  typename alloc::template rebind<t>::other
  /**/
#endif // defined(ASIO_HAS_CXX11_ALLOCATORS)


inline void* aligned_new(std::size_t align, std::size_t size) {
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  align = (align < ASIO_DEFAULT_ALIGN) ? ASIO_DEFAULT_ALIGN : align;
  size = (size % align == 0) ? size : size + (align - size % align);
  void* ptr = _aligned_malloc(size, align);
  if (!ptr) {
    std::bad_alloc ex;
    asio::detail::throw_exception(ex);
  }
  return ptr;
#else
  align = (align < ASIO_DEFAULT_ALIGN) ? ASIO_DEFAULT_ALIGN : align;
  size = (size % align == 0) ? size : size + (align - size % align);
  void* ptr = nullptr;
  posix_memalign(&ptr, align, size);
  if (!ptr) {
    std::bad_alloc ex;
    asio::detail::throw_exception(ex);
  }
  return ptr;
#endif
}

inline void aligned_delete(void* ptr) {
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  _aligned_free(ptr);
#else
  std::free(ptr);
#endif
}


} // namespace asio

