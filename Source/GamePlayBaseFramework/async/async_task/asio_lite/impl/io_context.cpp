#include "async_task/asio_lite/io_context.hpp"

#include <limits>
#include "async_task/asio_lite/sync_primitives/concurrency_hint.hpp"

#include "async_task/asio_lite/tools/scoped_ptr.hpp"
#include "async_task/asio_lite/service/service_registry.hpp"
#include "async_task/asio_lite/service/service_helper.hpp"
#include "async_task/asio_lite/error/throw_error.hpp"

#if defined(ASIO_HAS_IOCP)
# include "async_task/asio_lite/scheduler/win_iocp_io_context.hpp"
#else
# include "async_task/asio_lite/scheduler/scheduler.hpp"
#endif


namespace asio {

io_context::io_context()
  : impl_(add_impl(new impl_type(*this,
          ASIO_CONCURRENCY_HINT_DEFAULT, false)))
{
}

io_context::io_context(int concurrency_hint)
  : impl_(add_impl(new impl_type(*this, concurrency_hint == 1
          ? ASIO_CONCURRENCY_HINT_1 : concurrency_hint, false)))
{
}

io_context::impl_type& io_context::add_impl(io_context::impl_type* impl)
{
  asio::detail::scoped_ptr<impl_type> scoped_impl(impl);
  asio::add_service<impl_type>(*this, scoped_impl.get());
  return *scoped_impl.release();
}

io_context::~io_context()
{
}

io_context::count_type io_context::run()
{
  asio::error_code ec;
  count_type s = impl_.run(ec);
  asio::detail::throw_error(ec);
  return s;
}

#if !defined(ASIO_NO_DEPRECATED)
io_context::count_type io_context::run(asio::error_code& ec)
{
  return impl_.run(ec);
}
#endif // !defined(ASIO_NO_DEPRECATED)

io_context::count_type io_context::run_one()
{
  asio::error_code ec;
  count_type s = impl_.run_one(ec);
  asio::detail::throw_error(ec);
  return s;
}

#if !defined(ASIO_NO_DEPRECATED)
io_context::count_type io_context::run_one(asio::error_code& ec)
{
  return impl_.run_one(ec);
}
#endif // !defined(ASIO_NO_DEPRECATED)

io_context::count_type io_context::poll()
{
  asio::error_code ec;
  count_type s = impl_.poll(ec);
  asio::detail::throw_error(ec);
  return s;
}

#if !defined(ASIO_NO_DEPRECATED)
io_context::count_type io_context::poll(asio::error_code& ec)
{
  return impl_.poll(ec);
}
#endif // !defined(ASIO_NO_DEPRECATED)

io_context::count_type io_context::poll_one()
{
  asio::error_code ec;
  count_type s = impl_.poll_one(ec);
  asio::detail::throw_error(ec);
  return s;
}

#if !defined(ASIO_NO_DEPRECATED)
io_context::count_type io_context::poll_one(asio::error_code& ec)
{
  return impl_.poll_one(ec);
}
#endif // !defined(ASIO_NO_DEPRECATED)

void io_context::stop()
{
  impl_.stop();
}

bool io_context::stopped() const
{
  return impl_.stopped();
}

void io_context::restart()
{
  impl_.restart();
}

io_context::service::service(asio::io_context& owner)
  : execution_context::service(owner)
{
}

io_context::service::~service()
{
}

void io_context::service::shutdown()
{
#if !defined(ASIO_NO_DEPRECATED)
  shutdown_service();
#endif // !defined(ASIO_NO_DEPRECATED)
}

#if !defined(ASIO_NO_DEPRECATED)
void io_context::service::shutdown_service()
{
}
#endif // !defined(ASIO_NO_DEPRECATED)

void io_context::service::notify_fork(io_context::fork_event ev)
{
#if !defined(ASIO_NO_DEPRECATED)
  fork_service(ev);
#else // !defined(ASIO_NO_DEPRECATED)
  (void)ev;
#endif // !defined(ASIO_NO_DEPRECATED)
}

#if !defined(ASIO_NO_DEPRECATED)
void io_context::service::fork_service(io_context::fork_event)
{
}
#endif // !defined(ASIO_NO_DEPRECATED)

} // namespace asio

