#pragma once

#include <functional>
#include "async_task/coro_platform.hpp"

#if GBF_NO_COROUTINES
//#error "Coroutine support is required to use this header"
#endif

#if !GBF_NO_COROUTINES
#include GBF_COROUTINES_HEADER

namespace gbf::coro {

using GBF_COROUTINES_NAMESPACE::coroutine_handle;
using GBF_COROUTINES_NAMESPACE::suspend_always;
using GBF_COROUTINES_NAMESPACE::suspend_never;

}  // namespace gbf::coro
#endif

#if defined(GBF_ENABLE_CPP20)

namespace gbf::coro {

// Help class to divide coroutine return_value && return_void~~
template <typename ReturnType>
struct return_value_or_void {
  using ReturnArgType = std::add_rvalue_reference_t<ReturnType>;
  using ReturnValueSettingFunc = std::function<void(ReturnArgType)>;

  struct type {
    // template(typename Value = T)(requires convertible_to<Value, T> AND constructible_from<T, Value>)
    template <typename Value = ReturnType, typename Extra = std::enable_if_t<std::is_convertible_v<Value, ReturnType>>>
    void return_value(Value&& value) noexcept(std::is_nothrow_constructible_v<ReturnType, Value>) {
      return_value_setting_func_(std::move(value));
    }

    ReturnValueSettingFunc return_value_setting_func_;
  };
};

template <>
struct return_value_or_void<void> {
  using ReturnArgType = std::add_rvalue_reference_t<void>;
  using ReturnValueSettingFunc = std::function<void(void)>;

  struct type {
    void return_void() noexcept { return_value_setting_func_(); }

    ReturnValueSettingFunc return_value_setting_func_;
  };
};

//-------------------------------------------------------------------------------------
}  // namespace gbf::coro

#endif
