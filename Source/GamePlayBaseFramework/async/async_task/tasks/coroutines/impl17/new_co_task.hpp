#pragma once
#include <functional>
#include <stack>
#include <string>
#include <tuple>
#include <utility>

#include "async_task/async_task_export.hpp"
#include "async_task/tasks/tasks_define.hpp"
#include "async_task/tasks/coroutines/impl17/new_co_traits.hpp"

#define __co_begin                                      \
  switch ((int)(gbf::coro::current_promise()->get_state())) { \
    case gbf::coro::CoroRunningState::kBegin:

#ifdef __linux__
#define __co_yield()                                             \
  do {                                                           \
    gbf::coro::current_promise()->set_state(__COUNTER__ + 1); \
    return nullptr;                                              \
    case __COUNTER__:;                                           \
  } while (0)
#else
#define __co_yield()                                      \
  do {                                                    \
    gbf::coro::current_promise()->set_state(__LINE__); \
    return nullptr;                                       \
    case __LINE__:;                                       \
  } while (0)
#endif

////#ifdef __linux__
////#define __co_await(awaiter, ...)                                 \
////  do {                                                           \
////    gbf::co_task::current_promise()->set_state(__COUNTER__ + 1); \
////    awaiter(__VA_ARGS__);                                        \
////    return nullptr;                                              \
////    case __COUNTER__:;                                           \
////  } while (0)
////#else
////#define __co_await(awaiter, ...)                          \
////  do {                                                    \
////    gbf::co_task::current_promise()->set_state(__LINE__); \
////    awaiter(__VA_ARGS__);                                 \
////    return nullptr;                                       \
////    case __LINE__:;                                       \
////  } while (0)
////#endif

#define __co_end                                                         \
  default:                                                                 \
    break;                                                                 \
    }                                                                      \
    gbf::coro::current_promise()->set_state(gbf::coro::CoroRunningState::kEndOk); \
    return nullptr;

#define __co_end_failed                                                       \
  default:                                                                    \
    break;                                                                    \
    }                                                                         \
    gbf::coro::current_promise()->set_state(gbf::coro::CoroRunningState::kEndFailed); \
    return nullptr;

#define __co_return_internel                                                      \
  gbf::coro::current_promise()->set_state(gbf::coro::CoroRunningState::kEndOk); \
  return nullptr

namespace gbf {
namespace coro {

class coro_promise17;



class ASYNC_TASKS_API coroutine_handle17 {
 public:
  coroutine_handle17() = default;
  coroutine_handle17(std::nullptr_t) noexcept {}

  bool done() const;
  CoroRunningState get_state() const;

  void resume();
  void destroy();

  static coroutine_handle17 from_address(coro_promise17* ptr) {
    coroutine_handle17 h;
    h.m_ptr = ptr;
    return h;
  }
  coroutine_handle17& operator=(std::nullptr_t) noexcept {
    m_ptr = nullptr;
    return *this;
  }

  const coro_promise17* get_pointer() const { return m_ptr; }

  const void* get_user_data() const;

  void set_user_data(const void* ud);

  uint64_t get_user_id() const;

  void set_user_id(uint64_t uid);

  ////void defer(CoDeferFunction&& func);

  void set_return_type(uint64_t type_id, std::string_view type_name);

  std::string_view get_return_type_name() const;

  uint64_t get_return_type_id() const;
  ////uint64_t get_id() const;
 protected:
  coro_promise17* m_ptr = nullptr;
};

////struct CoroutineTaskBase {
////  CoroutineHandle m_handler = nullptr;
////
////  CoroutineTaskBase(std::nullptr_t) {}
////  CoroutineTaskBase(CoroutineHandle handler) : m_handler(handler) {}
////
////  // static constexpr bool initial_suspend() { return false; }
////  // static constexpr bool final_suspend() { return false; }
////};
//-----------------------------------------------------------------------------------------------

class ASYNC_TASKS_API coro_promise17 {
 public:
  virtual ~coro_promise17() {}
  bool done() const { return m_state < 0; }

  virtual void resume() = 0;

  inline void set_state(int state) { m_state = (CoroRunningState)state; }
  inline CoroRunningState get_state() { return m_state; }
  inline void set_final_suspend(bool final_suspend) { m_final_suspend = final_suspend; }
  inline bool is_final_suspend() { return m_final_suspend; }

  const void* get_user_data() const { return m_user_data; }
  void set_user_data(const void* ud) { m_user_data = ud; }

  uint64_t get_user_id() const { return m_user_id; }

  void set_user_id(uint64_t uid) { m_user_id = uid; }

  void set_return_type(uint64_t type_id, std::string_view type_name) {
    m_return_type_id = type_id;
    m_return_type_name = type_name;
  }

  uint64_t get_return_type_id() const { return m_return_type_id; }

  std::string_view get_return_type_name() const { return m_return_type_name; }

  ////void on_return() {

  ////  while (!m_defer_func_stack.empty())
  ////  {
  ////    auto& func = m_defer_func_stack.top();
  ////    func();
  ////    m_defer_func_stack.pop();
  ////  }
  ////}
 protected:
  CoroRunningState m_state = CoroRunningState::kBegin;
  const void* m_user_data = nullptr;
  uint64_t m_user_id = 0;

  bool m_final_suspend = false;

  uint64_t m_return_type_id = 0;
  std::string_view m_return_type_name;
};

////ASYNC_TASKS_API void push_promise(coro_promise17* handler);
////ASYNC_TASKS_API coro_promise17* pop_promise();
ASYNC_TASKS_API coro_promise17* current_promise();

// promise实现
template <class Ret, class... Args>
class coro_promise_impl17 : public coro_promise17 {
  static_assert(is_all_reference<Args...>::value, "type of coroutine function's param must be reference");

 public:
  template <class... ParamArgs>
  coro_promise_impl17(ParamArgs&&... args) : m_params(std::forward<ParamArgs>(args)...) {}

  void resume() override {
    if (done()) {
      return;
    }
    if (m_function) {
      //Not need push/pop here, done it in IAsyncTask::Resume()
      apply(m_function, m_params);
    }
    return;
  }

  ////virtual uint64_t get_id() const override
  ////{
  ////    return m_id;
  ////}

  template <class Func>
  inline void set_function(Func&& f) {
    m_function = std::move(f);
  }

 protected:
  // 这里参数列表怎么根据需要转为引用

  std::function<Ret(Args...)> m_function;

  std::tuple<typename std::remove_reference<Args>::type...> m_params;
};

// 协程创建 实现

// 普通函数
template <class Func>
struct _co_start_impl_ {
  using Ret = typename func_type_traits<Func>::return_type;
  static const int object_count = 0;

  template <class T, std::size_t... I, class... Args>
  static auto new_task(std::index_sequence<I...>, Args&&... args) {
    auto h = new coro_promise_impl17<Ret, typename T::template param_types<I>...>(std::forward<Args>(args)...);
    h->set_final_suspend(has_final_suspend<Ret>::value);
    return h;
  }

  template <class... Args>
  static Ret apply(Func f, Args&&... args) {
    using is = std::make_index_sequence<func_type_traits<Func>::param_count>;
    auto co_task = new_task<func_type_traits<Func>>(is(), std::forward<Args>(args)...);
    co_task->set_function(f);
    if constexpr (!has_initial_suspend<Ret>::value) {
      co_task->resume();
      if (co_task->done()) {
        delete co_task;
        return nullptr;
      }
    }

    return Ret(coroutine_handle17::from_address(co_task));
  }
};

// 类成员函数
template <class C, class Ret, class... Args>
struct _co_start_impl_<Ret (C::*)(Args...)> {
  static const int object_count = 1;

  template <class T, std::size_t... I, class... ParamArgs>
  static auto new_task(std::index_sequence<I...>, ParamArgs&&... args) {
    auto h = new coro_promise_impl17<Ret, typename T::template param_types<I>...>(std::forward<ParamArgs>(args)...);
    h->set_final_suspend(has_final_suspend<Ret>::value);
    return h;
  }

  using MemFunc = Ret (C::*)(Args...);
  template <class... ParamArgs>
  static Ret apply(MemFunc mf, C* obj, ParamArgs&&... args) {
    using Func = std::function<Ret(Args...)>;
    using is2 = std::make_index_sequence<func_type_traits<Func>::param_count>;
    auto co_task = new_task<func_type_traits<Func>>(is2(), std::forward<ParamArgs>(args)...);
    co_task->set_function([mf, obj](Args... args) { mf(obj, static_cast<Args&&>(args)...); });
    if constexpr (!has_initial_suspend<Ret>::value) {
      co_task->resume();
      if (co_task->done()) {
        delete co_task;
        return nullptr;
      }
    }
    return Ret(coroutine_handle17::from_address(co_task));
  }
};

struct _co_start_impl_operator_ {
  template <class Func, class... Args>
  typename func_type_traits<Func>::return_type operator()(Func f, Args&&... args) {
    using is = std::make_index_sequence<func_type_traits<Func>::param_count + _co_start_impl_<Func>::object_count - sizeof...(Args)>;
    return param_fill_helper<Func, Args...>::fill(is(), f, std::forward<Args>(args)...);
  }

  template <class Func, class... Args>
  struct param_fill_helper {
    template <std::size_t... I>
    static typename func_type_traits<Func>::return_type fill(std::index_sequence<I...>, Func f, Args&&... args) {
      return _co_start_impl_<Func>::apply(
          f, std::forward<Args>(args)...,
          typename std::remove_reference<
              typename func_type_traits<Func>::template param_types<I + sizeof...(Args) - _co_start_impl_<Func>::object_count>>::type()...);
    }
  };
};

ASYNC_TASKS_API extern _co_start_impl_operator_ co_start;

}
}
