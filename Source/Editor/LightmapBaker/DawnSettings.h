#pragma once
#include "PlatformPrefix.h"
#include "Platform/PlatformTypes.h"
#include <assert.h>
#include "SwarmDefines.h"
#include <string>
#include <vector>
#include "CrossBase/Math/CrossMath.h"
#include "Editor/LightmapBaker/TLBSDataProtocol/BakingCoreDefine.h"

NS_GPUBAKING_BEGIN

enum EDawnQualityLevel
{
	Preview		= 0,
	Medium		= 1,
	High		= 2,
	Production	= 3,
	TotalCount  = 4,
};

enum EDawnBakingMode
{
	FullBuild,
	InteractiveMode,
	BakingSelectedActors,
	BakingSelectedAreas,
};

struct FDawnBuildOptions
{
	FDawnBuildOptions()
		: QualityLevel(EDawnQualityLevel::Preview)
		, BakingMode(EDawnBakingMode::FullBuild)
	{
	}

	bool ShouldBuildLightingForLevel() const
	{
		return true;
	}

	EDawnQualityLevel QualityLevel;
	EDawnBakingMode BakingMode;
};

enum ELightmapDenoiserMode
{
    DAWN, //UMETA(DisplayName = "Dawn Lightmap Denoiser"),
	OPTIX, //UMETA(DisplayName = "Optix Denoiser"),
	OPENIMAGE_LM, //UMETA(DisplayName = "OpenImage Lightmap Denoiser"),
	OPENIMAGE_RT, //UMETA(DisplayName = "OpenImage RenderTexture Denoiser"),
	NONE, //UMETA(DisplayName = "Disable Denoiser"),
	MAX, //UMETA(Hidden),
};

class UDawnSettings;
struct FLightmassWorldInfoSettings;
struct FShadowSettingParameters;

struct FDawnLightmap2DSettings
{
	FDawnLightmap2DSettings()
		: bEnable(true)
		, SamplesPerPixel(128)
		, MaxBounces(2)
        , SuperSampleFactor(1)
		, PenumbraShadowFraction(1.0f)
		, RasterizationBias(0.1f)
		, bFillUnmappedTexel(false)
        , DenoiserMode(static_cast<uint8>(ELightmapDenoiserMode::DAWN))
        , NumSkyLightingBounces(1)
	{
	}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D)
	bool bEnable;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D, meta = (UIMin = "1", UIMax = "8192"))
	int32 SamplesPerPixel;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D, meta = (UIMin = "1", UIMax = "32"))
	int32 MaxBounces;

    /** Super sample factor for indirect light.Cannot be set on global setting */
    int32 SuperSampleFactor;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D, meta = (UIMin = "0.1", UIMax = "16.0"))
	float PenumbraShadowFraction;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D, meta = (UIMin = "0.01", UIMax = "2.0"))
	float RasterizationBias;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D)
	bool bFillUnmappedTexel;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Lightmap2D)
	uint8 DenoiserMode;

    /**
     * Number of skylight and emissive bounces to simulate.
     * Lightmass uses a non-distributable radiosity method for skylight bounces whose cost is proportional to the number of bounces.
     */
    // UPROPERTY(EditAnywhere, Category = LightmassGeneral, meta = (UIMin = "1.0", UIMax = "10.0"))
    int32 NumSkyLightingBounces;
};

struct FDawnSDFShadowSettings
{
	//GENERATED_BODY()

	FDawnSDFShadowSettings()
		: bEnable(true)
		, MaxUpsamplingFactor(3)
	{}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = SignedDistanceFieldShadow)
	bool bEnable;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = SignedDistanceFieldShadow, meta = (UIMin = "1", UIMax = "30"))
	int32 MaxUpsamplingFactor;
};

struct FDawnVolumetricLightmapSettings
{
	FDawnVolumetricLightmapSettings()
		: bEnable(true)
		, SamplesPerPixel(256)
		, MaxBounces(2)
	{}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = VolumetricLightmap)
	bool bEnable;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = VolumetricLightmap, meta = (UIMin = "1", UIMax = "8192"))
	int32 SamplesPerPixel;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = VolumetricLightmap, meta = (UIMin = "1", UIMax = "32"))
	int32 MaxBounces;
};

struct FDawnAmbientOcclusionSettings
{
	FDawnAmbientOcclusionSettings()
		: DirectOcclusionFraction(0.5f)
		, IndirectOcclusionFraction(1.0f)
		, OcclusionExponent(2.0f)
        , MaxOcclusionDistance(400.0f)
        , bUseAmbientOcclusion(false)
        , bVisualizeAmbientOcclusion(false)
	{}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AmbientOcclusion, meta = (UIMin = "0.0", UIMax = "2.0"))
	float DirectOcclusionFraction;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AmbientOcclusion, meta = (UIMin = "0.0", UIMax = "2.0"))
	float IndirectOcclusionFraction;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AmbientOcclusion, meta = (UIMin = "0.01", UIMax = "10"))
	float OcclusionExponent;

    /** Maximum distance for an object to cause occlusion on another object. */
    // UPROPERTY(EditAnywhere, Category = LightmassOcclusion)
    float MaxOcclusionDistance;

    /** If true, AmbientOcclusion will be enabled. */
    // UPROPERTY(EditAnywhere, Category = LightmassOcclusion)
    uint8 bUseAmbientOcclusion : 1;

    /** If true, override normal direct and indirect lighting with just the AO term. */
    // UPROPERTY(EditAnywhere, Category = LightmassDebug, AdvancedDisplay)
    uint8 bVisualizeAmbientOcclusion : 1;
};

struct FDawnAdaptiveSamplingSettings
{
	FDawnAdaptiveSamplingSettings()
		: StartBounces(3)
		, StartPercentage(0.2f)
		, Step(128)
		, MaxError(0.05f)
	{}
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AdaptiveSampling, meta = (UIMin = "1", UIMax = "100"))
	int32 StartBounces;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AdaptiveSampling, meta = (UIMin = "0.0", UIMax = "1.0"))
	float StartPercentage;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AdaptiveSampling, meta = (UIMin = "1", UIMax = "100"))
	int32 Step;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = AdaptiveSampling, meta = (UIMin = "0.05", UIMax = "1.0"))
	float MaxError;
};

struct FDawnSeamSettings
{

	FDawnSeamSettings()
		: bEnableFix(false)
		, NumSamples(1)
		, Lambda(0.1f)
		, CosNormalThreshold(0.5f)
	{}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Seam)
	bool bEnableFix;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Seam, meta = (UIMin = "1", UIMax = "5"))
	int32 NumSamples;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Seam, meta = (UIMin = "0.01", UIMax = "2.0"))
	float Lambda;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Seam, meta = (UIMin = "0.1", UIMax = "1.0"))
	float CosNormalThreshold;
};

struct FDawnPackSettings
{

	FDawnPackSettings()
		: bEnable(true)
		, Lightmap2DPackingSize(256)
		, SignedDistanceFieldShadowPackingSize(2048)
	{}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Packing)
	bool bEnable;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Packing, meta = (UIMin = "64", UIMax = "4096"))
	int32 Lightmap2DPackingSize;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Packing, meta = (UIMin = "512", UIMax = "4096"))
	int32 SignedDistanceFieldShadowPackingSize;
};

struct FDawnLightmapDenoiserSettings
{
    FDawnLightmapDenoiserSettings()
        : bEnable(true)
        , DirectSmoothingFactor(5)
        , IndirectSmoothingFactor(5)
    {}

    bool bEnable;

    int32 DirectSmoothingFactor;

    int32 IndirectSmoothingFactor;
};

struct FDawnMiscSettings
{

	FDawnMiscSettings()
		: bBakingPrecomputedRadianceTransfer(false)
		, bBakingPrecomputedLocalRadianceTransfer(false)
		, bEnablePerInstanceOverride(true)
		, MaxTaskTaskPerUpdate(10)
		, bBakingInBackground(true)
		, bExportBigWorld(false)
		, bBuildReflectionCapturesOnFinish(false)
		, bImportVolumetricTaskProgressive(true)
		, bBakeSubLevelSeparately(false)
		, bForceContentExport(false)
		, bDumpLightmapTexture(false)
		, bCheckLightmapTexture(false)
	{}

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bBakingPrecomputedRadianceTransfer;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bBakingPrecomputedLocalRadianceTransfer;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bEnablePerInstanceOverride;	

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc, meta = (UIMin = "5", UIMax = "200"))
	int32 MaxTaskTaskPerUpdate;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bBakingInBackground;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bExportBigWorld;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bBuildReflectionCapturesOnFinish;

	//UPROPERTY(Transient, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bImportVolumetricTaskProgressive;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bBakeSubLevelSeparately;
	
	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bForceContentExport;
	
	//UPROPERTY(Transient, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bDumpLightmapTexture;

	//UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = Misc)
	bool bCheckLightmapTexture;
};

class UDawnSettings
{

public:
	//UPROPERTY(config, EditAnywhere, Category=Lightmap2D, meta=(ShowOnlyInnerProperties))
	FDawnLightmap2DSettings Lightmap2D;

	//UPROPERTY(config, EditAnywhere, Category=VolumetricLightmap, meta=(ShowOnlyInnerProperties))
	FDawnVolumetricLightmapSettings VolumetricLightmap;

	//UPROPERTY(config, EditAnywhere, Category=SignedDistanceFieldShadow, meta=(ShowOnlyInnerProperties))
	FDawnSDFShadowSettings SDFShadow;

	//UPROPERTY(config, EditAnywhere, Category=AmbientOcclusion, meta=(ShowOnlyInnerProperties))
	FDawnAmbientOcclusionSettings AmbientOcclusion;

    //UPROPERTY(config, EditAnywhere, Category = DawnLightmapDenoiser, meta = (ShowOnlyInnerProperties))
    FDawnLightmapDenoiserSettings DawnLightmapDenoiser;

	//UPROPERTY(config, EditAnywhere, Category=AdaptiveSampling, meta=(ShowOnlyInnerProperties))
	FDawnAdaptiveSamplingSettings AdaptiveSampling;

	//UPROPERTY(config, EditAnywhere, Category=Seam, meta=(ShowOnlyInnerProperties))
	FDawnSeamSettings Seam;

	//UPROPERTY(config, EditAnywhere, Category=Packing, meta=(ShowOnlyInnerProperties))
	FDawnPackSettings Packing;

	//UPROPERTY(config, EditAnywhere, Category=Misc, meta=(ShowOnlyInnerProperties))
	FDawnMiscSettings Misc;
};

class UDawnSettingsPreview : public UDawnSettings
{

};

class UDawnSettingsMedium : public UDawnSettings
{
};

class UDawnSettingsHigh : public UDawnSettings
{
};

class UDawnSettingsProduction : public UDawnSettings
{
};

class UDawnSettingsInteractive : public UDawnSettings
{
};

class UDawnQualityLevel
{

public:
	//UPROPERTY(config, EditAnywhere, Category=DawnQuality)
    uint8 QualityLevel = EDawnQualityLevel::Preview;
};

class ENGINE_API LightingBakeConfig
{
public:
    struct BakeMatInfo
    {
        std::string mMaterialName;
        std::string mDiffuseName;
        std::string mEmissiveName;
    };

    static LightingBakeConfig& GetInstance();

    const std::string& GetProjectionRootPath() const { return mProjectionRootPath; }

    void SetProjectionRootPath(const std::string& rootPath) { mProjectionRootPath = rootPath; }

    bool GetLightmassDebug() const
    {
        return mLightmassDebug;
    }

    void SetLightmassDebug(bool lightmassDebug) { mLightmassDebug = lightmassDebug; }

    bool GetUseNormalMap() const { return mUseNormalMap; }

    void SetLightMapSavedRelPath(const std::string& relPath) { mLightMapSavedRelPath = relPath; }

    const std::string& GetLightMapSavedRelPath(void) const { return mLightMapSavedRelPath; }

    inline void SetDawnSettingLevel(const EDawnQualityLevel quality) { mDawnLevel = quality; }

    inline EDawnQualityLevel GetDawnSettingLevel() const { return mDawnLevel; }

    inline const UDawnSettings* GetDawnSetting() const { return mDawnSettings[mDawnLevel]; }

    bool UseAmbientOcclusion() const { return GetDawnSetting()->AmbientOcclusion.bUseAmbientOcclusion; }

    inline UDawnSettings* GetDawnSettingWriter() { return mDawnSettings[mDawnLevel]; }

    inline const FLightmassWorldInfoSettings& GetLevelSettings() const { return *LevelSettings; }

    inline FLightmassWorldInfoSettings& GetLevelSettingsWriter() { return *LevelSettings; }

    inline FShadowSettingParameters& GetShadowSettingParameter() const { return *ShadowSettingParameter; }

    inline void SetDebugCameraPosition(cross::Float3& pos) { DebugCameraPosition = pos; }

    inline cross::Float3 GetDebugCameraPosition() const { return DebugCameraPosition; }

    inline void SetDebugLookatPosition(cross::Float3& pos) { DebugLookatPosition = pos; }

    inline cross::Float3 GetDebugLookatPosition() { return DebugLookatPosition; }

    inline cross::Float3 GetBakeSceneOriginal()
    {
        return BakeSceneOrigPosition;
    }

    inline void SetBakeSceneOriginal(cross::Float3& sceneOrig) 
    {
        BakeSceneOrigPosition = sceneOrig;
    }

    inline void SetTextureMipLevel(unsigned int mip) { TextureMipLevel = mip; }

    inline unsigned int GetTextureMipLevel() const { return TextureMipLevel; }

    inline int GetMeshLODIndex() const { return 0; }

    inline float GetLightingIntensity() const { return mLightingIntensity; }

    inline void SetLightingIntensity(float intensity) { mLightingIntensity = intensity; }

    inline float GetSkyLightingIntensity() const
    {
        return mSkyLightingIntensity;
    }

    inline void SetSkyLightingIntensity(float intensity)
    {
        mSkyLightingIntensity = intensity;
    }

    inline float GetTargetDetailCellSize() const
    {
        return mTargetDetailCellSize;
    }

    inline void SetTargetDetailCellSize(float val)
    {
        mTargetDetailCellSize = val;
    }

    inline bool GetSkyLightStaticMode() 
    {
        return mSkyLightStaticMode;
    }

    inline void SetSkyLightStaticMode(bool staticMode)
    {
        mSkyLightStaticMode = staticMode;
    }

    inline void InsertBakeMaterial(const std::string& fxName, const std::string& bakeMatFile, const std::string& diffuseName, const std::string& emissiveName)
    {
        mBakeMaterialsMap[fxName] = {bakeMatFile, diffuseName, emissiveName};
    }

    const BakeMatInfo* QueryBakeMaterial(const std::string& fxFileName);

    inline void SetBakeLightMapEnable(bool bakeLightMapEnable)
    {
        mBakeLightMapEnable = bakeLightMapEnable;
    }

    inline bool GetBakeLightMapEnable()
    {
        return mBakeLightMapEnable;
    }

    inline void SetBakeVLMEnable(bool bakeVLMEnable)
    {
        mBakeVLMEnable = bakeVLMEnable;
    }

    inline bool GetBakeVLMEnable()
    {
        return mBakeVLMEnable;
    }

    inline int GetStreamingLOD() const
    {
        return mStreamingLOD;
    }

    inline void SetStreamingLOD(int lod)
    {
        mStreamingLOD = lod;
    }
    
    inline void SetUnitScale(float unitScale)
    {
        mUnitScale = std::max(unitScale, 0.1f);
    }

    inline float GetUnitScale() const
    {
        return mUnitScale;
    }

private:
    EDawnQualityLevel mDawnLevel{EDawnQualityLevel::Preview};

    std::string mProjectionRootPath;

    std::string mLightMapSavedRelPath;

    std::vector<UDawnSettings*> mDawnSettings;

    FLightmassWorldInfoSettings* LevelSettings;

    FShadowSettingParameters* ShadowSettingParameter;

    cross::Float3 DebugCameraPosition;
    cross::Float3 DebugLookatPosition;
    cross::Float3 BakeSceneOrigPosition;

    unsigned int TextureMipLevel{2};

    float mUnitScale{1.0f};
    
    std::map<std::string, BakeMatInfo> mBakeMaterialsMap;

public:
    int mStreamingLOD{10};
    float mLightingIntensity{ 1.0f };
    float mSkyLightingIntensity{1.0f};
    bool bPadMappings{true};
    bool mLightmassDebug{false};
    bool mUseNormalMap{false};
    bool mRefreshTexture{true};

    bool mBakeLightMapEnable{true};
    bool mBakeVLMEnable{true};

    float mTargetDetailCellSize{100.f};
    bool mSkyLightStaticMode{false};
    
    bool mDebugUE4{false};
    bool mDebugUE4LightOnly{false};
    bool mDebugUE4JobParamsOnly{false};

public:
    inline void SetDebugUE4(bool debugUE4)
    {
        mDebugUE4 = debugUE4;
    }

    inline bool GetDebugUE4() const
    {
        return mDebugUE4;
    }

    inline void SetDebugUE4LightOnly(bool debugUE4Light)
    {
        mDebugUE4LightOnly = debugUE4Light;
    }

    inline bool GetDebugUE4LightOnly() const
    {
        return mDebugUE4LightOnly;
    }

    inline void SetDebugUE4JobParamsOnly(bool enable)
    {
        mDebugUE4JobParamsOnly = enable;
    }

    inline bool GetDebugUE4JobParamsOnly() const
    {
        return mDebugUE4JobParamsOnly;
    }

private:
    LightingBakeConfig();
    ~LightingBakeConfig();
};

NS_GPUBAKING_END