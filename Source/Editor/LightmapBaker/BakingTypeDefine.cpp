#pragma once   

#include "BakingTypeDefine.h"
#include "CrossBase/Math/FloatConversion.h"

NS_GPUBAKING_BEGIN

constexpr float SMALL_NUMBER = 1.e-8f;

static cross::Float4x4A ueTransform(1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, 0, 0, 1);

static cross::Float4x4A ueTransformInv(1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1);

cross::Float4A Float4CE2UE(const cross::Float4A& v)
{
    return v * ueTransform;
}

cross::Float3A Float3UE2CE(const float4& v)
{
    auto ret = cross::Float4A(v.x, v.y, v.z, 1.0f) * ueTransformInv;
    return cross::Float3A(ret.x, ret.y, ret.z);
}

cross::Float3 Float3UE2CE(const cross::Float3& v)
{
    auto ret = cross::Float4A(v.x, v.y, v.z, 1.0f) * ueTransformInv;
    return cross::Float3(ret.x, ret.y, ret.z);
}

cross::Float3A Float3CE2UE(const float3& v)
{
    auto ret = cross::Float4A(v.x, v.y, v.z, 1.0) * ueTransform;
    return cross::Float3A(ret.x, ret.y, ret.z);
}

cross::Float3A Float3CE2UE(const cross::Float3A& v)
{
    auto ret = cross::Float4A(v.x, v.y, v.z, 1.0) * ueTransform;
    return cross::Float3A(ret.x, ret.y, ret.z);
}

cross::Float3A Float3CE2UE(const cross::Float3& v)
{
    auto ret = cross::Float4A(v.x, v.y, v.z, 1.0) * ueTransform;
    return cross::Float3A(ret.x, ret.y, ret.z);
}

cross::Float4x4A MatrixCE2UE(const cross::Float4x4A& m)
{
    return ueTransformInv * m * ueTransform;
}

cross::Float3 Float3Abs(const cross::Float3& box)
{
    return {fabsf(box.x), fabsf(box.y), fabsf(box.z)};
}

std::wstring string2wstring(std::string str)
{
	std::wstring result;
	int len = MultiByteToWideChar(CP_ACP, 0, str.c_str(), (int)str.size(), NULL, 0);
	WCHAR* buffer = new WCHAR[len + 1];
	MultiByteToWideChar(CP_ACP, 0, str.c_str(), (int)str.size(), buffer, len);
	buffer[len] = '\0';
	result.append(buffer);
	delete[] buffer;
	return result;
}

bool IsNearlyEqual(float A, float B, float ErrorTolerance = SMALL_NUMBER)
{
    return std::fabsf(A - B) <= ErrorTolerance;
}

FLinearColor LinearRGBToXYZ(const FLinearColor& InColor)
{
    // RGB to XYZ linear transformation used by sRGB
    // http://www.w3.org/Graphics/Color/sRGB
    const cross::Float4x4A RGBToXYZ(
        0.4124564f, 0.2126729f, 0.0193339f, 0,
        0.3575761f, 0.7151522f, 0.1191920f, 0,
        0.1804375f, 0.0721750f, 0.9503041f, 0,
        0, 0, 0, 0);

    const cross::Float4A ResultVector = RGBToXYZ * cross::Float4A(InColor.R, InColor.G, InColor.B, 0.f);
    return FLinearColor(ResultVector.x, ResultVector.y, ResultVector.z);
}

FLinearColor XYZToLinearRGB(const FLinearColor& InColor) 
{
    FLinearColor SourceXYZ(InColor);
    // Inverse of the transform in FLinearColor::LinearRGBToXYZ()
    const cross::Float4x4A XYZToRGB(
        3.2404548f, -0.9692664f, 0.0556434f, 0,
        -1.5371389f, 1.8760109f, -0.2040259f, 0,
        -0.4985315f, 0.0415561f, 1.0572252f, 0,
        0, 0, 0, 0);

    if (IsNearlyEqual(InColor.R, 0.0f, SMALL_NUMBER) && IsNearlyEqual(InColor.B, 0.0f, SMALL_NUMBER))
    {
        SourceXYZ.G = 0.0f;
    }
    const cross::Float4A LinearRGB = XYZToRGB * cross::Float4A(SourceXYZ.R, SourceXYZ.G, SourceXYZ.B, 0.f);
    return FLinearColor(std::max<float>(LinearRGB.x, 0.0f), std::max<float>(LinearRGB.y, 0.0f), std::max<float>(LinearRGB.z, 0.0f));
}

FLinearColor XYZToxyzY(const FLinearColor& InColor) 
{
    const float InvTotal = 1.0f / std::max<float>(InColor.R + InColor.G + InColor.B, SMALL_NUMBER);
    return FLinearColor(InColor.R * InvTotal, InColor.G * InvTotal, InColor.B * InvTotal, InColor.G);
}

FLinearColor xyzYToXYZ(const FLinearColor& InColor) 
{
    const float yInverse = 1.0f / std::max<float>(InColor.G, SMALL_NUMBER);
    return FLinearColor(InColor.R * InColor.A * yInverse, InColor.A, InColor.B * InColor.A * yInverse);
}

FFloat3Packed::FFloat3Packed(const FLinearColor& Src)
{
    uint32 IValue[4];
    IValue[0] = *reinterpret_cast<const uint32*>(&Src.R);
    IValue[1] = *reinterpret_cast<const uint32*>(&Src.G);
    IValue[2] = *reinterpret_cast<const uint32*>(&Src.B);
    IValue[3] = *reinterpret_cast<const uint32*>(&Src.A);

    uint32 Result[3];

    // X & Y Channels (5-bit exponent, 6-bit mantissa)
    for (uint32 j = 0; j < 2; ++j)
    {
        uint32 Sign = IValue[j] & 0x80000000;
        uint32 I = IValue[j] & 0x7FFFFFFF;

        if ((I & 0x7F800000) == 0x7F800000)
        {
            // INF or NAN
            Result[j] = 0x7c0;
            if ((I & 0x7FFFFF) != 0)
            {
                Result[j] = 0x7c0 | (((I >> 17) | (I >> 11) | (I >> 6) | (I)) & 0x3f);
            }
            else if (Sign)
            {
                // -INF is clamped to 0 since 3PK is positive only
                Result[j] = 0;
            }
        }
        else if (Sign)
        {
            // 3PK is positive only, so clamp to zero
            Result[j] = 0;
        }
        else if (I > 0x477E0000U)
        {
            // The number is too large to be represented as a float11, set to max
            Result[j] = 0x7BF;
        }
        else
        {
            if (I < 0x38800000U)
            {
                // The number is too small to be represented as a normalized float11
                // Convert it to a denormalized value.
                uint32 Shift = 113U - (I >> 23U);
                I = (0x800000U | (I & 0x7FFFFFU)) >> Shift;
            }
            else
            {
                // Rebias the exponent to represent the value as a normalized float11
                I += 0xC8000000U;
            }

            Result[j] = ((I + 0xFFFFU + ((I >> 17U) & 1U)) >> 17U) & 0x7ffU;
        }
    }

    // Z Channel (5-bit exponent, 5-bit mantissa)
    uint32 Sign = IValue[2] & 0x80000000;
    uint32 I = IValue[2] & 0x7FFFFFFF;

    if ((I & 0x7F800000) == 0x7F800000)
    {
        // INF or NAN
        Result[2] = 0x3e0;
        if (I & 0x7FFFFF)
        {
            Result[2] = 0x3e0 | (((I >> 18) | (I >> 13) | (I >> 3) | (I)) & 0x1f);
        }
        else if (Sign)
        {
            // -INF is clamped to 0 since 3PK is positive only
            Result[2] = 0;
        }
    }
    else if (Sign)
    {
        // 3PK is positive only, so clamp to zero
        Result[2] = 0;
    }
    else if (I > 0x477C0000U)
    {
        // The number is too large to be represented as a float10, set to max
        Result[2] = 0x3df;
    }
    else
    {
        if (I < 0x38800000U)
        {
            // The number is too small to be represented as a normalized float10
            // Convert it to a denormalized value.
            uint32 Shift = 113U - (I >> 23U);
            I = (0x800000U | (I & 0x7FFFFFU)) >> Shift;
        }
        else
        {
            // Rebias the exponent to represent the value as a normalized float10
            I += 0xC8000000U;
        }

        Result[2] = ((I + 0x1FFFFU + ((I >> 18U) & 1U)) >> 18U) & 0x3ffU;
    }

    // Pack Result into memory
    v = (Result[0] & 0x7ff) | ((Result[1] & 0x7ff) << 11) | ((Result[2] & 0x3ff) << 22);
}
NS_GPUBAKING_END