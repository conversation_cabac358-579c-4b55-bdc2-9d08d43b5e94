#pragma once   

#include <vector>
#include <map>
#include <string>
#include <iostream>
#include <fstream>
#include <array>
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Math/FloatConversion.h"
#include "ECS/Develop/Framework/Types.h"

#define DAWN_PRT_BRANCH 1
#define ENABLE_PRT_API  1

#ifndef GPUBAKING_NS_NAME
#    define GPUBAKING_NS_NAME GPUBaking
#endif

#define NS_GPUBAKING_BEGIN namespace GPUBAKING_NS_NAME {
#define NS_GPUBAKING_END  };
NS_GPUBAKING_BEGIN
// Magic numbers for numerical precision.
constexpr float DELTA = 0.00001f;
constexpr float KINDA_SMALL_NUMBER = 1.e-4f;

using int32 = std::int32_t;
using uint32 = std::uint32_t;
using uint8 = std::uint8_t;
using uint64 = std::uint64_t;
using uint16 = std::uint16_t;

enum class EGPUBakingVersion {
    GPUBAKING_EXPORT_VERSION_INVALID = 0,
    GPUBAKING_EXPORT_VERSION_1_0_0 = 1000,
    GPUBAKING_EXPORT_VERSION_1_0_1 = 1001,
    GPUBAKING_EXPORT_VERSION = GPUBAKING_EXPORT_VERSION_1_0_1,
};

/** The number of coefficients that are stored for each light sample. */
constexpr int32 NUM_STORED_LIGHTMAP_COEF = 4;

/** The number of directional coefficients which the lightmap stores for each light sample. */
constexpr int32 NUM_HQ_LIGHTMAP_COEF = 2;

/** The number of simple coefficients which the lightmap stores for each light sample. */
constexpr int32 NUM_LQ_LIGHTMAP_COEF = 2;

/** The index at which simple coefficients are stored in any array containing all NUM_STORED_LIGHTMAP_COEF coefficients. */
constexpr int32 LQ_LIGHTMAP_COEF_INDEX = 2;

struct LightMapOutInfo 
{
    std::array<cross::Float2, 2> LMUVBias;//lightmap uv; prt lightmap uv
    std::array<cross::Float2, 2> LMUVScale;

    std::array<cross::Float2, 4> LocalLMUVBias;//max local light = 4
    std::array<cross::Float2, 4> LocalLMUVScale;
    std::array<cross::ecs::EntityID, 4> LocalGroupID;

    /** The scale to apply to the quantized coefficients when expanding */
    float Scale[NUM_STORED_LIGHTMAP_COEF][4]{ 0 };

    /** Bias value to apply to the coefficients. */
    float Add[NUM_STORED_LIGHTMAP_COEF][4]{ 0 };
    bool LightMapValid{false};
    std::string Filename;
    std::string SDFShadowFilename;
    std::string SkyOcclusionTex;
    std::string AOMaterialMaskTex;

    bool TransferLMValid{false};
    std::string TransferLightMap0;
    std::string TransferLightMap1;
    float TransferLightMapScale[4][4];
    float TransferLightMapAdd[4][4];//[Coef] [ColorIndex]

    bool LocalTransferLMValid{false};
    std::string LocalTransferLightMap;
    cross::ecs::EntityID LocalLightGroup[4];
    float LocalTransferLightMapScale[4][4];
    float LocalTransferLightMapAdd[4][4];

    bool UseDirectionality{ true };

    //for packed lightmap
    int PackTextureIndex{ -1 };
};

struct float2 {
    float x;
    float y;
    float2() :x(0), y(0) {}
    float2(float x, float y) :x(x), y(y) {}

    friend bool operator != (const float2&A, const float2& B) {
        return fabs(A.x - B.x) + fabs(A.y - B.y) > 0.00001f;
    }

    friend float2 operator + (const float2&A, const float2& B) {
        return float2(A.x + B.x, A.y + B.y);
    }

    friend float2 operator - (const float2&A, const float2& B) {
        return float2(A.x - B.x, A.y - B.y);
    }

    friend float2 operator *(const float2&A, const float& factor) {
        return float2(A.x * factor, A.y * factor);
    }

    friend float2 operator /(const float2&A, const float& factor) {
        return float2(A.x / factor, A.y / factor);
    }
};


struct float3 {
    float x;
    float y;
    float z;

    float3() : x(0), y(0), z(0) {}

    float3(float x) : x(x), y(x), z(x) {}

    float3(float x, float y, float z) :x(x), y(y), z(z) {}

    float3(const cross::Float3& in) :x(in.x), y(in.y), z(in.z) {}

    float3 operator^(const float3& V) const
    {
        return float3
        (
            y * V.z - z * V.y,
            z * V.x - x * V.z,
            x * V.y - y * V.x
        );
    }

    float operator|(const float3& V) const
    {
        return x * V.x + y * V.y + z * V.z;
    }

    float3 operator*=(float Scale)
    {
        x *= Scale; y *= Scale; z *= Scale;
        return *this;
    }

    friend bool operator == (const float3&A, const float3& B) {
        return fabs(A.x - B.x) + fabs(A.y - B.y) < 0.00001f;
    }

    friend float3 operator*(const float3& A, const float& B)
    {
        return {A.x * B, A.y * B, A.z * B};
    }

    friend float3 operator*(const float& B, const float3& A)
    {
        return {A.x * B, A.y * B, A.z * B};
    }
    friend float3 operator/(const float3& A, const float& B)
    {
        return {A.x / B, A.y / B, A.z / B};
    }

    friend float3 operator+(const float3& A, const float3& B)
    {
        return {A.x + B.x, A.y + B.y, A.z + B.z};
    }

    friend float3 operator-(const float3& A, const float3& B)
    {
        return {A.x - B.x, A.y - B.y, A.z - B.z};
    }

    friend float3 operator*(const float3& A, const float3& B)
    {
        return {A.x * B.x, A.y * B.y, A.z * B.z};
    }

    friend float3 operator/(const float3& A, const float3& B)
    {
        return {A.x / B.x, A.y / B.y, A.z / B.z};
    }

    float3& operator+=(const float3& B)
    {
        x += B.x;
        y += B.y;
        z += B.z;
        return *this;
    }

    bool operator==(const float3& V) const
    {
        return x == V.x && y == V.y && z == V.z;
    }

    bool operator!=(const float3& V) const 
    {
        return x != V.x || y != V.y || z != V.z;
    }

    inline float GetSize() const
    {
        return sqrtf(x * x + y * y + z * z);
    }

    inline float GetSizeSquared() const
    {
        return x * x + y * y + z * z;
    }
};


struct float4 {
    float x;
    float y;
    float z;
    float w;

    float4() : x(0), y(0), z(0), w(0) {}

    float4(float x, float y, float z, float w = 1.0f) : x(x), y(y), z(z), w(w) {}

    float4(const float3& v, float w = 1.0f) : x(v.x), y(v.y), z(v.z), w(w) {}

    float4(cross::Float4A v) : x(v.x), y(v.y), z(v.z), w(v.w) {}

    float4& operator+=(const float4& B)
    {
        x += B.x;
        y += B.y;
        z += B.z;
        w += B.w;
        return *this;
    }

    friend float4 operator+(const float4& A, const float4& B)
    {
        return {A.x - B.x, A.y - B.y, A.z - B.z, A.w - B.w};
    }
};

struct float4x4 {
    float m[16];
};

struct int2 {
    int x = 0;
    int y = 0;

    int2() {}

    int2(int x, int y) : x(x), y(y) {}

    friend int2 operator+(const int2& A, const int2& B)
    {
        return int2(A.x + B.x, A.y + B.y);
    }

    friend float2 operator*(const int2& A, const float2& B)
    {
        return float2(A.x * B.x, A.y * B.y);
    }

    friend float2 operator*(const float2& A, const int2& B)
    {
        return float2(A.x * B.x, A.y * B.y);
    }
    friend bool operator==(const int2& A, const int2& B) 
    {
        return A.x == B.x && A.y == B.y;
    }
};

struct int3 {
    int x = 0;
    int y = 0;
    int z = 0;

    int3() {}

    int3(int x) :x(x), y(x), z(x) {}

    int3(int x, int y, int z) :x(x), y(y), z(z) {}

    bool operator==(const int3& Other) const
    {
        return x == Other.x && y == Other.y && z == Other.z;
    }

    bool operator!=(const int3& Other) const 
    {
        return x != Other.x || y != Other.y || z != Other.z;
    }

    friend int3 operator+(const int3& A, const int3& B)
    {
        return int3(A.x + B.x, A.y + B.y, A.z + B.z);
    }

    friend float3 operator+(const int3& A, const float3& B)
    {
        return float3(A.x + B.x, A.y + B.y, A.z + B.z);
    }

    friend float3 operator*(const int3& A, const float& B)
    {
        return float3(A.x * B, A.y * B, A.z * B);
    }

    friend int3 operator*(const int3& A, const int& B)
    {
        return int3(A.x * B, A.y * B, A.z * B);
    }

    friend float3 operator*(const int3& A, const float3& B)
    {
        return float3(A.x * B.x, A.y * B.y, A.z * B.z);
    }

    friend float3 operator/(const int3& A, const float& B)
    {
        return float3(A.x / B, A.y / B, A.z / B);
    }

    friend int3 operator/(const int3& A, const int& B)
    {
        return int3(A.x / B, A.y / B, A.z / B);
    }

    friend float3 operator/(const float3& A, const int3& B)
    {
        return float3(A.x / B.x, A.y / B.y, A.z / B.z);
    }
};

struct int4 {
    int x = 0;
    int y = 0;
    int z = 0;
    int w = 0;
};

struct uint2 {
    unsigned int x = 0;
    unsigned int y = 0;
};


struct uint3 {
    unsigned int x = 0;
    unsigned int y = 0;
    unsigned int z = 0;
};

struct ushort2 {
    uint16 x = 0;
    uint16 y = 0;
};

struct ushort3 {
    uint16 x = 0;
    uint16 y = 0;
    uint16 z = 0;
};

struct ubyte4 {
    uint8 x = 0;
    uint8 y = 0;
    uint8 z = 0;
    uint8 w = 0;
};

struct Matrix4x4 {
public:
    float mm[16];

    Matrix4x4() {};

    Matrix4x4(const cross::Float4x4& mat) {
        mm[0] = mat.m00;
        mm[1] = mat.m01;
        mm[2] = mat.m02;
        mm[3] = mat.m03;
        
        mm[4] = mat.m10;
        mm[5] = mat.m11;
        mm[6] = mat.m12;
        mm[7] = mat.m13;

        mm[8] = mat.m20;
        mm[9] = mat.m21;
        mm[10] = mat.m22;
        mm[11] = mat.m23;

        mm[12] = mat.m30;
        mm[13] = mat.m31;
        mm[14] = mat.m32;
        mm[15] = mat.m33;
    }
};

struct FColor
{
public:
    // Variables.
    // Win32 x86
    // PLATFORM_LITTLE_ENDIAN
    uint8 B, G, R, A;

    bool IsBGRAFormat() const
    {
        // Win32 x86
        return true;
    }

    uint32& DWColor(void)
    {
        return *reinterpret_cast<uint32*>(this);
    }

    const uint32& DWColor(void) const
    {
        return *reinterpret_cast<const uint32*>(this);
    }

    // Constructors.
    FORCEINLINE FColor() {}
    constexpr FORCEINLINE FColor(uint8 InR, uint8 InG, uint8 InB, uint8 InA = 255)
        // put these into the body for proper ordering with INTEL vs non-INTEL_BYTE_ORDER
        : B(InB)
        , G(InG)
        , R(InR)
        , A(InA)
    {}

    FORCEINLINE explicit FColor(uint32 InColor)
    {
        DWColor() = InColor;
    }

    // Operators.
    FORCEINLINE bool operator==(const FColor& C) const
    {
        return DWColor() == C.DWColor();
    }

    FORCEINLINE bool operator!=(const FColor& C) const
    {
        return DWColor() != C.DWColor();
    }

    FORCEINLINE void operator+=(const FColor& C)
    {
        R = static_cast<uint8>(std::fmin(static_cast<int32>(R) + C.R, 255));
        G = static_cast<uint8>(std::fmin(static_cast<int32>(G) + C.G, 255));
        B = static_cast<uint8>(std::fmin(static_cast<int32>(B) + C.B, 255));
        A = static_cast<uint8>(std::fmin(static_cast<int32>(A) + C.A, 255));
    }

    /**
     *	@return a new FColor based of this color with the new alpha value.
     *	Usage: const FColor& MyColor = FColorList::Green.WithAlpha(128);
     */
    FColor WithAlpha(uint8 Alpha) const
    {
        return FColor(R, G, B, Alpha);
    }

    /**
     * Converts this color value to a string.
     *
     * @return The string representation.
     * @see ToHex
     */
    FORCEINLINE std::string ToString() const
    {
        return std::string("RGBA=(") + std::to_string(R) + "," + std::to_string(G) + "," + std::to_string(B) + "," + std::to_string(A) + ")";
    }

    /**
     * Gets the color in a packed uint32 format packed in the order ARGB.
     */
    FORCEINLINE uint32 ToPackedARGB() const
    {
        return (A << 24) | (R << 16) | (G << 8) | (B << 0);
    }

    /**
     * Gets the color in a packed uint32 format packed in the order ABGR.
     */
    FORCEINLINE uint32 ToPackedABGR() const
    {
        return (A << 24) | (B << 16) | (G << 8) | (R << 0);
    }

    /**
     * Gets the color in a packed uint32 format packed in the order RGBA.
     */
    FORCEINLINE uint32 ToPackedRGBA() const
    {
        return (R << 24) | (G << 16) | (B << 8) | (A << 0);
    }

    /**
     * Gets the color in a packed uint32 format packed in the order BGRA.
     */
    FORCEINLINE uint32 ToPackedBGRA() const
    {
        return (B << 24) | (G << 16) | (R << 8) | (A << 0);
    }
};

/**
 * A linear, 32-bit/component floating point RGBA color.
 */
struct FLinearColor
{
    float R{0.f};
    float G{0.f};
    float B{0.f};
    float A{0.f};

    /** Static lookup table used for FColor -> FLinearColor conversion. Pow(2.2) */
    static float Pow22OneOver255Table[256];

    /** Static lookup table used for FColor -> FLinearColor conversion. sRGB */
    static float sRGBToLinearTable[256];

    FORCEINLINE FLinearColor() {}
    constexpr FORCEINLINE FLinearColor(float InR, float InG, float InB, float InA = 1.0f)
            : R(InR)
            , G(InG)
            , B(InB)
            , A(InA)
    {}

    // Operators.
    FORCEINLINE float& Component(int32 Index)
    {
        return (&R)[Index];
    }

    FORCEINLINE const float& Component(int32 Index) const
    {
        return (&R)[Index];
    }

    FORCEINLINE FLinearColor operator+(const FLinearColor& ColorB) const
    {
        return FLinearColor(this->R + ColorB.R, this->G + ColorB.G, this->B + ColorB.B, this->A + ColorB.A);
    }
    FORCEINLINE FLinearColor& operator+=(const FLinearColor& ColorB)
    {
        R += ColorB.R;
        G += ColorB.G;
        B += ColorB.B;
        A += ColorB.A;
        return *this;
    }

    FORCEINLINE FLinearColor operator-(const FLinearColor& ColorB) const
    {
        return FLinearColor(this->R - ColorB.R, this->G - ColorB.G, this->B - ColorB.B, this->A - ColorB.A);
    }
    FORCEINLINE FLinearColor& operator-=(const FLinearColor& ColorB)
    {
        R -= ColorB.R;
        G -= ColorB.G;
        B -= ColorB.B;
        A -= ColorB.A;
        return *this;
    }

    FORCEINLINE FLinearColor operator*(const FLinearColor& ColorB) const
    {
        return FLinearColor(this->R * ColorB.R, this->G * ColorB.G, this->B * ColorB.B, this->A * ColorB.A);
    }
    FORCEINLINE FLinearColor& operator*=(const FLinearColor& ColorB)
    {
        R *= ColorB.R;
        G *= ColorB.G;
        B *= ColorB.B;
        A *= ColorB.A;
        return *this;
    }

    FORCEINLINE FLinearColor operator*(float Scalar) const
    {
        return FLinearColor(this->R * Scalar, this->G * Scalar, this->B * Scalar, this->A * Scalar);
    }

    FORCEINLINE FLinearColor& operator*=(float Scalar)
    {
        R *= Scalar;
        G *= Scalar;
        B *= Scalar;
        A *= Scalar;
        return *this;
    }

    FORCEINLINE FLinearColor operator/(const FLinearColor& ColorB) const
    {
        return FLinearColor(this->R / ColorB.R, this->G / ColorB.G, this->B / ColorB.B, this->A / ColorB.A);
    }
    FORCEINLINE FLinearColor& operator/=(const FLinearColor& ColorB)
    {
        R /= ColorB.R;
        G /= ColorB.G;
        B /= ColorB.B;
        A /= ColorB.A;
        return *this;
    }

    FORCEINLINE FLinearColor operator/(float Scalar) const
    {
        const float InvScalar = 1.0f / Scalar;
        return FLinearColor(this->R * InvScalar, this->G * InvScalar, this->B * InvScalar, this->A * InvScalar);
    }
    FORCEINLINE FLinearColor& operator/=(float Scalar)
    {
        const float InvScalar = 1.0f / Scalar;
        R *= InvScalar;
        G *= InvScalar;
        B *= InvScalar;
        A *= InvScalar;
        return *this;
    }

    // clamped in 0..1 range
    FORCEINLINE FLinearColor GetClamped(float InMin = 0.0f, float InMax = 1.0f) const
    {
        FLinearColor Ret;

        Ret.R = std::clamp(R, InMin, InMax);
        Ret.G = std::clamp(G, InMin, InMax);
        Ret.B = std::clamp(B, InMin, InMax);
        Ret.A = std::clamp(A, InMin, InMax);

        return Ret;
    }

    /** Quantizes the linear color and returns the result as a FColor with optional sRGB conversion and quality as goal. */
    FColor ToFColor(const bool bSRGB) const;

    /** Comparison operators */
    FORCEINLINE bool operator==(const FLinearColor& ColorB) const
    {
        return this->R == ColorB.R && this->G == ColorB.G && this->B == ColorB.B && this->A == ColorB.A;
    }
    FORCEINLINE bool operator!=(const FLinearColor& Other) const
    {
        return this->R != Other.R || this->G != Other.G || this->B != Other.B || this->A != Other.A;
    }

    // Error-tolerant comparison.
    FORCEINLINE bool Equals(const FLinearColor& ColorB, float Tolerance = (1.e-4f)) const
    {
        return std::fabs(this->R - ColorB.R) < Tolerance && std::fabs(this->G - ColorB.G) < Tolerance && std::fabs(this->B - ColorB.B) < Tolerance && std::fabs(this->A - ColorB.A) < Tolerance;
    }

    /** Computes the perceptually weighted luminance value of a color. */
    inline float ComputeLuminance() const
    {
        return R * 0.3f + G * 0.59f + B * 0.11f;
    }

    /**
     * Returns the maximum value in this color structure
     *
     * @return The maximum color channel value
     */
    FORCEINLINE float GetMax() const
    {
        return std::fmax(std::fmax(std::fmax(R, G), B), A);
    }

    /**
     * Returns the minimum value in this color structure
     *
     * @return The minimum color channel value
     */
    FORCEINLINE float GetMin() const
    {
        return std::fmin(std::fmin(std::fmin(R, G), B), A);
    }

    FORCEINLINE float GetLuminance() const
    {
        return R * 0.3f + G * 0.59f + B * 0.11f;
    }

    FColor Quantize() const
    {
        auto TruncFloat2Uint8 = [](float v) { return std::clamp<uint8>(static_cast<uint8>(std::truncf(v * 255.f)), 0, 255); };
        return FColor(TruncFloat2Uint8(R), TruncFloat2Uint8(G), TruncFloat2Uint8(B), TruncFloat2Uint8(A));
    }

    FColor QuantizeRound() const
    {
        auto RoundFloat2Uint8 = [](float v) { return std::clamp<uint8>(static_cast<uint8>(std::roundf(v * 255.f)), 0, 255); };
        return FColor(RoundFloat2Uint8(R), RoundFloat2Uint8(G), RoundFloat2Uint8(B), RoundFloat2Uint8(A));
    }

    std::string ToString() const
    {
        return std::string("RGBA=(") + std::to_string(R) + "," + std::to_string(G) + "," + std::to_string(B) + "," + std::to_string(A) + ")";
    }
};

FORCEINLINE FLinearColor operator*(float Scalar, const FLinearColor& Color)
{
    return Color.operator*(Scalar);
}

class FFloat32
{
public:
    union
    {
        struct
        {
            // PLATFORM_LITTLE_ENDIAN
            uint32 Mantissa : 23;
            uint32 Exponent : 8;
            uint32 Sign     : 1;
        } Components;

        float FloatValue;
    };

    /**
     * Constructor
     *
     * @param InValue value of the float.
     */
    FFloat32(float InValue = 0.0f)
        : FloatValue(InValue)
    {}
};

/**
 * 16 bit float components and conversion
 *
 *
 * IEEE float 16
 * Represented by 10-bit mantissa M, 5-bit exponent E, and 1-bit sign S
 *
 * Specials:
 *
 * E=0, M=0             == 0.0
 * E=0, M!=0            == Denormalized value (M / 2^10) * 2^-14
 * 0<E<31, M=any        == (1 + M / 2^10) * 2^(E-15)
 * E=31, M=0            == Infinity
 * E=31, M!=0           == NAN
 *
 */
class FFloat16
{
public:
    union
    {
        struct
        {
            // PLATFORM_LITTLE_ENDIAN
            uint16 Mantissa : 10;
            uint16 Exponent : 5;
            uint16 Sign     : 1;
        } Components;

        uint16 Encoded;
    };

    FFloat16()
        : Encoded(0)
    {}

    /** Default constructor */
    FFloat16(uint16 encodedf16)
        : Encoded(encodedf16)
    {}

    /** Conversion constructor. Convert from Fp32 to Fp16. */
    FFloat16(float FP32Value)
    {
        Set(FP32Value);
    }

    void Set(float FP32Value)
    {
        FFloat32 FP32(FP32Value);

        // Copy sign-bit
        Components.Sign = FP32.Components.Sign;

        // Check for zero, denormal or too small value.
        if (FP32.Components.Exponent <= 112)   // Too small exponent? (0+127-15)
        {
            // Set to 0.
            Components.Exponent = 0;
            Components.Mantissa = 0;

            // Exponent unbias the single, then bias the halfp
            const int32 NewExp = FP32.Components.Exponent - 127 + 15;

            if ((14 - NewExp) <= 24)   // Mantissa might be non-zero
            {
                uint32 Mantissa = FP32.Components.Mantissa | 0x800000;   // Hidden 1 bit
                Components.Mantissa = static_cast<uint16>(Mantissa >> (14 - NewExp));
                // Check for rounding
                if ((Mantissa >> (13 - NewExp)) & 1)
                {
                    Encoded++;   // Round, might overflow into exp bit, but this is OK
                }
            }
        }
        // Check for INF or NaN, or too high value
        else if (FP32.Components.Exponent >= 143)   // Too large exponent? (31+127-15)
        {
            // Set to 65504.0 (max value)
            Components.Exponent = 30;
            Components.Mantissa = 1023;
        }
        // Handle normal number.
        else
        {
            Components.Exponent = static_cast<uint16>(static_cast<int32>(FP32.Components.Exponent) - 127 + 15);
            Components.Mantissa = static_cast<uint16>(FP32.Components.Mantissa >> 13);
        }
    }

    operator float() const
    {
        return GetFloat();
    }

    float GetFloat() const
    {
        FFloat32 Result;

        Result.Components.Sign = Components.Sign;
        if (Components.Exponent == 0)
        {
            uint32 Mantissa = Components.Mantissa;
            if (Mantissa == 0)
            {
                // Zero.
                Result.Components.Exponent = 0;
                Result.Components.Mantissa = 0;
            }
            else
            {
                // Denormal.
                uint32 MantissaShift = 10 - static_cast<uint32>(std::truncf(Log2(static_cast<float>(Mantissa))));
                Result.Components.Exponent = 127 - (15 - 1) - MantissaShift;
                Result.Components.Mantissa = Mantissa << (MantissaShift + 23 - 10);
            }
        }
        else if (Components.Exponent == 31)   // 2^5 - 1
        {
            // Infinity or NaN. Set to 65504.0
            Result.Components.Exponent = 142;
            Result.Components.Mantissa = 8380416;
        }
        else
        {
            // Normal number.
            Result.Components.Exponent = static_cast<int32>(Components.Exponent) - 15 + 127;   // Stored exponents are biased by half their range.
            Result.Components.Mantissa = static_cast<uint32>(Components.Mantissa) << 13;
        }

        return Result.FloatValue;
    }
};

/**
 * 3 component vector corresponding to DXGI_FORMAT_R11G11B10_FLOAT.
 * Conversion code from XMFLOAT3PK in DirectXPackedVector.h
 */
class FFloat3Packed
{
public:
    union
    {
        struct
        {
            uint32_t xm : 6;   // x-mantissa
            uint32_t xe : 5;   // x-exponent
            uint32_t ym : 6;   // y-mantissa
            uint32_t ye : 5;   // y-exponent
            uint32_t zm : 5;   // z-mantissa
            uint32_t ze : 5;   // z-exponent
        } f;
        uint32_t v;
    };

    FFloat3Packed() {}

    explicit FFloat3Packed(const FLinearColor& Src);

    inline FLinearColor ToLinearColor() const
    {
        {
            uint32 Result[4];
            uint32 Mantissa;
            uint32 Exponent;

            const FFloat3Packed* pSource = this;

            // X Channel (6-bit mantissa)
            Mantissa = pSource->f.xm;

            if (pSource->f.xe == 0x1f)   // INF or NAN
            {
                Result[0] = 0x7f800000 | (pSource->f.xm << 17);
            }
            else
            {
                if (pSource->f.xe != 0)   // The value is normalized
                {
                    Exponent = pSource->f.xe;
                }
                else if (Mantissa != 0)   // The value is denormalized
                {
                    // Normalize the value in the resulting float
                    Exponent = 1;

                    do
                    {
                        Exponent--;
                        Mantissa <<= 1;
                    } while ((Mantissa & 0x40) == 0);

                    Mantissa &= 0x3F;
                }
                else   // The value is zero
                {
                    Exponent = static_cast<uint32>(-112);
                }

                Result[0] = ((Exponent + 112) << 23) | (Mantissa << 17);
            }

            // Y Channel (6-bit mantissa)
            Mantissa = pSource->f.ym;

            if (pSource->f.ye == 0x1f)   // INF or NAN
            {
                Result[1] = 0x7f800000 | (pSource->f.ym << 17);
            }
            else
            {
                if (pSource->f.ye != 0)   // The value is normalized
                {
                    Exponent = pSource->f.ye;
                }
                else if (Mantissa != 0)   // The value is denormalized
                {
                    // Normalize the value in the resulting float
                    Exponent = 1;

                    do
                    {
                        Exponent--;
                        Mantissa <<= 1;
                    } while ((Mantissa & 0x40) == 0);

                    Mantissa &= 0x3F;
                }
                else   // The value is zero
                {
                    Exponent = static_cast<uint32>(-112);
                }

                Result[1] = ((Exponent + 112) << 23) | (Mantissa << 17);
            }

            // Z Channel (5-bit mantissa)
            Mantissa = pSource->f.zm;

            if (pSource->f.ze == 0x1f)   // INF or NAN
            {
                Result[2] = 0x7f800000 | (pSource->f.zm << 17);
            }
            else
            {
                if (pSource->f.ze != 0)   // The value is normalized
                {
                    Exponent = pSource->f.ze;
                }
                else if (Mantissa != 0)   // The value is denormalized
                {
                    // Normalize the value in the resulting float
                    Exponent = 1;

                    do
                    {
                        Exponent--;
                        Mantissa <<= 1;
                    } while ((Mantissa & 0x20) == 0);

                    Mantissa &= 0x1F;
                }
                else   // The value is zero
                {
                    Exponent = static_cast<uint32>(-112);
                }

                Result[2] = ((Exponent + 112) << 23) | (Mantissa << 18);
            }

            FLinearColor ResultColor;
            ResultColor.R = *reinterpret_cast<float*>(&Result[0]);
            ResultColor.G = *reinterpret_cast<float*>(&Result[1]);
            ResultColor.B = *reinterpret_cast<float*>(&Result[2]);
            ResultColor.A = 0;
            return ResultColor;
        }
    }

    bool IsBGRAFormat() const
    {
        return false;
    }
};

NS_GPUBAKING_END