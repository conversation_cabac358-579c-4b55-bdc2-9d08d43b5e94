#include "LocationShapeModule.h"
#include "ModulePipeline.h"
#include "CECommon/Utilities/Random.h"
#include "Resource/ParticleSystem/MinMaxCurve.h"
#include "../ParticleSystemUtils.h"

namespace cross::fx {

static inline Float3 RandomUnitVec3(Rand& random)
{
    float z = RangedRandom(random, -1.0f, 1.0f);
    float a = Random01(random) * MathUtils::Math2Pi;
    float r = std::sqrt(1.0f - z * z);

    float x = r * std::cos(a);
    float y = -r * std::sin(a);

    return Float3(x, y, z);
}

static inline Float3 GetRandomPointInUnitSphere(Rand& random)
{
    Float3 v = RandomUnitVec3(random);
    v *= std::pow(Random01(random), 1.0f / 3.0f);
    return v;
}

static inline void RandomUnitVec2(Float2& v2, Rand& random)
{
    float a = Random01(random) * MathUtils::Math2Pi;
    v2.x = cos(a);
    v2.y = -sin(a);
}

static inline void RandomPointInsideUnitCircle(Float2& v2, Rand& random)
{
    RandomUnitVec2(v2, random);

    // As the volume of the sphere increases (x^3) over an interval we have to
    // increase range as well with x^(1/3)
    v2 *= pow(Random01(random), 1.0f / 2.0f);
}

static inline Float2 GetRandomPointInCircle(Rand& random, float arc = 2.0f * MathUtils::MathPi)
{
    return Float2(std::cos(arc), -std::sin(arc)) * std::sqrt(Random01(random));
}

void LocationShapeModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("LocationShapeModule");
    if (static_cast<UInt32>(context.SpawnCount) < 1u)
    {
        return;
    }

    switch (mLocationShape->ShapeType)
    {
    case LocationShapeType::Box:
    {
        EmitFromBox(context);
        break;
    }
    case LocationShapeType::Sphere:
    {
        EmitFromSphere(context);
        break;
    }
    case LocationShapeType::Cone:
    {
        EmitFromCone(context);
        break;
    }
    case LocationShapeType::Circle:
    {
        EmitFromCircle(context);
        break;
    }
    case LocationShapeType::MeshSurface:
    {
        EmitFromMesh(context);
        break;
    }
    default:
        Assert(false);
        break;
    }
}

void LocationShapeModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mLocationShape = std::make_shared<LocationShapeInfo>(emitterInfo.LocationShape);
    mEnabled = mLocationShape->Enabled;

    switch (mLocationShape->ShapeType)
    {
    case LocationShapeType::Cone:
    case LocationShapeType::Circle:
        mMaxArcRadian = MathUtils::ConvertToRadians(mLocationShape->Arc);
    default:
        break;
    }

    mLastRadian = 0.0f;
    mLastArc = 0.0f;
    mRemainderArc = 0.0f;
    mSign = 1.0f;
    mFirstSpread = true;
    //mArcGap = mLocationShape->RadiusSpread > 0.01f ? mMaxArcRadian * mLocationShape->RadiusSpread : 0.0f;

    for (auto i = 0u; i < mSampleMeshes.size(); ++i)
    {
        mSampleMeshes[i].SetEmitType(mLocationShape->EmitType);
    }
}

size_t LocationShapeModule::Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output)
{
    mOffset = offset;
    offset += TransferProperty(mLocationShape->ShapeType, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->PositionOffset, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->EmitFrom, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->PositionOffsetScalar, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->Radius, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->Box, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->UDistribution, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->DiskCoverage, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->UniformSpiralAmount, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->UniformSpiralFalloff, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->DistributionType, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->EmitType, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->Length, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->Angle, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->InnerAngle, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->Axis, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->Arc, curvePool, output, offset);
    offset += TransferProperty(Float3::Zero(), curvePool, output, offset);
    offset += TransferProperty(mLocationShape->MoveState.PositionCurve, curvePool, output, offset);
    offset += TransferProperty(mLocationShape->MoveState.BoxCurve, curvePool, output, offset);

    mStride = offset - mOffset;
    return offset;
}

void LocationShapeModule::UpdateInplace(ParticleCurvePool& curvePool, std::vector<UInt8>& output)
{
    if (mStride == 0)
        return;
    Assert(mOffset + mStride <= output.size());
    auto offset = mOffset;
    offset += UpdatePropertyInplace(mLocationShape->ShapeType, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->PositionOffset, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->EmitFrom, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->PositionOffsetScalar, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->Radius, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->Box, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->UDistribution, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->DiskCoverage, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->UniformSpiralAmount, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->UniformSpiralFalloff, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->DistributionType, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->EmitType, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->Length, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->Angle, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->InnerAngle, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->Axis, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->Arc, curvePool, output, offset);
    offset += UpdatePropertyInplace(Float3::Zero(), curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->MoveState.PositionCurve, curvePool, output, offset);
    offset += UpdatePropertyInplace(mLocationShape->MoveState.BoxCurve, curvePool, output, offset);

    Assert(offset - mOffset == mStride);
}

void LocationShapeModule::ResetSeed(UInt32 randomSeed)
{
    mRandom.SetSeed(randomSeed);
}

void LocationShapeModule::UpdateTilePosition(ModulePipelineContext& context, UInt32 particleIndex)
{
    if (!context.LocalSpace)
    {
        Float3* tilePosition = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(BuiltInProperty::ce_TilePosition, ParticleDataType::Float3));
        Float3* preTilePosition = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(BuiltInProperty::ce_PreTilePosition, ParticleDataType::Float3));
        Assert(tilePosition && preTilePosition);
        tilePosition[particleIndex] = preTilePosition[particleIndex] = context.BaseTile;
    }
}

void LocationShapeModule::EmitFromBox(ModulePipelineContext& context)
{
    float x = 0.0f;
    float y = 0.0f;
    float z = 0.0f;
    float xRange = mLocationShape->Box.x * 0.5f;
    float yRange = mLocationShape->Box.y * 0.5f;
    float zRange = mLocationShape->Box.z * 0.5f;

    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));
    const UInt32 spawnedNum = static_cast<UInt32>(context.SpawnedSlots.size());
    for (auto index = 0u; index < spawnedNum; ++index)
    {
        UInt32 particleIndex = context.SpawnedSlots[index];
        switch (mLocationShape->EmitFrom)
        {
        case EmitFromType::Volume:
        {
            x = RangedRandom(mRandom, -1.0f, 1.0f) * xRange;
            y = RangedRandom(mRandom, -1.0f, 1.0f) * yRange;
            z = RangedRandom(mRandom, -1.0f, 1.0f) * zRange;
            break;
        }
        case EmitFromType::Shell:
        {
            auto i = mRandom.Get() % 3;   // 0, 1, 2
            auto j = mRandom.Get() % 2;   // 0, 1
            switch (i)
            {
            case 0:// x
            {
                x = (j == 0) ? -xRange : xRange;
                y = RangedRandom(mRandom, -1.0f, 1.0f) * yRange;
                z = RangedRandom(mRandom, -1.0f, 1.0f) * zRange;
                break;
            }
            case 1:// y
            {
                y = (j == 0) ? -yRange : yRange;
                x = RangedRandom(mRandom, -1.0f, 1.0f) * xRange;
                z = RangedRandom(mRandom, -1.0f, 1.0f) * zRange;
                break;
            }
            case 2:// z
            {
                z = (j == 0) ? -zRange : zRange;
                x = RangedRandom(mRandom, -1.0f, 1.0f) * xRange;
                y = RangedRandom(mRandom, -1.0f, 1.0f) * yRange;
                break;
            }
            }
            break;
        }
        case EmitFromType::Edge:
        {
            SInt32 i = mRandom.Get() % 4;   // 0, 1, 2, 3
            SInt32 j = mRandom.Get() % 2;   // 0, 1
            SInt32 k = mRandom.Get() % 2;   // 0, 1

            switch (i)
            {
            case 0:// x
            {
                x = (j == 0) ? -xRange : xRange;
                y = RangedRandom(mRandom, -1.0f, 1.0f) * yRange;
                z = (k == 0) ? -zRange : zRange;

                break;
            }
            case 1:// y
            {
                y = (j == 0) ? -yRange : yRange;
                x = RangedRandom(mRandom, -1.0f, 1.0f) * xRange;
                z = (k == 0) ? -zRange : zRange;
                break;
            }
            case 2:// z
            {
                z = (j == 0) ? -zRange : zRange;
                x = (k == 0) ? -xRange : xRange;
                y = RangedRandom(mRandom, -1.0f, 1.0f) * yRange;

                break;
            }
            case 3:// z=[-1,1], y=[-1,1], x=[-1,1]
            {
                z = RangedRandom(mRandom, -1.0f, 1.0f) * zRange;
                x = (k == 0) ? -xRange : xRange;
                y = (j == 0) ? -yRange : yRange;
                break;
            }
            }
            break;
        }
        default:
            break;
        }

        Float3 position(x, y, z);
        position += (mLocationShape->PositionOffset * mLocationShape->PositionOffsetScalar);
        if (!context.LocalSpace)
        {
            position = Float4x4::TransformPointF3(context.RelativeWorldMatrix, position); 
        }
        InitEvaluateParticle<Float3, Float3>(context, position, { RendererProp::POSITION, ParticleDataType::Float3 }, nullptr, static_cast<SInt32>(particleIndex));
        const Float3 defaultVelocity = context.EnableOriginVelocity ? Float3::Zero() : Float3(0, 1, 0);
        TransformParticleSpeed(context, defaultVelocity, particleIndex, velocitys);
        UpdateTilePosition(context, particleIndex);
    }
}

void LocationShapeModule::EmitFromSphere(ModulePipelineContext& context)
{
    float R = mLocationShape->Radius;
    Float3 position;
    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));

    const UInt32 spawnedNum = static_cast<UInt32>(context.SpawnedSlots.size());
    for (auto index = 0u; index < spawnedNum; ++index)
    {
        UInt32 particleIndex = context.SpawnedSlots[index];
        switch (mLocationShape->EmitFrom)
        {
        case EmitFromType::Volume:
        {
            position = GetRandomPointInUnitSphere(mRandom) * R;
            break;
        }
        case EmitFromType::Shell:
        {
            position = RandomUnitVec3(mRandom) * R;
            break;
        }
        case EmitFromType::HemiSphereVolume:
        {
            position = GetRandomPointInUnitSphere(mRandom) * R;
            if (position.y < 0.0f)
            {
                position.y *= -1.0f;
            }
            break;
        }
        case EmitFromType::HemiSphereShell:
        {
            position = RandomUnitVec3(mRandom) * R;
            if (position.y < 0.0f)
            {
                position.y *= -1.0f;
            }
            break;
        }
        default:
            break;
        }

        Float3 originVelocity = context.EnableOriginVelocity ? Float3::Zero() : position.Normalized();
        position += (mLocationShape->PositionOffset * mLocationShape->PositionOffsetScalar);
        if (!context.LocalSpace)
        {
            position = Float4x4::TransformPointF3(context.RelativeWorldMatrix, position);
        }
        InitEvaluateParticle<Float3, Float3>(context, position, { RendererProp::POSITION, ParticleDataType::Float3 }, nullptr, static_cast<SInt32>(particleIndex));
        if (mLocationShape->EmitFrom == EmitFromType::HemiSphereVolume || mLocationShape->EmitFrom == EmitFromType::HemiSphereShell)
        {
            if (originVelocity.y < 0.0f)
            {
                originVelocity.y = 0.0f;
            }
        }
        TransformParticleSpeed(context, originVelocity, particleIndex, velocitys);
        UpdateTilePosition(context, particleIndex);
    }
}

void LocationShapeModule::EmitFromCone(ModulePipelineContext& context)
{
    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));
    const float arc = mLocationShape->Arc;
    const float halfAngleRadian = (MathUtils::MathPi / 180.0f) * mLocationShape->Angle * 0.5f;
    const float halfInnerAngleRadian = (MathUtils::MathPi / 180.0f) * mLocationShape->InnerAngle * 0.5f;
    const float cosHalfAngle = cos(halfAngleRadian);
    const float cosHalfInnerAngle = cos(halfInnerAngleRadian);
    const float radialAngleRadian = clamp((MathUtils::MathPi / 180.0f) * arc, 0.0f, MathUtils::Math2Pi); 
    auto axis = mLocationShape->Axis;
    if (axis.Length() < MathUtils::MathEps)
        axis = {0, 0, 1};
    else
        axis.Normalize();
    Float2 point;
    const UInt32 spawnedNum = static_cast<UInt32>(context.SpawnedSlots.size());
    for (auto index = 0u; index < spawnedNum; ++index)
    {
        UInt32 particleIndex = context.SpawnedSlots[index];
        float cosTargetAngle = RangedRandom(mRandom, cosHalfAngle, cosHalfInnerAngle);
        float targetAngleRadian = acos(clamp(cosTargetAngle, -1.0f, 1.0f));
        cosTargetAngle = cos(targetAngleRadian);
        Float3 targetZ = cosTargetAngle * Float3(0, 0, 1);
        float targetXYPlaneLen = sin(targetAngleRadian);
        float targetRadialAngleRadian = RangedRandom(mRandom, 0.0f, radialAngleRadian);
        float cosRadialAngle = cos(targetRadialAngleRadian);
        Float3 targetX = cosRadialAngle * Float3(1, 0, 0);
        float sinRadialAngle = sin(targetRadialAngleRadian);
        Float3 targetY = sinRadialAngle * Float3(0, 1, 0);

        Float3 position = (targetX + targetY) * targetXYPlaneLen + targetZ;
        float distribution = std::max(mLocationShape->UDistribution, 0.0f);
        if (distribution != 1.0f)
        {
            distribution = pow(std::max(0.0f, RangedRandom(mRandom, distribution, 1.0f)), 0.333333f);
        }
        position *= distribution;
        auto quat = Quaternion::CreateFrom2Vectors(Float3(0, 0, 1), axis);
        position = quat.Float3Rotate(position) * mLocationShape->Length;

        position += (mLocationShape->PositionOffset * mLocationShape->PositionOffsetScalar);
        if (!context.LocalSpace)
        {
            position = Float4x4::TransformPointF3(context.RelativeWorldMatrix, position);
        }
        InitEvaluateParticle<Float3, Float3>(context, position, { RendererProp::POSITION, ParticleDataType::Float3 }, nullptr, static_cast<SInt32>(particleIndex));

        Float3 defaultVelocity = context.EnableOriginVelocity ? Float3::Zero() : axis;
        TransformParticleSpeed(context, defaultVelocity, particleIndex, velocitys);
        UpdateTilePosition(context, particleIndex);
    }
}

void LocationShapeModule::EmitFromCircle(ModulePipelineContext& context)
{
    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));
    float R = mLocationShape->Radius;
    Float2 point;
    float period = 1;
    auto GetPointOnDisk = [](float theta, float radius, float period) {
        Float2 res;
        res.x = cos(theta * (MathUtils::Math2Pi / period)) * radius;
        res.y = sin(theta * (MathUtils::Math2Pi / period)) * radius;
        return res;
    };

    auto UpdatePositionAndVelocity = [&](UInt32 particleIndex) {
        Float3 position(point.x, 0.0f, point.y);
        position += (mLocationShape->PositionOffset * mLocationShape->PositionOffsetScalar);
        if (!context.LocalSpace)
        {
            position = Float4x4::TransformPointF3(context.RelativeWorldMatrix, position);
        }
        InitEvaluateParticle<Float3, Float3>(context, position, {RendererProp::POSITION, ParticleDataType::Float3}, nullptr, static_cast<SInt32>(particleIndex));

        Float3 originVelocity = context.EnableOriginVelocity ? Float3::Zero() : Float3(point.x, 0.0f, point.y).Normalized();
        TransformParticleSpeed(context, originVelocity, particleIndex, velocitys);
        UpdateTilePosition(context, particleIndex);
    };

    const UInt32 spawnedNum = static_cast<UInt32>(context.SpawnedSlots.size());
    if (mLocationShape->DistributionType == EmitDistributionType::Random)
    {
        const float diskCoverage = mLocationShape->DiskCoverage;    //r
        const float distribution = mLocationShape->UDistribution;   //theta
        const float coverage = clamp(1 - diskCoverage, 0.0f, 999.0f);
        for (auto index = 0u; index < spawnedNum; ++index)
        {
            UInt32 particleIndex = context.SpawnedSlots[index];
            float r = RangedRandom(mRandom, coverage, 1.0f);
            r = sqrt(r) * R;
            float theta = RangedRandom(mRandom, clamp(distribution, 0.0f, 1.0f), 1.0f);
            point = GetPointOnDisk(theta, r, period);
            UpdatePositionAndVelocity(particleIndex);
        }
    }
    else if (mLocationShape->DistributionType == EmitDistributionType::Uniform)
    {
        const float spiralFallOff = mLocationShape->UniformSpiralFalloff;
        const float spriralAmount = mLocationShape->UniformSpiralAmount;
        for (auto index = 0u; index < spawnedNum; ++index)
        {
            UInt32 particleIndex = context.SpawnedSlots[index];
            const float normalizedExecIndex = std::max(float(index) / spawnedNum, 1e-6f);
            float r = std::pow(std::max(0.0f, sqrt(normalizedExecIndex)), spiralFallOff) * R;
            float theta = 1.61803f * index;
            period = spriralAmount;
            point = GetPointOnDisk(theta, r, period);
            UpdatePositionAndVelocity(particleIndex);
        }
    }
    else if (mLocationShape->DistributionType == EmitDistributionType::Direct)
    {
        for (auto index = 0u; index < spawnedNum; ++index)
        {
            UInt32 particleIndex = context.SpawnedSlots[index];
            float r = R;
            float theta = context.Age;
            point = GetPointOnDisk(theta, r, period);
            UpdatePositionAndVelocity(particleIndex);
        }
    }
    else
    {
        Assert(false);
    }
}

void LocationShapeModule::EmitFromMesh(ModulePipelineContext& context)
{
    if (mSampleMeshes.empty())
    {
        return;
    }

    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));
    const UInt32 spawnedNum = static_cast<UInt32>(context.SpawnedSlots.size());
    for (auto index = 0u; index < spawnedNum; ++index)
    {
        UInt32 particleIndex = context.SpawnedSlots[index];
        UInt32 meshPartIndex = RangedRandom(mRandom, 0u, static_cast<UInt32>(mSampleMeshes.size()));
        auto [position, velocity] = mSampleMeshes[meshPartIndex].GetEmitPoint(mLocationShape->EmitType, mRandom);

        position += mLocationShape->PositionOffset * mLocationShape->PositionOffsetScalar;
        if (!context.LocalSpace)
        {
            position = Float4x4::TransformPointF3(context.RelativeWorldMatrix, position);
        }
        InitEvaluateParticle<Float3, Float3>(context, position, {RendererProp::POSITION, ParticleDataType::Float3}, nullptr, static_cast<SInt32>(particleIndex));
        if (context.EnableOriginVelocity)
            velocity = Float3::Zero();
        TransformParticleSpeed(context, velocity, particleIndex, velocitys);
        UpdateTilePosition(context, particleIndex);
    }
}

void LocationShapeModule::TransformParticleSpeed(const ModulePipelineContext& context, const Float3& input, UInt32 particleIndex, Float3* outputAddr)
{
    Float3 velocity = input;
    if (!context.LocalSpace)
    {
        velocity = Float4x4::TransformVectorF3(context.RelativeWorldMatrixNoScale, velocity);
        velocity.Normalize();
        velocity += Float4x4::TransformVectorF3(context.RelativeWorldMatrixNoScale, outputAddr[particleIndex]);
    }
    else
    {
        velocity += outputAddr[particleIndex];
    }
    if (input.Length() > 0)
    {
        velocity *= context.ParticleStates.startSpeed[particleIndex];
    }
    outputAddr[particleIndex] = velocity;
}

}   // namespace cross::fx