#include "VortexForceModule.h"
#include "ModulePipeline.h"
#include "../ParticleSystemUtils.h"

namespace cross::fx {

void VortexForceModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("VortexForceModule");
    Float3 vortexOrigin = context.BasePosition;
    //context.WorldMatrix.CreateTranslation(vortexOrigin);

    const UInt32 particleCount = static_cast<UInt32>(context.ParticleStates.Size());
    Float3* positions = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::POSITION, ParticleDataType::Float3));
    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));
    for (auto particleIndex = 0u; particleIndex < particleCount; ++particleIndex)
    {
        if (!context.ParticleStates.IsAlive(particleIndex))
        {
            continue;
        }
        const float particleAge = context.ParticleStates.GetNormalizedAge(particleIndex);

        Float3 offset;
        offset.x = mVortex->VortexOriginOffset.X.Evaluate(particleAge, mRand);
        offset.y = mVortex->VortexOriginOffset.Y.Evaluate(particleAge, mRand);
        offset.z = mVortex->VortexOriginOffset.Z.Evaluate(particleAge, mRand);

        Float3 originVector = (positions[particleIndex] - (vortexOrigin + offset));
        originVector.Normalize();

        Float3 vortexAxis;
        vortexAxis.x = mVortex->VortexAxis.X.Evaluate(particleAge, mRand);
        vortexAxis.y = mVortex->VortexAxis.Y.Evaluate(particleAge, mRand);
        vortexAxis.z = mVortex->VortexAxis.Z.Evaluate(particleAge, mRand);

        const Float3& vortexVector = originVector.Cross(vortexAxis);
        Float3 vortexForceAmount = vortexVector * mVortex->VortexForceAmount.Evaluate(particleAge, mRand) +
            originVector * mVortex->OriginPullAmount.Evaluate(particleAge, mRand);

        ParticleSystemUtils::ApplyForceTransform(context.WorldMatrix, context.LocalSpace, mVortex->Space, vortexForceAmount);

        velocitys[particleIndex] += vortexForceAmount * context.DeltaTime;
    }
}

void VortexForceModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mVortex = std::make_shared<VortexForceInfo>(emitterInfo.VortexForce);
    mEnabled = mVortex->Enabled;
}

size_t VortexForceModule::Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output)
{
    offset += TransferProperty(mVortex->VortexAxis, curvePool, output, offset);
    offset += TransferProperty(mVortex->VortexOriginOffset, curvePool, output, offset);
    offset += TransferProperty(mVortex->VortexForceAmount, curvePool, output, offset);
    offset += TransferProperty(mVortex->OriginPullAmount, curvePool, output, offset);
    offset += TransferProperty(mVortex->Space, curvePool, output, offset);
    Float3 padding;
    offset += TransferProperty(padding, curvePool, output, offset);
    return offset;
}

void VortexForceModule::ResetSeed(UInt32 randomSeed)
{
    mRand.SetSeed(randomSeed);
}

}   // namespace cross::fx