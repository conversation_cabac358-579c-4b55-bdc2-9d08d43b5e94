#include "UVModule.h"
#include "ModulePipeline.h"
#include "CrossBase/Math/CrossMath.h"

namespace cross::fx {

static float Repeat(float t, float length)
{
    return t - floor(t / length) * length;
}

void UVModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("UVModule");
    const UInt32 particleCount = static_cast<UInt32>(context.ParticleStates.Size());
    Float4* uvScale = context.ParticleParams->GetParameterAddr<Float4>(ParticleVariable(RendererProp::UVSCALE, ParticleDataType::Float4));
    for (auto particleIndex = 0u; particleIndex < particleCount; ++particleIndex)
    {
        if (!context.ParticleStates.IsAlive(particleIndex))
        {
            continue;
        }

        if (mUV->Animation == ParticleAnimationMode::InfinityLoop)
        {
            UpdateGridInfinity(context.ParticleStates, particleIndex, uvScale[particleIndex]);
        }
        else if (mUV->Animation == ParticleAnimationMode::WholeSheet)
        {
            updateGridWholeSheet(context.ParticleStates, particleIndex, uvScale[particleIndex]);
        }
        else if (mUV->Animation == ParticleAnimationMode::SingleRow)
        {
            updateGridSingleRow(context.ParticleStates, particleIndex, uvScale[particleIndex]);
        }

        // z : tile width, w : tile height
        uvScale[particleIndex].z = 1.0f / mUV->TileX;
        uvScale[particleIndex].w = 1.0f / mUV->TileY;
    }
}

void UVModule::UpdateGridInfinity(ParticleStatePool& state, UInt32 particleIndex, Float4& output)
{
    const UInt32 tileX = std::max(1u, mUV->TileX);
    const UInt32 tileY = std::max(1u, mUV->TileY);

    Rand random(state.GetRandomSeed(particleIndex));
    float t = (state.lifetime[particleIndex] - state.age[particleIndex]) / state.lifetime[particleIndex] * mUV->Cycles;
    t = t - std::floor(t);
    float time = state.GetNormalizedAge(particleIndex) * mUV->Cycles;

    if (state.subuvIdxs[particleIndex] > (tileX * tileY))
    {
        state.subuvIdxs[particleIndex] = 0.0f;
    }
    float indexf = mUV->StartFrame.Evaluate(time, random) + state.subuvIdxs[particleIndex];
    UInt32 index = (static_cast<UInt32>(indexf)) % (tileX * tileY);
    UInt32 vIdx = index / tileX;
    UInt32 uIdx = index - vIdx * tileX;
    float offsetU = 1.0f / tileX;
    float offsetV = 1.0f / tileY;

    output.x = uIdx * offsetU;
    output.y = vIdx * offsetV;

    state.subuvIdxs[particleIndex] += std::max(0.000001f, mUV->PlayRate / (tileX * tileY));
}

void UVModule::updateGridWholeSheet(const ParticleStatePool& state, UInt32 particleIndex, Float4& output)
{
    const UInt32 tileX = std::max(1u, mUV->TileX);
    const UInt32 tileY = std::max(1u, mUV->TileY);

    Rand random(state.GetRandomSeed(particleIndex));
    float t = (state.lifetime[particleIndex] - state.age[particleIndex]) / state.lifetime[particleIndex] * mUV->Cycles;
    t = t - std::floor(t);
    float time = state.GetNormalizedAge(particleIndex) * mUV->Cycles;

    float indexf = mUV->Frame.Evaluate(time, random) + mUV->StartFrame.Evaluate(time, random);
    UInt32 index = (static_cast<UInt32>(indexf)) % (std::max(1u, tileX * tileY));
    UInt32 vIdx = index / tileX;
    UInt32 uIdx = index - vIdx * tileX;
    float offsetU = 1.0f / std::max(1u, tileX);
    float offsetV = 1.0f / std::max(1u, tileY);

    output.x = uIdx * offsetU;
    output.y = vIdx * offsetV;
}

void UVModule::updateGridSingleRow(const ParticleStatePool& state, UInt32 particleIndex, Float4& output)
{
    const float maxX = mUV->TileX - 0.0004f;
    const float animRange = (1.0f / (mUV->TileX * mUV->TileY)) * mUV->TileX;
    const float offsetU = 1.0f / mUV->TileX;
    const float offsetV = 1.0f / mUV->TileY;
    const UInt32 tileNum = mUV->TileX * mUV->TileY;

    if (mUV->RowMode == GridRowMode::Random)
    {
        Rand random(state.GetRandomSeed(particleIndex));
        float time = state.GetNormalizedAge(particleIndex);
        const float startRow = std::floor(random.GetFloat() * mUV->TileY);
        float b = startRow * animRange;
        float e = b + animRange;

        float multiplier = mUV->Frame.IsConstant() ? 1.0f : mUV->Frame.GetMultiplier();
        float s = mUV->StartFrame.Evaluate(time, random);
        float t = mUV->Cycles * (mUV->Frame.Evaluate(time, random) / multiplier);
        if (mUV->Frame.IsConstant())
        {
            float fot = mUV->Cycles * mUV->Frame.Evaluate(time, random);
            float sf = std::min<float>(s, maxX);

            UInt32 srow = static_cast<UInt32>(startRow);
            UInt32 idx = static_cast<UInt32>(fot + sf);
            UInt32 ieIdx = mUV->TileX;
            if (idx >= ieIdx)
            {
                idx = idx % (mUV->TileX);
            }

            UInt32 index0 = srow * mUV->TileX + idx;
            UInt32 vIdx = index0 / mUV->TileY;
            UInt32 uIdx = index0 - vIdx * mUV->TileX;

            output.x = static_cast<float>(uIdx) * offsetU;
            output.y = static_cast<float>(vIdx) * offsetV;
        }
        else
        {
            const float x = Repeat(t, 1.0f);
            float sheetIndex = MathUtils::LerpStable<float>(b, e, x) * tileNum + s;
            UInt32 istartRow = static_cast<UInt32>(startRow);
            UInt32 ieIdx = istartRow * mUV->TileX + mUV->TileX;
            if (sheetIndex > ieIdx)
            {
                sheetIndex = (sheetIndex - ieIdx) + istartRow * mUV->TileX;
            }

            const UInt32 index0 = static_cast<UInt32>(sheetIndex);
            UInt32 vIdx = index0 / mUV->TileX;
            UInt32 uIdx = index0 - vIdx * mUV->TileX;

            output.x = static_cast<float>(uIdx) * offsetU;
            output.y = static_cast<float>(vIdx) * offsetV;
        }
    }
    else if (mUV->RowMode == GridRowMode::Custom)
    {
        const float startRow = std::floor(mUV->RowIndex * animRange * mUV->TileY);
        float b = startRow * animRange;
        float e = b + animRange;
        Rand random(state.GetRandomSeed(particleIndex));
        float time = state.GetNormalizedAge(particleIndex);

        float multiplier = mUV->Frame.IsConstant() ? 1.0f : mUV->Frame.GetMultiplier();
        const float s = mUV->StartFrame.Evaluate(time, random);
        const float t = mUV->Cycles * (mUV->Frame.Evaluate(time, random) / multiplier);
        if (mUV->Frame.IsConstant())
        {
            float fot = mUV->Cycles * mUV->Frame.Evaluate(time, random);
            float sf = std::min<float>(s, maxX);

            UInt32 srow = static_cast<UInt32>(startRow);
            UInt32 idx = static_cast<UInt32>(fot + sf);

            UInt32 ieIdx = mUV->TileX;
            if (idx >= ieIdx)
            {
                idx = idx % (mUV->TileX);
            }

            const UInt32 index0 = srow * mUV->TileY + idx;
            UInt32 vIdx = index0 / mUV->TileX;
            UInt32 uIdx = index0 - vIdx * mUV->TileX;

            output.x = static_cast<float>(uIdx) * offsetU;
            output.y = static_cast<float>(vIdx) * offsetV;
        }
        else
        {
            const float x = Repeat(t, 1.0f);
            float sheetIndex = MathUtils::LerpStable<float>(b, e, x) * tileNum + s;

            UInt32 istartRow = static_cast<UInt32>(startRow);
            UInt32 ieIdx = istartRow * mUV->TileX + mUV->TileX;
            if (sheetIndex > ieIdx)
            {
                sheetIndex = (sheetIndex - ieIdx) + istartRow * mUV->TileX;
            }

            const UInt32 index0 = static_cast<UInt32>(sheetIndex);
            UInt32 vIdx = index0 / mUV->TileX;
            UInt32 uIdx = index0 - vIdx * mUV->TileX;

            output.x = static_cast<float>(uIdx) * offsetU;
            output.y = static_cast<float>(vIdx) * offsetV;
        }
    }
}

void UVModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mUV = std::make_shared<SubUVInfo>(emitterInfo.SubUV);
    mEnabled = mUV->Enabled;
}

size_t UVModule::Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output)
{
    offset += TransferProperty(mUV->TileX, curvePool, output, offset);
    offset += TransferProperty(mUV->TileY, curvePool, output, offset);
    offset += TransferProperty(mUV->Animation, curvePool, output, offset);
    offset += TransferProperty(mUV->Cycles, curvePool, output, offset);
    offset += TransferProperty(mUV->StartFrame, curvePool, output, offset);
    offset += TransferProperty(mUV->Frame, curvePool, output, offset);
    offset += TransferProperty(mUV->RowMode, curvePool, output, offset);
    offset += TransferProperty(mUV->RowIndex, curvePool, output, offset);
    offset += TransferProperty(mUV->PlayRate, curvePool, output, offset);
    float padding;
    offset += TransferProperty(padding, curvePool, output, offset);
    return offset;
}

}   // namespace cross::fx