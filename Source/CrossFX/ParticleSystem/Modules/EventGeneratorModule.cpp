#include "EventGeneratorModule.h"
#include "ModulePipeline.h"

namespace cross::fx {

void EventGeneratorModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("EventGeneratorModule");
    return;
}

void EventGeneratorModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mGeneratorInfo = std::make_shared<EventGeneratorInfo>(emitterInfo.EventGenerator);
    mEnabled = mGeneratorInfo->Enabled;
}

}   // namespace cross::fx