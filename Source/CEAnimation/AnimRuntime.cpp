#include "EnginePrefix.h"
#include "CEAnimation/AnimRuntime.h"
#include "CEAnimation/Skeleton/Skeleton.h"

namespace cross::anim 
{
    
    void BlendPosesTogether(std::vector<RootSpacePose>& sourcePoses, const std::vector<float>& sourceWeights, RootSpacePose& outPose)
    {
        Assert(sourcePoses.size() > 0 && sourceWeights.size() > 0);
    
        BlendPose<AnimRuntime::TransformBlendType::Overwrite>(sourcePoses[0], outPose, sourceWeights[0]);
    
        for (int i = 1; i < sourceWeights.size(); i++)
            BlendPose<AnimRuntime::TransformBlendType::Accumulate>(sourcePoses[i], outPose, sourceWeights[i]);
    }
    
    void MirrorNodeTransform(SkBoneHandle inMirrorBoneIndex, const Skeleton* inRunSkelt, Axis::Type inAxis, NodeTransform& outTrans)
    {
        if (inMirrorBoneIndex == SkBoneHandle::InvalidHandle())
            return;
    
        if (inRunSkelt == nullptr)
            return;
    
        if (inAxis == Axis::Type::None)
            return;
    
        static const Float4x4A mirrorAxisX = Float4x4A(-1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f);
    
        static const Float4x4A mirrorAxisY = Float4x4A(1.0f, 0.0f, 0.0f, 0.0f, 0.0f, -1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f);
    
        static const Float4x4A mirrorAxisZ = Float4x4A(1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, -1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f);
    
        Float4x4A mirrorM;
        if (inAxis == Axis::Type::X)
            mirrorM = mirrorAxisX;
        if (inAxis == Axis::Type::Y)
            mirrorM = mirrorAxisY;
        if (inAxis == Axis::Type::Z)
            mirrorM = mirrorAxisZ;
    
        // not support symmetrical bone for now, todo wxt
        SkBoneHandle symBoneH = inRunSkelt->GetSymmetricBoneIndex(inMirrorBoneIndex);
        if (symBoneH != SkBoneHandle::InvalidHandle())
            return;
    
        auto const& ref_skelt = inRunSkelt->GetReferenceSkeleton();
        auto const& ref_sym_bind_poses = ref_skelt.GetRawRefBoneSymWorldMatrix();
    
        auto const& sym_bp_local_2_world_cur = ref_sym_bind_poses[inMirrorBoneIndex];
        auto anim_pose_cur = outTrans.GetTransformMatrix();
    
        Float3A tempT, tempS;
        Quaternion tempR;
    
        Float4x4A curMatrix = sym_bp_local_2_world_cur * mirrorM * sym_bp_local_2_world_cur.Inverted() * anim_pose_cur * mirrorM.Inverted();
        curMatrix.Decompose(tempS, tempR, tempT);
    
        outTrans.SetTranslation(tempT);
        outTrans.SetRotation(tempR);
        outTrans.SetScale(tempS);
    }
    
    void InertializationPolynomial::Init(float x0, float v0, float t1)
    {
        const float t1_2 = t1 * t1;
        const float t1_3 = t1_2 * t1;
        const float t1_4 = t1_3 * t1;
        const float t1_5 = t1_4 * t1;
    
        const float a0 = MathUtils::Max(0.f, (-8.0f * v0 * t1 - 20.0f * x0) / (t1 * t1));
    
        A = -(a0 * t1_2 + 6.0f * v0 * t1 + 12.0f * x0) / (2.0f * t1_5);
        B = (3.0f * a0 * t1_2 + 16.0f * v0 * t1 + 30.0f * x0) / (2.0f * t1_4);
        C = -(3.0f * a0 * t1_2 + 12.0f * v0 * t1 + 20.0f * x0) / (2.0f * t1_3);
        D = 0.5f * a0;
        E = v0;
        F = x0;
        range = t1;
    }
    
    void InertializationBone::InitVector(const Float3& inPrevVecN, const Float3& inPrevVecN_1, const Float3& inCurVec, float deltaTime, float duration, Float3& outDir, InertializationPolynomial& outPolynomial)
    {
        // cal v0, x0, t1, a0 for input vector
        const Float3 vecX0 = inPrevVecN - inCurVec;
        const Float3 vecX_1 = inPrevVecN_1 - inCurVec;
    
        const float x0 = vecX0.Length();
        outDir = vecX0.Normalized();
    
        const float x1 = vecX_1.Dot(outDir);
        const float v0 = MathUtils::Min(deltaTime > 0.f ? (x0 - x1) / deltaTime : 0.f, 0.0f);
    
        const float t1 = v0 < 0.f ? MathUtils::Min(duration, -5.0f * x0 / v0) : duration;
        outPolynomial.Init(x0, v0, t1);
    }
    
    void InertializationBone::InitQuaternion(const Quaternion& inPrevRotN, const Quaternion& inPrevRotN_1, const Quaternion& inCurRot, float deltaTime, float duration, Float3& outDir, InertializationPolynomial& outPolynomial)
    {
        // cal v0, x0, t1, a0 for input quat
        const Quaternion q0 = inCurRot.Inverse() * inPrevRotN;
        const Quaternion q1 = inCurRot.Inverse() * inPrevRotN_1;
    
        float x0 = MathUtils::UnwindRadians(q0.GetAngleRad());
        Float3 vecX0 = q0.GetAxis();
        if (x0 < 0.f)
        {
            x0 = -x0;
            vecX0 = -vecX0;
        }
        outDir = vecX0;
    
        // twist of q1 around vecX0
        float x1 = x0 > 0.0001f ? MathUtils::UnwindRadians(2.0f * std::atan2f(Float3(q1.x, q1.y, q1.z).Dot(vecX0), q1.w)) : 0.0f;
    
        const float v0 = MathUtils::Min(deltaTime > 0.f ? MathUtils::UnwindRadians(x0 - x1) / deltaTime : 0.f, 0.0f);
        const float t1 = v0 < 0.f ? MathUtils::Min(duration, -5.0f * x0 / v0) : duration;
        outPolynomial.Init(x0, v0, t1);
    }
    
    void InertializationBone::ApplyVector(Float3& inOutCurVec, float t, const Float3& inVecDir, const InertializationPolynomial& inPolynomial)
    {
        float res = inPolynomial.Eval(t);
        inOutCurVec = inOutCurVec + inVecDir * res;
    }
    
    void InertializationBone::ApplyQuaternion(Quaternion& inOutCurQuat, float t, const Float3& inQuatDir, const InertializationPolynomial& inPolynomial)
    {
        float res = inPolynomial.Eval(t);
    
        if (inQuatDir.Length() > 0.0001f && res > 0.0001f)
        {
            inOutCurQuat = inOutCurQuat * Quaternion::CreateFromAxisAngle(inQuatDir, res);
        }
    }
    
    void InertializationPose::PushPose(const RootSpacePose& CurPose)
    {
        if (mPrevN_Index == -1)
        {
            mPrevN_Index = 0;
        }
        else if (mPrevN_1_Index == -1)
        {
            mPrevN_1_Index = 0;
            mPrevN_Index = 1;
        }
        else
        {
            std::swap(mPrevN_1_Index, mPrevN_Index);
        }
    
        mPrevPose[mPrevN_Index] = CurPose;
    }
    
    void InertializationPose::Start(RootSpacePose& CurPose, float t1)
    {
        // cal v0, x0, t1, a0 for each bone's rts
        if (mPrevN_Index == -1)
        {
            mPrevN_Index = 0;
            mPrevPose[mPrevN_Index] = CurPose;
        }
    
        if (mPrevN_1_Index == -1)
        {
            mPrevN_1_Index = 1 - mPrevN_Index;
            mPrevPose[mPrevN_1_Index] = mPrevPose[mPrevN_Index];
        }
        mPrevPose[mPrevN_1_Index].ConvertAllBoneToLocalSpace();
        mPrevPose[mPrevN_Index].ConvertAllBoneToLocalSpace();
        CurPose.ConvertAllBoneToLocalSpace();
    
        auto BoneNum = CurPose.GetFilteredSkelt().GetBoneIndicesArray().size();
        Assert(BoneNum == mPrevPose[mPrevN_Index].GetFilteredSkelt().GetBoneIndicesArray().size());
        Assert(BoneNum == mPrevPose[mPrevN_1_Index].GetFilteredSkelt().GetBoneIndicesArray().size());
        mInertiaBones.resize(BoneNum);
    
        for (UInt32 iBone = 0; iBone < BoneNum; iBone++)
        {
            const auto& prevN_Bone = mPrevPose[mPrevN_Index][{iBone}];
            const auto& prevN_1_Bone = mPrevPose[mPrevN_1_Index][{iBone}];
            const auto& curBone = CurPose[{iBone}];
            auto& inertiaBone = mInertiaBones[iBone];
    
            InertializationBone::InitVector(prevN_Bone.GetTranslation(), prevN_1_Bone.GetTranslation(), curBone.GetTranslation(), mPrevDeltaTime, t1, inertiaBone.TranslateDir, inertiaBone.TranslatePolynomial);
            InertializationBone::InitVector(prevN_Bone.GetScale(), prevN_1_Bone.GetScale(), curBone.GetScale(), mPrevDeltaTime, t1, inertiaBone.ScaleDir, inertiaBone.ScalePolynomial);
            InertializationBone::InitQuaternion(prevN_Bone.GetRotation(), prevN_1_Bone.GetRotation(), curBone.GetRotation(), mPrevDeltaTime, t1, inertiaBone.RotateDir, inertiaBone.RotatePolynomial);
        }
    }
    
    void InertializationPose::Apply(RootSpacePose& CurPose, float CurTime)
    {
        // cal x at CurTime for each bone's rts and apply it to CurPose
        auto BoneNum = CurPose.GetFilteredSkelt().GetBoneIndicesArray().size();
        Assert(BoneNum == mInertiaBones.size());
        for (UInt32 iBone = 0; iBone < BoneNum; iBone++)
        {
            auto& curBone = CurPose[{iBone}];
            auto& inertiaBone = mInertiaBones[iBone];
    
            auto trans = curBone.GetTranslation();
            auto scale = curBone.GetScale();
            auto rot = curBone.GetRotation();
    
            InertializationBone::ApplyVector(trans, CurTime, inertiaBone.TranslateDir, inertiaBone.TranslatePolynomial);
            InertializationBone::ApplyVector(scale, CurTime, inertiaBone.ScaleDir, inertiaBone.ScalePolynomial);
            InertializationBone::ApplyQuaternion(rot, CurTime, inertiaBone.RotateDir, inertiaBone.RotatePolynomial);
    
            curBone.SetTranslation(trans);
            curBone.SetScale(scale);
            curBone.SetRotation(rot.Normalized());
        }
    }

    void AnimRuntime::ConvertTransformToAdditive(NodeTransform& outTargetTrasnform, NodeTransform const& inBaseTransform) 
    {
        outTargetTrasnform.SetRotation(outTargetTrasnform.GetRotation() * inBaseTransform.GetRotation().Inverse());
        outTargetTrasnform.SetTranslation(outTargetTrasnform.GetTranslation() - inBaseTransform.GetTranslation());
        
        // additive scale considers how much it grow or lower
        // in order to support blending between different additive scale, we save [(target scale)/(source scale) - 1.f], and this can blend with
        // other delta scale value
        // when we apply to the another scale, we apply scale * (1 + [additive scale])
        
        outTargetTrasnform.SetScale(outTargetTrasnform.GetScale() * NodeTransform::GetSafeScaleReciprocal(inBaseTransform.GetScale()) - Float3A::One());
        outTargetTrasnform.NormalizeRotation();
    }
    
    void AnimRuntime::ConvertPoseToAdditive(RootSpacePose& outTargetPose, RootSpacePose const& inBasePose) 
    {
        for (PoseBoneHandle curPose = {0}, size = {static_cast<UInt32>(outTargetPose.GetNumBones())}; curPose < size; ++curPose)
        {
            NodeTransform& targetTransform = outTargetPose[curPose];
            const NodeTransform& baseTransform = inBasePose[curPose];

            ConvertTransformToAdditive(targetTransform, baseTransform);
        }
    }

    void AnimRuntime::ConvertPoseToRootSpaceForRotationOnly(RootSpacePose& outPose)
    {
        for (PoseBoneHandle curPose = {1}, size = {static_cast<UInt32>(outPose.GetNumBones())}; curPose < size; ++curPose)
        {
            // convert Pose handle into bone handle
            SkBoneHandle bone_index = outPose.GetFilteredBoneIndexFromPoseIndex(curPose);

            // grab parent handle for cur pose
            const SkBoneHandle parent_index = outPose.GetRunSkeltPtr()->GetReferenceSkeleton().GetRawBoneParentIndex(bone_index);

            // convert parent handle into parent bone handle
            const PoseBoneHandle parent_pose_index = outPose.GetPoseIndexFromFilteredBoneIndex(parent_index);

            const QuaternionA rootSpaceRotation = outPose[curPose].GetRotation() * outPose[parent_pose_index].GetRotation();
            outPose[curPose].SetRotation(rootSpaceRotation);
        }
    }

    void AnimRuntime::ConvertRootSpaceForRotationOnlyToLocalSpace(RootSpacePose& outPose)
    {
        for (PoseBoneHandle curPose = {static_cast<UInt32>(outPose.GetNumBones()) - 1}; curPose >0; --curPose)
        {
            // convert Pose handle into bone handle
            SkBoneHandle bone_index = outPose.GetFilteredBoneIndexFromPoseIndex(curPose);

            // grab parent handle for cur pose
            const SkBoneHandle parent_index = outPose.GetRunSkeltPtr()->GetReferenceSkeleton().GetRawBoneParentIndex(bone_index);

            // convert parent handle into parent bone handle
            const PoseBoneHandle parent_pose_index = outPose.GetPoseIndexFromFilteredBoneIndex(parent_index);

            const QuaternionA localSpaceRotation = outPose[curPose].GetRotation() * outPose[parent_pose_index].GetRotation().Inverse();
            outPose[curPose].SetRotation(localSpaceRotation);
        }
    }

    void AnimRuntime::AccumulateAdditivePose(RootSpacePose& outPose, const RootSpacePose& additivePose, AdditiveAnimSpace::Type additiveType, float weight /* = 1.0f*/) 
    {
        if (weight < ANIM_BLEND_SMALL_WEIGHT)
            return;

        switch (additiveType)
        {
        case cross::anim::AdditiveAnimSpace::LocalSpaceBase:
            AccumulateLocalSpaceAdditivePoseInternal(outPose, additivePose, weight);
            break;
        case cross::anim::AdditiveAnimSpace::RotationOffsetRootSpace:
            AccumulateRootSpaceRotationAdditiveToLocalPoseInternal(outPose, additivePose, weight);
            break;
        default:
            break;
        }

        // normalize
        outPose.NormalizeRotations();
    }

    void AnimRuntime::AccumulateLocalSpaceAdditivePoseInternal(RootSpacePose& outPose, const RootSpacePose& additivePose, float weight)
    {
        if (weight > 1.0f - ANIM_BLEND_SMALL_WEIGHT)
        {
            // fast path, no need to weight additive.
            for (PoseBoneHandle pose_index = {0}, pose_size = { static_cast<UInt32>(outPose.GetNumBones())}; pose_index < pose_size; ++pose_index)
            {
                outPose[pose_index].AccumulateWithAdditiveScale(additivePose[pose_index], weight);
            }
        }
        else
        {
            // Slower path w/ weighting
            for (PoseBoneHandle pose_index = {0}, pose_size = { static_cast<UInt32>(outPose.GetNumBones()) }; pose_index < pose_size; ++pose_index)
            {
                // copy additive, because BlendFromIdentityAndAccumulate modifies it.
                NodeTransform additive = additivePose[pose_index];
                NodeTransform::BlendFromIdentityAndAccumulate(outPose[pose_index], additive, weight);
            }
        }
    }
 
    void AnimRuntime::AccumulateRootSpaceRotationAdditiveToLocalPoseInternal(RootSpacePose& outPose, const RootSpacePose& additivePose, float weight)
    {
        // Convert base pose from local space to mesh space rotation.
        ConvertPoseToRootSpaceForRotationOnly(outPose);

        // Add MeshSpaceRotAdditive to it
        AccumulateLocalSpaceAdditivePoseInternal(outPose, additivePose, weight);

        // Convert back to local space
        ConvertRootSpaceForRotationOnlyToLocalSpace(outPose);
    }

}   // namespace cross::anim
