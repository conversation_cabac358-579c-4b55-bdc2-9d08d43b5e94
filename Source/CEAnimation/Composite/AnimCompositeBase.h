#pragma once
#include "CECommon/Common/FrameContainer.h"
#include "CEAnimation/IExecutableAnim.h"
#include "CEAnimation/Transform/AnimBlend.h"
#include "CEAnimation/Composite/AnimSegment.h"

namespace cross::anim
{
    /* The parent class holding binary-data for executable animation instance
     *	Persist A Asset which contain the relative path of Seq combined by multi Segment
     *	Persist A Track at least for runtime CompositeSeq, while AnimTracker holding tracks equals to Skeleton Slots num
     *	Persist A Reference Skeleton if Animation should be filtered by it
     */
    class Animation_API AnimCompositeBase
    {
    protected:
        AnimCompositeBase(const AnimNotifyTrack& notifyTrack, const FloatCurveList& animCurves);
        AnimCompositeBase(const std::vector<AnimNotifyTrack>& notifyTracks, const FloatCurveList& animCurves);

    public:
        AnimCompositeBase() = delete;

        virtual CEName GetName() const = 0;

        virtual bool IsDynamic() const { return false; }

        virtual std::string GetAssetPath() const = 0;

        virtual const CEName& GetGroupName() const = 0;

        virtual bool HasRootMotion() const = 0;

        virtual bool IsNotifyAvailable() const = 0;

        virtual bool IsValidSlot(const CEName& slotName) const = 0;

        virtual bool ContainRecursive(std::vector<const IAnimSequence*>& inAccumulatedList) const = 0;

        virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& slotName) const = 0;

    protected:
        virtual AnimTrack const* GetTrack(CEName const& slotAnimTrack) const = 0;

        virtual const CEName& GetSyncGroupName() const = 0;

        virtual bool AttachTo(const Skeleton* inSkeleton) = 0;

        virtual bool IsSkeletonAttached(Skeleton const* specifiedSkelt = nullptr) const = 0;

    public:
        void GetAllAnimSequence(FrameVector<const IAnimSequence*>* inContainer) const;

        // Extracts root motion from the specified AnimTrack between the [Start, End]
        void ExtractRootMotionFromTrack(
            const CEName& slotName,
            TrackUnWrapperH startTrackPosition,
            TrackUnWrapperH endTrackPosition,
            RootMotionParams& outRootMotionParams) const;
        
        // Extracts notifies from this Composite Object's own NotifyTrack
        void ExtractNotifiesFromSelfRange(const TrackUnWrapperH& startPos, const TrackUnWrapperH& endPos, std::vector<AnimNotifyEventReference>& outActiveNotifies) const;

        // Extracts notifies from the specified AnimTrack between the [Start, End]
        void ExtractNotifiesFromTrack(
            const CEName& slotName,
            TrackUnWrapperH startTrackPosition,
            TrackUnWrapperH endTrackPosition,
            std::vector<AnimNotifyEventReference>& outActiveNotifies) const;

        // Extracts curves from this Composite Object's own CurveTrack at specified time
        void ExtractAnimCurvesFromSelf(const TrackUnWrapperH& curPos, AnimCurveData& outCurveData) const;

        // Extracts curves from the specified AnimTrack at specified time
        void ExtractAnimCurvesFromTrack(const CEName& slotName, const TrackUnWrapperH& startPos, AnimCurveData& outCurveData) const;

    protected:
        /* Animation notifies, sorted by time (earliest notification first). */
        const std::vector<AnimNotifyEvent *> mNotifies;

        /* Animation curves */
        const FloatCurveList& mAnimCurves;

        /*  Runtime seq not in instance never attach to a skeleton, either preview or extract pose without bone-map **/
        std::vector<AnimSeqPtr> mRunAnims;

        /* Blend in time. */
        float mBlendInTime{ 0.2f };

        /* Blend out time. This is only used when it blends out itself. If it's interrupted by other ExectuableAnim, it will use new ExectuableAnim's BlendIn option to blend out. */
        float mBlendOutTime{ 0.2f };

        friend class AnimCmpInstanceBase;
    };

    using AnimCmpBasePtr = std::shared_ptr<AnimCompositeBase>;

    class Animation_API AnimCmpInstanceBase : public IExecutableAnim
    {
    public:
        AnimCmpInstanceBase(bool isTrigger);

        //// ~IExecutableAnim Interface Begin~
        //// 

        virtual CEName GetName() const override { return GetInstanceShell()->GetName(); }

        virtual float GetBlendWeight() const override { return mBlend.GetBlendedValue(); }

        virtual const AnimSyncParams& GetAnimSyncParams() const override { return mSyncParam; }

        virtual void SetAnimSyncParams(const AnimSyncParams& syncParams) override { mSyncParam = syncParams; }

        virtual bool IsSkeletonAttached() const override;

        virtual void GetAllAnimSequence(FrameVector<const IAnimSequence*>*) const override;

        virtual void PostUpdate() override
        {
            mExtractedRootMotionSlots.clear();
            mExtractedNotifySlots.clear();
        }

        ////
        //// ~IExecutableAnim Interface End~

    public:
        SInt32 GetInstanceID() const { return mInstanceID; }

        void SetPlayWithoutBlend(bool playing);
        // make PLAY_FLAG = true & blend in happened immediately
        void Play();
        // make INTERRUPT_FLAG = true if stop by user, modify play flag while blend out time = 0.f & make blend out happened immediately
        void Stop(float blendOutTime, bool isInterrupt = true);
        // make PLAY_FLAG = false & no cursor moved immediately in advance function
        void Pause() { mPlaying = false; }
        // make cursor = begin pos then PLAY() called
        virtual void Replay();
        // destroy any resource ptr holding in current anim instance
        virtual void Terminate() { mPlaying = false; }

        bool IsPlaying() const { return mPlaying; }
        bool IsInterrupted() const { return mInterrupted; }

        // is play called & blend not complete, then return true
        bool IsBlendingIn() const { return mBlend.GetDesiredValue() == 1.0f && !mBlend.IsComplete(); }
        // is stop called & blend not complete, then return true
        bool IsBlendingOut() const { return mBlend.GetDesiredValue() == 0.0f && !mBlend.IsComplete(); }
        // is stop called & blend complete, then return true
        bool IsBlendOuted() const { return mBlend.GetDesiredValue() == 0.0f && mBlend.IsComplete(); }
        // is stop called, then return true
        bool IsStopped() const { return mBlend.GetDesiredValue() == 0.0f; }

        float GetBlendInReferenceTime() const;
        float GetBlendOutReferenceTime() const;

        float GetBlendInLeftTime() const { return -1.0f; }
        float GetBlendOutLeftTime() const { return -1.0f; }

        const CEName& GetGroupName() const;
        bool IsValidSlot(const CEName& slotName) const;

        void SetWeight(float weight) { mBlend.SetCursor(weight); }

        void SetDesiredWeight(float value) { mBlend.SetDesiredValue(value); }

        void SetAutoBlendOut(bool enable) { mAutoBlendOut = enable; }

        virtual bool ActivateSingleSlot(CEName const& inSlotName) { return false; }
        //
        virtual bool IsSlotActivated(CEName const& inSlotName) const { return true; }
        // 
        virtual bool IsSlotCompleted(CEName const& inSlotName) const { return false; }
        // 
        virtual void GetActivatedSlots(std::vector<AnimReferenceTrackBase const*>& outSlots) const {}
        // 
        virtual AnimReferenceTrackBase const* GetActivatedSlot(CEName const& inSlotName) const { return nullptr; }

        virtual void SetCursor(float fromTime, float toTime) {}

        // 1st tick in animator, move cursor
        // calling extract any events or touching any of the instance data.
        virtual void Advance(float deltaTime) {}

    protected:
        virtual AnimCompositeBase const* GetInstanceShell() const = 0;

        void ExtractAnimCurves(const CEName& slotName, const TrackUnWrapperH& curPos, AnimCurveData& outAnimCurves);

       /*
        * Retrieves AnimNotifies given a contiguous Track position range.
        * [CONTIGUOUS] means that if playing forward StartTractPosition < EndTrackPosition.
        * No wrapping over if looping. No jumping across different sections.
        * So the ExecutableAnim has to break the update into contiguous pieces to handle those cases.
        *
        * No repeat retrieves happened by AnimExtractContext, Executable ITSELF notify Track & particular track from parameter at most once called
        */
        void ExtractAnimNotifies(
            const CEName& slotName,
            const TrackUnWrapperH& prevPos,
            const TrackUnWrapperH& curPos,
            std::vector<AnimNotifyEventReference>& outActiveNotifies);

       /*
        * Extract RootMotion Transform from a contiguous Track position range.
        * [CONTIGUOUS] means that if playing forward StartTractPosition < EndTrackPosition.
        * No wrapping over if looping. No jumping across different sections.
        * So the ExecutableAnim has to break the update into contiguous pieces to handle those cases.
        *
        * This does handle Animatrix playing backwards (StartTrackPosition > EndTrackPosition).
        *
        * It will break down the range into steps if needed to handle looping animations, or different animations.
        * These steps will be processed sequentially, and output the RootMotion transform in component space.
        */
        void ExtractRootMotion(
            const CEName& slotName,
            const TrackUnWrapperH& prevPos,
            const TrackUnWrapperH& curPos,
            RootMotionParams& rootMotion);

        void ExtractDataArbitraryDeltaTime_DefaultTrack(
            AnimReferenceDefTrack const& inTrack, 
            bool bExtractRootMotion, 
            RootMotionParams* RootMotionParamsPtr, 
            bool bExtractNotifies,
            AnimNotifyQueue* AnimNotifyQueuePtr);

        void ExtractDataArbitraryDeltaTime_SectionedTrack(
            IAnimator const* inAnimator,
            AnimReferenceSecTrack const& inTrack, 
            bool bExtractRootMotion,
            RootMotionParams* RootMotionParamsPtr,
            bool bExtractNotifies,
            AnimNotifyQueue* AnimNotifyQueuePtr);

        void UpdateByMarkerAsLeader(
            SyncMarkerUpdateRecord& markerUpdateRecord,
            SyncMarkerUpdateContext& markerUpdateContext,
            TrackUnWrapperH& inOutPrevPos,
            TrackUnWrapperH& inOutCurPos,
            const float moveDelta);

        void UpdateByMarkerAsFollower(
            SyncMarkerUpdateRecord& markerUpdateRecord,
            SyncMarkerUpdateContext& markerUpdateContext,
            TrackUnWrapperH& inOutPrevPos,
            TrackUnWrapperH& inOutCurPos,
            const float moveDelta);

    protected:
        // each anim instance scale game_world delta time by current instance's play-rate
        TrackUnWrapperH mDeltaTime{0.f};

        // 'true' means current executable anim is holding by animator directly 
        // otherwise it should be holding by anim graph's node
        const bool mIsTrigger = false;

        ////// Playing control vars
        bool mPlaying{ false };

        bool mInterrupted{ false };

        float mPlayRate{ 1.0f };

        /* When it hits end, it automatically blends out. If this is false, it won't blend out but keep the last pose until stopped explicitly */
        bool mAutoBlendOut{ true };

        ///// Sync params
        // whether this instance has advanced when animator update 
        AnimSyncParams mSyncParam;

        ///// for root motion and notify extraction controlling
        CENameSet mExtractedRootMotionSlots;
        CENameSet mExtractedNotifySlots;

        // transient value of blend in & out, treat them equally
        AnimBlend mBlend;

        // reference to Runtime Skeleton, should be valid throughout instance entire life cycle
        const Skeleton* mRunSkelt{ nullptr };

        SInt32 mInstanceID;

        float mPreCursorPosition;
        float mCurCursorPosition;
    };

    using AnimCmpInstanceBasePtr = std::shared_ptr<AnimCmpInstanceBase>;
}
