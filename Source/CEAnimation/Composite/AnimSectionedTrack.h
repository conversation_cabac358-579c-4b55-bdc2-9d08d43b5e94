#pragma once
#include <optional>
#include "CEAnimation/AnimBase.h"
#include "CEAnimation/Composite/AnimSegment.h"

namespace cross::anim
{
    extern const CEName sDefaultSectionName;

    class AnimSectionedTrack;
    class AnimReferenceSecTrack;

    /* Section data hold multi continuous segments, a dynamic priority bonus supplied while animatrix running
     *  for the way sync group should be be confined in the range of current section,
     *  'move to next section' operate handled by either notify or script driving manually
     */
    struct AnimSection
    {
        AnimSection() = default;

        CEName Name = sDefaultSectionName;
        /* */
        SectionHandle NextSectionH = SectionHandle::InvalidHandle();
        /* */
        std::vector<AnimNotifyEvent const*> InstantNotifies;
        /* */
        std::vector<AnimNotifyEvent const*> AllNotifies;
        /* Sorted by segment's start pos in Track space */
        std::vector<AnimSegment const*> Segments;
        /* Is current section should loop if meet its end */
        bool Loop = false;
        /* Size = number of segments hold by current section, bonus for execute anim's priority */
        std::vector<SInt32> PriorityDeltas;

        // current section's marker index start to end for all markers holding in 'SyncMarkerData'
        SInt32 mSyncMarkerIndexStart = -1;
        SInt32 mSyncMarkerIndexEnd = -1;

        AnimSegment const* GetSegmentAtTime(TrackUnWrapperH inTrackTime) const;

        AnimSegment const* GetSegmentAtTime(SectionUnwrapperH inSectionTime) const;

        inline time::TrackUnWrapperH RunLength(float inExecAnimPlayRate = 1.0f) const
        {
            TrackUnWrapperH curPos = { 0 };

            std::for_each(Segments.begin(), Segments.end(), [&curPos](auto& elem) {
                curPos = { curPos + elem->GetRunLoopingLength() };
                });

            return { curPos / MathUtils::Sign<float>(inExecAnimPlayRate) };
        }

        inline time::TrackUnWrapperH TrackStartPos(float inExecAnimPlayRate = 1.0f) const
        {
            Assert(Segments.size() > 0 && Segments[0] != nullptr);
            return { Segments[0]->StartPos / MathUtils::Sign<float>(inExecAnimPlayRate) };
        }

        inline time::TrackUnWrapperH TrackEndPos(float inExecAnimPlayRate = 1.0f) const
        {
            return { (TrackStartPos() + RunLength()) / MathUtils::Sign<float>(inExecAnimPlayRate) };
        }

        inline bool IsInRange(time::TrackUnWrapperH inPos) const
        {
            return inPos >= TrackStartPos() && inPos < TrackEndPos();
        }
    };

    /* 
     * Instance of AnimSection
     */
    class AnimReferenceSection
    {
    public:
        AnimReferenceSection() = delete;
        AnimReferenceSection(AnimSection const* inSectionPtr, AnimReferenceSecTrack const* inTrackPtr)
            : SectionPtr(inSectionPtr)
            , mOwnerPtr(inTrackPtr)
        {}

        AnimReferenceSection& operator=(AnimReferenceSection const& other) noexcept
        {
            this->SectionPtr = other.SectionPtr;
            this->mOwnerPtr = other.mOwnerPtr;
            return *this;
        }

        // 
        std::optional<AnimReferenceSection> Next() const;
        //
        inline TrackUnWrapperH GetCursor() const { return { mCursor + SectionPtr->TrackStartPos() }; }
        // 
        inline SectionUnwrapperH SetCursor(TrackUnWrapperH inPos)
        {
            if (!SectionPtr->IsInRange(inPos))
                return SectionUnwrapperH::InvalidHandle();

            mPreCursor = mCursor;
            mCursor = { inPos - SectionPtr->TrackStartPos() };
            return mCursor;
        }
        //
        AnimSegment const* GetActiveSegment() const;

    public:
        const AnimSection* SectionPtr{ nullptr };

    private:
        const AnimReferenceSecTrack* mOwnerPtr{ nullptr };

        SectionUnwrapperH mCursor = {0};

        SectionUnwrapperH mPreCursor = {0};

        friend class AnimReferenceSecTrack;
    };

    /* This contains section data and some necessary interface organize segments blend in & out inside a
     *  as we can only blend a contiguous range per AnimSectionTrack, no resemble behavior happens in AnimTrack
     *  exp Track- Start pose --> a Loop pose --> End pose
     *   Section 0      Section 1     Section 2
     */
    class Animation_API AnimSectionedTrack : public AnimTrack
    {
    public:
        std::vector<AnimSection> AnimSections;
        /* Get the index of the section at the given absolute playing time. */
        SectionHandle GetSectionIndexAtTime(TrackUnWrapperH inTime) const;

        /* Get the section at the given absolute playing time */
        AnimSection* GetSectionAtTime(TrackUnWrapperH inTime);
        AnimSection const* GetSectionAtTime(TrackUnWrapperH inTime) const;
        AnimSection const* GetSectionAtHandle(SectionHandle inHandle) const;

        //// ~AnimTrack Begin~
        //// 

        virtual bool Deserialize(const SlotTrackRes& slotTrack, const std::vector<AnimSeqPtr>& inAnimSeqs, const std::vector<AnimNotifyEvent *>& NotifiesInTrack) override;

        virtual bool IsAnySectionExist() const override { return AnimSections.size() > 0; }

        //// ~AnimTrack End~
        //// 
    };

    /* Instance of Track holding sections
     * Animatrix holding 0-N active sectioned track, we wish to correct 'notify' & 'root motion' & 'sync group' since multi tracks extracted simultaneously
     * each tracks activated in particular Animatrix holding a corresponding AnimRefSectionTrack recording SECTION-info stuffs.
     */
    class Animation_API AnimReferenceSecTrack final : public AnimReferenceTrackBase
    {
    public:
        explicit AnimReferenceSecTrack(AnimSectionedTrack const& inTrack)
            : mReference(inTrack)
            , mActiveRefSection(&inTrack.AnimSections[0], this)
        {
            mTrackMarkersData.RefreshDataFromAnimTrack(inTrack);
        }

        virtual AdvanceAnim::Type Advance(float deltaTime, bool isLoopAllowed = false) override;

        virtual bool IsCompleted(float inBlendTime = 0.001f, bool isLoopAllowed = false) const override;

        virtual float GetRunLength() const override { return mReference.GetRunLength(); }

        virtual float GetCurSectionedLength() const override { return mActiveRefSection.SectionPtr->RunLength(); }

        virtual void RatioScaleCursor(float preRatio, float curRatio) override;
        /* Move into next section if current is not the last in current track*/
        bool MoveNext() const;
        //
        virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const override;

        virtual void ExtractAnimCurves(AnimCurveData& outAnimCurves) const override;

        inline AnimSectionedTrack const& GetReference() const { return mReference; }

        AnimReferenceSection const& GetActiveSection() const;

    private:
        AnimReferenceSection& GetActiveSection();

    private:
        /*  */
        mutable bool mIsComplete = false;
        /*  */
        AnimSectionedTrack const& mReference;
        /*  */
        mutable AnimReferenceSection mActiveRefSection;
    };

}
