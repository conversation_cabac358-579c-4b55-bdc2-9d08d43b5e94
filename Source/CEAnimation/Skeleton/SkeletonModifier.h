#pragma once

#include "CEAnimation/Skeleton/Skeleton.h"

namespace cross::skeleton
{
	// Allow modifications to a reference skeleton while guaranteeing that virtual bones remain valid.
	class ReferenceSkeletonModifier
	{
	private:
		ReferenceSkeleton& mRefSkeleton;
		const Skeleton*	mRunSkeleton{ nullptr };
		std::map<std::string, std::string> mMergedBone2ParentLinkup;

	public:
		ReferenceSkeletonModifier(ReferenceSkeleton& inRefSkel, const Skeleton* inSkeleton)
			: mRefSkeleton(inRefSkel)
			, mRunSkeleton(inSkeleton)
		{}

		~ReferenceSkeletonModifier();

		// Update the reference pose transform of the specified bone
		//void UpdateRefPoseTransform(const SkBoneHandle boneIndex, const BoneTransform& bonePose);

		// Add a new bone. BoneName must not already exist! ParentIndex must be valid.
		void Add(const RefBoneNode& boneInfo, const Float4A& boneLocalTranslate, const QuaternionA& boneLocalRot, const Float4A& boneLocalScale, const CEName& boneParentName);

		/** Find Bone Index from BoneName. Precache as much as possible in speed critical sections! */
		SInt32 FindBoneIndex(const CEName& boneName) const;

		void RemoveRefSkeletonMergedNodes();

		/** Accessor to private data. Const so it can't be changed recklessly. */
		const std::vector<RefBoneNode> & GetRefBoneNode() const;

		const ReferenceSkeleton& GetReferenceSkeleton() const { return mRefSkeleton; }
	};

}
