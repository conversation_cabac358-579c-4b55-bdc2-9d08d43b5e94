#include "EnginePrefix.h"
#include "CrossBase/Math/CrossMath.h"
#include "CEAnimation/AnimBase.h"
#include "CEAnimation/Transform/AnimBlend.h"

namespace cross
{
	AnimBlend::AnimBlend(float newBlendTime)
		: mBlendTime(newBlendTime)
		, mBeginValue(0.0f)
		, mDesiredValue(1.0f)
	{
		Reset();
	}

	void AnimBlend::Reset()
	{
		// Set alpha target to full - will also handle zero blend times
		// if blend time is zero, transition now, don't wait to call update.
		if (mBlendTime <= 0.f)
		{
			SetCursor(1.f);
			mBlendTimeRemaining = 0.f;
		}
		else
		{
			SetCursor(0.f);
			// Blend time is to go all the way, so scale that by how much we have to travel
			mBlendTimeRemaining = mBlendTime * MathUtils::Abs(1.f - mNormalizedCursor);
		}

		mNeedsToResetCursor = false;
		mNeedsToResetBlendTime = false;
	}

	void AnimBlend::SetValueRange(float begin, float desired)
	{
		mBeginValue = begin;
		mDesiredValue = desired;

		mNeedsToResetCursor = true;
		mNeedsToResetBlendTime = true;
	}

	void AnimBlend::ResetCursor()
	{	
		// if blend time is <= 0, or begin == end is same, there is nothing to be done
		// blending is done and complete
		if ( std::abs(mBeginValue - mDesiredValue) < ANIM_BLEND_SMALL_WEIGHT)
			SetCursor(1.f);
		else
		{
			mNormalizedCursor = (mBlendedValue - mBeginValue) / (mDesiredValue - mBeginValue);
			SetCursor(mNormalizedCursor);
		}

		// reset the flag
		mNeedsToResetCursor = false;
	}

	void AnimBlend::ResetBlendTime()
	{
		// if blend time is <= 0, then blending is done and complete
		if (mBlendTime <= 0.f)
		{
			mBlendTimeRemaining = 0.f;
			SetCursor(1.f);
		}
		// Blend time is to go all the way, so scale that by how much we have to travel
		else
			mBlendTimeRemaining = mBlendTime * std::abs(1.f - mNormalizedCursor);

		mNeedsToResetBlendTime = false;
	}

	float AnimBlend::Update(float inDeltaTime)
	{
		// Make sure passed in delta time is positive
		Assert(inDeltaTime >= 0.f);

		// check if we should reset value
		if (mNeedsToResetCursor)
			ResetCursor();

		// or should re calc blend time remaining
		if (mNeedsToResetBlendTime)
			ResetBlendTime();

		// if not complete, 
		if (!IsComplete())
		{
			if (mBlendTimeRemaining > inDeltaTime)
			{
				const float blendDelta = 1.f - mNormalizedCursor;
				mNormalizedCursor += (blendDelta / mBlendTimeRemaining) * inDeltaTime;
				mBlendTimeRemaining -= inDeltaTime;

				SetCursor(mNormalizedCursor);
			}
			else
			{
				// Cache our overshoot to report to caller
				float overshoot = inDeltaTime - mBlendTimeRemaining;

				mBlendTimeRemaining = 0.f;
				SetCursor(1.f);

				return overshoot;
			}
		}

		return 0.f;
	}

	void AnimBlend::SetDesiredValue(float inDesiredTime)
	{
		SetValueRange(mBlendedValue, inDesiredTime);
	}

	void AnimBlend::SetBlendTime(float inBlendTime)
	{
		mBlendTime = MathUtils::Max(inBlendTime, 0.f);
		mNeedsToResetBlendTime = true;
	}

}
