#pragma compute ComputeTransmittance
#pragma compute NewMultiScattCS
#pragma compute ComputeSkyView
#pragma compute ComputeDistantSkyLight
#pragma compute RenderSkyCS
#pragma compute ClearSkyCS
#pragma keyword ATMOSPHERE_USE_VSM

[[vk::constant_id(0)]]
bool ILLUMINANCE_IS_ONE = false;

[[vk::constant_id(1)]]
bool MULTISCATAPPROX_ENABLED = false;

[[vk::constant_id(2)]]
bool CE_USE_DOUBLE_TRANSFORM = false;

[[vk::constant_id(3)]]
bool PLANET_TOP_AT_ORIGIN = true;

[[vk::constant_id(4)]]
bool UnitM = true;

[[vk::constant_id(5)]]
bool USE_SECONDARY_LIGHT = true;

#include "ShaderLibrary/GlobalModelVariables.hlsl"

#include "../Definitions.hlsl"

#define sun_direction0 (-ToUEVec(normalize(ce_AtmosphereLightData[0].LightDirection)))
#define sun_direction1 (-ToUEVec(normalize(ce_AtmosphereLightData[1].LightDirection)))
#define city_direction (-ToUEVec(normalize(ce_AtmosphereLightData[2].LightDirection)))

cbuffer cbPassConstants : register(space0)
{
    float3 SkyCameraOrigin;  // world position of the camera, unit cm
    float4 SkyPlanetCenterAndViewHeight;  // camera position relative to the virtual planet center, uint cm
    float MultiScatteringFactor;
    float4x4 SkyViewLutReferential;
    float3 ce_CameraForward;
    AtmosphereParameters ATMOSPHERE;
    AtmosphereLightData ce_AtmosphereLightData[3];
    float4x4 LUMINANCE_FROM_RADIANCE;
    float AerialPerspStartDepthKM;
    float Exposure;
	// Atmosphere console vars
	float SkyViewLUTSampleCountMin;
	float SkyViewLUTSampleCountMax;
	float SkyViewLUTDistanceToSampleCountMaxInv;
    float3 SkyLuminanceFactor;
    bool USE_WGS84;
}

Texture2D<float4>    exposure_texture                    : register(space0);
RWTexture2D<float4>  _TransmittanceLutTexture				: register(space0);
RWTexture2D<float4>  _SkyViewLutTexture					: register(space0);
RWTexture2D<float4>  _MultiScatTexture						: register(space0);
RWTexture2D<float4>  _DistantSkyLightTexture						: register(space0);

Texture2D<float4>  _TransmittanceLutTextureReadOnly				: register(space0);
Texture2D<float4>  _SkyViewLutTextureReadOnly					: register(space0);
Texture2D<float4>  _MultiScatTextureReadOnly						: register(space0);
Texture3D<float4>  _AtmosphereCameraScatteringVolumeReadOnly		: register(space0);

Texture2D<float4>  _ViewDepthTexture						: register(space0);


#include "Functions.hlsl"


[numthreads(8, 8, 1)]
void ComputeTransmittance(
    uint3 groupId : SV_GroupID,
    uint3 groupThreadId : SV_GroupThreadID,
    uint3 dispatchThreadId : SV_DispatchThreadID,
    uint groupIndex : SV_GroupIndex)
{
    _TransmittanceLutTexture[dispatchThreadId.xy] = float4(ComputeTransmittanceToTopAtmosphereBoundaryTexture(dispatchThreadId.xy), 1);
}


[numthreads(8, 8, 1)]
void NewMultiScattCS(uint3 ThreadId : SV_DispatchThreadID)
{
	float2 pixPos = float2(ThreadId.xy) + 0.5f;
	float2 uv = pixPos / MultiScatteringLUTRes;


	uv = float2(fromSubUvsToUnit(uv.x, MultiScatteringLUTRes), fromSubUvsToUnit(uv.y, MultiScatteringLUTRes));

	AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);

	float cosSunZenithAngle = uv.x * 2.0 - 1.0;
	float3 sunDir = float3(0.0, sqrt(saturate(1.0 - cosSunZenithAngle * cosSunZenithAngle)), cosSunZenithAngle);
	// We adjust again viewHeight according to PLANET_RADIUS_OFFSET to be in a valid range.
	float viewHeight = Atmosphere.BottomRadius + saturate(uv.y + PLANET_RADIUS_OFFSET) * (Atmosphere.TopRadius - Atmosphere.BottomRadius - PLANET_RADIUS_OFFSET);

	float3 WorldPos = float3(0.0f, 0.0f, viewHeight);
	float3 WorldDir = float3(0.0f, 0.0f, 1.0f);


	SamplingSetup Sampling = (SamplingSetup)0;
	{
		Sampling.VariableSampleCount = false;
		Sampling.SampleCountIni = 15;// a minimum set of step is required for accuracy unfortunately
	}
	const bool ground = true;
	const float DepthBufferValue = -1.0;
	const bool MieRayPhase = false;

	const float SphereSolidAngle = 4.0 * PI_SA;
	const float IsotropicPhase = 1.0 / SphereSolidAngle;

	AtmosphereLightParam LightParams[3];
	// Main sun light
	LightParams[0].Direction = sunDir;
	LightParams[0].Illuminance = float3(1.f, 1.f, 1.f);  // Assume a pure white light illuminance for the LUT to act as a transfer (be independent of the light, only dependent on the earth)
	LightParams[0].IsSun = true;
	LightParams[0].Reversed = false;
	LightParams[0].ReversedLightRadius = 0;
	// Dummy lights (not used in multi-scattering computation)
	LightParams[1].Direction = float3(0, 0, 1);
	LightParams[1].Illuminance = float3(0, 0, 0);
	LightParams[1].IsSun = false;
	LightParams[1].Reversed = false;
	LightParams[1].ReversedLightRadius = 0;
	// Dummy lights (not used in multi-scattering computation)
    // City light is WIP, not used now
    LightParams[2] = (AtmosphereLightParam)0;
	// LightParams[2].Direction = float3(0, 0, 1);
	// LightParams[2].Illuminance = float3(0, 0, 0);
	// LightParams[2].IsSun = false;
	// LightParams[2].Reversed = false;
	// LightParams[2].ReversedLightRadius = 0;

	SingleScatteringResult r0 = IntegrateScatteredLuminance(pixPos / MultiScatteringLUTRes, WorldPos, WorldDir, LightParams, 3, Atmosphere, ground, Sampling, DepthBufferValue, MieRayPhase);
	SingleScatteringResult r1 = IntegrateScatteredLuminance(pixPos / MultiScatteringLUTRes, WorldPos, -WorldDir, LightParams, 3, Atmosphere, ground, Sampling, DepthBufferValue, MieRayPhase);
	float3 IntegratedIlluminance = (SphereSolidAngle / 2.0f) * (r0.L + r1.L);
	float3 MultiScatAs1 = (1.0f / 2.0f)*(r0.MultiScatAs1 + r1.MultiScatAs1);
	float3 InScatteredLuminance = IntegratedIlluminance * IsotropicPhase;

	float3 MultiScatAs1SQR = MultiScatAs1 * MultiScatAs1;
	float3 L = InScatteredLuminance * (1.0f + MultiScatAs1 + MultiScatAs1SQR + MultiScatAs1 * MultiScatAs1SQR + MultiScatAs1SQR * MultiScatAs1SQR);

	_MultiScatTexture[ThreadId.xy] = float4(MultiScatteringFactor * L, 1.0f);
}

float4 GetUniformSphereSamplesDir(int LinearIndex )
{
	const float4 UniformSphereSamplesBuffer[64] = 
	{
		float4(0.47778,  0.3282,  0.81487,  0.00),
		float4(0.86046,  0.04313,  0.50768,  0.00),
		float4(0.86619,  0.32325,  0.38107,  0.00),
		float4(0.92988,  0.36688,  0.02682,  0.00),
		float4(0.98682,  0.13566, -0.08825,  0.00),
		float4(0.92624,  0.05843, -0.37237,  0.00),
		float4(0.77975,  0.14644, -0.60872,  0.00),
		float4(0.29193,  0.04189, -0.95552,  0.00),
		float4(0.03684,  0.1977,  0.97957,  0.00),
		float4(0.16067,  0.65769,  0.73595,  0.00),
		float4( 0.45193,  0.84597,  0.28302,  0.0),
		float4( 0.67033,  0.7061,  0.22819,  0.00),
		float4( 0.44871,  0.87189, -0.19612,  0.0),
		float4( 0.22475,  0.89621, -0.38249,  0.0),
		float4( 0.22558,  0.76872, -0.59848,  0.0),
		float4( 0.06579,  0.14871, -0.98669,  0.0),
		float4(-0.3813,  0.45931,  0.80228,  0.00),
		float4(-0.45008,  0.50893,  0.73377,  0.0),
		float4(-0.54338,  0.67604,  0.4977,  0.00),
		float4(-0.09091,  0.99159,  0.0921,  0.00),
		float4(-0.45068,  0.88589, -0.10997,  0.0),
		float4(-0.50254,  0.78582, -0.36048,  0.0),
		float4(-0.2647,  0.8014, -0.53637,  0.00),
		float4(-0.02099,  0.45625, -0.8896,  0.00),
		float4(-0.30121,  0.27401,  0.91334,  0.0),
		float4(-0.80374,  0.27383,  0.52822,  0.0),
		float4(-0.84571,  0.19738,  0.4958,  0.00),
		float4(-0.9325,  0.35225,  0.07974,  0.00),
		float4(-0.96532,  0.2481, -0.08129,  0.00),
		float4(-0.72387,  0.50171, -0.47361,  0.0),
		float4(-0.69586,  0.11719, -0.70855,  0.0),
		float4(-0.57862,  0.26181, -0.77244,  0.0),
		float4(-0.40183, -0.16196,  0.90128,  0.0),
		float4(-0.61404, -0.49068,  0.61822,  0.0),
		float4(-0.81284, -0.4591,  0.3585,  0.00),
		float4(-0.99121, -0.12964,  0.02643,  0.0),
		float4(-0.98515, -0.14046, -0.09871,  0.0),
		float4(-0.66522, -0.65269, -0.36261,  0.0),
		float4(-0.64749, -0.19774, -0.73597,  0.0),
		float4(-0.50887, -0.27864, -0.8145,  0.00),
		float4(-0.24011, -0.38839,  0.88966,  0.0),
		float4(-0.44003, -0.57226,  0.69202,  0.0),
		float4(-0.37811, -0.84161,  0.38565,  0.0),
		float4(-0.60524, -0.78355,  0.14046,  0.0),
		float4(-0.19452, -0.94887, -0.24861,  0.0),
		float4(-0.56046, -0.74505, -0.36166,  0.0),
		float4(-0.03089, -0.75608, -0.65375,  0.0),
		float4(-0.20537, -0.21415, -0.95497,  0.0),
		float4( 0.19314, -0.55179,  0.81131,  0.0),
		float4( 0.39453, -0.63194,  0.66708,  0.0),
		float4( 0.11647, -0.90857,  0.40118,  0.0),
		float4( 0.67573, -0.73341,  0.07415,  0.0),
		float4( 0.53887, -0.83254, -0.12847,  0.0),
		float4( 0.46548, -0.79147, -0.39611,  0.0),
		float4( 0.28962, -0.78804, -0.54324,  0.0),
		float4( 0.10689, -0.64189, -0.75931,  0.0),
		float4( 0.50264, -0.32378,  0.80157,  0.0),
		float4( 0.58862, -0.35983,  0.72391,  0.0),
		float4( 0.66333, -0.6141,  0.42764,  0.00),
		float4( 0.85308, -0.46384,  0.23895,  0.0),
		float4( 0.94241, -0.3033, -0.14099,  0.00),
		float4( 0.88032, -0.27571, -0.38602,  0.0),
		float4( 0.82202, -0.16523, -0.54496,  0.0),
		float4( 0.60714, -0.06858, -0.79163,  0.0)
	};
	return UniformSphereSamplesBuffer[LinearIndex];
}

groupshared float3 GroupSkyLuminanceSamples[8*8];

[numthreads(8, 8, 1)]
void ComputeDistantSkyLight(uint3 ThreadId : SV_DispatchThreadID)
{
	const int LinearIndex = ThreadId.y*8 + ThreadId.x;
	float2 PixPos = float2(ThreadId.xy) + 0.5f;

	AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);
	
	// float3 TilePosition = float3(0, 0, 0);
	// if(CE_USE_DOUBLE_TRANSFORM)
	// {
	// 	TilePosition = ce_CameraTilePosition;
	// }
    // float3 camera = ComputeCameraPosition(Atmosphere.BottomRadius, TilePosition);
    // float3 SamplePos = normalize(camera) * (Atmosphere.BottomRadius + 6.0f);
    
    // float3 camera = ComputeCameraPosition(Atmosphere.BottomRadius, TilePosition);
	// float3 SamplePos = normalize(camera) * (Atmosphere.BottomRadius + 6.0f);
    // Equivalent to the last two lines
    float3 SamplePos = normalize(SkyCameraOrigin - SkyPlanetCenterAndViewHeight.xyz) * (Atmosphere.BottomRadius + 6.0f);
	
	float ViewHeight = length(SamplePos);

	// We are going to trace 64 times using 64 parallel threads.
	// Result are written in shared memory and prefix sum is applied to integrate the lighting in a single RGB value
	// that can then be used to lit clouds in the sky mesh shader graph.
	// Select a direction for this thread
	const float3 SampleDir = GetUniformSphereSamplesDir(LinearIndex).xyz;//UniformSphereSamplesBuffer[LinearIndex].xyz;

	SamplingSetup Sampling = (SamplingSetup)0;
	{
		Sampling.VariableSampleCount = false;
		Sampling.SampleCountIni = 10.0f;
	}

	AtmosphereLightParam LightParams[3];
	// Main sun light
	LightParams[0].Direction = sun_direction0;
	LightParams[0].Illuminance = ce_AtmosphereLightData[0].LightIlluminanceOuterSpace;
	LightParams[0].IsSun = true;
	LightParams[0].Reversed = ce_AtmosphereLightData[0].LightReversed;
	LightParams[0].ReversedLightRadius = ce_AtmosphereLightData[0].ReversedLightRadius;
	// Secondary sun light
	LightParams[1].Direction = sun_direction1;
	LightParams[1].Illuminance = ce_AtmosphereLightData[1].LightIlluminanceOuterSpace;
	LightParams[1].IsSun = false;
	LightParams[1].Reversed = ce_AtmosphereLightData[1].LightReversed;
	LightParams[1].ReversedLightRadius = ce_AtmosphereLightData[1].ReversedLightRadius;
    // City light is WIP, not used now
    LightParams[2] = (AtmosphereLightParam)0;
	// LightParams[2].Direction = city_direction;
	// LightParams[2].Illuminance = ce_AtmosphereLightData[2].LightIlluminanceOuterSpace;
	// LightParams[2].IsSun = false;
	// LightParams[2].Reversed = ce_AtmosphereLightData[2].LightReversed;
	// LightParams[2].ReversedLightRadius = ce_AtmosphereLightData[2].ReversedLightRadius;

	SingleScatteringResult ss = IntegrateScatteredLuminance(PixPos / MultiScatteringLUTRes, SamplePos, SampleDir, LightParams, 3, Atmosphere, false, Sampling, -1.f, false);
	GroupSkyLuminanceSamples[LinearIndex] = ss.L * SkyLuminanceFactor;
	
	// Wait for all group threads to be done
	GroupMemoryBarrierWithGroupSync();

	if (LinearIndex < 32)
	{
		GroupSkyLuminanceSamples[LinearIndex] += GroupSkyLuminanceSamples[LinearIndex + 32];
	}
	GroupMemoryBarrierWithGroupSync();
	if (LinearIndex < 16)
	{
		GroupSkyLuminanceSamples[LinearIndex] += GroupSkyLuminanceSamples[LinearIndex + 16];
	}
	GroupMemoryBarrierWithGroupSync();

	if (LinearIndex < 8)
	{
		GroupSkyLuminanceSamples[LinearIndex] += GroupSkyLuminanceSamples[LinearIndex + 8];
	}
	GroupMemoryBarrierWithGroupSync();

	if (LinearIndex < 4)
	{
		GroupSkyLuminanceSamples[LinearIndex] += GroupSkyLuminanceSamples[LinearIndex + 4];
	}
	GroupMemoryBarrierWithGroupSync();
	// The smallest wave size is 4 on Mali G-71 hardware. So now we can do simple math operations without group sync.

	if (LinearIndex < 2)
	{
		GroupSkyLuminanceSamples[LinearIndex] += GroupSkyLuminanceSamples[LinearIndex + 2];
	}
	if (LinearIndex < 1)
	{
		const float3 AccumulatedLuminanceSamples = GroupSkyLuminanceSamples[LinearIndex] + GroupSkyLuminanceSamples[LinearIndex + 1];
		const float SamplerSolidAngle = 4.0f * PI_SA / float(8 * 8);
		const float3 Illuminanc = AccumulatedLuminanceSamples * SamplerSolidAngle;
		const float3 UniformPhaseFunction = 1.0f / (4.0f * PI_SA);
		_DistantSkyLightTexture[int2(0, 0)] = float4(Illuminanc * UniformPhaseFunction, 1.0f); // Luminance assuming scattering in a medium with a uniform phase function.
		// Since this is ran once per scene, we do not have access to view data (VIEWDATA_AVAILABLE==0). 
		// So this buffer is not pre-exposed today.
	}
}

#define NEARLY_ONE(x) (abs(abs(x) - 1.0) < 0.001)
#define NEARLY_ZERO(x) (abs(x) < 0.001)

// Compute SkyViewLut for ueatmo
[numthreads(8, 6, 1)]
void ComputeSkyView(uint3 ThreadId : SV_DispatchThreadID)
{	
    float2 pixPos = ThreadId.xy + float2(0.5, 0.5);
	AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);

	float2 uv = pixPos / float2(192.0, 108.0);
//
	//float3 ClipSpace = float3(uv * float2(2.0, -2.0) - float2(1.0, -1.0), 1.0);
	//float4 HPos = mul(ce_InvProjection, float4(ClipSpace, 1.0));
//
	//float3 WorldDir = ToUEVec(normalize(mul((float3x3)ce_InvView, HPos.xyz / HPos.w)));
	// float3 TilePosition = float3(0, 0, 0);
	// if(CE_USE_DOUBLE_TRANSFORM)
	// {
	// 	TilePosition = ce_CameraTilePosition;
	// }
    
	float3 WorldPos = (SkyCameraOrigin - SkyPlanetCenterAndViewHeight.xyz) * CM2KM;

	// ComputeCameraPosition(Atmosphere.BottomRadius, ce_CameraTilePosition);
    
	float viewHeight = length(WorldPos);
    
	float3 WorldDir;
	UvToSkyViewLutParams(Atmosphere, WorldDir, viewHeight, uv);

	float3 SunDir0, SunDir1, CityDir;
	{
		float3x3 localRef = (float3x3)SkyViewLutReferential;
        // localRef = GetSkyViewLocalReferential(ToUEVec(ce_CameraForward), WorldPos);
		SunDir0 = mul(localRef, sun_direction0);
		SunDir1 = mul(localRef, sun_direction1);
		CityDir = mul(localRef, city_direction);
	}
	WorldPos = float3(0.0f, 0.0f, viewHeight);
    
	if (!MoveToTopAtmosphere(WorldPos, WorldDir, Atmosphere.TopRadius))
	{
		// Ray is not intersecting the atmosphere
		_SkyViewLutTexture[ThreadId.xy] = float4(0, 0, 0, 1);
		return ;
	}

	SamplingSetup Sampling = (SamplingSetup)0;
	{
		Sampling.VariableSampleCount = true;
		Sampling.MinSampleCount = SkyViewLUTSampleCountMin;
		Sampling.MaxSampleCount = SkyViewLUTSampleCountMax;
		Sampling.DistanceToSampleCountMaxInv = SkyViewLUTDistanceToSampleCountMaxInv;
	}
	const bool ground = false;
	const float DepthBufferValue = -1.0;
	const bool MieRayPhase = true;
	AtmosphereLightParam LightParams[3];
	// Main sun light
	LightParams[0].Direction = SunDir0;
	LightParams[0].Illuminance = ce_AtmosphereLightData[0].LightIlluminanceOuterSpace;
	LightParams[0].IsSun = true;
	LightParams[0].Reversed = ce_AtmosphereLightData[0].LightReversed;
	LightParams[0].ReversedLightRadius = ce_AtmosphereLightData[0].ReversedLightRadius;
	// Secondary sun light
	LightParams[1].Direction = SunDir1;
	LightParams[1].Illuminance = ce_AtmosphereLightData[1].LightIlluminanceOuterSpace;
	LightParams[1].IsSun = false;
	LightParams[1].Reversed = ce_AtmosphereLightData[1].LightReversed;
	LightParams[1].ReversedLightRadius = ce_AtmosphereLightData[1].ReversedLightRadius;
	// City light is WIP, not used now
    LightParams[2] = (AtmosphereLightParam)0;
	// LightParams[2].Direction = CityDir;
	// LightParams[2].Illuminance = ce_AtmosphereLightData[2].LightIlluminanceOuterSpace;
	// LightParams[2].IsSun = false;
	// LightParams[2].Reversed = ce_AtmosphereLightData[2].LightReversed;
	// LightParams[2].ReversedLightRadius = ce_AtmosphereLightData[2].ReversedLightRadius;
    
	SingleScatteringResult ss = IntegrateScatteredLuminance(uv, WorldPos, WorldDir, LightParams, 3, Atmosphere,
	    ground, Sampling, DepthBufferValue, MieRayPhase);
    
    const float Transmittance = dot(ss.Transmittance, float3(1.0f / 3.0f, 1.0f / 3.0f, 1.0f / 3.0f));
    
	_SkyViewLutTexture[ThreadId.xy] = float4(ss.L, Transmittance);
}


float3 ComputeCameraPosition2(float bottomRadius, float3 TilePosition = float3(0, 0, 0))
{
	float UnitToKMScaler = UnitM ? 0.001f : 0.00001f;
    float3 camera = GetLargeCoordinateReltvPosition(ce_CameraPos, TilePosition, float3(0.0, 0.0, 0.0)) * UnitToKMScaler;

	if (PLANET_TOP_AT_ORIGIN)
	{
		camera = camera + float3(0, bottomRadius, 0);
	}

	if(length(camera) <= bottomRadius + 0.02)
	{
		camera = normalize(camera) * (bottomRadius + 0.02);
	}
	return ToUEVec(camera);
}

RWTexture2D<float4>  _RenderSkyTarget						: register(space0);


float3 GetRayFromDepthAndUV(float depth, float2 uv)
{
    float4 ndc = float4(uv.x * 2 - 1, (1 - uv.y) * 2 - 1, depth, 1);
    float4 view = mul(ce_InvProjection, ndc);
    view /= view.w;
    return mul((float3x3)ce_InvView, view.xyz);
}

const static float MaxHalfFloat = 65504.0f;
const static float Max11BitsFloat = 65024.0f;
const static float Max10BitsFloat = 64512.0f;
const static float3 Max111110BitsFloat3 = float3(Max11BitsFloat, Max11BitsFloat, Max10BitsFloat);

float4 post(float4 input)
{
	float4 rgbA = input / input.aaaa;	// Normalise according to sample count when path tracing

	// Similar setup to the Bruneton demo
	float3 white_point = float3(1, 1, 1);//float3(1.08241, 0.96756, 0.95003);
	float exposure = 1;
	float4 r = float4(rgbA.rgb / white_point * exposure, 1);
	r.xyz = min(r.xyz, Max10BitsFloat.xxx * 0.5f);
	return r;
}

float4 PrepareOutput(float3 PreExposedLuminance, float3 Transmittance = float3(1.0f, 1.0f, 1.0f))
{
    // Sky materials can result in high luminance values, e.g. the sun disk. 
    // This is so we make sure to at least stay within the boundaries of fp10 and not cause NaN on some platforms.
    // We also half that range to also make sure we have room for other additive elements such as bloom, clouds or particle visual effects.
    const float3 SafePreExposedLuminance = min(PreExposedLuminance, Max10BitsFloat.xxx * 0.5f);
	
    const float GreyScaleTransmittance = dot(Transmittance, float3(1.0f / 3.0f, 1.0f / 3.0f, 1.0f / 3.0f));
    float4 LuminanceAlpha = float4(SafePreExposedLuminance, GreyScaleTransmittance);
    
    LuminanceAlpha.a	= 1 - GreyScaleTransmittance;
	
    return LuminanceAlpha;

}

// Compute RealtimeCaptureSkyViewLut, but not used now
[numthreads(8, 8, 1)]
void RenderSkyCS(uint3 ThreadId : SV_DispatchThreadID)
{
#if 1
    float2 pixPos = ThreadId.xy + float2(0.5, 0.5);
    AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);

    float2 uv = pixPos / float2(128.f, 128.f);

    float3 WorldPos = (SkyCameraOrigin - SkyPlanetCenterAndViewHeight.xyz) * CM2KM;
    float viewHeight = length(WorldPos);
    WorldPos = float3(0.0f, 0.0f, viewHeight);
    
    float3 SunDir0, SunDir1, CityDir;
    SunDir0 = sun_direction0;
    SunDir1 = sun_direction1;
    CityDir = city_direction;
    
    float3 view_ray = GetRayFromDepthAndUV(1.f, uv);
    float3 view_direction = normalize(view_ray);

    float3 WorldDir = ToUEVec(view_direction);
    
    if (USE_WGS84)  // Rotate to WGS84 Coordinates
    {
        float3x3 localRef = (float3x3)SkyViewLutReferential;
        SunDir0 = mul(localRef, SunDir0);
        SunDir1 = mul(localRef, SunDir1);
        CityDir = mul(localRef, CityDir);
        WorldDir = mul(localRef, WorldDir);
    }

    float3 OutLuminance = 0.f;
    float3 PreExposedL = 0.f;
    float3 LuminanceScale = SkyLuminanceFactor;

    if (!MoveToTopAtmosphere(WorldPos, WorldDir, Atmosphere.TopRadius))
    {
        OutLuminance = PrepareOutput(PreExposedL);
        _RenderSkyTarget[ThreadId.xy] = float4(OutLuminance.rgb, 1.f);
        return;
    }

    SamplingSetup Sampling = (SamplingSetup)0;
    {
        Sampling.VariableSampleCount = true;
        Sampling.MinSampleCount = SkyViewLUTSampleCountMin;
        Sampling.MaxSampleCount = SkyViewLUTSampleCountMax;
        Sampling.DistanceToSampleCountMaxInv = SkyViewLUTDistanceToSampleCountMaxInv;
    }

    const bool ground = false;
    const float DepthBufferValue = -1.0;
    const bool MieRayPhase = true;
    AtmosphereLightParam LightParams[3];
    // Main sun light
    LightParams[0].Direction = SunDir0;
    LightParams[0].Illuminance = ce_AtmosphereLightData[0].LightIlluminanceOuterSpace;
    LightParams[0].IsSun = true;
    LightParams[0].Reversed = ce_AtmosphereLightData[0].LightReversed;
    LightParams[0].ReversedLightRadius = ce_AtmosphereLightData[0].ReversedLightRadius;
    // Secondary sun light
    LightParams[1].Direction = SunDir1;
    LightParams[1].Illuminance = ce_AtmosphereLightData[1].LightIlluminanceOuterSpace;
    LightParams[1].IsSun = false;
    LightParams[1].Reversed = ce_AtmosphereLightData[1].LightReversed;
    LightParams[1].ReversedLightRadius = ce_AtmosphereLightData[1].ReversedLightRadius;
    // City light is WIP, not used now
    LightParams[2] = (AtmosphereLightParam)0;
    // LightParams[2].Direction = CityDir;
    // LightParams[2].Illuminance = ce_AtmosphereLightData[2].LightIlluminanceOuterSpace;
    // LightParams[2].IsSun = false;
    // LightParams[2].Reversed = ce_AtmosphereLightData[2].LightReversed;
    // LightParams[2].ReversedLightRadius = ce_AtmosphereLightData[2].ReversedLightRadius;

    SingleScatteringResult ss = IntegrateScatteredLuminance(uv, WorldPos, WorldDir, LightParams, 3, Atmosphere, ground, Sampling, DepthBufferValue, MieRayPhase);

    PreExposedL += ss.L * LuminanceScale;

    OutLuminance = PrepareOutput(PreExposedL, ss.Transmittance);
    _RenderSkyTarget[ThreadId.xy] = float4(OutLuminance.rgb, 1.f);
#else
        float2 inPixPos = ThreadId.xy + float2(0.5, 0.5);
        float2 uv = inPixPos / 128;

        AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);
	
        float3 view_ray = GetRayFromDepthAndUV(1.0, uv);
        float3 view_direction = normalize(view_ray);
        float2 pixPos = uv;

        float3 WorldDir = ToUEVec(view_direction);
        float3 WorldPos = (SkyCameraOrigin - SkyPlanetCenterAndViewHeight.xyz) * CM2KM;

        float viewHeight = length(WorldPos);
        float3 L = float3(0, 0, 0);
        {
            float2 SkyViewUV;
            float3x3 LocalReferencial = (float3x3)SkyViewLutReferential;

            // Input vectors expressed in this referencial: Up is always Z. Also note that ViewHeight is unchanged in this referencial.
            float3 WorldPosLocal = float3(0.0, 0.0, viewHeight);
            float3 UpVectorLocal = float3(0.0, 0.0, 1.0);
            float3 WorldDirLocal = mul(LocalReferencial, WorldDir);

            float viewZenithCosAngle = dot(WorldDirLocal, UpVectorLocal);

            bool IntersectGround = raySphereIntersectNearest(WorldPos, WorldDir, float3(0, 0, 0), Atmosphere.BottomRadius) >= 0.0f;
            SkyViewLutParamsToUv(Atmosphere, IntersectGround, viewZenithCosAngle, WorldDirLocal, viewHeight, SkyViewUV);
		
            float4 output = float4(_SkyViewLutTextureReadOnly.SampleLevel(ce_Sampler_Clamp, SkyViewUV, 0).rgb * SkyLuminanceFactor * Exposure, 1);
            _RenderSkyTarget[ThreadId.xy] = float4(output.rgb, 1.f);
        }
#endif
}

[numthreads(8, 8, 1)]
void ClearSkyCS(uint3 ThreadId : SV_DispatchThreadID)
{
	_RenderSkyTarget[ThreadId.xy] = float4(0, 0, 0, 1);
}