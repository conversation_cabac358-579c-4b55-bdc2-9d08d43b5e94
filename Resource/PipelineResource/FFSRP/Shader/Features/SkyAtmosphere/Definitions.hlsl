/**
 * Copyright (c) 2017 <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holders nor the names of its
 *    contributors may be used to endorse or promote products derived from
 *    this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 */

/*<h2>atmosphere/definitions.glsl</h2>

<p>This GLSL file defines the physical types and constants which are used in the
main <a href="functions.glsl.html">functions</a> of our atmosphere model, in
such a way that they can be compiled by a GLSL compiler (a
<a href="reference/definitions.h.html">C++ equivalent</a> of this file
provides the same types and constants in C++, to allow the same functions to be
compiled by a C++ compiler - see the <a href="../index.html">Introduction</a>).

<h3>Physical quantities</h3>

<p>The physical quantities we need for our atmosphere model are
<a href="https://en.wikipedia.org/wiki/Radiometry">radiometric</a> and
<a href="https://en.wikipedia.org/wiki/Photometry_(optics)">photometric</a>
quantities. In GLSL we can't define custom numeric types to enforce the
homogeneity of expressions at compile time, so we define all the physical
quantities as <code>float</code>, with preprocessor macros (there is no
<code>typedef</code> in GLSL).

<p>We start with six base quantities: length, wavelength, angle, solid angle,
power and luminous power (wavelength is also a length, but we distinguish the
two for increased clarity).
*/

#define TYPE_Length float
#define TYPE_Wavelength float
#define TYPE_Angle float
#define TYPE_SolidAngle float
#define TYPE_Power float
#define TYPE_LuminousPower float

/*
<p>From this we "derive" the irradiance, radiance, spectral irradiance,
spectral radiance, luminance, etc, as well pure numbers, area, volume, etc (the
actual derivation is done in the <a href="reference/definitions.h.html">C++
equivalent</a> of this file).
*/

#define TYPE_Number float
#define TYPE_InverseLength float
#define TYPE_Area float
#define TYPE_Volume float
#define TYPE_NumberDensity float
#define TYPE_Irradiance float
#define TYPE_Radiance float
#define TYPE_SpectralPower float
#define TYPE_SpectralIrradiance float
#define TYPE_SpectralRadiance float
#define TYPE_SpectralRadianceDensity float
#define TYPE_ScatteringCoefficient float
#define TYPE_InverseSolidAngle float
#define TYPE_LuminousIntensity float
#define TYPE_Luminance float
#define TYPE_Illuminance float

/*
<p>We  also need vectors of physical quantities, mostly to represent functions
depending on the wavelength. In this case the vector elements correspond to
values of a function at some predefined wavelengths. Again, in GLSL we can't
define custom vector types to enforce the homogeneity of expressions at compile
time, so we define these vector types as <code>vec3</code>, with preprocessor
macros. The full definitions are given in the
<a href="reference/definitions.h.html">C++ equivalent</a> of this file).
*/

// A generic function from Wavelength to some other type.
#define TYPE_AbstractSpectrum float3
// A function from Wavelength to Number.
#define TYPE_DimensionlessSpectrum float3
// A function from Wavelength to SpectralPower.
#define TYPE_owerSpectrum float3
// A function from Wavelength to SpectralIrradiance.
#define TYPE_IrradianceSpectrum float3
// A function from Wavelength to SpectralRadiance.
#define TYPE_RadianceSpectrum float3
// A function from Wavelength to SpectralRadianceDensity.
#define TYPE_RadianceDensitySpectrum float3
// A function from Wavelength to ScaterringCoefficient.
#define TYPE_ScatteringSpectrum float3

// A position in 3D (3 length values).
#define TYPE_Position float3
// A unit direction vector in 3D (3 unitless values).
#define TYPE_Direction float3
// A vector of 3 luminance values.
#define TYPE_Luminance3 float3
// A vector of 3 illuminance values.
#define TYPE_Illuminance3 float3

/*
<p>Finally, we also need precomputed textures containing physical quantities in
each texel. Since we can't define custom sampler types to enforce the
homogeneity of expressions at compile time in GLSL, we define these texture
types as <code>sampler2D</code> and <code>sampler3D</code>, with preprocessor
macros. The full definitions are given in the
<a href="reference/definitions.h.html">C++ equivalent</a> of this file).
*/

#define TransmittanceTexture Texture2D<float4>
#define AbstractScatteringTexture Texture3D<float4>
#define ReducedScatteringTexture Texture3D<float4>
#define ScatteringTexture Texture3D<float4>
#define ScatteringDensityTexture Texture3D<float4>
#define IrradianceTexture Texture2D<float4>

/*
<h3>Physical units</h3>

<p>We can then define the units for our six base physical quantities:
meter (m), nanometer (nm), radian (rad), steradian (sr), watt (watt) and lumen
(lm):
*/

static const TYPE_Length m = 1.0;
static const TYPE_Wavelength nm = 1.0;
static const TYPE_Angle rad = 1.0;
static const TYPE_SolidAngle sr = 1.0;
static const TYPE_Power watt = 1.0;
static const TYPE_LuminousPower lm = 1.0;
static double kLengthUnitInMeters = 1000.0;

static int TRANSMITTANCE_TEXTURE_WIDTH = 256;
static int TRANSMITTANCE_TEXTURE_HEIGHT = 64;

static int SCATTERING_TEXTURE_R_SIZE = 32;
static int SCATTERING_TEXTURE_MU_SIZE = 128;
static int SCATTERING_TEXTURE_MU_S_SIZE = 32;
static int SCATTERING_TEXTURE_NU_SIZE = 8;

static int SCATTERING_TEXTURE_WIDTH = SCATTERING_TEXTURE_NU_SIZE * SCATTERING_TEXTURE_MU_S_SIZE;
static int SCATTERING_TEXTURE_HEIGHT = SCATTERING_TEXTURE_MU_SIZE;
static int SCATTERING_TEXTURE_DEPTH = SCATTERING_TEXTURE_R_SIZE;

static int IRRADIANCE_TEXTURE_WIDTH = 64;
static int IRRADIANCE_TEXTURE_HEIGHT = 16;

static int MultiScatteringLUTRes = 32;
static int CAMERA_VOLUME_LUT_RES = 32;
static int CAMERA_VOLUME_LUT_DEPTH = 64;
static int SKY_VIEW_LUT_WIDTH = 192;
static int SKY_VIEW_LUT_HEIGHT = 108;

// The conversion factor between watts and lumens.
static float MAX_LUMINOUS_EFFICACY = 683.0;

/*
<p>From which we can derive the units for some derived physical quantities,
as well as some derived units (kilometer km, kilocandela kcd, degree deg):
*/

static const float PI_SA = 3.1415926535897932f;

static const TYPE_Length km = 1000.0 * m;
static const TYPE_Area m2 = m * m;
static const TYPE_Volume m3 = m * m * m;
static const TYPE_Angle pi = PI_SA * rad;
static const TYPE_Angle deg = PI_SA / 180.0;
static const TYPE_Irradiance watt_per_square_meter = watt / m2;
static const TYPE_Radiance watt_per_square_meter_per_sr = watt / (m2 * sr);
static const TYPE_SpectralIrradiance watt_per_square_meter_per_nm = watt / (m2 * nm);
static const TYPE_SpectralRadiance watt_per_square_meter_per_sr_per_nm =
     watt / (m2 * sr * nm);
static const TYPE_SpectralRadianceDensity watt_per_cubic_meter_per_sr_per_nm =
     watt / (m3 * sr * nm);
static const TYPE_LuminousIntensity cd = lm / sr;
static const TYPE_LuminousIntensity kcd = 1000.0 * cd;
static const TYPE_Luminance cd_per_square_meter = cd / m2;
static const TYPE_Luminance kcd_per_square_meter = kcd / m2;

/*
<h3>Atmosphere parameters</h3>

<p>Using the above types, we can now define the parameters of our atmosphere
model. We start with the definition of density profiles, which are needed for
parameters that depend on the altitude:
*/

// An atmosphere layer of width 'width', and whose density is defined as
//   'exp_term' * exp('exp_scale' * h) + 'linear_term' * h + 'constant_term',
// clamped to [0,1], and where h is the altitude.
struct DensityProfileLayer {
  TYPE_Length width;
  TYPE_Number exp_term;
  TYPE_InverseLength exp_scale;
  TYPE_InverseLength linear_term;
  TYPE_Number constant_term;
  float padding0;
  float padding1;
  float padding2;
};

DensityProfileLayer InitDensityProfileLayer(
    TYPE_Length width,
    TYPE_Number exp_term,
    TYPE_InverseLength exp_scale,
    TYPE_InverseLength linear_term,
    TYPE_Number constant_term)
{
  DensityProfileLayer ret;
  ret.width = width;
  ret.exp_term = exp_term;
  ret.exp_scale = exp_scale;
  ret.linear_term = linear_term;
  ret.constant_term = constant_term;
  return ret;
}
// An atmosphere density profile made of several layers on top of each other
// (from bottom to top). The width of the last layer is ignored, i.e. it always
// extend to the top atmosphere boundary. The profile values vary between 0
// (null density) to 1 (maximum density).
struct DensityProfile {
  DensityProfileLayer layers[2];
};

/*
The atmosphere parameters are then defined by the following struct:
*/

struct AtmosphereParameters {
  // The solar irradiance at the top of the atmosphere.
  TYPE_IrradianceSpectrum solar_irradiance;
  // The sun's angular radius. Warning: the implementation uses approximations
  // that are valid only if this angle is smaller than 0.1 radians.
  TYPE_Angle sun_angular_radius;
  // The scattering coefficient of air molecules at the altitude where their
  // density is maximum (usually the bottom of the atmosphere), as a function of
  // wavelength. The scattering coefficient at altitude h is equal to
  // 'rayleigh_scattering' times 'rayleigh_density' at this altitude.
  TYPE_ScatteringSpectrum rayleigh_scattering;
  // The distance between the planet center and the bottom of the atmosphere.
  TYPE_Length bottom_radius;
  // The scattering coefficient of aerosols at the altitude where their density
  // is maximum (usually the bottom of the atmosphere), as a function of
  // wavelength. The scattering coefficient at altitude h is equal to
  // 'mie_scattering' times 'mie_density' at this altitude.
  TYPE_ScatteringSpectrum mie_scattering;
  // The distance between the planet center and the top of the atmosphere.
  TYPE_Length top_radius;
  // The density profile of air molecules, i.e. a function from altitude to
  // dimensionless values between 0 (null density) and 1 (maximum density).
  DensityProfile rayleigh_density;
  // The density profile of aerosols, i.e. a function from altitude to
  // dimensionless values between 0 (null density) and 1 (maximum density).
  DensityProfile mie_density;
  // The extinction coefficient of aerosols at the altitude where their density
  // is maximum (usually the bottom of the atmosphere), as a function of
  // wavelength. The extinction coefficient at altitude h is equal to
  // 'mie_extinction' times 'mie_density' at this altitude.
  TYPE_ScatteringSpectrum mie_extinction;
  // The asymetry parameter for the Cornette-Shanks phase function for the
  // aerosols.
  TYPE_Number mie_phase_function_g;
  // The density profile of air molecules that absorb light (e.g. ozone), i.e.
  // a function from altitude to dimensionless values between 0 (null density)
  // and 1 (maximum density).
  DensityProfile absorption_density;
  // The extinction coefficient of molecules that absorb light (e.g. ozone) at
  // the altitude where their density is maximum, as a function of wavelength.
  // The extinction coefficient at altitude h is equal to
  // 'absorption_extinction' times 'absorption_density' at this altitude.
  TYPE_ScatteringSpectrum absorption_extinction;
  // The cosine of the maximum Sun zenith angle for which atmospheric scattering
  // must be precomputed (for maximum precision, use the smallest Sun zenith
  // angle yielding negligible sky light radiance values. For instance, for the
  // Earth case, 102 degrees is a good choice - yielding mu_s_min = -0.2).
  TYPE_Number mu_s_min;
  // The average albedo of the ground.
  TYPE_DimensionlessSpectrum ground_albedo;
  float padding;
};

struct AtmosphereParametersUE
{
	// Radius of the planet (center to ground)
	float BottomRadius;
	// Maximum considered atmosphere height (center to atmosphere top)
	float TopRadius;

	// Rayleigh scattering exponential distribution scale in the atmosphere
	float RayleighDensityExpScale;
	// Rayleigh scattering coefficients
	float3 RayleighScattering;

	// Mie scattering exponential distribution scale in the atmosphere
	float MieDensityExpScale;
	// Mie scattering coefficients
	float3 MieScattering;
	// Mie extinction coefficients
	float3 MieExtinction;
	// Mie absorption coefficients
	float3 MieAbsorption;
	// Mie phase function excentricity
	float MiePhaseG;

	// Another medium type in the atmosphere
	float AbsorptionDensity0LayerWidth;
	float AbsorptionDensity0ConstantTerm;
	float AbsorptionDensity0LinearTerm;
	float AbsorptionDensity1ConstantTerm;
	float AbsorptionDensity1LinearTerm;
	// This other medium only absorb light, e.g. useful to represent ozone in the earth atmosphere
	float3 AbsorptionExtinction;

	// The albedo of the ground.
	float3 GroundAlbedo;
};

struct AtmosphereLightData
{
    // Normalized direction
    float3 LightDirection;
    float LightIntensity;

    float3 LightDiscLuminance; // LightIlluminanceOuterSpace / angle
    float LightDiscCosHalhApexAngle;

    float3 LightIlluminanceOuterSpace; // color * intensity
    bool LightReversed;

    uint ReversedLightRadius;
    float3 dummy;
};

AtmosphereParametersUE GetAtmosphereParametersUE(in AtmosphereParameters ATMOSPHERE)
{
	AtmosphereParametersUE Parameters;
	Parameters.AbsorptionExtinction = ATMOSPHERE.absorption_extinction;

	// Traslation from Bruneton2017 parameterisation.
	Parameters.RayleighDensityExpScale = ATMOSPHERE.rayleigh_density.layers[1].exp_scale;
	Parameters.MieDensityExpScale = ATMOSPHERE.mie_density.layers[1].exp_scale;
	Parameters.AbsorptionDensity0LayerWidth = ATMOSPHERE.absorption_density.layers[0].width;
	Parameters.AbsorptionDensity0ConstantTerm = ATMOSPHERE.absorption_density.layers[0].constant_term;
	Parameters.AbsorptionDensity0LinearTerm = ATMOSPHERE.absorption_density.layers[0].linear_term;
	Parameters.AbsorptionDensity1ConstantTerm = ATMOSPHERE.absorption_density.layers[1].constant_term;
	Parameters.AbsorptionDensity1LinearTerm = ATMOSPHERE.absorption_density.layers[1].linear_term;

	Parameters.MiePhaseG = ATMOSPHERE.mie_phase_function_g;
	Parameters.RayleighScattering = ATMOSPHERE.rayleigh_scattering;
	Parameters.MieScattering = ATMOSPHERE.mie_scattering;
	Parameters.MieAbsorption = ATMOSPHERE.mie_extinction - ATMOSPHERE.mie_scattering;
	Parameters.MieExtinction = ATMOSPHERE.mie_extinction;
	Parameters.GroundAlbedo = ATMOSPHERE.ground_albedo;
	Parameters.BottomRadius = ATMOSPHERE.bottom_radius;
	Parameters.TopRadius = ATMOSPHERE.top_radius;
	return Parameters;
}