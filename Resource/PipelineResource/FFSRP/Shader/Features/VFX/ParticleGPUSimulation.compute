// #pragma enable debug_symbol
#pragma compute LocalSpaceParticleSimulationTick
#pragma compute GlobalSpaceParticleSimulationTick
#pragma only_renderers vulkan
#include "/Features/VFX/ParticleSystemUtils.hlsl"
#include "/Features/VFX/ParticleSpawnModules.hlsl"
#include "/Features/VFX/ParticleUpdateModules.hlsl"
#include "/Features/VFX/ParticleGPUScene.hlsl"

bool IsModuleEnable(int mask, int currentBit)
{
    return (mask & (1 << currentBit)) != 0;
}

[numthreads(THREADS_PER_GROUP, 1, 1)]
void LocalSpaceParticleSimulationTick(
    uint3 groupId          : SV_GroupID,
    uint3 groupThreadId    : SV_GroupThreadID,
    uint3 dispatchThreadId : SV_DispatchThreadID,
    uint  groupIndex       : SV_GroupIndex)
{
    uint particleThreadId = groupIndex + (groupId.z * _ParticleIndirectArgsSRV[0] * _ParticleIndirectArgsSRV[1] + groupId.y * _ParticleIndirectArgsSRV[0] + groupId.x) * THREADS_PER_GROUP;
    uint aliveParticleCount = _ParticleIndirectArgsSRV[3];
    gSpawnCount = aliveParticleCount - _ParticleIndirectArgsSRV[4];

    GDispatchThreadId	= dispatchThreadId;
	GGroupId			= groupId;
	GGroupThreadId		= groupThreadId;
	GGroupIndex			= groupIndex;
    GEmitterTickCounter = _EmitterTickCounter;
    GEmitterRandomSeed  = _EmitterRandomSeed;
    GLinearThreadId     = GDispatchThreadId.x;

    if (groupIndex == 0)
    {
        LoadEmitterParams(0);
        SetLocalRelativeMatrix();
    }
    GroupMemoryBarrierWithGroupSync();
    gOutputIndex = aliveParticleCount;
    gSpawnStartIndex = gOutputIndex - gSpawnCount;
    const bool bIsValid = particleThreadId < aliveParticleCount;
    const bool bRunUpdateLogic = bIsValid && particleThreadId < gSpawnStartIndex; //bRunSpawnUpdateLogic && GLinearThreadId < GSpawnStartInstance && GLinearThreadId < MaxInstances;
	const bool bRunSpawnLogic = bIsValid && particleThreadId >= gSpawnStartIndex;

	[branch]
    if (bRunSpawnLogic)
    {
        ResetParticleState();
        update_ParticleInit(particleThreadId);
        InitWorldTransform();
        update_LocationShape(0, particleThreadId, context);
    }
    else if (bRunUpdateLogic)
    {
        LoadParticleState(particleThreadId);
        UpdateWorldTransform();
    }

    bool bDoAcquire = bIsValid;
    bool bRunningState = _EmitterSimulationState == 0;
    context.particleForce = float3(0, 0, 0);
    if (bRunningState)
    {
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SIZE_SCALE))
        {
            update_SizeScale(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_COLOR_SCALE))
        {
            update_ColorScale(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SPRITE_ROTATION_RATE))
        {
            update_SpriteRotationRate(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_VELOCITY))
        {
            update_Velocity(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_GRAVITY))
        {
            update_GravityForce(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_FORCE))
        {
            update_Force(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_VORTEX_FORCE))
        {
            update_VortoxForce(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_POINT_ATTRACTION))
        {
            update_PointAttractionForce(particleThreadId, context, bRunningState);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_VECTOR_NOISE))
        {
            update_VectorNoise(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SOLVE_FORCE_VELOCITY))
        {
            update_SolveForceVelocity(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SUBUV))
        {
            update_SubUV(particleThreadId, context);
        }
    }

    update_ParticleState(particleThreadId, groupIndex, bRunningState, bDoAcquire);
    if (bDoAcquire)
        UpdateLocalSpaceGPUScene();
}

[numthreads(THREADS_PER_GROUP, 1, 1)]
void GlobalSpaceParticleSimulationTick(
    uint3 groupId          : SV_GroupID,
    uint3 groupThreadId    : SV_GroupThreadID,
    uint3 dispatchThreadId : SV_DispatchThreadID,
    uint  groupIndex       : SV_GroupIndex)
{
    uint particleThreadId = groupIndex + (groupId.z * _ParticleIndirectArgsSRV[0] * _ParticleIndirectArgsSRV[1] + groupId.y * _ParticleIndirectArgsSRV[0] + groupId.x) * THREADS_PER_GROUP;
    uint aliveParticleCount = _ParticleIndirectArgsSRV[3];
    gSpawnCount = aliveParticleCount - _ParticleIndirectArgsSRV[4];

    GDispatchThreadId	= dispatchThreadId;
	GGroupId			= groupId;
	GGroupThreadId		= groupThreadId;
	GGroupIndex			= groupIndex;
    GEmitterTickCounter = _EmitterTickCounter;
    GEmitterRandomSeed  = _EmitterRandomSeed;
    GLinearThreadId     = GDispatchThreadId.x;

    if (groupIndex == 0)
    {
        LoadEmitterParams(0);
        SetLocalRelativeMatrix();
    }
    GroupMemoryBarrierWithGroupSync();
    gOutputIndex = aliveParticleCount;
    gSpawnStartIndex = gOutputIndex - gSpawnCount;
    const bool bIsValid = particleThreadId < aliveParticleCount;
    const bool bRunUpdateLogic = bIsValid && particleThreadId < gSpawnStartIndex; //bRunSpawnUpdateLogic && GLinearThreadId < GSpawnStartInstance && GLinearThreadId < MaxInstances;
	const bool bRunSpawnLogic = bIsValid && particleThreadId >= gSpawnStartIndex;

	[branch]
    if (bRunSpawnLogic)
    {
        ResetParticleState();
        update_ParticleInit(particleThreadId);
        InitWorldTransform();
        update_LocationShape(0, particleThreadId, context);
    }
    else if (bRunUpdateLogic)
    {
        LoadParticleState(particleThreadId);
        UpdateWorldTransform();
    }

    bool bDoAcquire = bIsValid;
    bool bRunningState = _EmitterSimulationState == 0;
    context.particleForce = float3(0, 0, 0);
    if (bRunningState)
    {
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SIZE_SCALE))
        {
            update_SizeScale(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_COLOR_SCALE))
        {
            update_ColorScale(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SPRITE_ROTATION_RATE))
        {
            update_SpriteRotationRate(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_VELOCITY))
        {
            update_Velocity(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_GRAVITY))
        {
            update_GravityForce(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_FORCE))
        {
            update_Force(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_VORTEX_FORCE))
        {
            update_VortoxForce(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_POINT_ATTRACTION))
        {
            update_PointAttractionForce(particleThreadId, context, bRunningState);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_VECTOR_NOISE))
        {
            update_VectorNoise(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SOLVE_FORCE_VELOCITY))
        {
            update_SolveForceVelocity(particleThreadId, context);
        }
        [branch]
        if (IsModuleEnable(_EmitterDashboardMask.w, PARTICLE_UPDATE_SUBUV))
        {
            update_SubUV(particleThreadId, context);
        }
    }

    update_ParticleState(particleThreadId, groupIndex, bRunningState, bDoAcquire);
    if (bDoAcquire)
        UpdateGlobalSpaceGPUScene();
}