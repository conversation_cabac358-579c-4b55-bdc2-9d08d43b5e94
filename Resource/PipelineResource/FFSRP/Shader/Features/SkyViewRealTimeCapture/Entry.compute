#pragma compute ComputeSkyEnvMapDiffuseIrradianceCS
#pragma compute ApplyLowerHemisphereColorCS
#pragma compute ApplyLowerHemisphereColorCSSimple
#pragma compute DownsampleCS
#pragma compute FilterCS
#include "ShaderLibrary/Common.hlsl"
SamplerState ce_Sampler_Point : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

SHADER_CONST(bool, _REVERSE_Z, true);
SHADER_CONST(bool, UnitM, false);
SHADER_CONST(bool, PLANET_TOP_AT_ORIGIN, true);

#include "../../Lighting/LightCommon.hlsl"

#include "../../SmartGI/SHCommon.hlsl"
#include "../SkyAtmosphere/UE/Interfaces.hlsl"
#include "../../../../../EngineResource/Shader/ReflectionEnvironmentCommon.hlsl"

TextureCube<float4> _InColorMap : register(space0);
RWStructuredBuffer<float4> OutIrradianceEnvMapSH : register(space0);


cbuffer para : register(space0)
{
    int _CurrentMip;
    int _Mip0Size;
    float _NumSamples;
    float _UniformSampleSolidAngle;
    int _MipIndex;

    //New process
    uint _NumMips;
    uint _CubeFace;
}

cbuffer hemiColorParam : register(space0)
{
    float3 SkyCameraOrigin;  // world position of the camera, unit cm
    float4 SkyPlanetCenterAndViewHeight;  // camera position relative to the virtual planet center, uint cm
	float4 LowerHemisphereSolidColor;
    bool TODLowerHemisphereSolidColor;
	uint2 ValidDispatchCoord;
	uint FaceThreadGroupSize;
    int CubeFace;
    float3 ce_CameraPos;
    float3 ce_CameraTilePosition;
	
	matrix ce_InvProjection;
	matrix ce_InvView;
};

float RadicalInverse(uint bits) {
    //reverse bit
    //高低16位换位置
    bits = (bits << 16u) | (bits >> 16u);
    //A是5的按位取反
    bits = ((bits & 0x55555555) << 1u) | ((bits & 0xAAAAAAAA) >> 1u);
    //C是3的按位取反
    bits = ((bits & 0x33333333) << 2u) | ((bits & 0xCCCCCCCC) >> 2u);
    bits = ((bits & 0x0F0F0F0F) << 4u) | ((bits & 0xF0F0F0F0) >> 4u);
    bits = ((bits & 0x00FF00FF) << 8u) | ((bits & 0xFF00FF00) >> 8u);
    return  float(bits) * 2.3283064365386963e-10;
}

float2 Hammersley(uint i, uint N) {
    return float2(float(i) / float(N), RadicalInverse(i));
}

float3 ImportanceSampleGGX(float2 Xi, float Roughness, float3 N)
{
    float a = Roughness * Roughness;
    float Phi = 2 * PI * Xi.x;
    float CosTheta = sqrt((1 - Xi.y) / (1 + (a * a - 1) * Xi.y));
    float SinTheta = sqrt(1 - CosTheta * CosTheta);
    float3 H;
    H.x = SinTheta * cos(Phi);
    H.y = SinTheta * sin(Phi);
    H.z = CosTheta;
    float3 UpVector = abs(N.z) < 0.999 ? float3(0, 0, 1) : float3(1, 0, 0);
    float3 TangentX = normalize(cross(UpVector, N));
    float3 TangentY = cross(N, TangentX);
    // Tangent to world space
    return TangentX * H.x + TangentY * H.y + N * H.z;
}


float3 PrefilterEnvMap(float Roughness, float3 R)
{
    float3 N = R;
    float3 V = R;
    float3 PrefilteredColor = 0;
    const uint NumSamples = (uint)(_NumSamples);// 1024;
    float TotalWeight = 0;
    for (uint i = 0; i < NumSamples; i++)
    {
        float2 Xi = Hammersley(i, NumSamples);
        float3 H = ImportanceSampleGGX(Xi, Roughness, N);
        float3 L = 2 * dot(V, H) * H - V;
        float NoL = saturate(dot(N, L));
        if (NoL > 0)
        {
            PrefilteredColor += _InColorMap.SampleLevel(ce_Sampler_Point, L, 0).rgb * NoL;
            TotalWeight += NoL;
        }
    }
    return PrefilteredColor / TotalWeight;
}

RWTexture2D<float4> _GenNextMipRenderTarget		: register(space0);

float4 UniformSampleSphere( float2 E )
{
	float Phi = 2 * PI * E.x;
	float CosTheta = 1 - 2 * E.y;
	float SinTheta = sqrt( 1 - CosTheta * CosTheta );

	float3 H;
	H.x = SinTheta * cos( Phi );
	H.y = SinTheta * sin( Phi );
	H.z = CosTheta;

	float PDF = 1.0 / (4 * PI);

	return float4( H, PDF );
}

groupshared FThreeBandSHVectorRGB IrradianceSHShared[64];

FThreeBandSHVectorRGB SampleSHRGB(in float3 SampleDirection, in float weight, in float MipLevel)
{
	const float3 SampleColor = _InColorMap.SampleLevel(ce_Sampler_Point, SampleDirection, MipLevel).rgb;

	FThreeBandSHVector Sh3Vector = SHBasisFunction3(SampleDirection);
	FThreeBandSHVectorRGB Result;
	Result.R = MulSH3(Sh3Vector, SampleColor.r * weight);
	Result.G = MulSH3(Sh3Vector, SampleColor.g * weight);
	Result.B = MulSH3(Sh3Vector, SampleColor.b * weight);
	return Result;
}

[numthreads(8, 8, 1)]
void ComputeSkyEnvMapDiffuseIrradianceCS(uint3 ThreadId : SV_DispatchThreadID)
{
	const uint LinearIndex = 8 * ThreadId.y + ThreadId.x;

	// For a 128x128 cubemap, sampling mip level 2 with only 8x8 samples matches closely the super sampled version.
	const float3 SampleDirection = UniformSampleSphere((float2(ThreadId.xy)+0.5f) / float2(8, 8)).xyz;
	IrradianceSHShared[LinearIndex] = SampleSHRGB(SampleDirection, _UniformSampleSolidAngle, _MipIndex);

	// Wait for all group threads to be done
	GroupMemoryBarrierWithGroupSync();

	if (LinearIndex < 32)
	{
		IrradianceSHShared[LinearIndex] = AddSH(IrradianceSHShared[LinearIndex], IrradianceSHShared[LinearIndex + 32]);
	}
	GroupMemoryBarrierWithGroupSync();

	if (LinearIndex < 16)
	{
		IrradianceSHShared[LinearIndex] = AddSH(IrradianceSHShared[LinearIndex], IrradianceSHShared[LinearIndex + 16]);
	}
	GroupMemoryBarrierWithGroupSync();

	// The smallest wave size is 16 on Intel hardware. So now we can do simple math operations without group sync.

	if (LinearIndex < 8)
	{
		IrradianceSHShared[LinearIndex] = AddSH(IrradianceSHShared[LinearIndex], IrradianceSHShared[LinearIndex + 8]);
	}
	if (LinearIndex < 4)
	{
		IrradianceSHShared[LinearIndex] = AddSH(IrradianceSHShared[LinearIndex], IrradianceSHShared[LinearIndex + 4]);
	}
	if (LinearIndex < 2)
	{
		IrradianceSHShared[LinearIndex] = AddSH(IrradianceSHShared[LinearIndex], IrradianceSHShared[LinearIndex + 2]);
	}

	if (LinearIndex < 1)
	{
		FThreeBandSHVectorRGB SkyIrradiance = AddSH(IrradianceSHShared[LinearIndex], IrradianceSHShared[LinearIndex + 1]);

		// Pack the SH coefficients in a way that makes applying the lighting use the least shader instructions
		// This has the diffuse convolution coefficients baked in. See "Stupid Spherical Harmonics (SH) Tricks".
		// Also see UpdateSkyIrradianceGpuBuffer.
		const float SqrtPI = sqrt(PI);
		const float Coefficient0 = 1.0f / (2.0f * SqrtPI);
		const float Coefficient1 = sqrt(3.0f) / (3.0f * SqrtPI);
		const float Coefficient2 = sqrt(15.0f) / (8.0f * SqrtPI);
		const float Coefficient3 = sqrt(5.0f) / (16.0f * SqrtPI);
		const float Coefficient4 = 0.5f * Coefficient2;

		OutIrradianceEnvMapSH[0].x = -Coefficient1 * SkyIrradiance.R.V0[3];
		OutIrradianceEnvMapSH[0].y = -Coefficient1 * SkyIrradiance.R.V0[1];
		OutIrradianceEnvMapSH[0].z =  Coefficient1 * SkyIrradiance.R.V0[2];
		OutIrradianceEnvMapSH[0].w =  Coefficient0 * SkyIrradiance.R.V0[0] - Coefficient3 * SkyIrradiance.R.V1[2];//[6];

		OutIrradianceEnvMapSH[1].x = -Coefficient1 * SkyIrradiance.G.V0[3];
		OutIrradianceEnvMapSH[1].y = -Coefficient1 * SkyIrradiance.G.V0[1];
		OutIrradianceEnvMapSH[1].z =  Coefficient1 * SkyIrradiance.G.V0[2];
		OutIrradianceEnvMapSH[1].w =  Coefficient0 * SkyIrradiance.G.V0[0] - Coefficient3 * SkyIrradiance.G.V1[2];//[6];

		OutIrradianceEnvMapSH[2].x = -Coefficient1 * SkyIrradiance.B.V0[3];
		OutIrradianceEnvMapSH[2].y = -Coefficient1 * SkyIrradiance.B.V0[1];
		OutIrradianceEnvMapSH[2].z =  Coefficient1 * SkyIrradiance.B.V0[2];
		OutIrradianceEnvMapSH[2].w =  Coefficient0 * SkyIrradiance.B.V0[0] - Coefficient3 * SkyIrradiance.B.V1[2];//[6];

		OutIrradianceEnvMapSH[3].x =  Coefficient2 * SkyIrradiance.R.V1[0];//[4];
		OutIrradianceEnvMapSH[3].y = -Coefficient2 * SkyIrradiance.R.V1[1];//V[5];
		OutIrradianceEnvMapSH[3].z = 3.0f * Coefficient3 * SkyIrradiance.R.V1[2];//[6];
		OutIrradianceEnvMapSH[3].w = -Coefficient2 * SkyIrradiance.R.V1[3];//[7];

		OutIrradianceEnvMapSH[4].x =  Coefficient2 * SkyIrradiance.G.V1[0];//[4];
		OutIrradianceEnvMapSH[4].y = -Coefficient2 * SkyIrradiance.G.V1[1];//[5];
		OutIrradianceEnvMapSH[4].z = 3.0f * Coefficient3 * SkyIrradiance.G.V1[2];//[6];
		OutIrradianceEnvMapSH[4].w = -Coefficient2 * SkyIrradiance.G.V1[3];//[7];

		OutIrradianceEnvMapSH[5].x =  Coefficient2 * SkyIrradiance.B.V1[0];//[4];
		OutIrradianceEnvMapSH[5].y = -Coefficient2 * SkyIrradiance.B.V1[1];//[5];
		OutIrradianceEnvMapSH[5].z = 3.0f * Coefficient3 * SkyIrradiance.B.V1[2];//[6];
		OutIrradianceEnvMapSH[5].w = -Coefficient2 * SkyIrradiance.B.V1[3];//[7];

		OutIrradianceEnvMapSH[6].x = Coefficient4 * SkyIrradiance.R.V2;//[8];
		OutIrradianceEnvMapSH[6].y = Coefficient4 * SkyIrradiance.G.V2;//[8];
		OutIrradianceEnvMapSH[6].z = Coefficient4 * SkyIrradiance.B.V2;//[8];
		OutIrradianceEnvMapSH[6].w = 1.0f;
	}
}


float3 GetRayFromDepthAndUV(float depth, float2 uv)
{
    float4 ndc = float4(uv.x * 2 - 1, (1 - uv.y) * 2 - 1, depth, 1);
    float4 view = mul(ce_InvProjection, ndc);
    view /= view.w;
    return mul((float3x3)ce_InvView, view.xyz);
}

RWTexture2D<float4> OutTextureMipColor		: register(space0);

float3 ComputeCameraPosition(float bottomRadius, float3 TilePosition = float3(0, 0, 0))
{
	float UnitToKMScaler = UnitM ? 0.001f : 0.00001f;
    float3 camera = GetLargeCoordinateReltvPosition(ce_CameraPos, TilePosition, float3(0.0, 0.0, 0.0)) * UnitToKMScaler;

	if (PLANET_TOP_AT_ORIGIN)
	{
		camera = camera + float3(0, bottomRadius, 0);
	}

	if(length(camera) <= bottomRadius + PLANET_RADIUS_OFFSET)
	{
		camera = normalize(camera) * (bottomRadius + PLANET_RADIUS_OFFSET);
	}
	return ToUEVec(camera);
}

[numthreads(8, 8, 1)]
void ApplyLowerHemisphereColorCS(uint3 ThreadId : SV_DispatchThreadID)
{
	float3 TilePosition = ce_CameraTilePosition;

	AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);

    // float3 camera = ComputeCameraPosition(Atmosphere.BottomRadius, TilePosition);
	
    float3 view_ray = GetRayFromDepthAndUV(1.0, (ThreadId.xy + 0.5f) / 128.0f);
    float3 view_direction = normalize(view_ray);

	float3 WorldDir = ToUEVec(view_direction);
	// float3 WorldPos = camera;
    float3 WorldPos = (SkyCameraOrigin - SkyPlanetCenterAndViewHeight.xyz) * CM2KM;
    float dist = raySphereIntersectNearest(WorldPos, WorldDir, float3(0, 0, 0), Atmosphere.BottomRadius);
	bool IntersectGround = dist >= 0.0f;
	
	if(IntersectGround)
	{
        float4 result = float4(LowerHemisphereSolidColor.rgb * LowerHemisphereSolidColor.a, 1.0f);
        // this is mainly  an approximatio for TOD of solid color lower hemisphere,we assume such color will represent the groud color and aid for gi sampling
        if(TODLowerHemisphereSolidColor)
        {
            float3 intersect = ToCEVec(WorldPos + WorldDir * dist * 0.9);
            //float3 trans =  GetAtmosphereTransmittance(intersect, ce_AtmosphereLightData[0].LightDirection);
            float NdotL =  dot(normalize(intersect), -ce_AtmosphereLightData[0].LightDirection);
            result.xyz *= saturate(NdotL) ;
        }
		OutTextureMipColor[ThreadId.xy] = result;
        //OutTextureMipColor[ThreadId.xy] = float4(LowerHemisphereSolidColor.rgb * LowerHemisphereSolidColor.a, 1.0f);
	}
}

[numthreads(8, 8, 1)]
void ApplyLowerHemisphereColorCSSimple(uint3 ThreadId : SV_DispatchThreadID)
{
	// if(_CurrentMip == 2)
	// {
	// 	// Y+
	// 	return ;
	// }
	// else if(_CurrentMip == 3 || ThreadId.y >= FaceThreadGroupSize / 2)
	// {
	// 	// Y-
	// 	OutTextureMipColor[ThreadId.xy] = float4(LowerHemisphereSolidColor.rgb * LowerHemisphereSolidColor.a, 1.0f);
	// }

    float2 UV = ThreadId.xy / 128.xx;
    float2 ScaledUVs = UV * 2 - 1;

    float3 CubeCoordinates = GetCubemapVector(ScaledUVs, CubeFace);
    float3 N = normalize(CubeCoordinates);
    
    if (N.z < 0.0f)
    {
        float Coverage = 0.0f;
        Coverage = pow(saturate(LowerHemisphereSolidColor.a), 2.2);
        Coverage = saturate(LowerHemisphereSolidColor.a);
        OutTextureMipColor[ThreadId.xy] = float4(LowerHemisphereSolidColor.rgb * Coverage, 1.0 - Coverage);
    }
}


//******** New mipmap process *********/
[numthreads(8, 8, 1)]
void DownsampleCS(
uint3 dispatchThreadId : SV_DispatchThreadID)
{
    int nextMipSize = _Mip0Size >> (_CurrentMip + 1);
    if (dispatchThreadId.x >= nextMipSize || dispatchThreadId.y >= nextMipSize)
    {
        return;
    }

    float2 uv = (dispatchThreadId.xy + 0.5) / (float)nextMipSize;
    //uv = float2(uv.x, 1.0 - uv.y);//test

    float2 ScaledUVs = uv * 2 - 1;
    const int SelectedCubeFace = _CubeFace;

    float3 CubeCoordinates = GetCubemapVector(ScaledUVs, SelectedCubeFace);

    uint MipSize = 1u << (_NumMips - _CurrentMip - 1);

    float3 TangentZ = normalize(CubeCoordinates);
    float3 TangentX = normalize(cross(GetCubemapVector(ScaledUVs + float2(0, 1), SelectedCubeFace), TangentZ));
    float3 TangentY = cross(TangentZ, TangentX);

    const float SampleOffset = 2.0 * 2 / MipSize;

    float2 Offsets[] =
    {
        float2(-1, -1) * 0.7,
        float2(1, -1) * 0.7,
        float2(-1,  1) * 0.7,
        float2(1,  1) * 0.7,

        float2(0, -1),
        float2(-1,  0),
        float2(1,  0),
        float2(0,  1),
    };

    float4 OutColor = _InColorMap.SampleLevel(ce_Sampler_Point, CubeCoordinates, 0);

    [unroll]
    for (uint i = 0; i < 8; i++)
    {
        float Weight = 0.375;

        float3 SampleDir = CubeCoordinates;
        SampleDir += TangentX * (Offsets[i].x * SampleOffset);
        SampleDir += TangentY * (Offsets[i].y * SampleOffset);
        OutColor += _InColorMap.SampleLevel(ce_Sampler_Point, SampleDir, 0) * Weight;
    }

    OutColor *= rcp(1.0 + 1.0 + 2.0);

    _GenNextMipRenderTarget[dispatchThreadId.xy] = OutColor;
}


[numthreads(8, 8, 1)]
void FilterCS(
uint3 dispatchThreadId : SV_DispatchThreadID)
{
    int filterMipSize = _Mip0Size >> _CurrentMip;
    if (dispatchThreadId.x >= filterMipSize || dispatchThreadId.y >= filterMipSize)
    {
        return;
    }

    float2 uv = (dispatchThreadId.xy + 0.5) / (float)filterMipSize;

    float2 ScaledUVs = uv * 2 - 1;
    const int SelectedCubeFace = _CubeFace;

    float3 CubeCoordinates = GetCubemapVector(ScaledUVs, SelectedCubeFace);

    float3 N = normalize(CubeCoordinates);
    float3x3 TangentToWorld = GetTangentBasis(N);

    float Roughness = ComputeReflectionCaptureRoughnessFromMip((float)(_CurrentMip), (half)(_NumMips - 1));

    float4 OutColor = 0;
    if (Roughness < 0.01)
    {
        OutColor = _InColorMap.SampleLevel(ce_Sampler_Point, CubeCoordinates, 0);
    }
    else
    {
        uint CubeSize = 1u << (_NumMips - 1);
        const float SolidAngleTexel = 4.0f * PI / float(6 * CubeSize * CubeSize) * 2.0f;

        const uint NumSamples = Roughness < 0.1 ? 32 : 64;

        float4 FilteredColor = 0;

        if (Roughness > 0.99)
        {// Roughness=1, GGX is constant. Use cosine distribution instead
            [loop]
            for (uint i = 0; i < NumSamples; i++)
            {
                float2 E = Hammersley(i, NumSamples, 0);

                float3 L = CosineSampleHemisphere(E).xyz;

                float NoL = L.z;

                float PDF = NoL / PI;
                float SolidAngleSample = 1.0 / (NumSamples * PDF);
                float Mip = 0.5 * log2(SolidAngleSample / SolidAngleTexel);

                L = mul(L, TangentToWorld);
                FilteredColor += _InColorMap.SampleLevel(ce_Sampler_Point, L, Mip);
            }

            OutColor = FilteredColor / NumSamples;
        }
        else
        {
            float Weight = 0;

            [loop]
            for (uint i = 0; i < NumSamples; i++)
            {
                float2 E = Hammersley(i, NumSamples, 0);

                E.y *= 0.995;

                float3 H = ImportanceSampleGGX(E, pow(Roughness, 4)).xyz;
                float3 L = 2 * H.z * H - float3(0, 0, 1);

                float NoL = L.z;
                float NoH = H.z;

                if (NoL > 0)
                {
                    float PDF = D_GGX(pow(Roughness, 4), NoH) * 0.25;
                    float SolidAngleSample = 1.0 / (NumSamples * PDF);
                    float Mip = 0.5 * log2(SolidAngleSample / SolidAngleTexel);

                    float ConeAngle = acos(1 - SolidAngleSample / (2 * PI));

                    L = mul(L, TangentToWorld);
                    FilteredColor += _InColorMap.SampleLevel(ce_Sampler_Point, L, Mip) * NoL;
                    Weight += NoL;
                }
            }

            OutColor = FilteredColor / Weight;
        }
    }

    _GenNextMipRenderTarget[dispatchThreadId.xy] = OutColor;
}