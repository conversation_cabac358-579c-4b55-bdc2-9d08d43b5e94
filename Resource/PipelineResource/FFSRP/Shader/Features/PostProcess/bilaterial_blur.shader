Texture2D<float4> src_texture : register(space0);
Texture2D<float4> ref_depth : register(space0);
SamplerState texture_sampler : register(space0);



cbuffer cbPass : register(space0)
{
	float4 src_dimension;
    //xy: direction, z: depth influnce
    float4 blur_dir;

}

cbuffer cbMtl:register(space1)
{
    float blur_depth_weight;
    float blur_strength;
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

#define PI 3.14159
#define KERNEL_RADIUS 4

//TODO: Switch to LUT
float GaussianWeight(float offset, float deviation)
{
    float weight = 1.0f / sqrt(2.0f * PI * deviation * deviation);
    weight *= exp(-(offset * offset) / (2.0f * deviation * deviation));
    return weight;
}

float4 BilaterialBlurDir(uint2 position, uint2 dir, Texture2D<float4> color_texture, Texture2D<float4> depth_texture)
{
    float center_depth = depth_texture.Load(uint3(position,0)).r;
    float weight_sum = 0;

    float4 color = (float4)0;

    [unroll]
    for(int i = -KERNEL_RADIUS; i <= KERNEL_RADIUS; i++)
    {
        int3 pos = int3(i*dir + position,0);
        pos.xy = clamp(pos.xy, int2(0,0), src_dimension.xy);
        float depth_diff = abs(depth_texture.Load(pos).r - center_depth);
        float depth_factor = depth_diff * blur_depth_weight;
        float weight = GaussianWeight(i,  KERNEL_RADIUS * blur_strength) * exp(-depth_factor * depth_factor);
        weight_sum += weight;

        color += weight * color_texture.Load(pos).rgba;
    }

    return color/weight_sum;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
    uint2 position = input.UV * src_dimension.xy;
    return BilaterialBlurDir(position, blur_dir.xy, src_texture, ref_depth);
}
