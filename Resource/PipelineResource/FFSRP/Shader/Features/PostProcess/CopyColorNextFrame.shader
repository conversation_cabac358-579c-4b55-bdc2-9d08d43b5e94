#pragma vertex VSMain
#pragma pixel PSMain
// #pragma keyword USE_MULTI_TARGET

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

Texture2D<float4> _ColorTex : register(space0);
SamplerState texture_sampler : register(space0);

struct PSOutput
{
    float4 color1 : SV_Target0;
// #if defined(USE_MULTI_TARGET) && USE_MULTI_TARGET == 1
//     float4 color2 : SV_Target1;
// #endif
};

PSOutput PSMain(VS2PS input)
{
    PSOutput psOut = (PSOutput)0;
    float4 sampleCol = _ColorTex.Sample(texture_sampler, input.UV);
    psOut.color1 = sampleCol;
// #if defined(USE_MULTI_TARGET) && USE_MULTI_TARGET == 1
//     psOut.color2 = sampleCol;
// #endif
	return psOut;
}