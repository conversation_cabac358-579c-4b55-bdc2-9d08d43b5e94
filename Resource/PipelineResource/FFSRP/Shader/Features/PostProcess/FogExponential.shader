// #pragma enable debug_symbol
#pragma keyword CE_USE_DOUBLE_TRANSFORM
//SamplerState ce_Sampler_Point : register(space0);
//SamplerState ce_Sampler_Clamp : register(space0);
Texture2D<float> src_depth: register(space0);
Texture2D<float4> sceneColor: register(space0);
Texture2D<float4> volumetricFogTex: register(space0);

cbuffer cbPassFpgExp : register(space0)
{
//    matrix ce_InvView;
//    matrix ce_InvProjection;
//    float3 ce_CameraPos;
	bool PLANET_TOP_AT_ORIGIN;
    
//#ifdef CE_USE_DOUBLE_TRANSFORM
//	float3 ce_CameraTilePosition;
//#endif
}

#include "../../ShaderLibrary/Common.hlsl"
#include "../Fog/FogCommon.hlsl"

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
    float depth = src_depth.SampleLevel(ce_Sampler_Clamp, input.UV, 0).r;
	//if(depth != 0)
	//return float4(depth * 100000, 0, 0, 1);
#ifdef CROSS_NGI_GLES3//Not support Gather
    float4 ndc = float4(input.UV.x * 2 - 1, input.UV.y * 2 - 1, depth * 2 - 1, 1);
#else
    float4 ndc = float4(input.UV.x * 2 - 1, (1 - input.UV.y) * 2 - 1, depth, 1);
#endif

    float4 view = mul(ce_InvProjection, ndc);
	view.w = max(view.w, 2e-8);
    view /= view.w;
    float4 world = mul(ce_InvView, view);
    float3 rayW = GetRayFromDepthAndUV(1.0, input.UV);
	
	float3 camPosW = ce_CameraPos.xyz;
	
	float depthInCM = depth == 0 ? 25000000 : length(GetVectorFromDepthAndUV(depth, input.UV));
	float depthLinearKM =  depthInCM * 0.00001;
	
	float4 color = sceneColor.Sample(ce_Sampler_Clamp, input.UV);

	bool IntersectAtmosphere = MoveToTop(camPosW, rayW);

	if(UseWGS84 && !IntersectAtmosphere)
		return color;
	depthInCM = min(depthInCM, 24900000);
	//if(FogVisibility > depthInCM) 
	//	return float4(1, 0,0, 1);
	//else
	//	return float4(0, 1, 0, 1);	

	float4 ret = GetScreenFogResult(useVFog, world.xyz, camPosW, UseWGS84, view.xyz);

	//return float4(depthInCM / 24900000, 0, 0, 1);
	//return float4(ret.a, 0, 0, 1);
	//return float4(ret.xyz, 1);	
	
	if (useVFog)
	{
        float4 vfogColor = volumetricFogTex.Sample(ce_Sampler_Clamp, input.UV);
		ret = float4(ret.xyz * vfogColor.w + vfogColor.xyz, ret.w * vfogColor.w);
	}
	
	ret.xyz = ret.xyz + color.xyz * ret.w; 
	return ret;

	// UE version ----------------
	// DeltaH -= HeightOffset;
	// Dist = clamp(FogNear, FogFar, Dist);
	// float exponentialHeightLineIntegral = CalculateLineIntegralShared(DeltaH) * Dist;
	// float expFogFactor = 1 - saturate(exp2(-exponentialHeightLineIntegral));
	// expFogFactor = lerp(0, MaxDensity, expFogFactor);
	// float3 scatter = ce_LightColors[0].rgb * FogColorFar;
	// return float4(scatter, expFogFactor);


	// Seperate Depth Fog & Height Fog version-------------------------
	// float PlaneDistSqr = (world.x - camPosW.x) * (world.x - camPosW.x) + (world.z - camPosW.z) * (world.z - camPosW.z);
	// float HorizontalDist = sqrt(HorizontalDistSqr);
	// float DistSqr = DeltaH * DeltaH + HorizontalDistSqr;
	// float Dist = sqrt(DistSqr);
	// float PlaneDist = sqrt(HorizontalDistSqr);

	// float HeightFogResult = 0.f;
	// if (EnableHeightFog){
	// 	float FallOff = HeightFallOff;	
	// 	float FogFactor = (1-exp(-FallOff))/FallOff;
	// 	HeightFogResult = exp(-HeightFallOff * 0.01 * (ObjHeight - HeightOffset)) * FogFactor * HeightFogDensity;
	// 	//HeightFogResult = exp(-HeightFallOff* 0.01 * (camPosW.y + world.y + min(0, DeltaH) - HeightOffset)) * FogFactor * HeightFogDensity;
	// }

	// float DepthFogResult = 0.f;
	// if (EnableDepthFog){
	// 	DepthFogResult = lerp(0.f, DepthFogDensity, saturate((PlanDist-FogNear)/(FogFar-FogNear)));
	// 	if (PlanDist<FogNear) DepthFogResult = 0.f;
	// }
	
	// if (EnableDepthFog && EnableHeightFog)
	// {
	// 	fogResult = HeightFogResult * DepthFogResult * GlobalFogDensity * 100.f;
	// }
	// else{
	// 	fogResult = max(HeightFogResult, DepthFogResult) * GlobalFogDensity;
	// }
	// fogResult = clamp(fogResult, 0.f, MaxDensity);

	// float3 fogColor = 0.f.xxx;
	// if (Dist < ColorSplitDistance1){
	// 	fogColor = FogColorNear;
	// }
	// else if (Dist<ColorSplitDistance2){
	// 	fogColor = lerp(FogColorNear, FogColorMid, (Dist-ColorSplitDistance1) / (ColorSplitDistance2 - ColorSplitDistance1));
	// }
	// else if (Dist<FogFar){
	// 	fogColor = lerp(FogColorMid, FogColorFar, (Dist - ColorSplitDistance2) / (FogFar - ColorSplitDistance2));
	// }
	// else {
	// 	fogColor = FogColorFar;
	// }
    //return  saturate(float4(ce_LightColors[0].rgb * fogColor, fogResult));
}