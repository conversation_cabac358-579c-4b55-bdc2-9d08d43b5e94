#include "../../ShaderLibrary/Common.hlsl"
Texture2D<half4> src_texture : register(space0);
SamplerState texture_sampler : register(space0);

#ifdef CROSS_NGI_GLES3
SamplerState ce_Sampler_Point : register(space0);
cbuffer cbPass: register(space0)
{
	float4 src_dimension;
}
#endif

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

//#define MOST_REPRESENT
//#define CHECKER_BOARD

float4 PSMain(VS2PS input) : SV_TARGET
{
	float4 values = (float4)0;

#ifdef CROSS_NGI_GLES3//Not support Gather
	values.x = src_texture.Sample(ce_Sampler_Point, input.UV - 0.5 * src_dimension.zw);
	values.y = src_texture.Sample(ce_Sampler_Point, input.UV + 0.5 * float2(src_dimension.z, -src_dimension.w));
	values.z = src_texture.Sample(ce_Sampler_Point, input.UV + 0.5 * float2(-src_dimension.z, src_dimension.w));
	values.w = src_texture.Sample(ce_Sampler_Point, input.UV + 0.5 * src_dimension.zw);
#else
    values = src_texture.Gather(texture_sampler, input.UV);
#endif


#ifdef MOST_REPRESENT

	float sum = values.r + values.g + values.b + values.a;
	float avg = sum / 4;
	float4 diff = abs(values - avg);
	float maxDiff = max(max(diff.x, diff.y), max(diff.z, diff.w));

	int ej = 0;
	ej += step(maxDiff, diff.g) * 1 * step(ej, 0);
	ej += step(maxDiff, diff.b) * 2 * step(ej, 0);
	ej += step(maxDiff, diff.a) * 3 * step(ej, 0);

	int4 live = int4(1,1,1,1);
	live[ej] = 0;

	sum = dot(values, live);
	avg = sum / 3;
	diff = abs(values - avg);
	diff[ej] = 100;
	float minDiff = min(min(diff.x, diff.y), min(diff.z, diff.w));

	float result = 0;
	result += step(diff.x, minDiff) * values.r * step(result, 0);
	result += step(diff.y, minDiff) * values.g * step(result, 0);
	result += step(diff.z, minDiff) * values.b * step(result, 0);
	result += step(diff.w, minDiff) * values.a * step(result, 0);

	return result;
#endif

#ifdef CHECKER_BOARD
	if((input.Pos.x + input.Pos.y)%2)
		return float4(min(min(values.r, values.g),min(values.b, values.a)),0,0,0);
	else
		return float4(max(max(values.r, values.g),max(values.b, values.a)),0,0,0);
#else
	return float4(min(min(values.r, values.g),min(values.b, values.a)),0,0,0);
#endif

}
