#pragma compute ClusterCulling
#pragma compute DownsampleDepth

struct IndirectDrawCMD
{
	uint indexCount;
	uint instanceCount;
	uint firstIndex;
	int vertexOffset;

	uint firstInstance;
    float apexOffset;
    float2 padding;

    float4 boundingSphere;

    float4 normalCone;
};

struct CompactDrawCMD
{
    uint indexCount;
	uint instanceCount;
	uint firstIndex;
	int vertexOffset;

	uint firstInstance;
};

struct InstanceType
{
    matrix world;
    uint clusterStartID;
    uint clusterCount;
    uint cmdStart;
    float pading;
};

StructuredBuffer<InstanceType> _InstanceBuffer;
StructuredBuffer<IndirectDrawCMD> _SrcDrawBuffer;
StructuredBuffer<uint> _ClusterInstanceMappingBuffer;
RWStructuredBuffer<CompactDrawCMD> _DstDrawBuffer;
Texture2D<float> _DepthSrc;
RWTexture2D<float> _DepthDst;
Texture2D<float> hi_z;
SamplerState ce_Sampler_Clamp;
SamplerState ce_Sampler_Point;

cbuffer _cbCommon
{
    matrix ce_Projection;
    matrix ce_View;
    float4 _Frustums[6];
    float3 ce_CameraPos;
    float _DrawCMD_Count;
    float2 _Camera_Range;
    bool CPUCulling;
    bool normalCulling;
 }


//Michael Mara, Morgan McGuire. 2013
//ViewSpace
bool projectSphere(float3 center, float r, float znear, float P00, float P11, out float4 aabb)
{
	///if (center.z < r + znear)
		//return false;

	float2 cx = -center.xz;
	float2 vx = float2(sqrt(dot(cx, cx) - r * r), r);
	float2 minx = mul(float2x2(vx.x, vx.y, -vx.y, vx.x), cx);
	float2 maxx = mul(float2x2(vx.x, -vx.y, vx.y, vx.x), cx);

	float2 cy = -center.yz;
	float2 vy = float2(sqrt(dot(cy, cy) - r * r), r);
	float2 miny = mul(float2x2(vy.x, vy.y, -vy.y, vy.x), cy);
	float2 maxy = mul(float2x2(vy.x, -vy.y, vy.y, vy.x), cy);

	aabb = float4(minx.x / minx.y * P00, miny.x / miny.y * P11, maxx.x / maxx.y * P00, maxy.x / maxy.y * P11);
	aabb = aabb.xwzy * float4(0.5f, -0.5f, 0.5f, -0.5f) + 0.5f; // clip space -> uv space

	return true;
}



bool FrustumTestVisible(float4 viewSphere)
{
    for(int i = 0; i < 6; i++)
    {
        if(dot(float4(viewSphere.xyz,1),_Frustums[i]) > viewSphere.w)
        {
            return false;
        }
    }

    return true;
}

 
bool FrontFaceTestVisible(float4 normalCone, matrix world,float scale,float3 center,float apexoffset)
{

    normalCone.xyz = normalize(mul(world, float4(normalCone.xyz, 0)).xyz);
    float3 apex = center - normalCone.xyz * apexoffset * scale;
    float3 v = normalize(ce_CameraPos - apex);

    return dot(normalCone.xyz, -v) < normalCone.w || normalCulling == false;
}

float LinearEyeDepth(float depth, float4x4 projMatrix)
{
	return (projMatrix[2][3] / (depth - projMatrix[2][2]));
}

float LinearEyeDepth(float3 positionWS, float4x4 viewMatrix)
{
	float viewSpaceZ = mul(viewMatrix, float4(positionWS, 1.0)).z;
	return abs(viewSpaceZ);
}

float Linear01Depth(float depth, float4x4 projMatrix)
{
	float zNear = -projMatrix[2][3] / projMatrix[2][2];
	float zFar = projMatrix[2][3] / (1.0 - projMatrix[2][2]);

	return (LinearEyeDepth(depth, projMatrix) - zNear) / (zFar - zNear);
}

//ViewSpace
bool OcclussionTestVisible(float4 bounding)
{
    /*    
    if(hi_z.SampleLevel(ce_Sampler_Clamp, float2(0,0), 0) >= 0)
    return true;
    */

/*
    float3 minBox, maxBox;
    GetProjectedBounds(bounding.xyz, bounding.w, minBox, maxBox);
    float3 centerNDC = (minBox + maxBox) * 0.5;
    float scaleNDC = max(maxBox.x - minBox.x, maxBox.y - minBox.y);
    return float2(centerNDC.x, bounding.w);
*/

    float4 aabb;
    projectSphere(bounding.xyz, bounding.w, _Camera_Range.x, ce_Projection._m00, ce_Projection._m11, aabb);    
    float2 depthSize = float2(0,0);
    hi_z.GetDimensions(depthSize.x, depthSize.y);
    float width = (aabb.x - aabb.z) * depthSize.x;
    float height = (aabb.y - aabb.w) * depthSize.y;
    float mipLvl = floor(log2(max(width, height))) + 1;
    float2 uv = (aabb.xy + aabb.zw) * 0.5;

    float occluderD = hi_z.SampleLevel(ce_Sampler_Point, uv, mipLvl).r;
    //float oc_depth = max(max(occluderD.x,occluderD.y), max(occluderD.z,occluderD.w));

    float4 projPos = mul(ce_Projection, float4(bounding.xy, bounding.z - bounding.w, 1));
    projPos /= projPos.w;

    return occluderD > projPos.z;
}


[numthreads(32, 1, 1)]
void ClusterCulling(
	uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID
)
{
    uint TotalCommandId = Gid.x *32  + GI;

    if(TotalCommandId >= _DrawCMD_Count)
        return;

    uint instanceId = _ClusterInstanceMappingBuffer[TotalCommandId];
    uint clusterID  = TotalCommandId - _InstanceBuffer[instanceId].cmdStart + _InstanceBuffer[instanceId].clusterStartID;
    uint targetCommandID = TotalCommandId;


    //if(GTid.x >= _InstanceBuffer[Gid.x].clusterCount) return;

    //uint clusterID = _InstanceBuffer[Gid.x].clusterStartID + GTid.x;
    //uint targetCommandID = _InstanceBuffer[Gid.x].cmdStart + GTid.x;

    _DstDrawBuffer[targetCommandID].indexCount = _SrcDrawBuffer[clusterID].indexCount;
    _DstDrawBuffer[targetCommandID].instanceCount = _SrcDrawBuffer[clusterID].instanceCount;
    _DstDrawBuffer[targetCommandID].firstIndex = _SrcDrawBuffer[clusterID].firstIndex;
    _DstDrawBuffer[targetCommandID].vertexOffset = _SrcDrawBuffer[clusterID].vertexOffset;
    _DstDrawBuffer[targetCommandID].firstInstance = _SrcDrawBuffer[clusterID].firstInstance;

    if(CPUCulling)
       return;

    float3 center = _SrcDrawBuffer[clusterID].boundingSphere.xyz;
    float4 world_ceneter = mul(_InstanceBuffer[instanceId].world, float4(center, 1));
    float scale =  max(max(_InstanceBuffer[instanceId].world._m00, _InstanceBuffer[instanceId].world._m11), _InstanceBuffer[instanceId].world._m22);
    world_ceneter.w = _SrcDrawBuffer[clusterID].boundingSphere.w  * scale;

    float4 viewSphere = mul(ce_View, mul(_InstanceBuffer[instanceId].world, float4(center, 1)));
    viewSphere.w = _SrcDrawBuffer[clusterID].boundingSphere.w  * scale;
    //FrustumTestVisible(world_ceneter) &&
    // && OcclussionTestVisible(viewSphere)
    if( FrustumTestVisible(world_ceneter) && FrontFaceTestVisible(_SrcDrawBuffer[clusterID].normalCone, _InstanceBuffer[instanceId].world,scale,world_ceneter.xyz,_SrcDrawBuffer[clusterID].apexOffset))
    {
        _DstDrawBuffer[targetCommandID].instanceCount = 1;
    }
    else
    {
        _DstDrawBuffer[targetCommandID].instanceCount = 0;
    }
/*
    if(DTid.x >= _DrawCMD_Count) return;

    float4 viewSphere = mul(ce_View, mul(ce_World, float4(_SrcDrawBuffer[DTid.x].boundingSphereX,_SrcDrawBuffer[DTid.x].boundingSphereY, _SrcDrawBuffer[DTid.x].boundingSphereZ, 1)));
    viewSphere.w = _SrcDrawBuffer[DTid.x].boundingSphereR;

    _DstDrawBuffer[DTid.x] = _SrcDrawBuffer[DTid.x];

    if(OcclussionTestVisible(viewSphere) && FrontFaceTestVisible(DTid.x))
    {
        _DstDrawBuffer[DTid.x].instanceCount = 1;
    }
    else
    {
        _DstDrawBuffer[DTid.x].instanceCount = 0;
    }
    */
}

//Dimension = target Size
[numthreads(8, 8, 1)]
void DownsampleDepth(uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID)
{
    uint2 targetSize;
    _DepthDst.GetDimensions(targetSize.x, targetSize.y);
    float2 uv = (float2(DTid.xy) + float2(0.5,0.5)) / targetSize;
    float4 minDepth = _DepthSrc.Gather(ce_Sampler_Clamp, uv);
    if(DTid.x >= targetSize.x || DTid.y >= targetSize.y)
        return;
    _DepthDst[DTid.xy] = max(max(minDepth.x,minDepth.y),max(minDepth.z,minDepth.w));
}