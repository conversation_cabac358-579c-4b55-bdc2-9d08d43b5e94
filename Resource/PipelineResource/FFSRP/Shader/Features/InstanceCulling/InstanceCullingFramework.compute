#pragma compute CullPerPageDrawUnits
#pragma compute AllocateCommandInstanceOutputSpace
#pragma compute OutputCommandInstanceLists

#include "Features/GPUScene/CullingSceneData.hlsl"
#include "ShaderLibrary/Common.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define PERSISTENT_CULLING_GROUP_SIZE 64
#define PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP PERSISTENT_CULLING_GROUP_SIZE * 32
#define INDIRECT_ARGS_NUM_WORDS 5

cbuffer cbPass
{
    matrix ce_Projection;
    matrix ce_View;
    matrix ce_PreViewMatrix;
    matrix ce_PreProjMatrix;
    float3 ce_CameraTilePosition;
    float3 ce_PrevCameraTilePosition;
    float4 ce_ScreenParams;

    uint _CurrSceneFrameCount;
    uint _PayloadCount;
    uint _VisibleObjectCommandBufferMaxNum;
    uint _IndirectArgCount;
}

struct VisibleObjectCommand
{
    uint objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<PrimitiveCullingData> _GPUScenePrimitiveCullingDatas;
StructuredBuffer<ObjectCullingData> _GPUSceneObjectCullingDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

SamplerState ce_Sampler_Point;

#include "HZBCull.hlsl"

void WriteCommand(uint objectIndex, uint indirectArgIndex)
{
    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
    }
}

struct GroupObjectPayloadData
{
    int objectCullingGUID;
    int objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<ObjectPayloadData2> _ObjectPayloadDatas;
RWStructuredBuffer<uint> _ObjectPayloadIndexAndObjectOffsetAndMutex;

#define _ObjectPayloadIndex _ObjectPayloadIndexAndObjectOffsetAndMutex[0]
#define _ObjectPayloadObjectOffset _ObjectPayloadIndexAndObjectOffsetAndMutex[1]
#define _ObjectPayloadMutex _ObjectPayloadIndexAndObjectOffsetAndMutex[2]

groupshared uint _GroupObjectPayloadIndex;
groupshared uint _GroupObjectPayloadIndexOffset;
groupshared uint _GroupObjectPayloadIndexEnd;
groupshared uint _GroupObjectPayloadIndexOffsetEnd;

void LockAcquire()
{
    uint value = 1u;
    while (value)
    {
        InterlockedCompareExchange(_ObjectPayloadMutex, 0u, 1u, value);
    }
}

void LockRelease()
{
    uint value;
    InterlockedExchange(_ObjectPayloadMutex, 0, value);
}

void InstanceCulling(GroupObjectPayloadData payload)
{
    int objectCullingGUID = payload.objectCullingGUID;
    int objectIndex = payload.objectIndex;

    if (objectCullingGUID == -1)
    {
        return;
    }

    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

    uint flag = primitiveCullingData.flag;
    bool isAlwaysVisible = flag & CULLING_FLAG_ALWAYSVISIBLE_BIT_MASK;

    if (isAlwaysVisible)
    {
        WriteCommand(objectIndex, payload.indirectArgIndex);
        InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], 1);
    }
    else
    {
        // FrustumCulling
        matrix currViewProjMatrix = mul(ce_Projection, ce_View);
        matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, currViewProjMatrix, primitiveCullingData.tilePosition, ce_CameraTilePosition);

        FrustumCullData currCull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
        bool isVisible = currCull.isVisible;

        // OcclusionCulling (LastFrameHiZ)
        if (isVisible)
        {
            matrix prevViewProjMatrix = mul(ce_PreProjMatrix, ce_PreViewMatrix);
            matrix prevLocalToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, prevViewProjMatrix, primitiveCullingData.tilePosition, ce_PrevCameraTilePosition);

            FrustumCullData prevCull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, prevLocalToClip, true);

            if ((prevCull.isVisible || prevCull.isFrustumSideCulled) && !prevCull.isCrossesNearPlane)
            {
                ScreenRect prevRect = GetScreenRect(int4(0, 0, ce_ScreenParams.xy), prevCull, 4);
                isVisible = IsVisibleHZB(prevRect, true);
            }
        }

        if (isVisible)
        {
            WriteCommand(objectIndex, payload.indirectArgIndex);
            InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], 1);
        }
    }
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE, 1, 1)]
void CullPerPageDrawUnits(uint dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex)
{
    while(true)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex == 0)
        {
            LockAcquire();

            uint objectPayloadIndexStart = _ObjectPayloadIndex;
            uint objectPayloadObjectOffsetStart = _ObjectPayloadObjectOffset;
            uint objectPayloadIndex = objectPayloadIndexStart;
            uint objectPayloadObjectOffset = objectPayloadObjectOffsetStart;

            uint groupPayloadDataIndex = 0;
            while (groupPayloadDataIndex < PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP)
            {
                if (objectPayloadIndex == _PayloadCount)
                    break;

                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[objectPayloadIndex];

                if (objectPayloadObjectOffset < currentPayload.objectCount)
                {
                    uint count = min(PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP - groupPayloadDataIndex, currentPayload.objectCount - objectPayloadObjectOffset);
                    groupPayloadDataIndex += count;
                    objectPayloadObjectOffset += count;
                }

                if (objectPayloadObjectOffset >= currentPayload.objectCount)
                {
                    objectPayloadIndex++;
                    objectPayloadObjectOffset = 0;
                }
            }

            uint originValue;
            InterlockedExchange(_ObjectPayloadIndex, objectPayloadIndex, originValue);
            InterlockedExchange(_ObjectPayloadObjectOffset, objectPayloadObjectOffset, originValue);

            LockRelease();

            _GroupObjectPayloadIndex = objectPayloadIndexStart;
            _GroupObjectPayloadIndexOffset = objectPayloadObjectOffsetStart;
            _GroupObjectPayloadIndexEnd = objectPayloadIndex;
            _GroupObjectPayloadIndexOffsetEnd = objectPayloadObjectOffset;
        }

        GroupMemoryBarrierWithGroupSync();

        if (_GroupObjectPayloadIndex == _PayloadCount)
            break;

        while (true)
        {            
            GroupMemoryBarrierWithGroupSync();

            uint payloadIndex = _GroupObjectPayloadIndex;
            if (payloadIndex > _GroupObjectPayloadIndexEnd || payloadIndex == _GroupObjectPayloadIndexEnd && _GroupObjectPayloadIndexOffset >= _GroupObjectPayloadIndexOffsetEnd)
                break;

            uint payloadObjectOffset;
            InterlockedAdd(_GroupObjectPayloadIndexOffset, 1u, payloadObjectOffset);

            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

            if (payloadObjectOffset < currentPayload.objectCount)
            {
                GroupObjectPayloadData groupPayload;
                groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + payloadObjectOffset;
                groupPayload.objectIndex = currentPayload.objectIndex + payloadObjectOffset;
                groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
            
                InstanceCulling(groupPayload);
            }

            GroupMemoryBarrierWithGroupSync();

            if (payloadObjectOffset + 1 == currentPayload.objectCount)
            {
                _GroupObjectPayloadIndex++;
                _GroupObjectPayloadIndexOffset = 0;
            }
        }
    }
}

StructuredBuffer<uint> _DrawIndirectArgs;
RWStructuredBuffer<uint> _OutOffsetBufferCount;
RWStructuredBuffer<uint> _OutObjectIndexOffsetBuffer;
RWStructuredBuffer<uint> _OutTempObjectIndexOffsetBuffer;
 
[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void AllocateCommandInstanceOutputSpace(uint indirectArgIndex : SV_DispatchThreadID)
{
    if (indirectArgIndex < _IndirectArgCount)
    {
        uint objectCommandCount = _DrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1];
        uint objectCommandOffset = 0u;
        if (objectCommandCount > 0u)
        {
            InterlockedAdd(_OutOffsetBufferCount[0], objectCommandCount, objectCommandOffset);
        }
        _OutObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
        _OutTempObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
    }
}

StructuredBuffer<VisibleObjectCommand> _VisibleObjectCommands;
StructuredBuffer<uint> _VisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutObjectIndexBuffer;

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void OutputCommandInstanceLists(uint visibleObjectCommandIndex : SV_DispatchThreadID)
{
    uint visibleObjectCommandCount = _VisibleObjectCommandCount[0];

    if (visibleObjectCommandIndex < visibleObjectCommandCount)
    {
        VisibleObjectCommand visibleObjectCommand = _VisibleObjectCommands[visibleObjectCommandIndex];

        uint objectIndexOutputOffset = 0u;
        InterlockedAdd(_OutTempObjectIndexOffsetBuffer[visibleObjectCommand.indirectArgIndex], 1u, objectIndexOutputOffset);

        _OutObjectIndexBuffer[objectIndexOutputOffset] = visibleObjectCommand.objectIndex;
    }
}