#ifndef CLUSTER_DECODE_HLSL
#define CLUSTER_DECODE_HLSL

struct Barycentrics
{
	float3 UVW;
	float3 UVW_dx;
	float3 UVW_dy;
};

/** Calculates perspective correct barycentric coordinates and partial derivatives using screen derivatives. */
Barycentrics CalculateTriangleBarycentrics(float2 PixelClip, float4 PointClip0, float4 PointClip1, float4 PointClip2, float2 ViewInvSize)
{
	Barycentrics Result;

	float3 Pos0 = PointClip0.xyz / PointClip0.w;
	float3 Pos1 = PointClip1.xyz / PointClip1.w;
	float3 Pos2 = PointClip2.xyz / PointClip2.w;

	float3 RcpW = rcp(float3(PointClip0.w, PointClip1.w, PointClip2.w));

	float3 Pos120X = float3(Pos1.x, Pos2.x, Pos0.x);
	float3 Pos120Y = float3(Pos1.y, Pos2.y, Pos0.y);
	float3 Pos201X = float3(Pos2.x, Pos0.x, Pos1.x);
	float3 Pos201Y = float3(Pos2.y, Pos0.y, Pos1.y);

	float3 C_dx = Pos201Y - Pos120Y;
	float3 C_dy = Pos120X - Pos201X;

	float3 C = C_dx * (PixelClip.x - Pos120X) + C_dy * (PixelClip.y - Pos120Y);	// Evaluate the 3 edge functions
	float3 G = C * RcpW;

	float H = dot(C, RcpW);
	float RcpH = rcp(H);

	// UVW = C * RcpW / dot(C, RcpW)
	Result.UVW = G * RcpH;

	// Texture coordinate derivatives:
	// UVW = G / H where G = C * RcpW and H = dot(C, RcpW)
	// UVW' = (G' * H - G * H') / H^2
	// float2 TexCoordDX = UVW_dx.y * TexCoord10 + UVW_dx.z * TexCoord20;
	// float2 TexCoordDY = UVW_dy.y * TexCoord10 + UVW_dy.z * TexCoord20;
	float3 G_dx = C_dx * RcpW;
	float3 G_dy = C_dy * RcpW;

	float H_dx = dot(C_dx, RcpW);
	float H_dy = dot(C_dy, RcpW);

	Result.UVW_dx = (G_dx * H - G * H_dx) * (RcpH * RcpH) * ( 2.0f * ViewInvSize.x);
	Result.UVW_dy = (G_dy * H - G * H_dy) * (RcpH * RcpH) * (-2.0f * ViewInvSize.y);

	return Result;
}

uint BitFieldMaskU32(uint MaskWidth, uint MaskLocation)
{
	MaskWidth &= 31u;
	MaskLocation &= 31u;

	return ((1u << MaskWidth) - 1u) << MaskLocation;
}

uint BitFieldExtractU32(uint Data, uint Size, uint Offset)
{
	Size &= 31u;
	Offset &= 31u;

	if (Size == 0u)
		return 0u;
	else if (Offset + Size < 32u)
		return (Data << (32u - Size - Offset)) >> (32u - Size);
	else
		return Data >> Offset;
}

uint2 CEByteAddressBufferLoad2(CEByteAddressBuffer attributeData, uint index)
{
    uint2 byteData;
#ifdef CROSS_NGI_GLES3
    uint offset = index / 4;
    byteData.x = attributeData[offset].data;
    byteData.y = attributeData[offset + 1].data;
#else
    byteData = attributeData.Load2(index);
#endif
    return byteData;
}

uint ReadBits(CEByteAddressBuffer attributeData, out uint byteOffset, out uint bitOffset, uint numBits)
{
    if (bitOffset > 8)
    {
        byteOffset += bitOffset / 8;
        bitOffset = bitOffset % 8;
    }
    // uint2 byteData = attributeData.Load2(byteOffset);
    uint2 byteData = CEByteAddressBufferLoad2(attributeData, byteOffset);
    uint indexOffset = byteOffset % 4;
    // read bits may crossing two uint, so need to combine bytes
    uint result = byteData.x;
    if (indexOffset > 0)
    {
        result = byteData.x >> 8 * indexOffset | byteData.y << 8 * (4 - indexOffset);
    }
    result >>= bitOffset;
    result &= (1u << numBits) - 1;
    bitOffset += numBits;
    return result;
}

float2 DecodeUV(uint2 packed, UVRange uvRange)
{
	uint2 T = packed + select((packed > uvRange.gapStart), uvRange.gapLength, 0u);

	const float scale = asfloat(asint(1.0f) - (uvRange.precision << 23));
	return float2((int2)T + uvRange.min) * scale;
}

float3 DecodeNormal(uint packed, uint bits)
{
	uint mask = BitFieldMaskU32(bits, 0);
	float2 f = uint2(BitFieldExtractU32(packed, bits, 0), BitFieldExtractU32(packed, bits, bits)) * (2.0f / mask) - 1.0f;
	float3 n = float3(f.xy, 1.0 - abs(f.x) - abs(f.y));
	float t = saturate(-n.z);
    n.xy += select(n.xy >= 0.0, -t, t);
    return normalize(n);
}

float3 DecodePosition(CEByteAddressBuffer attributeData, Cluster cluster, uint vertIndex)
{
    float3 pos;
    return pos;
}

void DecodeVertexAttribute(CEByteAddressBuffer attributeData, uint attributeDataOffset, Cluster cluster, uint vertIndex, out float3 position, out float2 uvs[4], out float3 normal)
{
    // locate vertex attribute offset of this vertex
    uint vertexByteOffset = attributeDataOffset + cluster.dataOffset;
    uint vertexBitOffset = cluster.bitsPerVertex * vertIndex;

    // decode position
    int x, y, z;
    x = ReadBits(attributeData, vertexByteOffset, vertexBitOffset, cluster.posBits.x);
    y = ReadBits(attributeData, vertexByteOffset, vertexBitOffset, cluster.posBits.y);
    z = ReadBits(attributeData, vertexByteOffset, vertexBitOffset, cluster.posBits.z);

    float posScale = 1.0f / exp2((float)cluster.posPrecision);
    position.x = (x + cluster.posStart.x) * posScale;
    position.y = (y + cluster.posStart.y) * posScale;
    position.z = (z + cluster.posStart.z) * posScale;

    // decode uv
    for (int uvIndex = 0; uvIndex < cluster.uvChannelCount; ++uvIndex)
    {
        UVRange rangeInfo = cluster.uvRange[uvIndex];

        int u, v;
        u = ReadBits(attributeData, vertexByteOffset, vertexBitOffset, rangeInfo.bitsU);
        v = ReadBits(attributeData, vertexByteOffset, vertexBitOffset, rangeInfo.bitsV);
        u += u > rangeInfo.gapStart.x ? rangeInfo.gapLength.x : 0;
        v += v > rangeInfo.gapStart.y ? rangeInfo.gapLength.y : 0;

        float uvScale = 1.0f / exp2((float)rangeInfo.precision);
        uvs[uvIndex].x = (u + rangeInfo.min.x) * uvScale;
        uvs[uvIndex].y = (v + rangeInfo.min.y) * uvScale;
    }

    // decode normal
    uint packed = ReadBits(attributeData, vertexByteOffset, vertexBitOffset, 2 * 9);
    normal = DecodeNormal(packed, 9);
}

uint3 DecodeTriangleIndices(CEByteAddressBuffer attributeData, uint attributeDataOffset, Cluster cluster, uint triIndex)
{
    // locate index offset of this cluster(bytes)
    uint indexOffset = attributeDataOffset + cluster.dataOffset + cluster.indexOffset;
    
    // locate triangle offset(bits)
    uint triOffset = 3 * cluster.indexBit * triIndex;

    uint3 indices;
    indices.x = ReadBits(attributeData, indexOffset, triOffset, cluster.indexBit);
    indices.y = ReadBits(attributeData, indexOffset, triOffset, cluster.indexBit);
    indices.z = ReadBits(attributeData, indexOffset, triOffset, cluster.indexBit);

    return indices;
}

void CalculateTangent(CEByteAddressBuffer attributeData, uint attributeDataOffset, Cluster cluster, float4 svPosition, uint3 tri, float3 tilePosition, matrix worldMat, matrix vp, float2 viewInvSize, out float3 tangent, out float3 binormal)
{
    float3 position0, position1, position2, normal0, normal1, normal2;
    float2 uv0[4], uv1[4], uv2[4];

    DecodeVertexAttribute(attributeData, attributeDataOffset, cluster, tri.x, position0, uv0, normal0);
    DecodeVertexAttribute(attributeData, attributeDataOffset, cluster, tri.y, position1, uv1, normal1);
    DecodeVertexAttribute(attributeData, attributeDataOffset, cluster, tri.z, position2, uv2, normal2);

    float4 worldPos0 = mul(worldMat, float4(position0, 1));
    float4 worldPos1 = mul(worldMat, float4(position1, 1));
    float4 worldPos2 = mul(worldMat, float4(position2, 1));
    
#ifdef CE_USE_DOUBLE_TRANSFORM
    worldPos0.xyz = GetLargeCoordinateReltvPosition(worldPos0.xyz, tilePosition, ce_CameraTilePosition);
    worldPos1.xyz = GetLargeCoordinateReltvPosition(worldPos1.xyz, tilePosition, ce_CameraTilePosition);
    worldPos2.xyz = GetLargeCoordinateReltvPosition(worldPos2.xyz, tilePosition, ce_CameraTilePosition);
#endif
    float4 posCilp0 = mul(vp, float4(worldPos0.xyz, 1));
    float4 posCilp1 = mul(vp, float4(worldPos1.xyz, 1));
    float4 posCilp2 = mul(vp, float4(worldPos2.xyz, 1));

    float2 pixelClip = svPosition.xy * viewInvSize * float2(2, -2) + float2(-1, 1);
    
    Barycentrics barycentrics = CalculateTriangleBarycentrics(pixelClip, posCilp0, posCilp1, posCilp2, viewInvSize);

    // calculate tangent from uv0
    float3 PointLocal10 = position1 - position0;
    float3 PointLocal20 = position2 - position0;
    float2 TexCoord10 = uv1[0] - uv0[0];
    float2 TexCoord20 = uv2[0] - uv0[0];
    
	float3 TangentZ = normalize(barycentrics.UVW.x * normal0 + barycentrics.UVW.y * normal1 + barycentrics.UVW.z * normal2);

    bool TangentXValid = abs(TexCoord10.x) + abs(TexCoord20.x) > 1e-6;

    // float3 TangentX; // tangent
    // float3 TangentY; // binormal
    if (TangentXValid)
    {
        float3 Perp2 = cross(TangentZ, PointLocal20);
        float3 Perp1 = cross(PointLocal10, TangentZ);
        float3 TangentU = Perp2 * TexCoord10.x + Perp1 * TexCoord20.x;
        float3 TangentV = Perp2 * TexCoord10.y + Perp1 * TexCoord20.y;

        tangent = normalize(TangentU);
        binormal = cross(TangentZ, tangent);

        float unMirrored = dot(TangentV, binormal) < 0.0f ? -1.0f : 1.0f;
        binormal *= unMirrored;
    }
    else
    {
        const float Sign = TangentZ.z >= 0 ? 1 : -1;
        const float a = -rcp( Sign + TangentZ.z );
        const float b = TangentZ.x * TangentZ.y * a;

        tangent = float3(1 + Sign * a * pow(TangentZ.x, 2), Sign * b, -Sign * TangentZ.x);
        binormal = float3(b,  Sign + a * pow(TangentZ.y, 2), -TangentZ.y);
    }
    tangent *= -1;
    // binormal *= -1;

    // float3x3 TangentToLocal = float3x3(TangentX, TangentY, TangentZ);
}

uint Hash(uint a)
{
   a = (a+0x7ed55d16) + (a<<12);
   a = (a^0xc761c23c) ^ (a>>19);
   a = (a+0x165667b1) + (a<<5);
   a = (a+0xd3a2646c) ^ (a<<9);
   a = (a+0xfd7046c5) + (a<<3);
   a = (a^0xb55a4f09) ^ (a>>16);
   return a;
}

#endif