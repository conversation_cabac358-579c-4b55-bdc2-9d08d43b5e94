#ifndef VERTEX_FOLIAGE_HLSL
#define VERTEX_FOLIAGE_HLSL

#ifdef QTANGENT
#include "QTangents.hlsl"
#endif

SHADER_CONST(bool, USE_FOLIAGE_INSTANCE, false)

#include "VertexFoliageFactory.hlsl"

StructuredBuffer<FoliageCompactSceneData> _FoliageObjectSceneDatas;
StructuredBuffer<FoliageEntityData> _FoliageEntityBuffer;
StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);

#ifndef WORLD_POSITION_OFFSET
// be carefull with potential mv, shadow, prez, all geometric relelated pass;
float3 GetWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn, in FoliageObjectSceneData instance)
{
	return Cur_or_Prev_worldPosition;
}
#endif

#ifndef WORLD_POSITION_OFFSET_PREVIOUS
float3 GetPreviousWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn, in FoliageObjectSceneData instance)
{
	return Cur_or_Prev_worldPosition;
}
#endif

VSOutput VSInputToVSOutput(VSInput input)
{
	VSOutput output = (VSOutput)0;

	uint indexOffset = input.instanceIDOffset + input.instanceID;
	uint instanceID = _ObjectIndexBuffer[indexOffset];
	FoliageObjectSceneData instance = (FoliageObjectSceneData)0;
	if (USE_FOLIAGE_INSTANCE)
	{
		FoliageCompactSceneData compactData = _FoliageObjectSceneDatas[instanceID];
		DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], instance);
	}
	else
	{
		ObjectSceneData objectData = ce_PerObject[instanceID];
		instance.world = objectData.ce_World;
		instance.preWorld = objectData.ce_PreWorld;
		instance.tilePosition = objectData.ce_TilePosition;
		instance.preTilePosition = objectData.ce_PreTilePosition;
		instance.invWorld = objectData.ce_InvWorld;
		instance.invTransposeWorld = objectData.ce_InvTransposeWorld;
	}

	float4 tangent;
	float3 normal;
#ifndef QTANGENT
	tangent = input.tangent;
	normal = input.normal;
#else
	// transform Qtangent to tangents.
	decode_QTangent_to_tangent(input.Qtangent, normal, tangent);
#endif

	matrix worldMat = instance.world;
	float4 positionWS = mul(worldMat, input.position);
	output.normalWS = normalize(mul(instance.invTransposeWorld, float4(normalize(normal + float3(0.0, 0.001, 0.0)), 0)).xyz);
	output.tangentWS = normalize(mul(worldMat, float4(tangent.xyz  + float3(0.0, 0.001, 0.0), 0)).xyz);


#ifdef CE_USE_DOUBLE_TRANSFORM
	positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, instance.tilePosition, ce_CameraTilePosition);
#endif

	output.binormalWS = tangent.w * cross(output.normalWS, output.tangentWS);

#if NUM_MATERIAL_TEXCOORDS
    if (NO_INVERTUV)
    {
        output.uv[0].xy = float2(input.uv.x, input.uv.y);
    }
    else
    {
        output.uv[0].xy = float2(input.uv.x, 1 - input.uv.y);
    }
#endif

#if NUM_MATERIAL_TEXCOORDS > 1
	output.uv[0].zw = input.uv1;
#endif

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	output.uvIdx = input.uvIdx;
#endif

#ifdef USE_VERTEX_COLOR
    output.color = input.color;
#endif

	output.instanceID = instanceID;

#ifdef VEGATATION_AO_UV
    output.uv2 = input.uv2;
#endif

	float3 reservedPositionWS = positionWS;
    output = ExecuteVertexOut(output, input);
	positionWS = float4(GetWorldPositionOffset(reservedPositionWS, output, input, instance), 1.0);

	// motion vector
	VSOutput reservedVSOut = output;
	float4 reservedPositionNDC = mul(ce_Projection, mul(ce_View, float4(reservedPositionWS, 1)));
	reservedVSOut.positionNDC = reservedPositionNDC;
	
	float3 prevPosition = input.position.xyz;
#ifdef USED_WITH_SKELETAL_MESH
	prevPosition = input.prePosition.xyz;
#endif

	float4 prevWorldPosition;

	prevWorldPosition = mul(instance.preWorld, float4(prevPosition, 1.0));
	#ifdef CE_USE_DOUBLE_TRANSFORM
		prevWorldPosition.xyz = GetLargeCoordinateReltvPosition(prevWorldPosition.xyz, instance.preTilePosition, ce_PrevCameraTilePosition);
	#endif

	// must use reserved vsout
	#ifdef WORLD_POSITION_OFFSET_PREVIOUS
		prevWorldPosition.xyz = GetPreviousWorldPositionOffset(prevWorldPosition.xyz, reservedVSOut, input, instance);
	#else
		prevWorldPosition.xyz = GetWorldPositionOffset(prevWorldPosition.xyz, reservedVSOut, input, instance);
	#endif

#if WRITES_VELOCITY_TO_GBUFFER
	output.prePositionNDC = mul(ce_PreProjMatrix, mul(ce_PreViewMatrix, float4(prevWorldPosition.xyz, 1.0)));
	
	matrix ce_ProjectionNoJitter = ce_Projection;
	ce_ProjectionNoJitter[0][2] = ce_ProjMatrixJitter[0];
	ce_ProjectionNoJitter[1][2] = ce_ProjMatrixJitter[1];
	output.nowPositionNDC = mul(ce_ProjectionNoJitter, mul(ce_View, positionWS));
	output.nowPositionNDC.z = distance(positionWS.xyz, prevWorldPosition.xyz);
#endif
	output.positionNDC = mul(ce_Projection, mul(ce_View, positionWS));

	TransferCustomDataVS(input, output);
	
	return output;
}

PSInput VSOutputToPSInput(VSOutput input)
{
	PSInput output = (PSInput)0;
	output.normalWS = input.normalWS;
	output.tangentWS = input.tangentWS.xyz;
	output.binormalWS = input.binormalWS;
#if WRITES_VELOCITY_TO_GBUFFER
	output.prePositionNDC = input.prePositionNDC;
    output.nowPositionNDC = input.nowPositionNDC;
#endif

#ifdef VEGATATION_AO_UV
    output.uv2 = input.uv2;
#endif

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	output.uvIdx = input.uvIdx;
#endif

	output.screenUV = input.positionNDC.xy * ce_ScreenParams.zw;
	output.positionNDC = input.positionNDC;
	float2 screenUV = input.positionNDC.xy * ce_ScreenParams.zw;
    screenUV = float2(screenUV.x, 1 - screenUV.y) * 2 - 1.0;
	float4 posNDC = float4(float3(screenUV, input.positionNDC.z), 1.0);
	float4 posWS = mul(ce_InvViewProjMatrix, posNDC);
	output.positionWS = posWS.xyz / posWS.w;
#if NUM_MATERIAL_TEXCOORDS > 0
	output.uv = input.uv[0].xy;
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
	output.uv1 = input.uv[0].zw;
#endif

#ifdef USE_VERTEX_COLOR
    output.color = input.color;
#endif

	output.instanceID = input.instanceID;

	TransferCustomDataPS(input, output);

	return output;
}

#endif