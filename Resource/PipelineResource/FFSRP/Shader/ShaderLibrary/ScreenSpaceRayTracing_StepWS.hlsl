#ifndef SCREEN_SPACE_RAY_TRACING_STEP_WS_HLSL
#define SCREEN_SPACE_RAY_TRACING_STEP_WS_HLSL

bool ScreenSpaceRayTracing_StepWS(
	float3 positionWS, 
	float3 rayDirWS, 
	float rayLength, 
	float2 positionSS, 
	int frameCountModed, 
	float4 screenParams,
	int steps,
	int minSteps, 
	int maxSteps, 
	float thresholdScale, 
	float linearDepth, 
	float depthBias, 
	float3 normalBias, 
	float globalUnitScale,
	bool calculatesHitCount, 
	float stepMultiplier,
	bool multiLevelTrace,
	bool enableRayDebug,
	inout int hitCount,
	inout float3 hitPointWS)
{
	float dither = CalculateDither(positionSS, frameCountModed);

	float3 startWS = positionWS + rayDirWS * depthBias + normalBias;
	float3 endWS = positionWS + rayDirWS * rayLength;

	float2 startUV = WorldSpacePositionToUV(startWS);
	float2 endUV = WorldSpacePositionToUV(endWS);

	int sampleCount = (int)(length((endUV - startUV)) * steps);
	sampleCount = max(minSteps, sampleCount);

	float3 stepWS = (endWS - startWS) / sampleCount;
	float step = length(stepWS);

	// sampleCount = clamp(sampleCount, minSteps, maxSteps);
	sampleCount = clamp(sampleCount, minSteps, maxSteps);

	float startDepth = SampleLinearDepth(saturate(startUV), screenParams);

	float3 currentWS = startWS + stepWS * dither + stepWS;
	float2 currentUV = WorldSpacePositionToUV(currentWS);
	float currentDepth = WorldPositionToLinearDepth(currentWS);

	hitCount = 0;
	hitPointWS = float3(0.0, 0.0, 0.0);

	bool occluded = false;
	int i = 0;
	[loop]
	while (true)
	{
		if (i >= sampleCount)
		{
			break;
		}

		currentUV = WorldSpacePositionToUV(currentWS);
		currentDepth = WorldPositionToLinearDepth(currentWS);

		if (any(currentUV.xy < 0.0) || any(currentUV.xy > 1.0))
		{
			break;
		}

		if (currentDepth > 0.0)
		{
			float threshold = step * 2 * thresholdScale;
			float sampledDepth = SampleLinearDepth(currentUV.xy, screenParams);
			if (
#if !defined(DISABLE_START_DEPTH_CONDITION_1)
				abs(sampledDepth - currentDepth) < threshold &&
#endif
#if defined(ENABLE_START_DEPTH_CONDITION_2)
			    abs(sampledDepth - startDepth) > 0.05 * globalUnitScale &&
#endif
			    abs(sampledDepth + threshold - currentDepth) < threshold)
			{
				if (occluded == false)
				{
					hitPointWS = currentWS;
				}
				occluded = true;
				if (calculatesHitCount)
				{
					hitCount++;
				}
				else
				{
					break;
				}
			}
		}

		currentWS += stepWS;
		i++;
	}

	return occluded;
}

#endif