#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword DISABLE_SSAO
#pragma keyword CE_USE_DOUBLE_TRANSFORM

#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../Material/Lit/LitUEVariables.hlsl"

#include "../../ShaderLibrary/Vertex.hlsl"

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

#include "../../Material/Lit/LitDataUE.hlsl"
#include "../../Lighting/LightLoop/LightLoop.hlsl"
#include "../../RenderPass/ShaderPassForward.hlsl"