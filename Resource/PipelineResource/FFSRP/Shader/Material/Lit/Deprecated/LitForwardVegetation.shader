#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword USE_VEGETATION_ANIM
#pragma keyword INSTANCING
#pragma keyword QTANGENT
#pragma keyword VEGATATION_AO_UV
#pragma keyword USE_VERTEX_COLOR
#pragma keyword LOD_VISUALIZER

[[vk::constant_id(0)]]
int MATERIAL_TYPE = 0;

[[vk::constant_id(1)]]
bool ALPHA_CLIPPING = false;

[[vk::constant_id(2)]]
bool _NORMALMAP_TANGENT_SPACE = true;

[[vk::constant_id(3)]]
bool DOUBLE_SIDED = false;

[[vk::constant_id(4)]]
bool ENABLE_SSS = false;

[[vk::constant_id(5)]]
bool LIGHE_MAP_ENABLE = false;

[[vk::constant_id(6)]]
bool USE_LM_DIRECTIONALITY = false;

[[vk::constant_id(7)]]
bool ENABLE_LM_AO = false;

[[vk::constant_id(8)]]
bool ANIM_SINGLE_PIVOT_COLOR = false;

[[vk::constant_id(9)]]
bool ANIM_HIERARCHY_PIVOT = false;

[[vk::constant_id(10)]]
bool ANIM_PROCEDURAL_BRANCH = false;

[[vk::constant_id(11)]]
bool DISABLE_LM_GI = false;

[[vk::constant_id(12)]]
bool DEBUG_LM_GI_ONLY = false;

[[vk::constant_id(13)]]
bool DEBUG_LM_COLOR_ONLY = false;

[[vk::constant_id(14)]]
bool ENABLE_LIGHT_PROBE = false;

[[vk::constant_id(15)]]
bool ENABLE_VOLUMETRIC_LIGHT_MAP = false;

[[vk::constant_id(16)]]
bool USE_SKY_OCCLUSION = false;

[[vk::constant_id(17)]]
bool NO_INVERTUV = false;

[[vk::constant_id(18)]]
int ALPHA_CLIPPING_TYPE = 0;

[[vk::constant_id(19)]]
bool DISABLE_BAKE_SHADOW = false;

[[vk::constant_id(20)]]
bool DISABLE_BAKE_NORMAL = false;

[[vk::constant_id(21)]]
bool SHOW_DEBUG_LM_UV = false;

[[vk::constant_id(22)]]
bool SHOW_DEBUG_NORMAL = false;

[[vk::constant_id(23)]]
bool FLIP_NORMAL_Y = true;

[[vk::constant_id(24)]]
bool CHANNEL_PACKING = false;

[[vk::constant_id(25)]]
bool ENABLE_LIGHTMAP_PRT_API = false;

[[vk::constant_id(26)]]
bool ENABLE_LOCAL_LM_PRT = false;

#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../ShaderLibrary/ShaderMaterialVariables.hlsl"

#include "../../ShaderLibrary/QTangents.hlsl"
struct VSInput
{
	float4 position : POSITION;
	float2 uv : TEXCOORD0;
#ifndef SHADOW_PASS
	float4 Qtangent : QUATTAN;
#endif
	//#ifdef USE_VEGETATION_ANIM
#ifdef USE_VERTEX_COLOR
	float4 color : COLOR;
#endif
#ifdef VEGATATION_AO_UV
	float2 uv2 : TEXCOORD1;
#endif
	//#endif
#if defined(INSTANCING) && INSTANCING == 1
	matrix ce_World : INSTANCE_ce_World;
	int4 ce_LightIndex : INSTANCE_ce_LightIndex;
	int ce_LightCount : INSTANCE_ce_LightCount;
#endif
};

struct PSInput
{
#ifdef USE_VERTEX_COLOR
	float4 color : COLOR;
#endif
#ifndef SHADOW_PASS	
	float3 normalWS : NORMAL;
	float3 tangentWS : TANGENT;
	float3 binormalWS : BINORMAL;
#endif //SHADOW_PASS
	float2 uv : TEXCOORD0;
	float2 uv2 : TEXCOORD2;
#ifndef SHADOW_PASS	
	float4 positionNDC : SV_POSITION;
#endif //SHADOW_PASS
	float3 positionWS : TEXCOORD5; // stay out of shadow_pass, incase of hashed alpha

#if defined(INSTANCING) && INSTANCING == 1
	int4 ce_LightIndex : TEXCOORD6;
	int ce_LightCount : TEXCOORD7;
	matrix ce_World : TEXCOORD8;
#endif
};

struct VSOutput
{
#ifdef USE_VERTEX_COLOR
	float4 color : COLOR;
#endif
#ifndef SHADOW_PASS
	float3 normalWS : NORMAL;
	float3 tangentWS : TANGENT;
#endif // SHADOW_PASS
	float2 uv : TEXCOORD0;
#ifdef VEGATATION_AO_UV
	float2 uv2 : TEXCOORD2;
#endif
	float3 positionWS : TEXCOORD5;
	float4 positionNDC : SV_POSITION;
#if defined(INSTANCING) && INSTANCING == 1
	int4 ce_LightIndex : TEXCOORD6;
	int ce_LightCount : TEXCOORD7;
	matrix ce_World : TEXCOORD8;
#endif
};


VSOutput VSInputToVSOutput(VSInput input)
{
	VSOutput output = (VSOutput)0;
#ifndef SHADOW_PASS
	float4 tangent;
	float3 normal;
	// transform Qtangent to tangents.
	decode_QTangent_to_tangent(input.Qtangent, normal, tangent);
#endif

#if defined(INSTANCING) && INSTANCING == 1
	float4 positionWS = mul(input.ce_World, input.position);
#ifndef SHADOW_PASS
	output.normalWS = normalize(mul(transpose(inverse(input.ce_World)), float4(normalize(normal), 0)).xyz);
	output.tangentWS = normalize(mul(input.ce_World, float4(tangent.xyz, 0)).xyz);
#endif //SHADOW_PASS
#else
	float4 positionWS = mul(ce_World, input.position);
#ifndef SHADOW_PASS
	output.normalWS = normalize(mul(ce_InvTransposeWorld, float4(normalize(normal), 0)).xyz);
	output.tangentWS = normalize(mul(ce_World, float4(tangent.xyz, 0)).xyz);
#endif //SHADOW_PASS
#endif

	float3 positionWS3 = positionWS.xyz;

	output.positionNDC = mul(ce_Projection, mul(ce_View, float4(positionWS3, 1.0)));

	output.positionWS = positionWS3;
	if (NO_INVERTUV)
	{
		output.uv = float2(input.uv.x, input.uv.y);
	}
	else
	{
		output.uv = float2(input.uv.x, 1 - input.uv.y);
	}
#ifdef VEGATATION_AO_UV
	output.uv2 = input.uv2;
#endif
#if defined(INSTANCING) && INSTANCING == 1
	output.ce_LightIndex = input.ce_LightIndex;
	output.ce_LightCount = input.ce_LightCount;
	output.ce_World = input.ce_World;
#endif

#ifdef USE_VERTEX_COLOR
	output.color = input.color;
#endif

	return output;
}

PSInput VSOutputToPSInput(VSOutput input)
{
	PSInput output = (PSInput)0;
#ifndef SHADOW_PASS	
	output.normalWS = input.normalWS;
	output.tangentWS = input.tangentWS.xyz;
	output.binormalWS =  cross(input.normalWS, input.tangentWS);
	output.positionNDC = input.positionNDC;
#endif //SHADOW_PASS
	output.positionWS = input.positionWS;
	output.uv = input.uv;
#ifdef VEGATATION_AO_UV
	output.uv2 = input.uv2;
#else
	output.uv2 = input.uv;
#endif
#if defined(INSTANCING) && INSTANCING == 1
	output.ce_LightIndex = input.ce_LightIndex;
	output.ce_LightCount = input.ce_LightCount;
	output.ce_World = input.ce_World;
#endif

#ifdef USE_VERTEX_COLOR
	output.color = input.color;
#endif
	return output;
}

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"
#include"Lit.hlsl"
#include"../MaterialUtilities.hlsl"

SurfaceData GetSurfaceData(float3 V, PSInput psInput, bool isFrontFace)
{
	SurfaceData surfaceData = (SurfaceData)0;

	// BaseColor Alpha
	float4 color = _BaseMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias);
	surfaceData.baseColor = _BaseColor.xyz * color.xyz;
	surfaceData.alpha = _BaseColor.w * color.w;

#ifdef USE_VERTEX_COLOR
	surfaceData.baseColor *= psInput.color.rgb;
#endif

#ifndef SHADOW_PASS
	// Normal
	surfaceData.geomNormalWS = psInput.normalWS;

	float3 normalTS = UnpackNormalUE(_NormalMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias), _NormalScale);
	float3x3 tangentToWorld = float3x3(psInput.tangentWS, psInput.binormalWS, psInput.normalWS);
	if (DOUBLE_SIDED && !isFrontFace)
	{
		normalTS.x *= _DoubleSidedConstants.x;
		normalTS.y *= _DoubleSidedConstants.y;
		normalTS.z *= _DoubleSidedConstants.z;
	}
	surfaceData.normalWS = mul(normalTS, tangentToWorld);


	// Roughness Metallic
	float4 mask = _MaskMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias);

	surfaceData.metallic = mask.r * _Metallic;

	float ambientOcclusion = mask.g;
	surfaceData.ambientOcclusion = ambientOcclusion * _MaskMap.SampleBias(texture_sampler, psInput.uv2, _TextureSampleBias).b;

	float smoothness = mask.a * _Smoothness;
	smoothness = lerp(_SmoothnessRemapMin, _SmoothnessRemapMax, smoothness);
	surfaceData.roughness = PerceptualRoughnessToRoughness(1 - smoothness);

	// Emissive
	surfaceData.emissiveColor = 0;

	// MaterialType
	surfaceData.materialType = MATERIAL_TYPE;

	if (MATERIAL_TYPE == MaterialType_SubsurfaceScattering)
	{
		// Thickness
		surfaceData.thickness = _Thickness * _ThicknessMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias).x;

		// DiffusionProfileID
		surfaceData.diffusionProfileID = _DiffusionProfileID;

		// Subsurface Mask
		if (ENABLE_SSS) {
			surfaceData.subsurfaceMask = _SubsurfaceMask;
		}
		else {
			surfaceData.subsurfaceMask = 0;
		}
	}
#endif // SHADOW_PASS 
	return surfaceData;
}
#include "../../Lighting/LightLoop/LightLoop.hlsl"


VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

struct PSOutput
{
	float3 diffuse : SV_Target0;
	float4 specularRoughness : SV_Target1;
	float2 sss : SV_Target2;
};

PSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace)
{
	PSOutput output = (PSOutput)0;
	PSInput input = VSOutputToPSInput(vsoutput);

	PositionInputs posInput = GetPositionInput(input.positionWS, input.positionNDC);

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	SurfaceData surfaceData = GetSurfaceData(V, input, isFrontFace);

	BSDFData bsdfData = ConvertSurfaceDataToBSDFData(surfaceData);

	PreLightData preLightData = GetPreLightData(V, posInput, bsdfData);

	LightLoopOutput lightLoopOutput = LightLoop(V, posInput, bsdfData, preLightData, input);

	output.diffuse = lightLoopOutput.diffuseLighting;

	output.specularRoughness.rgb = lightLoopOutput.specularLighting * _LightSpecIntensity;

	output.sss = float2(bsdfData.subsurfaceMask, PackByte(bsdfData.diffusionProfileID));

	half3 bakedDiffuse = SampleBakedGI(input.positionWS, surfaceData.normalWS, preLightData.lm_uv, 1, 1, 1);

	if (MATERIAL_TYPE == MaterialType_SubsurfaceScattering)
	{
		bakedDiffuse += SampleBakedGI(input.positionWS, -surfaceData.normalWS, preLightData.lm_uv, 1, 1, 1) * bsdfData.transmittance;
	}
	float2 suv = vsoutput.positionNDC.xy * ce_ScreenParams.zw;

	float indirectDiffuseOcclusion = ao_texture.Sample(texture_sampler, suv).r;
	surfaceData.ambientOcclusion *= indirectDiffuseOcclusion;
	saturate(surfaceData.ambientOcclusion);

	if (DISABLE_LM_GI) {
		bakedDiffuse = half3(0, 0, 0);
	}
	output.diffuse += surfaceData.baseColor * bakedDiffuse;

	output.diffuse *= (1.0 - _AO_Intensity * (1.0 - surfaceData.ambientOcclusion));

#ifdef LOD_VISUALIZER
	output.diffuse *= _LODColorMask.xyz;
#endif

	float3 viewDirW = normalize(ce_CameraPos.xyz - posInput.positionWS.xyz);
	float3 refleDirW = normalize(reflect(-viewDirW, surfaceData.normalWS));
	float2 envBRDFLutColor = _EnvBRDFLutMap.SampleLevel(texture_sampler, float2(saturate(dot(surfaceData.normalWS, viewDirW)), (1.0 - surfaceData.roughness)), 0).rg;
	float3 envBRDF = bsdfData.fresnel0 * envBRDFLutColor.r + envBRDFLutColor.g;
	half3 rpColor = half3(0, 0, 0);
	for (uint i = 0; i < ce_ReflectionProbeCount; i++)
	{
		if (ce_ReflectionProbeIndex[i] == 0)
		{
			float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[0].xyz);
			float maxD = max(ce_RPExtentMipmapCount[0].x, max(ce_RPExtentMipmapCount[0].y, ce_RPExtentMipmapCount[0].z));
			float blendWeight = distance < ce_ReflectionProbePosDistance[0].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[0].w), 0.0, 1.0);
			rpColor += ce_ReflectionProbeMap1.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[0].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
		}
		else if (ce_ReflectionProbeIndex[i] == 1)
		{
			float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[1].xyz);
			float maxD = max(ce_RPExtentMipmapCount[1].x, max(ce_RPExtentMipmapCount[1].y, ce_RPExtentMipmapCount[1].z));
			float blendWeight = distance < ce_ReflectionProbePosDistance[1].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[1].w), 0.0, 1.0);
			rpColor += ce_ReflectionProbeMap2.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[1].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
		}
		else if (ce_ReflectionProbeIndex[i] == 2)
		{
			float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[2].xyz);
			float maxD = max(ce_RPExtentMipmapCount[2].x, max(ce_RPExtentMipmapCount[2].y, ce_RPExtentMipmapCount[2].z));
			float blendWeight = distance < ce_ReflectionProbePosDistance[2].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[2].w), 0.0, 1.0);
			rpColor += ce_ReflectionProbeMap3.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[2].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
		}
		else if (ce_ReflectionProbeIndex[i] == 3)
		{
			float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[3].xyz);
			float maxD = max(ce_RPExtentMipmapCount[3].x, max(ce_RPExtentMipmapCount[3].y, ce_RPExtentMipmapCount[3].z));
			float blendWeight = distance < ce_ReflectionProbePosDistance[3].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[3].w), 0.0, 1.0);
			rpColor += ce_ReflectionProbeMap4.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[3].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
		}
	}

	float vlmAO = SampleBakedSkyVisibility(input.positionWS, refleDirW);
	half3 vlmrpColor = (SampleBakedSpecGI(input.positionWS, refleDirW, _VLMReflectionProbeAOIntensity) * _VLMReflectionProbeIntensity);
	vlmrpColor *= lerp(1.0f, vlmAO, _VLMReflectionProbeAOIntensity);
	output.specularRoughness.rgb += vlmrpColor * envBRDF;

	output.specularRoughness.rgb += vlmAO * _ReflectionProbeIntensity * rpColor * envBRDF;
	output.specularRoughness.a = surfaceData.roughness;

	const int Debug = 0;

	if (Debug == 1)
	{
		float alpha;
		int shadowSplitIndex = EvalShadow_GetSplitIndex(input.positionWS, alpha);
		float3 cascadeColor;
		switch (shadowSplitIndex)
		{
		case -1:
			cascadeColor = float3(1, 0, 0);
			break;
		case 0:
			cascadeColor = float3(1, 0.5, 0);
			break;
		case 1:
			cascadeColor = float3(1, 1, 0);
			break;
		case 2:
			cascadeColor = float3(0.5, 1, 0);
			break;
		case 3:
			cascadeColor = float3(0, 1, 0);
			break;
		case 4:
			cascadeColor = float3(0, 1, 0.5);
			break;
		default:
			cascadeColor = float3(0, 1, 1);
			break;
		}
		output.diffuse = lerp(output.diffuse, cascadeColor, 0.9);
	}

	if (DEBUG_LM_GI_ONLY)
	{
		if (DEBUG_LM_COLOR_ONLY)
		{
			output.diffuse = bakedDiffuse;
		}
		else
		{
			output.diffuse = surfaceData.baseColor * bakedDiffuse;
		}
		output.specularRoughness = float4(0, 0, 0, 0);
		output.sss = float2(0, 0);
		return output;
	}
	else if (SHOW_DEBUG_LM_UV)
	{
		output.diffuse = float3(preLightData.lm_uv, 0);
		output.specularRoughness = float4(0, 0, 0, 0);
		output.sss = float2(0, 0);
		return output;
	}
	else if (SHOW_DEBUG_NORMAL)
	{
		output.diffuse = surfaceData.normalWS * 0.5f + 0.5f;
		output.specularRoughness = float4(0, 0, 0, 0);
		output.sss = float2(0, 0);
		return output;
	}

	return output;
}