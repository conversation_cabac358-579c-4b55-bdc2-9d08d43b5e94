#include "LitCommonStruct.hlsl"
#include "../MaterialUtilities.hlsl"
#include "../NormalBuffer.hlsl"

#define AlphaClipType_Standrad 0
#define AlphaClipType_Adjuct 1
#define AlphaClipType_Hashed 2

#ifndef DEFERRED_SHADING
SurfaceData GetSurfaceData(float3 V, PSInput psInput, bool isFrontFace)
{
	SurfaceData surfaceData = (SurfaceData)0;

	// BaseColor Alpha
	float4 color = _BaseMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias);
	surfaceData.baseColor = _BaseColor.xyz * color.xyz;
	surfaceData.opacityMask = _BaseColor.w * color.w;

#ifdef USE_VERTEX_COLOR
    surfaceData.baseColor *= psInput.color.rgb;
#endif

#ifndef DISABLE_ALPHA_TEST	
	// AlphaClip
	if (ALPHA_CLIPPING)
	{
		// AlphaClip Function
		float alphaclip = 0;
		if(ALPHA_CLIPPING_TYPE == AlphaClipType_Adjuct)
		{
			alphaclip = _AlphaClip - clamp((abs(length(ce_CameraPos.xyz-psInput.positionWS.xyz)) - _MinAdjustClipDistance)/_MaxAdjustClipDistance, 0, _AlphaClip);
		}
		else if(ALPHA_CLIPPING_TYPE == AlphaClipType_Hashed)
		{
			if(abs(length(ce_CameraPos.xyz-psInput.positionWS.xyz)) - _HashedAlphaDistance>0)	/* hashed alpha */
				alphaclip = clamp(hashedAlpha(float3(psInput.uv,1.0)),_AlphaClip, 1);
		}
		else // AlphaClipType_Standrad
		{
			alphaclip = _AlphaClip;
		}

		surfaceData.opacityMaskClip = alphaclip;
	}
#endif
	
#ifndef SHADOW_PASS
	// Normal
    surfaceData.normalTS = UnpackNormalUE(_NormalMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias), _NormalScale);
	
	// Roughness Metallic
	float4 mask = _MaskMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias);
	
	surfaceData.metallic = mask.r * _Metallic;

	float ambientOcclusion = mask.g;
	surfaceData.ambientOcclusion = ambientOcclusion* _MaskMap.SampleBias(texture_sampler, psInput.uv2, _TextureSampleBias).b;

	float smoothness = mask.a * _Smoothness;
    smoothness = lerp(_SmoothnessRemapMin, _SmoothnessRemapMax, smoothness);
	surfaceData.roughness = smoothness;

	// Emissive
	surfaceData.emissiveColor = 0;

	// MaterialType
	surfaceData.materialType = MATERIAL_TYPE;

	if (MATERIAL_TYPE == MaterialType_SubsurfaceScattering)
	{
		// Thickness
		surfaceData.thickness = _Thickness * _ThicknessMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias).x;

		// DiffusionProfileID
		surfaceData.diffusionProfileID = _DiffusionProfileID;

		// Subsurface Mask
		if (ENABLE_SSS) {
			surfaceData.subsurfaceMask = _SubsurfaceMask;
		}
		else {
			surfaceData.subsurfaceMask = 0;
		}
	}

#ifdef	USE_VEGETATION_ANIM
    float4 treeOcclusionInput = float4(psInput.uv2.xy, psInput.uv3.xy);
    surfaceData.treeOcclusion = GetTreeOcclusion(psInput.positionWS, treeOcclusionInput, psInput);
#endif

#endif // SHADOW_PASS 
	return surfaceData;
}
#endif