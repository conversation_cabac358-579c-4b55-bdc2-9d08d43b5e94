#pragma vertex VSMain
#pragma pixel PSMain
 
#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword CE_INSTANCING

#define CE_INSTANCING
#define CE_USE_DOUBLE_TRANSFORM

#define ADDITIONAL_MTL_PARAM float _CustomRoughness; 
#define NUM_MATERIAL_TEXCOORDS 2

#include "SurfaceShaderIncludes.hlsl"

#if defined(CE_INSTANCING)
SurfaceData GetSurfaceData(ObjectSceneData objectData, SurfaceInput input)
#else
SurfaceData GetSurfaceData(SurfaceInput input)
#endif
{
	SurfaceData surfaceData = (SurfaceData)0;
	float4 color = _BaseMap.Sample(texture_sampler, input.uv);
	surfaceData.baseColor = color.rgb;
	surfaceData.opacityMask = color.a;
	surfaceData.opacityMaskClip = 0.3333;
	surfaceData.normalTS = float3(0, 0, 1);
	surfaceData.ambientOcclusion = 1;
	surfaceData.roughness = _CustomRoughness;
	surfaceData.emissiveColor = 0;
	surfaceData.materialType = MATERIAL_TYPE;

	float randomValue = PerInstanceRandom(input.instanceID);

	return surfaceData;
}

#include "../../Material/Lit/Lit.hlsl"
#include "../../RenderPass/ShaderPassGBuffer.hlsl"