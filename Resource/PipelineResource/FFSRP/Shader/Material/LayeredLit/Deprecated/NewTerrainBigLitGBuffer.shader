#pragma vertex VSMain
#pragma pixel PSMain
#pragma keyword QTANGENT

#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../Material/LayeredLit/NewTerrainBigLitVariables.hlsl"

#include "../../ShaderLibrary/Vertex.hlsl"

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

#include "../../Material/LayeredLit/NewTerrainBigLitData.hlsl"
#include "../../RenderPass/ShaderPassGBuffer.hlsl"