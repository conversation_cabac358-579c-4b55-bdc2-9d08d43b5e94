#pragma compute CullPerLightDrawUnitsFast
#pragma compute CountPerLightDrawUnits
#pragma compute DistributeCommandPerLight
#pragma compute CullPerLightDrawUnits
#pragma compute AllocateCommandInstanceOutputSpace
#pragma compute OutputCommandInstanceLists

SamplerState ce_Sampler_Point : register(space0);

#include "../VirtualShadowMap/SceneData.hlsl"
#include "../../../Features/InstanceCulling/HZBCull.hlsl"
#include "../../../ShaderLibrary/Common.hlsl"
#include "LocalLightShadowCommon.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define PERSISTENT_CULLING_GROUP_SIZE 64
#define PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP PERSISTENT_CULLING_GROUP_SIZE * 32
#define INDIRECT_ARGS_NUM_WORDS 5
#define GROUP_SIZE 1024

cbuffer cbPass
{
    uint4 _UIntParams0;
    uint _RangePayloadOffset;
}

#define _LightViewCount                     _UIntParams0.x
#define _ObjectCount                        _UIntParams0.y
#define _PayloadCount                       _UIntParams0.y
#define _VisibleObjectCommandBufferMaxNum   _UIntParams0.z
#define _IndirectArgCount                   _UIntParams0.w
#define _MaxLightPerObject                  _UIntParams0.w   //in CullPerLightDrawUnits kernel

struct VisibleObjectCommand
{
    uint lightIndex;
    uint objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<PrimitiveCullingData> _GPUScenePrimitiveCullingDatas;
StructuredBuffer<ObjectCullingData> _GPUSceneObjectCullingDatas;
StructuredBuffer<LocalLightViewData> _LocalLightViewDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

struct GroupObjectPayloadData
{
    int objectCullingGUID;
    int objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<ObjectPayloadData2> _ObjectPayloadDatas;
RWStructuredBuffer<uint> _ObjectPayloadIndexAndObjectOffsetAndMutex;

#define _ObjectPayloadIndex _ObjectPayloadIndexAndObjectOffsetAndMutex[0]
#define _ObjectPayloadObjectOffset _ObjectPayloadIndexAndObjectOffsetAndMutex[1]
#define _ObjectPayloadMutex _ObjectPayloadIndexAndObjectOffsetAndMutex[2]

groupshared uint _GroupObjectPayloadIndex;
groupshared uint _GroupObjectPayloadIndexOffset;
groupshared uint _GroupObjectPayloadIndexEnd;
groupshared uint _GroupObjectPayloadIndexOffsetEnd;

void LockAcquire()
{
    uint value = 1u;
    while (value)
    {
        InterlockedCompareExchange(_ObjectPayloadMutex, 0u, 1u, value);
    }
}

void LockRelease()
{
    uint value;
    InterlockedExchange(_ObjectPayloadMutex, 0, value);
}

bool WriteCommand(uint viewIndex, uint objectIndex, uint indirectArgIndex)
{
    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;
    visibleObjectCommand.lightIndex = viewIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
        return true;
    }

    _OutVisibleObjectCommandCount[0] = _VisibleObjectCommandBufferMaxNum;
    return false;
}

void InstanceCulling(GroupObjectPayloadData payload)
{
    int objectCullingGUID = payload.objectCullingGUID;
    int objectIndex = payload.objectIndex;

    if (objectCullingGUID == -1)
    {
        return;
    }

    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

    uint visibleCount = 0;
    bool fullCmd = false;
    for (uint viewIdx = 0; viewIdx < _LightViewCount; viewIdx++)
    {
        if (fullCmd) break;
        LocalLightViewData viewData = _LocalLightViewDatas[viewIdx];
        matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, viewData.lightViewProjMatrix, primitiveCullingData.tilePosition, viewData.lightTileAndRange);
        FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
        if (cull.isVisible)
        {
            fullCmd = !WriteCommand(viewIdx, objectIndex, payload.indirectArgIndex);
            if (!fullCmd)
            {
                ++visibleCount;
            }
        }
    }
    InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], visibleCount); 
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE, 1, 1)]
void CullPerLightDrawUnitsFast(uint dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex)
{
    // Step0
    const uint threadCount = 32 * PERSISTENT_CULLING_GROUP_SIZE;
    uint payloadIndex = dispatchThreadId;
    while (payloadIndex < _RangePayloadOffset)
    {
        ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

        GroupObjectPayloadData groupPayload;
        groupPayload.objectCullingGUID = currentPayload.objectCullingGUID;
        groupPayload.objectIndex = currentPayload.objectIndex;
        groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
    
        InstanceCulling(groupPayload);
    
        payloadIndex += threadCount;
    }

    // Step1
    while (true)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex == 0)
        {
            LockAcquire();

            uint objectPayloadIndexStart = _ObjectPayloadIndex;
            uint objectPayloadObjectOffsetStart = _ObjectPayloadObjectOffset;
            uint objectPayloadIndex = objectPayloadIndexStart;
            uint objectPayloadObjectOffset = objectPayloadObjectOffsetStart;

            uint groupPayloadDataIndex = 0;
            while (groupPayloadDataIndex < PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP)
            {
                if (objectPayloadIndex >= _PayloadCount)
                    break;

                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[objectPayloadIndex];

                if (objectPayloadObjectOffset < currentPayload.objectCount)
                {
                    uint count = min(PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP - groupPayloadDataIndex, currentPayload.objectCount - objectPayloadObjectOffset);
                    groupPayloadDataIndex += count;
                    objectPayloadObjectOffset += count;
                }

                if (objectPayloadObjectOffset >= currentPayload.objectCount)
                {
                    objectPayloadIndex++;
                    objectPayloadObjectOffset = 0;
                }
            }

            uint originValue;
            InterlockedExchange(_ObjectPayloadIndex, objectPayloadIndex, originValue);
            InterlockedExchange(_ObjectPayloadObjectOffset, objectPayloadObjectOffset, originValue);

            LockRelease();

            _GroupObjectPayloadIndex = objectPayloadIndexStart;
            _GroupObjectPayloadIndexOffset = objectPayloadObjectOffsetStart;
            _GroupObjectPayloadIndexEnd = objectPayloadIndex;
            _GroupObjectPayloadIndexOffsetEnd = objectPayloadObjectOffset;
        }

        GroupMemoryBarrierWithGroupSync();

        if (_GroupObjectPayloadIndex >= _PayloadCount)
            break;

        while (true)
        {
            GroupMemoryBarrierWithGroupSync();

            uint payloadIndex = _GroupObjectPayloadIndex;
            if (payloadIndex > _GroupObjectPayloadIndexEnd || payloadIndex == _GroupObjectPayloadIndexEnd && _GroupObjectPayloadIndexOffset >= _GroupObjectPayloadIndexOffsetEnd)
                break;

            uint payloadObjectOffset;
            InterlockedAdd(_GroupObjectPayloadIndexOffset, 1u, payloadObjectOffset);

            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

            if (payloadObjectOffset < currentPayload.objectCount)
            {
                GroupObjectPayloadData groupPayload;
                groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + payloadObjectOffset;
                groupPayload.objectIndex = currentPayload.objectIndex + payloadObjectOffset;
                groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
            
                InstanceCulling(groupPayload);
            }

            GroupMemoryBarrierWithGroupSync();

            if (payloadObjectOffset + 1 == currentPayload.objectCount)
            {
                _GroupObjectPayloadIndex++;
                _GroupObjectPayloadIndexOffset = 0;
            }
        }
    }
}

static const uint TraversePurpose_CountDrawUnitsPerLight = 0;
static const uint TraversePurpose_CullDrawUnitsPerLight = 1;

RWStructuredBuffer<int> _OutDrawUnitsCountPerLight;
StructuredBuffer<int> _InDrawUnitsCountPerLight;

RWStructuredBuffer<int> _UseableDrawUnitsPerLight;

void CountDrawUnit(GroupObjectPayloadData payload)
{
    int objectCullingGUID = payload.objectCullingGUID;

    if (objectCullingGUID == -1)
    {
        return;
    }

    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

    for (uint viewIdx = 0; viewIdx < _LightViewCount; viewIdx++)
    {
        LocalLightViewData viewData = _LocalLightViewDatas[viewIdx];
        matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, viewData.lightViewProjMatrix, primitiveCullingData.tilePosition, viewData.lightTileAndRange);
        FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
        if (cull.isVisible)
        {
            InterlockedAdd(_OutDrawUnitsCountPerLight[viewIdx], 1);
        }
    }
}

void CullDrawUnit(GroupObjectPayloadData payload)
{
    int objectCullingGUID = payload.objectCullingGUID;
    int objectIndex = payload.objectIndex;

    if (objectCullingGUID == -1)
    {
        return;
    }

    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

    uint visibleCount = 0;
    for (uint viewIdx = 0; viewIdx < _LightViewCount; viewIdx++)
    {
        int useableDrawUnitsCount = 0;
        InterlockedAdd(_UseableDrawUnitsPerLight[viewIdx], -1, useableDrawUnitsCount);
        if (useableDrawUnitsCount <= 0)
        {
            continue;
        }

        uint visibleCount = 0;
        bool fullCmd = false;
        LocalLightViewData viewData = _LocalLightViewDatas[viewIdx];
        matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, viewData.lightViewProjMatrix, primitiveCullingData.tilePosition, viewData.lightTileAndRange);
        FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
        if (cull.isVisible)
        {
            fullCmd = !WriteCommand(viewIdx, objectIndex, payload.indirectArgIndex);
            if (!fullCmd)
            {
                ++visibleCount;
            }
        }
    }
    InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], visibleCount);
}

template<uint TraversePurpose>
void HandleObjectPayloadData(GroupObjectPayloadData groupPayload)
{
    if (TraversePurpose == TraversePurpose_CountDrawUnitsPerLight)
    {
        CountDrawUnit(groupPayload);
    }
    else if (TraversePurpose == TraversePurpose_CullDrawUnitsPerLight)
    {
        CullDrawUnit(groupPayload);
    }
}

template<uint TraversePurpose>
void TraverseDrawUnits(uint dispatchThreadId, uint groupIndex)
{
    // Step0
    const uint threadCount = 32 * PERSISTENT_CULLING_GROUP_SIZE;
    uint payloadIndex = dispatchThreadId;
    while (payloadIndex < _RangePayloadOffset)
    {
        ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

        GroupObjectPayloadData groupPayload;
        groupPayload.objectCullingGUID = currentPayload.objectCullingGUID;
        groupPayload.objectIndex = currentPayload.objectIndex;
        groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;

        HandleObjectPayloadData<TraversePurpose>(groupPayload);
    
        payloadIndex += threadCount;
    }

    // Step1
    while (true)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex == 0)
        {
            LockAcquire();

            uint objectPayloadIndexStart = _ObjectPayloadIndex;
            uint objectPayloadObjectOffsetStart = _ObjectPayloadObjectOffset;
            uint objectPayloadIndex = objectPayloadIndexStart;
            uint objectPayloadObjectOffset = objectPayloadObjectOffsetStart;

            uint groupPayloadDataIndex = 0;
            while (groupPayloadDataIndex < PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP)
            {
                if (objectPayloadIndex >= _PayloadCount)
                    break;

                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[objectPayloadIndex];

                if (objectPayloadObjectOffset < currentPayload.objectCount)
                {
                    uint count = min(PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP - groupPayloadDataIndex, currentPayload.objectCount - objectPayloadObjectOffset);
                    groupPayloadDataIndex += count;
                    objectPayloadObjectOffset += count;
                }

                if (objectPayloadObjectOffset >= currentPayload.objectCount)
                {
                    objectPayloadIndex++;
                    objectPayloadObjectOffset = 0;
                }
            }

            uint originValue;
            InterlockedExchange(_ObjectPayloadIndex, objectPayloadIndex, originValue);
            InterlockedExchange(_ObjectPayloadObjectOffset, objectPayloadObjectOffset, originValue);

            LockRelease();

            _GroupObjectPayloadIndex = objectPayloadIndexStart;
            _GroupObjectPayloadIndexOffset = objectPayloadObjectOffsetStart;
            _GroupObjectPayloadIndexEnd = objectPayloadIndex;
            _GroupObjectPayloadIndexOffsetEnd = objectPayloadObjectOffset;
        }

        GroupMemoryBarrierWithGroupSync();

        if (_GroupObjectPayloadIndex >= _PayloadCount)
            break;

        while (true)
        {
            GroupMemoryBarrierWithGroupSync();

            uint payloadIndex = _GroupObjectPayloadIndex;
            if (payloadIndex > _GroupObjectPayloadIndexEnd || payloadIndex == _GroupObjectPayloadIndexEnd && _GroupObjectPayloadIndexOffset >= _GroupObjectPayloadIndexOffsetEnd)
                break;

            uint payloadObjectOffset;
            InterlockedAdd(_GroupObjectPayloadIndexOffset, 1u, payloadObjectOffset);

            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

            if (payloadObjectOffset < currentPayload.objectCount)
            {
                GroupObjectPayloadData groupPayload;
                groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + payloadObjectOffset;
                groupPayload.objectIndex = currentPayload.objectIndex + payloadObjectOffset;
                groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
            
                HandleObjectPayloadData<TraversePurpose>(groupPayload);
            }

            GroupMemoryBarrierWithGroupSync();

            if (payloadObjectOffset + 1 == currentPayload.objectCount)
            {
                _GroupObjectPayloadIndex++;
                _GroupObjectPayloadIndexOffset = 0;
            }
        }
    }
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE, 1, 1)]
void CountPerLightDrawUnits(uint dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex)
{
    TraverseDrawUnits<TraversePurpose_CountDrawUnitsPerLight>(dispatchThreadId, groupIndex);
}

[numthreads(1, 1, 1)]
void DistributeCommandPerLight()
{
    int useableDrawUnitsCount = _VisibleObjectCommandBufferMaxNum;
    for (uint lightIdx = 0; lightIdx < _LightViewCount; ++lightIdx)
    {
        int drawUnitsCount = _InDrawUnitsCountPerLight[lightIdx];
        if (drawUnitsCount <= useableDrawUnitsCount)
        {
            _UseableDrawUnitsPerLight[lightIdx] = drawUnitsCount;
            useableDrawUnitsCount -= drawUnitsCount;
        }
        else
        {
            _UseableDrawUnitsPerLight[lightIdx] = useableDrawUnitsCount;
            useableDrawUnitsCount = 0;
        }
    }
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE, 1, 1)]
void CullPerLightDrawUnits(uint dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex)
{
    TraverseDrawUnits<TraversePurpose_CullDrawUnitsPerLight>(dispatchThreadId, groupIndex);
}

StructuredBuffer<uint> _DrawIndirectArgs;
RWStructuredBuffer<uint> _OutOffsetBufferCount;
RWStructuredBuffer<uint> _OutObjectIndexOffsetBuffer;
RWStructuredBuffer<uint> _OutTempObjectIndexOffsetBuffer;
 
[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void AllocateCommandInstanceOutputSpace(uint indirectArgIndex : SV_DispatchThreadID)
{
    if (indirectArgIndex < _IndirectArgCount)
    {
        uint objectCommandCount = _DrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1];
        uint objectCommandOffset = 0u;
        if (objectCommandCount > 0u)
        {
            InterlockedAdd(_OutOffsetBufferCount[0], objectCommandCount, objectCommandOffset);
        }
        _OutObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
        _OutTempObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
    }
}

StructuredBuffer<VisibleObjectCommand> _VisibleObjectCommands;
StructuredBuffer<uint> _VisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutObjectIndexBuffer;
RWStructuredBuffer<uint> _OutLightInfoBuffer;

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void OutputCommandInstanceLists(uint visibleObjectCommandIndex : SV_DispatchThreadID)
{
    uint visibleObjectCommandCount = _VisibleObjectCommandCount[0];

    if (visibleObjectCommandIndex < visibleObjectCommandCount)
    {
        VisibleObjectCommand visibleObjectCommand = _VisibleObjectCommands[visibleObjectCommandIndex];

        uint objectIndexOutputOffset = 0u;
        InterlockedAdd(_OutTempObjectIndexOffsetBuffer[visibleObjectCommand.indirectArgIndex], 1u, objectIndexOutputOffset);

        _OutObjectIndexBuffer[objectIndexOutputOffset] = visibleObjectCommand.objectIndex;
        _OutLightInfoBuffer[objectIndexOutputOffset] = visibleObjectCommand.lightIndex;
    }
}