#pragma compute CalculateMassiveLightsClusters
#pragma compute CalculateMassiveLightsShadow
//#pragma enable debug_symbol

#include "../../../ShaderLibrary/Common.hlsl"
#include "../../../ShaderLibrary/Random.hlsl"
#include "../../../ShaderLibrary/LightingCommon.hlsl"
#include "../../../Lighting/LightDefinition.hlsl"
#include "../../../Material/NormalBuffer.hlsl"

#define MASSIVE_SHADOW_TILE_SIZE 8

cbuffer MassiveLightsShadowConstants : register(space0)
{
	uint _LightCount;
	uint _TotalClusterCount;
	
	float4 ce_ScreenParams;
	float4x4 ce_View;
	float4x4 ce_Projection;
	float4x4 ce_InvView;
	float4x4 ce_InvViewProjMatrix;
	float3 ce_CameraTilePosition;
};

Texture2D<float4> _GBuffer1Map : register(space0);//Normal + roughness
Texture2D<float> _DepthMap : register(space0);

RWStructuredBuffer<uint> _MassiveLightsShadowRW : register(space0);

SamplerState ce_Sampler_Point : register(space0);//ce_Sampler_Point

#include "MassiveLightsShadowCommon.hlsl"

// Clusterize massive lights

RWStructuredBuffer<MassiveLightCluster> _MassiveLightsClustersRW : register(space0);

void Cluster_AddLight(inout MassiveLightCluster cluster, inout MassiveLightClusterScores clusterScores, uint lightInstanceID, int lightScore)
{
	uint lightCount = cluster.LightCount;
	if (lightCount < MAX_LIGHTS_PER_CLUSTER)
	{
		cluster.LightCount++;
		cluster.LightInstanceIDs[lightCount] = lightInstanceID;
		clusterScores.LightInstanceScores[lightCount] = lightScore;
	}
	else
	{
		int minScoreIndex = 0;
        int minScore = 1000000000;
        for (int i = 0; i < MAX_LIGHTS_PER_CLUSTER; i++)
        {
            int score = clusterScores.LightInstanceScores[i];
            if (score < minScore)
            {
                minScore = score;
                minScoreIndex = i;
            }
        }
        if (lightScore > minScore)
        {
            cluster.LightInstanceIDs[minScoreIndex] = lightInstanceID;
            clusterScores.LightInstanceScores[minScoreIndex] = lightScore;
        }
	}
}

int Cluster_CalculateLightScore(float X, float Z, InstanceLightType light)
{
	float halfClusterSize = 0.5 * _ClusterSize;
	float clusterCenterX = X + halfClusterSize;
	float clusterCenterZ = Z + halfClusterSize;

    float lightX = light.lightDirPos.x;
    float lightZ = light.lightDirPos.z;

    float differenceX = clusterCenterX - lightX;
    float differenceZ = clusterCenterZ - lightZ;

    float distanceSquared = differenceX * differenceX + differenceZ * differenceZ;
    
    float radius = GetLightRange(light);
    float radius1 = radius + 1.5 * _ClusterSize;
    float radius1Squared = radius1 * radius1;

    if (distanceSquared > radius1Squared)
    {
    	return 0.0;
    }

    int score = 0.01 + (int)(1000000000.0 / (1 + distanceSquared));
    return score;
}

[numthreads(64, 1, 1)]
void CalculateMassiveLightsClusters(uint id : SV_DispatchThreadID)
{
	if (id < _TotalClusterCount)
	{
		int clusterIndexX = id / _ClusterCount;
		int clusterIndexZ = id % _ClusterCount;
		
		float clusterPositionX = 0.0;
		float clusterPositionZ = 0.0;
		ToClusterPosition(clusterIndexX, clusterIndexZ, clusterPositionX, clusterPositionZ);
		
		float randomR = clusterIndexX * 32 / 255.0f;
		float randomG = clusterIndexZ * 32 / 255.0f;
		float randomB = 0.0f;

		MassiveLightCluster cluster;
		cluster.DebugColor = float4(randomR, randomG, randomB, 1.0);
		cluster.LightCount = 0;

		MassiveLightClusterScores clusterScores;

		for (int i = 0; i < MAX_LIGHTS_PER_CLUSTER; i++)
		{
			cluster.LightInstanceIDs[i] = 0;
			clusterScores.LightInstanceScores[i] = 0.0;
		}

		for (uint lightInstanceID = 0; lightInstanceID < _LightCount; lightInstanceID++)
		{
			InstanceLightType light = _LightInstanceData[lightInstanceID];
			int score = Cluster_CalculateLightScore(clusterPositionX, clusterPositionZ, light);
			if (score > 0)
			{
				Cluster_AddLight(cluster, clusterScores, lightInstanceID, score);
			}
		}

		_MassiveLightsClustersRW[id] = cluster;
	}
}

// Massive lights shadow

float3 EstimatePointLightDiffuse(float3 positionWS, InstanceLightType lightData)
{
	PunctualLightData light = (PunctualLightData)0;
	light.positionWS = lightData.lightDirPos.xyz;
	light.tilePosition = lightData.lightTilePos.xyz;
	light.color = lightData.lightColor.rgb;
	light.attenuation = lightData.lightAttenuation;

	float3 lightPos = light.positionWS.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
    lightPos = GetLargeCoordinateReltvPosition(lightPos, light.tilePosition.xyz, ce_CameraTilePosition);
#endif

	float3 L;
    float Falloff;
    LocalRadialLight pointLight;
    pointLight.LightPosition = lightPos;
    //pointLight.Radius = GetLightRange(lightData);
    //pointLight.InvRadius = 1.0 / pointLight.Radius;

    //float atten =  GetLocalPointLightAttenuation(positionWS, pointLight, L, Falloff);
    float atten = 1;
    float spot_atten = SpotAttenuation(L, float3(1,0,0), float2(-2, 1.0));
	float3 lightColor = light.color * atten * spot_atten;
	lightColor *= Falloff;

	return lightColor;
}

[numthreads(MASSIVE_SHADOW_TILE_SIZE, MASSIVE_SHADOW_TILE_SIZE, 1)]
void CalculateMassiveLightsShadow(
	uint2 groupThreadId : SV_GroupThreadID,
	uint2 groupId : SV_GroupID)
{
	uint2 pixelCoord = groupId * MASSIVE_SHADOW_TILE_SIZE + groupThreadId;
	if(pixelCoord.x >= ce_ScreenParams.x  || pixelCoord.y >= ce_ScreenParams.y) 
	{
		return;
	}

	float depth = LoadCameraDepth(pixelCoord);

	bool isNotSky = IsDeviceDepthNotSky(depth);

	float2 screenUV = pixelCoord * ce_ScreenParams.zw;

	float4 gBuffer1 = _GBuffer1Map.SampleLevel(ce_Sampler_Point, screenUV, 0);
    float3 normalData;
    DecodeFromNormalBuffer(gBuffer1, normalData);
    float3 normalWS = normalize(normalData);

	PositionInputs posInput = GetPositionInput(screenUV, depth, ce_InvViewProjMatrix, ce_View);
	float2 positionSS = posInput.uv * ce_ScreenParams.xy;

	uint shadowBits = 0;

	float3 positionWS = posInput.positionWS;
	float clusterPositionX = positionWS.x;
	float clusterPositionZ = positionWS.z;

	int clusterIndexX = 0;
	int clusterIndexZ = 0;
	ToClusterIndex(clusterPositionX, clusterPositionZ, clusterIndexX, clusterIndexZ);

	if (clusterIndexX >= 0 && clusterIndexX < _ClusterCount && clusterIndexZ >= 0 && clusterIndexZ < _ClusterCount)
	{
		uint clusterIndex = clusterIndexX * _ClusterCount + clusterIndexZ;
		MassiveLightCluster cluster = _MassiveLightsClusters[clusterIndex];

		int clusterLightCount = cluster.LightCount;

		for (int i = 0; i < clusterLightCount; i++)
		{
			uint lightInstanceID = cluster.LightInstanceIDs[i];
			InstanceLightType light = _LightInstanceData[lightInstanceID];
			float3 lightPositionWS = GetLightPosition(light);
			[branch]
			if (IsPointInLightRange(positionWS, lightPositionWS, light))
			{
				[branch]
				if (IsPointFaceToLight(positionWS, lightPositionWS, normalWS, light) || !MASSIVE_LIGHTS_SHADOW_CULL_BACK)
				{
					//float3 diffuse = EstimatePointLightDiffuse(positionWS, light);
					//float lunminance = EstimateLuminanceOfDiffuse(diffuse);
					//if (lunminance > 0.01)
					{
						bool calculatesHitCount = false;
						int hitCount = 0;
						bool hit = false;
						if (isNotSky)
						{
							hit = ScreenSpaceRayTracing_Simple(posInput, light.lightDirPos.xyz, normalWS, positionSS, calculatesHitCount, hitCount);
						}
						if (hit)
						{
							shadowBits |= (1U << i);
						}
					}
				}
				else
				{
					shadowBits |= (1U << i);
				}
			}
		}
	}

	uint massiveLightsShadowIndex = pixelCoord.y * ce_ScreenParams.x + pixelCoord.x;
	_MassiveLightsShadowRW[massiveLightsShadowIndex] = shadowBits;
}