#ifndef SHADOW_HLSL
#define SHADOW_HLSL

#include "ShadowDefines.hlsl"

//-----------------------------------------------------------------------------
// ShadowResource
//-----------------------------------------------------------------------------

SHADER_CONST(bool, ENABLE_VSM, false);
SHADER_CONST(bool, USE_CACHED_LOCAL_LIGHT_SHADOW, false);

cbuffer _cbShadow
{
    // for CSM and LocalLight
    float4 _DirShadowMapSize;
    float4 _ReprojectionDirShadowMapSize;
    float4 _PointShadowCubemapSize;
    float4 _SpotShadowMapSize;
    float _ShadowCascadeCount;
    uint4 _CascadeControl;
    uint4 _CascadeUseReprojection;

    // for VSM
    uint _SMRTRayCount;
    uint _SMRTSamplesPerRay;
    uint _FrameCount;
}

Texture2DArray<float> _DirShadowMaps;
Texture2DArray<float> _DirShadowMapsReprojection;
Texture2DArray<float> _SpotShadowMaps;
TextureCubeArray<float> _PointShadowCubemaps;
Texture2DArray<float> _LocalLightMultiTargetShadowMap;

struct SubMapInfo
{
    int posX;
    int posY;
    uint width;
    uint height;
    float4 uvRange;
    uint arrayIdx;
    uint3 alignBits;
};

StructuredBuffer<SubMapInfo> _SpotLightShadowRanges;
StructuredBuffer<SubMapInfo> _PointLightShadowRanges;

cbuffer _cbLocalLightShadowParam
{
    float4 _LocalLightShadowMapSize;
}

//-----------------------------------------------------------------------------
// SamplingFunction
//-----------------------------------------------------------------------------
#include "ShadowSampling.hlsl"
#include "VirtualShadowMap/VirtualShadowMapProjection.hlsl"

#define HARD_SHADOW 0
#define SOFT_SHADOW_PCF_MANUAL_1X1 1
#define SOFT_SHADOW_PCF_MANUAL_3X3 2
#define SOFT_SHADOW_PCF_MANUAL_5X5 3
#define SOFT_SHADOW_PCSS 8

int EvalShadow_GetSplitIndex(float depth, DirectionalShadowData dirShadowData, out float alpha)
{
    uint i = 0;
    for(; i < _ShadowCascadeCount ; i++)
    {
        const float splitFar = dirShadowData.splitFar[i];
        const float fadePlaneOffset = dirShadowData.fadePlaneOffset[i];
        const float fadePlaneLength = max(splitFar - fadePlaneOffset, 0.001);

        if (depth < splitFar)
        {
            alpha = saturate((depth - fadePlaneOffset) / fadePlaneLength);
            return i;
        }
    }

    alpha = 0.f;
    return -1;
}

float3 EvalShadow_GetTexcoordsAtlas(uint shadowMatrixIndex, float3 shadowTilePosition, float3 cameraTilePosition, float3 positionWS, bool isPerspProj, out float3 positionNDC)
{
    positionWS = GetLargeCoordinateModelPosition(positionWS, shadowTilePosition, cameraTilePosition);

	float4 positionCS = mul(_ShadowMatrices[shadowMatrixIndex], float4(positionWS, 1.0));
	positionNDC = (isPerspProj && positionCS.w != 0) ? (positionCS.xyz / positionCS.w) : positionCS.xyz;

	float3 positionTC = float3(saturate(positionNDC.xy * 0.5 + 0.5), positionNDC.z);
#ifdef CROSS_NGI_GLES3
	positionTC.z = (positionTC.z + 1) * 0.5;
#else
	positionTC.y = (1 - positionTC.y);
#endif
	return positionTC;
}

float3 EvalShadow_GetTexcoordsAtlas(uint shadowMatrixIndex, float3 shadowTilePosition, float3 cameraTilePosition, float3 positionWS, bool isPerspProj)
{
	float3 positionNDC;
	return EvalShadow_GetTexcoordsAtlas(shadowMatrixIndex, shadowTilePosition, cameraTilePosition, positionWS, isPerspProj, positionNDC);
}

// for VolumetricFog and Atmosphere
float GetDirectionalShadowAttenuation_Direct(float3 positionWS, int shadowDataIndex, int virtualShadowMapId)
{
    if (ENABLE_VSM)
    {
        return SampleVirtualShadowMap(virtualShadowMapId, positionWS);
    }
    else
    {
        // CascadeShadowMap
        ShadowData shadowData = _ShadowDatas[shadowDataIndex];
        DirectionalShadowData dirShadowData = _DirectionalShadowDatas[shadowData.directionalShadowDataIndex];

        float3 positionCS = mul(ce_View, float4(positionWS, 1.0f)).xyz;

        float alpha;
        int shadowSplitIndex = EvalShadow_GetSplitIndex(positionCS.z, dirShadowData, alpha);
        float shadow = 1.0;
        uint cascadeShadowCount = (uint)_ShadowCascadeCount;

        if (shadowSplitIndex >= 0)
        {
            uint shadowMapIndex = shadowData.shadowMapIndex + shadowSplitIndex;
            uint shadowMatrixIndex = shadowData.matrixIndex + shadowSplitIndex;

            // Get shadowmap texcoords
            float3 positionTC = EvalShadow_GetTexcoordsAtlas(shadowMatrixIndex, ce_CameraTilePosition, ce_CameraTilePosition, positionWS, false);

            // Evalute the first cascade
            shadow = SampleShadowDirect(_DirShadowMaps, shadowMapIndex, ce_Sampler_Point, positionTC.xy, positionTC.z);
        }

        shadow = 1.0 - (1.0 - shadow) * shadowData.shadowAmount;
        return shadow;
    }
}

float EvalShadow_ShadowMapSplit(ShadowData shadowData, DirectionalShadowData dirShadowData, uint shadowSplitIndex, float3 positionWS, float NoL, float opacity, float transmissionDensityScale, bool isSubsurface)
{
    uint shadowMapIndex = shadowData.shadowMapIndex + shadowSplitIndex;
    uint shadowMatrixIndex = shadowData.matrixIndex + shadowSplitIndex;

    // Get shadowmap texcoords
    float3 positionTC = EvalShadow_GetTexcoordsAtlas(shadowMatrixIndex, ce_CameraTilePosition, ce_CameraTilePosition, positionWS, false);

    float transitionScale = dirShadowData.transitionScale[shadowSplitIndex];
    float shadowReceiverBias = dirShadowData.shadowReceiverBias[shadowSplitIndex];

    PCFSamplerSettings shadowSetting;
    shadowSetting.isSubsurface = isSubsurface;
    shadowSetting.densityMulContant = SubsurfaceDensityFromOpacity(opacity) * transmissionDensityScale * dirShadowData.transmissionScale[shadowSplitIndex];
    shadowSetting.transitionScale = transitionScale * lerp(shadowReceiverBias, 1.0, NoL);
    
    float shadow = 1.0;

    if (_CascadeUseReprojection[shadowSplitIndex])
    {
        switch (shadowData.shadowType)
        {
            case SOFT_SHADOW_PCF_MANUAL_1X1: shadow = SoftShadow2D_PCF_Manual_1X1(_DirShadowMapsReprojection, shadowMapIndex, positionTC.xy, positionTC.z, _ReprojectionDirShadowMapSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_3X3: shadow = SoftShadow2D_PCF_Manual_3X3(_DirShadowMapsReprojection, shadowMapIndex, positionTC.xy, positionTC.z, _ReprojectionDirShadowMapSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_5X5: shadow = SoftShadow2D_PCF_Manual_5X5(_DirShadowMapsReprojection, shadowMapIndex, positionTC.xy, positionTC.z, _ReprojectionDirShadowMapSize, shadowSetting); break;
            default: shadow = 1;
        }
    }
    else
    {
        switch (shadowData.shadowType)
        {
            case HARD_SHADOW:                shadow = PCFShadow_HW(_DirShadowMaps, shadowMapIndex, ce_Sampler_PCF, positionTC.xy, positionTC.z, _DirShadowMapSize.zw, true, shadowSetting);break;
            case SOFT_SHADOW_PCF_MANUAL_1X1: shadow = SoftShadow2D_PCF_Manual_1X1(_DirShadowMaps, shadowMapIndex, positionTC.xy, positionTC.z, _DirShadowMapSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_3X3: shadow = SoftShadow2D_PCF_Manual_3X3(_DirShadowMaps, shadowMapIndex, positionTC.xy, positionTC.z, _DirShadowMapSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_5X5: shadow = SoftShadow2D_PCF_Manual_5X5(_DirShadowMaps, shadowMapIndex, positionTC.xy, positionTC.z, _DirShadowMapSize, shadowSetting); break;
            case SOFT_SHADOW_PCSS:           shadow = SoftShadow2D_PCSS(_DirShadowMaps, shadowMapIndex, positionTC.xy, positionTC.z, 0, _DirShadowMapSize.zw, shadowData.pcssSoftness, (int)(shadowData.pcssSampleCount), shadowSetting); break;
            default: shadow = 1;
        }
    }

    if (_CascadeControl[shadowSplitIndex] == 0)
    {
        shadow = 1.0;
    }

    return shadow;
}

float GetDirectionalShadowAttenuation(float3 positionWS, uint2 positionSS, float3 normalWS, float3 lightDirection, float opacity, float transmissionDensityScale, bool isSubsurface, bool isUseReprojectionShadow, ShadowData shadowData)
{
    DirectionalShadowData dirShadowData = _DirectionalShadowDatas[shadowData.directionalShadowDataIndex];

    float3 positionCS = mul(ce_View, float4(positionWS, 1.0f)).xyz;
    const float NoL = saturate(dot(normalWS, -lightDirection));

    float alpha;
    int shadowSplitIndex = EvalShadow_GetSplitIndex(positionCS.z, dirShadowData, alpha);
    float shadow = 1.0;
    uint cascadeShadowCount = (uint)_ShadowCascadeCount;

    if (shadowSplitIndex >= 0)
    {
        shadow = EvalShadow_ShadowMapSplit(shadowData, dirShadowData, shadowSplitIndex, positionWS, NoL, opacity, transmissionDensityScale, isSubsurface);

        float shadow1 = 1.0f;
        shadowSplitIndex++;
        if (shadowSplitIndex < cascadeShadowCount)
        {
            if (alpha > 0.0)
            {
                shadow1 = EvalShadow_ShadowMapSplit(shadowData, dirShadowData, shadowSplitIndex, positionWS, NoL, opacity, transmissionDensityScale, isSubsurface);
            }
        }
        shadow = lerp(shadow, shadow1, alpha);
    }

    shadow = 1.0 - (1.0 - shadow) * shadowData.shadowAmount;
    return shadow;
}

float GetSpotShadowAttenuation(float3 positionWS, float3 lightPos, float3 lightTilePosition, ShadowData shadowData, PCFSamplerSettings shadowSetting = (PCFSamplerSettings)(0))
{
    float shadow = 1.0;
    int shadowMapIndex = shadowData.shadowMapIndex;

    // Get shadowmap texcoords
    float3 positionTC = EvalShadow_GetTexcoordsAtlas(shadowData.matrixIndex, lightTilePosition, ce_CameraTilePosition, positionWS, true);

    float cameraDepth = length(positionWS - lightPos);
    float refDepth = cameraDepth / shadowData.normalizeFactor - 0.001f;
    
    shadowSetting.transitionScale = 100.0f; // temp
    
    if (USE_CACHED_LOCAL_LIGHT_SHADOW)
    {
        float4 subMapRange = _SpotLightShadowRanges[shadowMapIndex].uvRange;
        float2 passUv = positionTC.xy * (subMapRange.zw - subMapRange.xy) + subMapRange.xy;
        uint arrayIdx = _SpotLightShadowRanges[shadowMapIndex].arrayIdx;
        float4 texSize = _LocalLightShadowMapSize;
        switch (shadowData.shadowType)
        {
            case HARD_SHADOW:                shadow = SpotShadow(_LocalLightMultiTargetShadowMap, arrayIdx, passUv, refDepth, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_1X1: shadow = SoftShadow2D_PCF_Manual_1X1(_LocalLightMultiTargetShadowMap, arrayIdx, passUv, refDepth, texSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_3X3: shadow = SoftShadow2D_PCF_Manual_3X3(_LocalLightMultiTargetShadowMap, arrayIdx, passUv, refDepth, texSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_5X5: shadow = SoftShadow2D_PCF_Manual_5X5(_LocalLightMultiTargetShadowMap, arrayIdx, passUv, refDepth, texSize, shadowSetting); break;
            case SOFT_SHADOW_PCSS:           shadow = SoftShadow2D_PCSS(_LocalLightMultiTargetShadowMap, arrayIdx, passUv, refDepth, 0, texSize.zw, shadowData.pcssSoftness, (int)(shadowData.pcssSampleCount), shadowSetting); break;
            default:                         shadow = SpotShadow(_LocalLightMultiTargetShadowMap, arrayIdx, passUv, refDepth, shadowSetting); break;
        }
    }
    else{
        float2 passUv = positionTC.xy;
        uint arrayIdx = shadowMapIndex;
        float4 texSize = _SpotShadowMapSize;
        switch (shadowData.shadowType)
        {
            case HARD_SHADOW:                shadow = SpotShadow(_SpotShadowMaps, arrayIdx, passUv, refDepth, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_1X1: shadow = SoftShadow2D_PCF_Manual_1X1(_SpotShadowMaps, arrayIdx, passUv, refDepth, texSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_3X3: shadow = SoftShadow2D_PCF_Manual_3X3(_SpotShadowMaps, arrayIdx, passUv, refDepth, texSize, shadowSetting); break;
            case SOFT_SHADOW_PCF_MANUAL_5X5: shadow = SoftShadow2D_PCF_Manual_5X5(_SpotShadowMaps, arrayIdx, passUv, refDepth, texSize, shadowSetting); break;
            case SOFT_SHADOW_PCSS:           shadow = SoftShadow2D_PCSS(_SpotShadowMaps, arrayIdx, passUv, refDepth, 0, texSize.zw, shadowData.pcssSoftness, (int)(shadowData.pcssSampleCount), shadowSetting); break;
            default:                         shadow = SpotShadow(_SpotShadowMaps, arrayIdx, passUv, refDepth, shadowSetting); break;
        }
    }
    shadow = 1.0 - (1.0 - shadow) * shadowData.shadowAmount;
    return shadow;
}

float GetSpotShadowAttenuation_Direct(float3 positionWS, float3 lightPos, float3 lightTilePosition, ShadowData shadowData)
{
    int shadowMapIndex = shadowData.shadowMapIndex;

    // Get shadowmap texcoords
    float3 positionTC = EvalShadow_GetTexcoordsAtlas(shadowData.matrixIndex, lightTilePosition, ce_CameraTilePosition, positionWS, true);
   
    float cameraDepth = length(positionWS - lightPos);
    float refDepth = cameraDepth / shadowData.normalizeFactor - 0.01;
    float shadow;
    if (!USE_CACHED_LOCAL_LIGHT_SHADOW){
        shadow = SpotShadow(_SpotShadowMaps, shadowMapIndex, positionTC.xy, refDepth);
    }
    else{
        uint numMap;
        uint stride;
        _SpotLightShadowRanges.GetDimensions(numMap, stride);
        if (shadowMapIndex < numMap){
            float4 subMapRange = _SpotLightShadowRanges[shadowMapIndex].uvRange;
            uint arrayIdx = _SpotLightShadowRanges[shadowMapIndex].arrayIdx;
            float2 subMapUv = positionTC.xy * (subMapRange.zw - subMapRange.xy) + subMapRange.xy;
            shadow = SpotShadow(_LocalLightMultiTargetShadowMap, arrayIdx, subMapUv, refDepth);
        }
        else 
        {
            shadow = 1.f;
        }
        
    }
    shadow = 1.0 - (1.0 - shadow) * shadowData.shadowAmount;
    return shadow;
}

float GetPointShadowAttenuation(float3 positionWS, float3 lightPos, ShadowData shadowData, PCFSamplerSettings shadowSetting = (PCFSamplerSettings)(0))
{
    float3 sampleDir = positionWS - lightPos;
    float cameraDepth = length(sampleDir);
    sampleDir = normalize(sampleDir);

    float refDepth = cameraDepth / shadowData.normalizeFactor - 0.001f;
    float shadow;

    if (!USE_CACHED_LOCAL_LIGHT_SHADOW)
    {
        shadow = PointShadow(_PointShadowCubemaps, shadowData.shadowMapIndex, sampleDir, refDepth, shadowSetting);
    }
    else
    {
        shadow = PointShadow_cached(_LocalLightMultiTargetShadowMap, shadowData.shadowMapIndex, sampleDir, refDepth, shadowSetting);
    }
    shadow = 1.0 - (1.0 - shadow) * shadowData.shadowAmount;
    return shadow;
}

float GetPointShadowAttenuation_Direct(float3 positionWS, float3 lightPos, ShadowData shadowData)
{
    float3 sampleDir = positionWS - lightPos;
    float cameraDepth = length(sampleDir);
    sampleDir = normalize(sampleDir);

    float refDepth = cameraDepth / shadowData.normalizeFactor - 0.001;
    float shadow;

    if (!USE_CACHED_LOCAL_LIGHT_SHADOW)
    {
        shadow = PointShadow(_PointShadowCubemaps, shadowData.shadowMapIndex, sampleDir, refDepth);
    }
    else
    {
        shadow = PointShadow_cached(_LocalLightMultiTargetShadowMap, shadowData.shadowMapIndex, sampleDir, refDepth);
    }
    shadow = 1.0 - (1.0 - shadow) * shadowData.shadowAmount;
    return shadow;
}

#endif