#ifndef FINAL_GATHER_INTERPOLATION_HLSL
#define FINAL_GATHER_INTERPOLATION_HLSL

#include "Common.hlsl"
#include "SmartGICommon.hlsl"
#include "SceneView.hlsl"
#include "FinalGatherCommon.hlsl"

RWTexture2D<uint> _RWScreenProbeSceneDepth;
RWTexture2D<unorm float2> _RWScreenProbeWorldNormal;
RWTexture2D<float> _RWScreenProbeWorldSpeed;
RWTexture2D<float4> _RWScreenProbeTranslatedWorldPosition;
SHADER_CONST(bool, PROBE_INTERPOLATION_WITH_NORMAL, false);

void WriteDownsampledProbeGBuffer(float2 ScreenUV, uint2 ScreenProbeAtlasCoord, FScreenProbeGBuffer ProbeGBuffer)
{
	float EncodedDepth = ProbeGBuffer.SceneDepth;
	if (!ProbeGBuffer.bLit)
	{
		// Store unlit in sign bit
		EncodedDepth *= -1.0f;
	}
	_RWScreenProbeSceneDepth[ScreenProbeAtlasCoord] = asuint(EncodedDepth);
	_RWScreenProbeWorldNormal[ScreenProbeAtlasCoord] = UnitVectorToOctahedron(ProbeGBuffer.WorldNormal) * 0.5 + 0.5;

	float3 ProbeWorldVelocity = 0;
	float3 ProbeTranslatedWorldPosition = 0;
	{
		float2 ProbeScreenPosition = (ScreenUV - _View_ScreenPositionScaleBias.wz) / _View_ScreenPositionScaleBias.xy;
		ProbeTranslatedWorldPosition = MUL_UE(float4(ProbeScreenPosition * ProbeGBuffer.SceneDepth, ProbeGBuffer.SceneDepth, 1), _View_ScreenToTranslatedWorld).xyz;

		// float ProbeDeviceZ = ConvertToDeviceZ(ProbeGBuffer.SceneDepth);
		// 	float3 ProbeHistoryScreenPosition = GetHistoryScreenPositionIncludingTAAJitter(ProbeScreenPosition, ScreenUV, ProbeDeviceZ);
		// 	ProbeWorldVelocity = ProbeTranslatedWorldPosition - GetPrevTranslatedWorldPosition(ProbeHistoryScreenPosition);
	}

	_RWScreenProbeWorldSpeed[ScreenProbeAtlasCoord] = length(ProbeWorldVelocity);
	_RWScreenProbeTranslatedWorldPosition[ScreenProbeAtlasCoord] = float4(ProbeTranslatedWorldPosition, 0.0f);
}

float GetScreenProbeDepthFromUAV(uint2 ScreenProbeAtlasCoord)
{
	return asfloat(_RWScreenProbeSceneDepth[ScreenProbeAtlasCoord]);
}

float3 GetScreenProbeNormalFromUAV(uint2 ScreenProbeAtlasCoord)
{
	return OctahedronToUnitVector(_RWScreenProbeWorldNormal[ScreenProbeAtlasCoord] * 2.0 - 1.0);
}

void CalculateUniformUpsampleInterpolationWeights(
	float2 ScreenCoord, 
	float2 NoiseOffset, 
	float3 WorldPosition, 
	float SceneDepth, 
	float3 WorldNormal, 
	uniform bool bIsUpsamplePass,
	out uint2 ScreenTileCoord00, 
	out float4 InterpolationWeights,
	out float4 FallbackWeights)
{
	uint2 ScreenProbeFullResScreenCoord = clamp(ScreenCoord.xy - _View_RectMin.xy - GetScreenTileJitter(SCREEN_TEMPORAL_INDEX) + NoiseOffset, 0.0f, _View_SizeAndInvSize.xy - 1.0f);
	ScreenTileCoord00 = min(ScreenProbeFullResScreenCoord / _ScreenProbeDownsampleFactor, (uint2)_ScreenProbeViewSize - 2);

	uint BilinearExpand = 1;
	float2 BilinearWeights = (ScreenProbeFullResScreenCoord - ScreenTileCoord00 * _ScreenProbeDownsampleFactor + BilinearExpand) / (float)(_ScreenProbeDownsampleFactor + 2 * BilinearExpand);

	float4 CornerDepths;
	CornerDepths.x = bIsUpsamplePass ? GetScreenProbeDepth(ScreenTileCoord00) : GetScreenProbeDepthFromUAV(ScreenTileCoord00);
	CornerDepths.y = bIsUpsamplePass ? GetScreenProbeDepth(ScreenTileCoord00 + int2(1, 0)) : GetScreenProbeDepthFromUAV(ScreenTileCoord00 + int2(1, 0));
	CornerDepths.z = bIsUpsamplePass ? GetScreenProbeDepth(ScreenTileCoord00 + int2(0, 1)) : GetScreenProbeDepthFromUAV(ScreenTileCoord00 + int2(0, 1));
	CornerDepths.w = bIsUpsamplePass ? GetScreenProbeDepth(ScreenTileCoord00 + int2(1, 1)) : GetScreenProbeDepthFromUAV(ScreenTileCoord00 + int2(1, 1));

	float4 NormalWeights = float4(1, 1, 1, 1);
	if(PROBE_INTERPOLATION_WITH_NORMAL)
	{
		float3 ProbeNormal00 = bIsUpsamplePass ? GetScreenProbeNormal(ScreenTileCoord00) : GetScreenProbeNormalFromUAV(ScreenTileCoord00);
		float3 ProbeNormal10 = bIsUpsamplePass ? GetScreenProbeNormal(ScreenTileCoord00 + int2(1, 0)) : GetScreenProbeNormalFromUAV(ScreenTileCoord00 + int2(1, 0));
		float3 ProbeNormal01 = bIsUpsamplePass ? GetScreenProbeNormal(ScreenTileCoord00 + int2(0, 1)) : GetScreenProbeNormalFromUAV(ScreenTileCoord00 + int2(0, 1));
		float3 ProbeNormal11 = bIsUpsamplePass ? GetScreenProbeNormal(ScreenTileCoord00 + int2(1, 1)) : GetScreenProbeNormalFromUAV(ScreenTileCoord00 + int2(1, 1));

		NormalWeights = float4(
			saturate(dot(WorldNormal, ProbeNormal00)),
			saturate(dot(WorldNormal, ProbeNormal10)),
			saturate(dot(WorldNormal, ProbeNormal01)),
			saturate(dot(WorldNormal, ProbeNormal11))
		);
	}

	InterpolationWeights = float4(
		(1 - BilinearWeights.y) * (1 - BilinearWeights.x),
		(1 - BilinearWeights.y) * BilinearWeights.x,
		BilinearWeights.y * (1 - BilinearWeights.x),
		BilinearWeights.y * BilinearWeights.x);

	float4 DepthWeights;

#define PLANE_WEIGHTING 1
#if PLANE_WEIGHTING
	{
		float4 ScenePlane = float4(WorldNormal, dot(WorldPosition, WorldNormal));

		float3 Position00 = GetWorldPositionFromScreenUV(GetScreenUVFromScreenTileCoord(ScreenTileCoord00), CornerDepths.x);
		float3 Position10 = GetWorldPositionFromScreenUV(GetScreenUVFromScreenTileCoord(ScreenTileCoord00 + uint2(1, 0)), CornerDepths.y);
		float3 Position01 = GetWorldPositionFromScreenUV(GetScreenUVFromScreenTileCoord(ScreenTileCoord00 + uint2(0, 1)), CornerDepths.z);
		float3 Position11 = GetWorldPositionFromScreenUV(GetScreenUVFromScreenTileCoord(ScreenTileCoord00 + uint2(1, 1)), CornerDepths.w);

		float4 PlaneDistances;
		PlaneDistances.x = abs(dot(float4(Position00, -1), ScenePlane));
		PlaneDistances.y = abs(dot(float4(Position10, -1), ScenePlane));
		PlaneDistances.z = abs(dot(float4(Position01, -1), ScenePlane));
		PlaneDistances.w = abs(dot(float4(Position11, -1), ScenePlane));
			
		float4 RelativeDepthDifference = PlaneDistances / SceneDepth;

        DepthWeights = select(CornerDepths > 0 , exp2(-10000.0f * (RelativeDepthDifference * RelativeDepthDifference)) , 0);
    }
#else
	{
		float4 DepthDifference = abs(CornerDepths - SceneDepth); // FIXME
		float4 RelativeDepthDifference = DepthDifference / SceneDepth;
		DepthWeights = CornerDepths > 0 ? exp2(-100.0f * (RelativeDepthDifference * RelativeDepthDifference)) : 0;
	}
#endif
	FallbackWeights = InterpolationWeights;
	InterpolationWeights = InterpolationWeights * DepthWeights * (bIsUpsamplePass == 0 ? NormalWeights : clamp(NormalWeights, float4(0.1f, 0.1f, 0.1f, 0.1f), float4(1, 1, 1, 1)));
}

RWStructuredBuffer<uint> _RWNumAdaptiveScreenProbes;
RWStructuredBuffer<uint> _RWAdaptiveScreenProbeData;
RWTexture2D<uint> _RWScreenTileAdaptiveProbeHeader;
RWTexture2D<uint> _RWScreenTileAdaptiveProbeIndices;

struct FScreenProbeSample
{
	uint2 AtlasCoord[4];
	float4 Weights;
	float4 FallbackWeights;
};

void CalculateUpsampleInterpolationWeights(
	float2 ScreenCoord,
	float2 NoiseOffset,
	float3 WorldPosition,
	float SceneDepth,
	float3 WorldNormal,
	uniform bool bIsUpsamplePass,
	out FScreenProbeSample ScreenProbeSample)
{
	uint2 ScreenTileCoord00;
	CalculateUniformUpsampleInterpolationWeights(ScreenCoord, NoiseOffset, WorldPosition, SceneDepth, WorldNormal, bIsUpsamplePass, ScreenTileCoord00, ScreenProbeSample.Weights, ScreenProbeSample.FallbackWeights);

	ScreenProbeSample.AtlasCoord[0] = ScreenTileCoord00;
	ScreenProbeSample.AtlasCoord[1] = ScreenTileCoord00 + uint2(1, 0);
	ScreenProbeSample.AtlasCoord[2] = ScreenTileCoord00 + uint2(0, 1);
	ScreenProbeSample.AtlasCoord[3] = ScreenTileCoord00 + uint2(1, 1);

	bool bUseAdaptiveProbesForUpsample = true;

	if (bUseAdaptiveProbesForUpsample || !bIsUpsamplePass)
	{		
		float Epsilon = .01f;
		float4 ScenePlane = float4(WorldNormal, dot(WorldPosition, WorldNormal));

		UNROLL
		for (uint CornerIndex = 0; CornerIndex < 4; CornerIndex++)
		{
			if (ScreenProbeSample.Weights[CornerIndex] <= Epsilon)
			{
				uint2 ScreenTileCoord = ScreenTileCoord00 + uint2(CornerIndex % 2, CornerIndex / 2);
				uint NumAdaptiveProbes = bIsUpsamplePass ? _ScreenTileAdaptiveProbeHeader[ScreenTileCoord] : _RWScreenTileAdaptiveProbeHeader[ScreenTileCoord];

				for (uint AdaptiveProbeListIndex = 0; AdaptiveProbeListIndex < NumAdaptiveProbes; AdaptiveProbeListIndex++)
				{
					uint2 AdaptiveProbeCoord = GetAdaptiveProbeCoord(ScreenTileCoord, AdaptiveProbeListIndex);
					uint AdaptiveProbeIndex = bIsUpsamplePass ? _ScreenTileAdaptiveProbeIndices[AdaptiveProbeCoord] : _RWScreenTileAdaptiveProbeIndices[AdaptiveProbeCoord];
					uint ScreenProbeIndex = AdaptiveProbeIndex + _NumUniformScreenProbes;

					uint2 ScreenProbeScreenPosition = bIsUpsamplePass ? GetScreenProbeScreenPosition(ScreenProbeIndex) : DecodeScreenProbeData(_RWAdaptiveScreenProbeData[AdaptiveProbeIndex]);
					uint2 ScreenProbeAtlasCoord = uint2(ScreenProbeIndex % _ScreenProbeAtlasViewSize.x, ScreenProbeIndex / _ScreenProbeAtlasViewSize.x);
					float ProbeDepth = bIsUpsamplePass ? GetScreenProbeDepth(ScreenProbeAtlasCoord) : GetScreenProbeDepthFromUAV(ScreenProbeAtlasCoord);
					float NewNormalWeight = 1.0f;
					if(PROBE_INTERPOLATION_WITH_NORMAL)
					{
						float3 ProbeNormal = bIsUpsamplePass ? GetScreenProbeNormal(ScreenProbeAtlasCoord) : GetScreenProbeNormalFromUAV(ScreenProbeAtlasCoord);
						NewNormalWeight = saturate(dot(WorldNormal, ProbeNormal));
					}
					
					float NewDepthWeight = 0;
					bool bPlaneWeighting = true;
					if (bPlaneWeighting)
					{
						float3 ProbePosition = GetWorldPositionFromScreenUV(GetScreenUVFromScreenProbePosition(ScreenProbeScreenPosition), ProbeDepth);
						float PlaneDistance = abs(dot(float4(ProbePosition, -1), ScenePlane));
						float RelativeDepthDifference = PlaneDistance / SceneDepth;
						NewDepthWeight = exp2(-10000.0f * (RelativeDepthDifference * RelativeDepthDifference));
					}
					else
					{
						float DepthDifference = abs(ProbeDepth - SceneDepth);
						float RelativeDepthDifference = DepthDifference / SceneDepth; // FIXME
						NewDepthWeight = ProbeDepth > 0 ? exp2(-100.0f * (RelativeDepthDifference * RelativeDepthDifference)) : 0;
					}

					float2 DistanceToScreenProbe = abs(ScreenProbeScreenPosition - ScreenCoord);
					float NewCornerWeight = 1.0f - saturate(min(DistanceToScreenProbe.x, DistanceToScreenProbe.y) / (float)_ScreenProbeDownsampleFactor);
					float NewInterpolationWeight = NewDepthWeight * NewCornerWeight * (bIsUpsamplePass == 0 ? NewNormalWeight : clamp(NewNormalWeight, 0.1f, 1));

					if (NewInterpolationWeight > ScreenProbeSample.Weights[CornerIndex])
					{
						ScreenProbeSample.Weights[CornerIndex] = NewInterpolationWeight;
						ScreenProbeSample.AtlasCoord[CornerIndex] = ScreenProbeAtlasCoord;
					}
				}
			}
		}
	}
}

#endif // FINAL_GATHER_INTERPOLATION_HLSL