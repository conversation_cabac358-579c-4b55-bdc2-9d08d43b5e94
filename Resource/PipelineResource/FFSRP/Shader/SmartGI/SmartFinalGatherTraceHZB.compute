#pragma compute TraceHZBCS
//#pragma enable debug_symbol

#include "../Lighting/LightMap.h"
#include "SmartTracingCommon.hlsl"

#define WORLD_MAX 2097152.0f

//StructuredBuffer<float4> ce_UE4AmbientProbeSH : register(space0);

#define FurthestHZBTextureSampler ce_Sampler_Point
#define GlobalPointClampedSampler ce_Sampler_Point

Texture2D FurthestHZBTexture;
Texture2D PrevSceneColorTexture;
Texture2D EmissiveColorTexture;
Texture2D HistorySceneDepthTexture;

const static float Max10BitsFloat = 64512.0f;

cbuffer _cbCommon
{
	float4 HistoryUVMinMax;
	float4 _HZBUvFactorAndInvFactor;
	float2 _DispatchThreadIdToScreenUV;
	float2 _HZBBaseTexelSize;
	float _MaxTraceDistance;
	float _MaxScreenTraceFraction;
	float _PrevSceneColorExposureScale;
    float _EmissiveGIIntensity;
	uint _NumRayCastSteps;
}

#include "../ShaderLibrary/Common.hlsl"
#include "Common.hlsl"
#include "SceneView.hlsl"
#include "FinalGatherCommon.hlsl"

SHADER_CONST(bool, TRACE_SKY_LIGHTING, false);
SHADER_CONST(bool, DEBUG_TRACE_SKY_LIGHTING_ONLY, false);
SHADER_CONST(bool, HIERARCHICAL_SCREEN_TRACING, false);
SHADER_CONST(float, DISABLE_EMISSIVE_THRESHOLD_VALUE, 0.0f);

#include "../Material/NormalBuffer.hlsl"
#include "SSRTRayCast.hlsl"

Texture2D ClosestHZBTexture;
//float4 HZBUVToScreenUVScaleBias;

void TraceScreen(
	matrix TranslatedWorldToView,
	matrix TranslatedWorldToClip,
	float3 RayTranslatedWorldOrigin,
	float3 RayWorldDirection,
	float MaxWorldTraceDistance,
	float4 HZBUvFactorAndInvFactor,
	float MaxIterations,
	float RelativeDepthThickness,
	float NumThicknessStepsToDetermineCertainty,
	uint MinimumTracingThreadOccupancy,
	inout bool bHit,
	inout bool bUncertain,
	inout float3 OutScreenUV,
	inout float OutHitTileZ)
{
	float4 HZBUVToScreenUVScaleBias = float4(HZBUvFactorAndInvFactor.zw, 0, 0);
	InternalTraceScreen(
		TranslatedWorldToView,
		TranslatedWorldToClip,
		SceneDepthTexture,
		ClosestHZBTexture,
		_HZBBaseTexelSize,
		HZBUVToScreenUVScaleBias,
		RayTranslatedWorldOrigin,
		RayWorldDirection,
		MaxWorldTraceDistance,
		HZBUvFactorAndInvFactor,
		MaxIterations,
		RelativeDepthThickness,
		NumThicknessStepsToDetermineCertainty,
		MinimumTracingThreadOccupancy,
		bHit,
		bUncertain,
		OutScreenUV,
		OutHitTileZ);
} 

float3 GetViewSpacePosition(float2 uv, float deviceZ)
{
	uv.y = 1.0 - uv.y;
	uv = 2.0 * uv - 1.0;
    float4 result = mul(ce_InvProjection, float4(uv, deviceZ, 1.0));
    return result.xyz / result.w;
}

float3 GetWorldSpacePosition(float2 uv, float deviceZ)
{
	float3 viewSpacePosition = GetViewSpacePosition(uv, deviceZ);
	float4 result = mul(ce_InvView, float4(viewSpacePosition, 1.0f));
	return result.xyz / result.w;
}

//Dimension = target Size
[numthreads(PROBE_THREADGROUP_SIZE_2D, PROBE_THREADGROUP_SIZE_2D, 1)]
void TraceHZBCS(uint GroupIndex:SV_GroupIndex,
	uint3 GroupId : SV_GroupID,
	uint3 GroupThreadId : SV_GroupThreadID,
	uint3 DispatchThreadId : SV_DispatchThreadID)
{
	uint2 ScreenProbeAtlasCoord = DispatchThreadId.xy / _ScreenProbeTracingOctahedronResolution;
	uint2 TraceTexelCoord = DispatchThreadId.xy - ScreenProbeAtlasCoord * _ScreenProbeTracingOctahedronResolution;

	uint ScreenProbeIndex = ScreenProbeAtlasCoord.y * _ScreenProbeAtlasViewSize.x + ScreenProbeAtlasCoord.x;

	uint2 ScreenProbeScreenPosition = GetScreenProbeScreenPosition(ScreenProbeIndex);
	uint2 ScreenTileCoord = GetScreenTileCoord(ScreenProbeScreenPosition);

	float3 WorldPosition = float3(1.f, 0.f, 0.f); 
	float3 PrimaryView_PreViewTranslation = float3(0, 0, 0);//TODO:
	float3 Lighting = float3(0, 0, 0);
	float HitDistance = 0;
	bool bHit = false;
	bool bWriteDepthOnMiss = false;
	bool bFastMoving = false;
	float2 TraceCoord = GetTraceBufferCoord(ScreenProbeAtlasCoord, TraceTexelCoord);

	if (ScreenProbeIndex < GetNumScreenProbes() 
		&& ScreenProbeAtlasCoord.x < _ScreenProbeAtlasViewSize.x
		&& all(TraceTexelCoord < _ScreenProbeTracingOctahedronResolution))
	{
		float2 ScreenUV = GetScreenUVFromScreenProbePosition(ScreenProbeScreenPosition);
		float SceneDepth = GetScreenProbeDepth(ScreenProbeAtlasCoord);

		float3 WorldNormal = GetScreenProbeNormal(ScreenProbeAtlasCoord);
		if(SceneDepth > 0.0f)
		{
			WorldPosition = GetWorldPositionFromScreenUV(ScreenUV, SceneDepth);
			
			float2 ProbeUV;
			float ConeHalfAngle;
			GetProbeTracingUV(ScreenProbeAtlasCoord, TraceTexelCoord, GetProbeTexelCenter(ScreenTileCoord), 1, ProbeUV, ConeHalfAngle);
			
			//float3 WorldConeDirection = Float3UE2CE(OctahedronToUnitVector(ProbeUV * 2.0 - 1.0));
			float3 WorldConeDirection = OctahedronToUnitVector(ProbeUV * 2.0 - 1.0);

			//@todo - this should be applied with the hit UV, not the origin, but still works for self-shadowing
			float ScreenTraceNoFallbackThicknessScale = 2; //Larger scales effectively treat depth buffer surfaces as thicker for screen traces when there is no Distance Field present to resume the occluded ray.
			float DepthThresholdScale = ScreenTraceNoFallbackThicknessScale;

			//if(dot(WorldConeDirection, WorldNormal) > 0.1)
			{
				float TraceDistance = _MaxTraceDistance;//1000
				// Can only get decent quality out of fixed step count screen traces by limiting the trace distance
				float MaxWorldTraceDistance = SceneDepth * _MaxScreenTraceFraction * 2.0 * GetTanHalfFieldOfView().x;
				TraceDistance = min(TraceDistance, MaxWorldTraceDistance);

				uint NumSteps = _NumRayCastSteps;//64;

				bool bUncertain;
				float3 HitUVz;
				if(HIERARCHICAL_SCREEN_TRACING)
				{
				float Unused;

				float RelativeDepthThickness = 0.02f;//Determines depth thickness of objects hit by HZB tracing, as a relative depth threshold.
				float NumThicknessStepsToDetermineCertainty = 4;
				uint MinimumTracingThreadOccupancy = 0;
				TraceScreen(
					ce_View,
					mul(ce_Projection, ce_View),
					WorldPosition,
					WorldConeDirection,
					TraceDistance,
					_HZBUvFactorAndInvFactor,
					NumSteps,
					RelativeDepthThickness * DepthThresholdScale,
					NumThicknessStepsToDetermineCertainty,
					MinimumTracingThreadOccupancy,
					bHit,
					bUncertain,
					HitUVz,
					Unused
				);
				bHit = bHit & !bUncertain;

				}else{

				bool bCoveredByRadianceCache = false;
				float StartMipLevel = 1.0f;

				float2 NoiseCoord = ScreenProbeAtlasCoord * _ScreenProbeTracingOctahedronResolution + TraceTexelCoord;
				float StepOffset = InterleavedGradientNoise(NoiseCoord + 0.5f, 0);

				float RayRoughness = .2f;
				StepOffset = StepOffset * 0.5f + 0.5f;
				//StepOffset = 1.0f;

				float Level;
				bool bRayWasClipped;

				FSSRTRay Ray = InitScreenSpaceRayFromWorldSpace(
					ce_View,
					mul(ce_Projection, ce_View),
					ce_Projection,
					WorldPosition + LWCHackToFloat(PrimaryView_PreViewTranslation), WorldConeDirection,
					/* WorldTMax = */ TraceDistance,
					/* SceneDepth = */ SceneDepth,
					/* SlopeCompareToleranceScale */ 2.0f * DepthThresholdScale,
					/* bExtendRayToScreenBorder = */ false,
					/* out */ bRayWasClipped);

				CastScreenSpaceRay(
					FurthestHZBTexture, FurthestHZBTextureSampler,
					StartMipLevel,
					Ray, RayRoughness, NumSteps, StepOffset,
					_HZBUvFactorAndInvFactor,
					true,
					/* out */ HitUVz,
					/* out */ Level,
					/* out */ bHit,
					/* out */ bUncertain);
				}

				// CastScreenSpaceRay skips Mesh SDF tracing in a lot of places where it shouldn't, in particular missing thin occluders due to low NumSteps.  
				//bool bWriteDepthOnMiss = !bUncertain;
				bWriteDepthOnMiss = true;

				float3 HitPoint = GetWorldSpacePosition(HitUVz.xy, HitUVz.z);
				HitDistance = length(HitPoint - WorldPosition);

				//TraceCoord = GetTraceBufferCoord(ScreenProbeAtlasCoord, TraceTexelCoord);
				if(DEBUG_TRACE_SKY_LIGHTING_ONLY)
				{
					bHit = false;
				}
				if(bHit)
				{
					float2 ScreenUV = HitUVz.xy;
					float2 ScreenPosition = (ScreenUV - _View_ScreenPositionScaleBias.wz) / _View_ScreenPositionScaleBias.xy;
					float DeviceZ = HitUVz.z;
					float3 HistoryScreenPosition = GetHistoryScreenPosition(ScreenPosition, ScreenUV, DeviceZ);
					float2 HitHistoryScreenUV = HistoryScreenPosition.xy * _View_ScreenPositionScaleBias.xy + _View_ScreenPositionScaleBias.wz;
					float HitScreenPosition = HistoryScreenPosition.xy;

					bool bHistoryWasOnscreen = all(HitHistoryScreenUV < HistoryUVMinMax.zw) && all(HitHistoryScreenUV > HistoryUVMinMax.xy);
					HitHistoryScreenUV = clamp(HitHistoryScreenUV, HistoryUVMinMax.xy, HistoryUVMinMax.zw);

					// float2 HitHistoryScreenUV = HitUVz.xy;
					// float HitScreenPosition = HitUVz.xy * float2( 2, -2 ) + float2( -1, 1 );

					float Vignette = ComputeHitVignetteFromScreenPos(HitScreenPosition);
					float Noise = InterleavedGradientNoise(TraceCoord + 0.5f, 0);

					// Skip reporting a hit if near the edge of the screen
					if (Vignette < Noise)
					{
						bHit = false;
					}

					if(bHit)
					{
						float PrevSceneDepth = ConvertToScreenDepthLastFrame(HistoryScreenPosition.z);

						float HistoryDepth = ConvertToScreenDepthLastFrame(HistorySceneDepthTexture.SampleLevel(GlobalPointClampedSampler, HitHistoryScreenUV, 0).x);
						float HistoryDepthTestRelativeThickness = 0.1f;
						bHit = abs(HistoryDepth - PrevSceneDepth) < HistoryDepthTestRelativeThickness * lerp(0.5f, 5.0f, Noise) * PrevSceneDepth;
					}

					if(bHit && bHistoryWasOnscreen)
					{
						// EmissiveColorTexture priority > SceneColorTexture priority
                        float3 emissiveColor = SampleScreenColor(EmissiveColorTexture, GlobalPointClampedSampler, HitHistoryScreenUV).xyz;
						if (length(emissiveColor) > 0.f)
						{
                            Lighting = emissiveColor * _EmissiveGIIntensity * _PrevSceneColorExposureScale;

                        } else
                        {
                            Lighting = SampleScreenColor(PrevSceneColorTexture, GlobalPointClampedSampler, HitHistoryScreenUV).xyz * _PrevSceneColorExposureScale;
                        }
						if(all(Lighting.xyz >= Max10BitsFloat.xxx * 0.5))//trick: RenderSkyRayMarching.shader convert nan to Max10BitsFloat.xxx * 0.5
						{
							Lighting = float3(0, 0, 0);
						}
#if DEBUG_VISUALIZE_TRACE_TYPES
                        Lighting = float3(1.0, 0.0, 0.0);
#endif
                    }
				}

				//TODO: move sky light sample after voxel tracing
				if(TRACE_SKY_LIGHTING)
				{
					if(!bHit)
					{
						Lighting = EvaluateSkyRadiance(WorldConeDirection, 1.0);
						bHit = true;
						HitDistance = _MaxTraceDistance; //WORLD_MAX/2.0f + 1.0f;
#if DEBUG_VISUALIZE_TRACE_TYPES
                        Lighting = float3(0.0, 0.0, 1.0);
#endif
                    }
				}
			}
		}
	}

	if(bHit || bWriteDepthOnMiss)
	{
		RWTraceRadiance[TraceCoord] = float4(Lighting, 1.0f);
		RWTraceHit[TraceCoord] = EncodeProbeRayDistance(HitDistance, bHit, bFastMoving);
	}
	
	// float2 screenUV = (DispatchThreadId.xy + float2(0.5, 0.5)) * _DispatchThreadIdToScreenUV;
	// float SceneDepth = _DepthSrc[DispatchThreadId.xy];
	// //float SceneDepth = GetScreenProbeDepth(screenUV * float2(40, 40));
	// //PositionInputs posInput = GetPositionInput(DispatchThreadId.xy, _DispatchThreadIdToScreenUV, SceneDepth, ce_InvViewProjMatrix, ce_View);
	// float3 WorldPosition = GetWorldSpacePosition(screenUV, SceneDepth);
	// //float3 WorldPosition = posInput.positionWS;
	// float3 viewDirW = normalize(ce_CameraPos.xyz - WorldPosition);

	// float4 gBuffer1 = _GBuffer1[DispatchThreadId.xy];
	// NormalData normalData;
	// DecodeFromNormalBuffer(gBuffer1, normalData);
	// float3 normalWS = normalData.normalWS;
	// //float3 normalWS = float3(0, 1, 0);
	// float3 reflectDirW = normalize(reflect(-viewDirW, normalWS));

	// float TraceDistance = 500;
	// float DepthThresholdScale = 10;

	// float3 WorldConeDirection = reflectDirW;

	// float RayRoughness = .2f;
	// float StepOffset = 1.f;

	// uint NumSteps = 16;
	// float StartMipLevel = 1.0f;

	// bool bHit = false;
	// float Level;
	// float3 HitUVz;
	// bool bRayWasClipped;

	// FSSRTRay Ray = InitScreenSpaceRayFromWorldSpace(
	// 	WorldPosition + LWCHackToFloat(PrimaryView_PreViewTranslation), WorldConeDirection,
	// 	/* WorldTMax = */ TraceDistance,
	// 	/* SceneDepth = */ SceneDepth,
	// 	/* SlopeCompareToleranceScale */ 2.0f * DepthThresholdScale,
	// 	/* bExtendRayToScreenBorder = */ false,
	// 	/* out */ bRayWasClipped);

	// bool bUncertain;

	// CastScreenSpaceRay(
	// 	FurthestHZBTexture, FurthestHZBTextureSampler,
	// 	StartMipLevel,
	// 	Ray, RayRoughness, NumSteps, StepOffset,
	// 	_HZBUvFactorAndInvFactor,
	// 	/* out */ HitUVz,
	// 	/* out */ Level,
	// 	/* out */ bHit,
	// 	/* out */ bUncertain);

	// float3 Lighting = float3(0, 0, 0);
	// if(bHit)
	// {
	// 	float2 HitHistoryScreenUV = HitUVz.xy;
	// 	Lighting = SampleScreenColor(PrevSceneColorTexture, GlobalPointClampedSampler, HitHistoryScreenUV).xyz;
	// }
	// RWTraceRadiance[DispatchThreadId.xy] = float4(Lighting, 1.0f);

	//RWTraceRadiance[DispatchThreadId.xy] = float4(WorldPosition, 1.0f);

	//float4 positionCS = mul(ce_Projection, mul(ce_View, posInput.positionWS2));

	//float4 positionCS = mul(ce_Projection, mul(ce_View, float4(posInput.positionWS, 1.0f)));
	//float4 positionCS = mul(View_TranslatedWorldToClip, float4(posInput.positionWS, 1.0f));
	//float4 positionCS = mul(float4(posInput.positionWS, 1.0f), View_TranslatedWorldToClip);
	//float3 positionScreen = positionCS.xyz * rcp(positionCS.w);
	//RWTraceRadiance[DispatchThreadId.xy] = SampleScreenColor(PrevSceneColorTexture, GlobalPointClampedSampler, HitUVz.xy * _HZBUvFactorAndInvFactor.zw);
	//RWTraceRadiance[DispatchThreadId.xy] = float4(HitUVz.xy * _HZBUvFactorAndInvFactor.zw, HitUVz.z, 1.0f);
	//RWTraceRadiance[DispatchThreadId.xy] = float4(normalWS*0.5f + 0.5f, 1.0f);
}