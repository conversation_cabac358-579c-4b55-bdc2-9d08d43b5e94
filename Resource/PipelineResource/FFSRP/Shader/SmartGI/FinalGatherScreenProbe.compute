#pragma compute DownsampleDepthUniformCS
#pragma compute AdaptivePlacementCS
//#pragma enable debug_symbol

#define THREADGROUP_SIZE 8

#include "SceneView.hlsl"
#include "ScreenTextures.hlsl"
#include "FinalGatherCommon.hlsl"
#include "FinalGatherInterpolation.hlsl"

FScreenProbeGBuffer GetProbeGBufferFromScreenTextures(uint2 ScreenProbeScreenPosition)
{
	float3 Normals = GetNormalDataUint(ScreenProbeScreenPosition);
	float Depth = GetDepthValueUint(ScreenProbeScreenPosition);
	FScreenProbeGBuffer ProbeGBuffer;
	ProbeGBuffer.WorldNormal = Normals;
	ProbeGBuffer.SceneDepth = ConvertToScreenDepth(Depth);
	ProbeGBuffer.bLit = ProbeGBuffer.SceneDepth > 0;
	return ProbeGBuffer;
}

[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, 1)]
void DownsampleDepthUniformCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	uint2 ScreenProbeAtlasCoord = DispatchThreadId.xy;

	if (all(ScreenProbeAtlasCoord < _ScreenProbeViewSize)) // ScreenProbeAtlasViewSize -> _ScreenProbeViewSize
	{
		uint2 ScreenJitter = GetScreenTileJitter(SCREEN_TEMPORAL_INDEX);
		uint2 ScreenProbeScreenPosition = min((uint2)(ScreenProbeAtlasCoord * _ScreenProbeDownsampleFactor + ScreenJitter), (uint2)(_View_RectMin.xy + _View_SizeAndInvSize.xy) - 1);
		float2 ScreenUV = (ScreenProbeScreenPosition + .5f) * _View_BufferSizeAndInvSize.zw;
		FScreenProbeGBuffer ProbeGBuffer = GetProbeGBufferFromScreenTextures(ScreenProbeScreenPosition);
		
		WriteDownsampledProbeGBuffer(ScreenUV, ScreenProbeAtlasCoord, ProbeGBuffer);
	}
}

cbuffer _cbCommon
{
	uint _PlacementDownsampleFactor;
}

groupshared uint SharedNumProbesToAllocate;
groupshared uint SharedAdaptiveProbeBaseIndex;
groupshared uint2 SharedProbeScreenPositionsToAllocate[THREADGROUP_SIZE * THREADGROUP_SIZE];
groupshared FScreenProbeGBuffer SharedScreenProbeGBuffer[THREADGROUP_SIZE * THREADGROUP_SIZE];

[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, 1)]
void AdaptivePlacementCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	uint ThreadIndex = GroupThreadId.y * THREADGROUP_SIZE + GroupThreadId.x;

	if (ThreadIndex == 0)
	{
		SharedNumProbesToAllocate = 0;
	}

	GroupMemoryBarrierWithGroupSync();

	{
		uint2 ScreenProbeScreenPosition = DispatchThreadId.xy * _PlacementDownsampleFactor + GetScreenTileJitter(SCREEN_TEMPORAL_INDEX) + (uint2)_View_RectMin.xy;

		if (all(ScreenProbeScreenPosition < (uint2)(_View_RectMin.xy + _View_SizeAndInvSize.xy)) && any((DispatchThreadId.xy & 0x1) != 0))
		{
			FScreenProbeGBuffer ProbeGBuffer = GetProbeGBufferFromScreenTextures(ScreenProbeScreenPosition);

			if (ProbeGBuffer.bLit)
			{
				float2 ScreenUV = (ScreenProbeScreenPosition + .5f) * _View_SizeAndInvSize.zw;
				float3 WorldPosition = GetWorldPositionFromScreenUV(ScreenUV, ProbeGBuffer.SceneDepth);
				float2 NoiseOffset = 0.0f;

				FScreenProbeSample ScreenProbeSample = (FScreenProbeSample)0;
				
				CalculateUpsampleInterpolationWeights(
					ScreenProbeScreenPosition,
					NoiseOffset,
					WorldPosition,
					ProbeGBuffer.SceneDepth,
					ProbeGBuffer.WorldNormal,
					false,
					ScreenProbeSample);

				float Epsilon = .01f;
				ScreenProbeSample.Weights /= max(dot(ScreenProbeSample.Weights, 1), Epsilon);

				float LightingIsValid = (dot(ScreenProbeSample.Weights, 1) < 1.0f - Epsilon) ? 0.0f : 1.0f;

				if (!LightingIsValid && ProbeGBuffer.SceneDepth > 0)
				{
					uint SharedListIndex;
					InterlockedAdd(SharedNumProbesToAllocate, 1, SharedListIndex);
					SharedProbeScreenPositionsToAllocate[SharedListIndex] = ScreenProbeScreenPosition;
					SharedScreenProbeGBuffer[SharedListIndex] = ProbeGBuffer;
				}
			}
		}
	}

	GroupMemoryBarrierWithGroupSync();

	if (ThreadIndex == 0)
	{
		InterlockedAdd(_RWNumAdaptiveScreenProbes[0], SharedNumProbesToAllocate, SharedAdaptiveProbeBaseIndex);
	}

	GroupMemoryBarrierWithGroupSync();

	uint AdaptiveProbeIndex = ThreadIndex + SharedAdaptiveProbeBaseIndex;

	if (ThreadIndex < SharedNumProbesToAllocate && AdaptiveProbeIndex < _MaxNumAdaptiveProbes)
	{
		uint2 ScreenProbeScreenPosition = SharedProbeScreenPositionsToAllocate[ThreadIndex];
		_RWAdaptiveScreenProbeData[AdaptiveProbeIndex] = EncodeScreenProbeData(ScreenProbeScreenPosition);
		uint2 ScreenTileCoord = GetScreenTileCoord(ScreenProbeScreenPosition);

		uint TileProbeIndex;
		InterlockedAdd(_RWScreenTileAdaptiveProbeHeader[ScreenTileCoord], 1, TileProbeIndex);
		uint2 AdaptiveProbeCoord = GetAdaptiveProbeCoord(ScreenTileCoord, TileProbeIndex);
		_RWScreenTileAdaptiveProbeIndices[AdaptiveProbeCoord] = AdaptiveProbeIndex;
		
		float2 ScreenUV = (ScreenProbeScreenPosition + .5f) * _View_BufferSizeAndInvSize.zw;
		uint ScreenProbeIndex = _NumUniformScreenProbes + AdaptiveProbeIndex;
		uint2 ScreenProbeAtlasCoord = uint2(ScreenProbeIndex % _ScreenProbeAtlasViewSize.x, ScreenProbeIndex / _ScreenProbeAtlasViewSize.x);
		WriteDownsampledProbeGBuffer(ScreenUV, ScreenProbeAtlasCoord, SharedScreenProbeGBuffer[ThreadIndex]);
	}
}